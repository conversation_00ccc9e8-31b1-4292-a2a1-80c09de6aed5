<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Auto-fill Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .dev-notice {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            color: #1976d2;
        }
        .env-info {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        button {
            background-color: #2196f3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Login Auto-fill Test</h1>
        
        <div class="env-info">
            <h3>Environment Detection:</h3>
            <p><strong>VITE_APP_ENV:</strong> <span id="env-value">Not set</span></p>
            <p><strong>Auto-fill Active:</strong> <span id="autofill-status">No</span></p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="Enter your email">
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Enter your password">
                <div id="dev-notice" class="dev-notice" style="display: none;">
                    ℹ️ Development mode: Password auto-filled with test credentials
                </div>
            </div>

            <button type="submit">Sign In</button>
        </form>
    </div>

    <script>
        // Simulate the environment detection logic from the React component
        function checkEnvironment() {
            // In a real Vite app, this would be import.meta.env.VITE_APP_ENV
            // For this test, we'll simulate it
            const envValue = 'development'; // This would come from Vite in real app
            
            document.getElementById('env-value').textContent = envValue;
            
            if (envValue === 'development') {
                // Auto-fill password
                document.getElementById('password').value = 'TestPassword123!';
                document.getElementById('autofill-status').textContent = 'Yes';
                document.getElementById('dev-notice').style.display = 'block';
            } else {
                document.getElementById('autofill-status').textContent = 'No';
                document.getElementById('dev-notice').style.display = 'none';
            }
        }

        // Run environment check when page loads
        document.addEventListener('DOMContentLoaded', checkEnvironment);

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            alert(`Login attempt:\nEmail: ${email}\nPassword: ${password}`);
        });
    </script>
</body>
</html>
