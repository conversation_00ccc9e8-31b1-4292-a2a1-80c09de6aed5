#!/usr/bin/env python3
"""
Assivy Backend - Unified CLI Tool
A comprehensive command-line interface for managing the Assivy backend application.

This tool consolidates all development, testing, and deployment operations into a single
entry point, following best practices for Python CLI applications.
"""

import os
import sys
import asyncio
import subprocess
import argparse
import uvicorn
from pathlib import Path
from typing import Optional, List

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import warnings suppression first
import utils.warnings_suppression


class AssivyCLI:
    """Main CLI class for Assivy Backend operations."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser."""
        parser = argparse.ArgumentParser(
            description="Assivy Backend - Unified CLI Tool",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s dev                    # Start development server with auto-reload
  %(prog)s start                  # Start production server
  %(prog)s test                   # Run all tests
  %(prog)s test --unit            # Run unit tests only
  %(prog)s test --integration     # Run integration tests only
  %(prog)s services start         # Start Docker services (MinIO, Weaviate)
  %(prog)s services stop          # Stop Docker services
  %(prog)s install                # Install dependencies
  %(prog)s lint                   # Run code linting
  %(prog)s format                 # Format code
  %(prog)s clean                  # Clean generated files
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Server commands
        self._add_server_commands(subparsers)
        
        # Testing commands
        self._add_test_commands(subparsers)
        
        # Development commands
        self._add_dev_commands(subparsers)
        
        # Services commands
        self._add_services_commands(subparsers)
        
        # Utility commands
        self._add_utility_commands(subparsers)
        
        return parser
    
    def _add_server_commands(self, subparsers):
        """Add server-related commands."""
        # Start command
        start_parser = subparsers.add_parser('start', help='Start the server')
        start_parser.add_argument(
            '--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)'
        )
        start_parser.add_argument(
            '--port', type=int, default=8000, help='Port to bind to (default: 8000)'
        )
        start_parser.add_argument(
            '--reload', action='store_true', help='Enable auto-reload (development mode)'
        )
        start_parser.add_argument(
            '--skip-init', action='store_true', help='Skip system initialization'
        )
        
        # Dev command (alias for start with reload)
        dev_parser = subparsers.add_parser('dev', help='Start development server with auto-reload')
        dev_parser.add_argument(
            '--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)'
        )
        dev_parser.add_argument(
            '--port', type=int, default=8000, help='Port to bind to (default: 8000)'
        )
        dev_parser.add_argument(
            '--skip-init', action='store_true', help='Skip system initialization'
        )
    
    def _add_test_commands(self, subparsers):
        """Add testing-related commands."""
        test_parser = subparsers.add_parser('test', help='Run tests')
        test_parser.add_argument(
            '--unit', action='store_true', help='Run unit tests only'
        )
        test_parser.add_argument(
            '--integration', action='store_true', help='Run integration tests only'
        )
        test_parser.add_argument(
            '--fast', action='store_true', help='Run fast tests (exclude slow tests)'
        )
        test_parser.add_argument(
            '--watch', action='store_true', help='Run tests in watch mode'
        )
        test_parser.add_argument(
            '--debug', action='store_true', help='Run tests with debug output'
        )
        test_parser.add_argument(
            '--coverage', action='store_true', help='Generate coverage report'
        )
        test_parser.add_argument(
            '--open-coverage', action='store_true', help='Open coverage report in browser'
        )
        test_parser.add_argument(
            '--ci', action='store_true', help='Run tests for CI environment'
        )
        test_parser.add_argument(
            'pattern', nargs='?', help='Test pattern to match'
        )
    
    def _add_dev_commands(self, subparsers):
        """Add development-related commands."""
        # Install command
        install_parser = subparsers.add_parser('install', help='Install dependencies')
        install_parser.add_argument(
            '--dev', action='store_true', help='Install development dependencies'
        )
        install_parser.add_argument(
            '--test', action='store_true', help='Install test dependencies'
        )
        
        # Lint command
        lint_parser = subparsers.add_parser('lint', help='Run code linting')
        lint_parser.add_argument(
            '--fix', action='store_true', help='Auto-fix linting issues where possible'
        )
        
        # Format command
        format_parser = subparsers.add_parser('format', help='Format code')
        format_parser.add_argument(
            '--check', action='store_true', help='Check formatting without making changes'
        )
        
        # Type check command
        subparsers.add_parser('type-check', help='Run type checking with mypy')
        
        # Security check command
        subparsers.add_parser('security', help='Run security checks')
    
    def _add_services_commands(self, subparsers):
        """Add services-related commands."""
        services_parser = subparsers.add_parser('services', help='Manage Docker services')
        services_parser.add_argument(
            'action', choices=['start', 'stop', 'restart', 'status'],
            help='Action to perform on services'
        )
        services_parser.add_argument(
            '--wait', action='store_true', help='Wait for services to be ready'
        )
    
    def _add_utility_commands(self, subparsers):
        """Add utility commands."""
        # Clean command
        clean_parser = subparsers.add_parser('clean', help='Clean generated files')
        clean_parser.add_argument(
            '--all', action='store_true', help='Clean all files including virtual environment'
        )
        
        # Environment check command
        subparsers.add_parser('env-check', help='Check environment setup')
        
        # Help command
        help_parser = subparsers.add_parser('help', help='Show detailed help')
        help_parser.add_argument(
            'command', nargs='?', help='Command to get help for'
        )
    
    def run(self, args: Optional[List[str]] = None) -> int:
        """Run the CLI with the given arguments."""
        parsed_args = self.parser.parse_args(args)
        
        if not parsed_args.command:
            self.parser.print_help()
            return 1
        
        try:
            if parsed_args.command == 'start':
                return self._start_server(parsed_args)
            elif parsed_args.command == 'dev':
                return self._start_dev_server(parsed_args)
            elif parsed_args.command == 'test':
                return self._run_tests(parsed_args)
            elif parsed_args.command == 'install':
                return self._install_dependencies(parsed_args)
            elif parsed_args.command == 'lint':
                return self._run_lint(parsed_args)
            elif parsed_args.command == 'format':
                return self._run_format(parsed_args)
            elif parsed_args.command == 'type-check':
                return self._run_type_check()
            elif parsed_args.command == 'security':
                return self._run_security_check()
            elif parsed_args.command == 'services':
                return self._manage_services(parsed_args)
            elif parsed_args.command == 'clean':
                return self._clean_files(parsed_args)
            elif parsed_args.command == 'env-check':
                return self._check_environment()
            elif parsed_args.command == 'help':
                return self._show_help(parsed_args)
            else:
                print(f"Unknown command: {parsed_args.command}")
                return 1
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 130
        except Exception as e:
            print(f"Error: {e}")
            return 1
    
    def _start_server(self, args) -> int:
        """Start the production server."""
        print("🚀 Starting Assivy Backend Server...")
        print(f"📍 Server will be available at: http://{args.host}:{args.port}")
        print(f"📖 API Documentation: http://{args.host}:{args.port}/docs")
        
        if not args.skip_init:
            success = asyncio.run(self._initialize_system())
            if not success:
                return 1
        
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level="info"
        )
        return 0
    
    def _start_dev_server(self, args) -> int:
        """Start the development server."""
        print("🚀 Starting Assivy Backend Development Server...")
        print(f"📍 Server will be available at: http://{args.host}:{args.port}")
        print(f"📖 API Documentation: http://{args.host}:{args.port}/docs")
        print("🔄 Auto-reload enabled for development")
        
        if not args.skip_init:
            success = asyncio.run(self._initialize_system())
            if not success:
                return 1
        
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            reload=True,
            log_level="info"
        )
        return 0
    
    async def _initialize_system(self) -> bool:
        """Initialize the system with required roles, tenants, and admin users."""
        try:
            print("🚀 Initializing Assivy System...")
            
            # Initialize Weaviate connection first
            from data_access.weaviatedb import connect_to_weaviate
            await connect_to_weaviate()
            
            from services.system_service import system_service
            result = await system_service.initialize_system()
            
            print("\n✅ System Initialization Results:")
            print(f"   • System roles created: {result['system_roles_created']}")
            print(f"   • Assivy Platform tenant created: {result['platform_tenant_created']}")
            print(f"   • Assivy Customer Test tenant created: {result['customer_tenant_created']}")
            print(f"   • System admin created: {result['system_admin_created']}")
            print(f"   • Customer admin created: {result['customer_admin_created']}")
            
            if result["errors"]:
                print(f"\n⚠️  Errors encountered: {len(result['errors'])}")
                for error in result["errors"]:
                    print(f"   - {error}")
            
            print("\n🎉 System initialization completed!")
            
            # Print login credentials
            from config import settings
            print("\n🔑 Login Credentials:")
            print(f"   System Admin (Assivy Platform):")
            print(f"   • Email: {settings.system_admin_email}")
            print(f"   • Password: {settings.system_admin_password}")
            print(f"   • Tenant: Assivy Platform")
            print(f"   • Capabilities: Full system access, tenant management")
            print(f"   ")
            print(f"   Administrator (Assivy Customer Test):")
            print(f"   • Email: {getattr(settings, 'customer_admin_email', '<EMAIL>')}")
            print(f"   • Password: {getattr(settings, 'customer_admin_password', 'Administrator123!')}")
            print(f"   • Tenant: Assivy Customer Test")
            print(f"   • Capabilities: Full access within customer test tenant")
            
            return True
            
        except Exception as e:
            print(f"❌ System initialization failed: {str(e)}")
            return False
    
    def _run_tests(self, args) -> int:
        """Run tests based on the given arguments."""
        cmd = ["uv", "run", "pytest"]
        
        if args.unit:
            cmd.extend(["-m", "unit"])
        elif args.integration:
            cmd.extend(["-m", "integration"])
        elif args.fast:
            cmd.extend(["-m", "not slow"])
        elif args.watch:
            cmd = ["uv", "run", "ptw", "--", "-v"]
        elif args.debug:
            cmd.extend(["-v", "-s", "--tb=short"])
        elif args.ci:
            cmd.extend(["--cov=.", "--cov-report=xml", "--junitxml=junit.xml", "-v"])
        else:
            cmd.extend(["--cov=.", "--cov-report=term-missing", "--cov-report=html:htmlcov", "-v"])
        
        if args.coverage:
            cmd.extend(["--cov=.", "--cov-report=html:htmlcov", "--cov-report=xml"])
        
        if args.pattern:
            cmd.append(args.pattern)
        
        result = subprocess.run(cmd, cwd=self.project_root)
        
        if args.open_coverage and result.returncode == 0:
            try:
                subprocess.run(["open", "htmlcov/index.html"])
            except FileNotFoundError:
                print("Could not open coverage report automatically")
        
        return result.returncode
    
    def _install_dependencies(self, args) -> int:
        """Install dependencies."""
        print("📦 Installing dependencies...")
        
        cmd = ["uv", "sync"]
        if args.dev:
            cmd = ["uv", "add", "--group", "dev", "black", "isort", "flake8", "mypy", "pre-commit"]
        elif args.test:
            cmd = ["uv", "add", "--group", "test", "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock", "httpx", "factory-boy", "faker", "freezegun"]
        
        return subprocess.run(cmd, cwd=self.project_root).returncode
    
    def _run_lint(self, args) -> int:
        """Run code linting."""
        print("🔍 Running code linting...")
        
        if args.fix:
            # Run formatters that can auto-fix
            subprocess.run(["uv", "run", "black", "."], cwd=self.project_root)
            subprocess.run(["uv", "run", "isort", "."], cwd=self.project_root)
        
        # Run all linting checks
        results = []
        results.append(subprocess.run(["uv", "run", "black", "--check", "."], cwd=self.project_root))
        results.append(subprocess.run(["uv", "run", "isort", "--check-only", "."], cwd=self.project_root))
        results.append(subprocess.run(["uv", "run", "flake8", "."], cwd=self.project_root))
        
        return max(result.returncode for result in results)
    
    def _run_format(self, args) -> int:
        """Format code."""
        print("🎨 Formatting code...")
        
        if args.check:
            results = []
            results.append(subprocess.run(["uv", "run", "black", "--check", "."], cwd=self.project_root))
            results.append(subprocess.run(["uv", "run", "isort", "--check-only", "."], cwd=self.project_root))
            return max(result.returncode for result in results)
        else:
            subprocess.run(["uv", "run", "black", "."], cwd=self.project_root)
            subprocess.run(["uv", "run", "isort", "."], cwd=self.project_root)
            return 0
    
    def _run_type_check(self) -> int:
        """Run type checking."""
        print("🔍 Running type checking...")
        return subprocess.run(["uv", "run", "mypy", "."], cwd=self.project_root).returncode
    
    def _run_security_check(self) -> int:
        """Run security checks."""
        print("🔒 Running security checks...")
        
        results = []
        results.append(subprocess.run(["uv", "run", "bandit", "-r", ".", "-x", "tests/"], cwd=self.project_root))
        results.append(subprocess.run(["uv", "run", "safety", "check"], cwd=self.project_root))
        
        return max(result.returncode for result in results)
    
    def _manage_services(self, args) -> int:
        """Manage Docker services."""
        if args.action == "start":
            print("🐳 Starting Docker services...")
            result = subprocess.run(["docker-compose", "up", "-d", "minio", "weaviate"], cwd=self.project_root)
            
            if result.returncode == 0 and args.wait:
                return self._wait_for_services()
            
            return result.returncode
        
        elif args.action == "stop":
            print("🛑 Stopping Docker services...")
            return subprocess.run(["docker-compose", "down"], cwd=self.project_root).returncode
        
        elif args.action == "restart":
            print("🔄 Restarting Docker services...")
            subprocess.run(["docker-compose", "down"], cwd=self.project_root)
            result = subprocess.run(["docker-compose", "up", "-d", "minio", "weaviate"], cwd=self.project_root)
            
            if result.returncode == 0 and args.wait:
                return self._wait_for_services()
            
            return result.returncode
        
        elif args.action == "status":
            print("📊 Checking service status...")
            return subprocess.run(["docker-compose", "ps"], cwd=self.project_root).returncode
        
        return 1
    
    def _wait_for_services(self) -> int:
        """Wait for services to be ready."""
        print("⏳ Waiting for services to be ready...")
        
        # Wait for MinIO
        print("🗄️  Checking MinIO...")
        for i in range(30):
            try:
                result = subprocess.run(
                    ["curl", "-f", "http://localhost:9000/minio/health/live"],
                    capture_output=True, timeout=5
                )
                if result.returncode == 0:
                    print("✅ MinIO is ready!")
                    break
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass
            
            if i == 29:
                print("❌ MinIO failed to start within 30 seconds")
                return 1
            
            import time
            time.sleep(1)
        
        # Wait for Weaviate
        print("🔍 Checking Weaviate...")
        for i in range(30):
            try:
                result = subprocess.run(
                    ["curl", "-f", "http://localhost:8080/v1/meta"],
                    capture_output=True, timeout=5
                )
                if result.returncode == 0:
                    print("✅ Weaviate is ready!")
                    break
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass
            
            if i == 29:
                print("❌ Weaviate failed to start within 30 seconds")
                return 1
            
            import time
            time.sleep(1)
        
        print("\n🎉 All services are ready!")
        print("\n📋 Service URLs:")
        print("   MinIO API:     http://localhost:9000")
        print("   MinIO Console: http://localhost:9001 (admin/minioadmin)")
        print("   Weaviate:      http://localhost:8080")
        
        return 0
    
    def _clean_files(self, args) -> int:
        """Clean generated files."""
        print("🧹 Cleaning generated files...")
        
        files_to_remove = [
            "htmlcov/",
            ".coverage",
            "coverage.xml",
            ".pytest_cache/",
            "junit.xml"
        ]
        
        for file_path in files_to_remove:
            path = self.project_root / file_path
            if path.exists():
                if path.is_dir():
                    import shutil
                    shutil.rmtree(path)
                else:
                    path.unlink()
                print(f"   Removed: {file_path}")
        
        # Remove Python cache files
        for pycache in self.project_root.rglob("__pycache__"):
            import shutil
            shutil.rmtree(pycache)
            print(f"   Removed: {pycache.relative_to(self.project_root)}")
        
        for pyc_file in self.project_root.rglob("*.pyc"):
            pyc_file.unlink()
            print(f"   Removed: {pyc_file.relative_to(self.project_root)}")
        
        if args.all:
            print("🧹 Cleaning all files including virtual environment...")
            venv_path = self.project_root / ".venv"
            if venv_path.exists():
                import shutil
                shutil.rmtree(venv_path)
                print("   Removed: .venv/")
            
            lock_file = self.project_root / "uv.lock"
            if lock_file.exists():
                lock_file.unlink()
                print("   Removed: uv.lock")
        
        print("✅ Cleanup completed!")
        return 0
    
    def _check_environment(self) -> int:
        """Check environment setup."""
        print("🔍 Checking environment setup...")
        
        # Check Python version
        python_version = sys.version_info
        print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 12):
            print("⚠️  Warning: Python 3.12+ is recommended")
        
        # Check UV
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"UV version: {result.stdout.strip()}")
            else:
                print("❌ UV not found or not working properly")
                return 1
        except FileNotFoundError:
            print("❌ UV not found. Please install UV first.")
            return 1
        
        # Check project dependencies
        print("\nProject dependencies:")
        subprocess.run(["uv", "tree"], cwd=self.project_root)
        
        return 0
    
    def _show_help(self, args) -> int:
        """Show detailed help."""
        if args.command:
            # Show help for specific command
            try:
                subprocess.run([sys.executable, __file__, args.command, "--help"])
            except Exception:
                print(f"No help available for command: {args.command}")
                return 1
        else:
            # Show general help
            self.parser.print_help()
        
        return 0


def main():
    """Main entry point."""
    cli = AssivyCLI()
    sys.exit(cli.run())


if __name__ == "__main__":
    main() 