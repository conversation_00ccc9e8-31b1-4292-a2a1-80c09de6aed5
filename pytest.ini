[pytest]
minversion = 6.0
addopts = -ra --strict-markers --strict-config -v
testpaths = tests
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
markers =
    unit: marks tests as unit tests (deselect with '-m "not unit"')
    integration: marks tests as integration tests (deselect with '-m "not integration"')
    slow: marks tests as slow (deselect with '-m "not slow"')
    auth: marks tests as requiring authentication
    database: marks tests as requiring database access
    external: marks tests as requiring external services
