from typing import Dict, List, Any, Annotated, TypeVar, Union, Optional, Set, Tuple
from langgraph.graph.graph import CompiledGraph
from langgraph.graph import Graph, StateGraph
from langchain_core.agents import AgentAction, AgentFinish
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.language_models.base import BaseLanguageModel
from langgraph.prebuilt import create_react_agent
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain_anthropic import ChatAnthropic
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from models.agent import Agent, Usecase, AgentUsecaseConfig
from models.ai_model import AIModel
from models.action import (
    BaseAction, APICallAction, FunctionCallAction,
    PromptTemplateAction, KnowledgeBaseAnswerAction
)
from models.user import User
from services.resource_service import ResourceService
from services.action_service import ActionService
from services.resource_indexer import ResourceIndexer
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository
import logging
from config import settings
from datetime import datetime
from uuid import UUID, uuid4

logger = logging.getLogger(__name__)

class AgentService:
    def __init__(self):
        self.resource_service: Optional[ResourceService] = None
        # Note: ResourceIndexer instances should be created with specific tenant_id in methods
        self.action_service: Optional[ActionService] = None
        self.agent_repository: Optional[BaseRepository[Agent]] = None
        self.usecase_repository: Optional[BaseRepository[Usecase]] = None
        self.ai_model_repository: Optional[BaseRepository[AIModel]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize the repositories and dependent services"""
        if self._initialized:
            return
            
        try:
            # Initialize repositories
            if not self.agent_repository:
                self.agent_repository = await RepositoryFactory.create_repository(Agent)
            if not self.usecase_repository:
                self.usecase_repository = await RepositoryFactory.create_repository(Usecase)
            if not self.ai_model_repository:
                self.ai_model_repository = await RepositoryFactory.create_repository(AIModel)
            
            # Initialize dependent services - delay this to avoid circular dependency
            # Services will be lazy-loaded when needed
            
            self._initialized = True
            logger.info("AgentService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AgentService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()
    
    async def _ensure_resource_service(self):
        """Lazy load resource service to avoid circular dependency"""
        if not self.resource_service:
            from .service_manager import get_resource_service
            self.resource_service = await get_resource_service()
        return self.resource_service
    
    async def _ensure_action_service(self):
        """Lazy load action service to avoid circular dependency"""
        if not self.action_service:
            from .service_manager import get_action_service
            self.action_service = await get_action_service()
        return self.action_service

    async def create_agent(self, agent: Agent, creator: User) -> Agent:
        """Create a new agent"""
        try:
            await self._ensure_initialized()
            
            # Set metadata
            agent.id = uuid4()
            agent.owner_id = creator.id
            agent.created_by = creator.id
            agent.updated_by = creator.id
            agent.created_at = datetime.utcnow()
            agent.updated_at = datetime.utcnow()
            
            # Save to database
            created_agent = await self.agent_repository.create(agent)
            return created_agent
        except Exception as e:
            logger.error(f"Error creating agent: {str(e)}")
            raise

    async def list_agents(
        self, 
        requester: User, 
        skip: int = 0, 
        limit: int = 100,
        query_conditions: Dict[str, Any] = None
    ) -> List[Agent]:
        """List agents with optional filtering"""
        try:
            await self._ensure_initialized()
            
            # Add requester-specific filtering if needed
            conditions = query_conditions or {}
            
            # For now, allow users to see all agents, but in production 
            # you might want to filter by owner_id or tenant
            agents = await self.agent_repository.get_all(
                skip=skip,
                limit=limit,
                query_conditions=conditions
            )
            return agents
        except Exception as e:
            logger.error(f"Error listing agents: {str(e)}")
            raise

    async def get_agent(self, agent_id: str, requester: User) -> Optional[Agent]:
        """Get a specific agent by ID"""
        try:
            await self._ensure_initialized()
            
            agent_uuid = UUID(agent_id)
            agent = await self.agent_repository.get_by_id(agent_uuid)
            
            # Check if user has access to this agent
            if agent and self._has_agent_access(agent, requester):
                return agent
            return None
        except ValueError:
            raise ValueError(f"Invalid agent ID format: {agent_id}")
        except Exception as e:
            logger.error(f"Error getting agent: {str(e)}")
            raise

    async def update_agent(self, agent_id: str, agent: Agent, updater: User) -> Optional[Agent]:
        """Update an existing agent"""
        try:
            await self._ensure_initialized()
            
            agent_uuid = UUID(agent_id)
            existing_agent = await self.agent_repository.get_by_id(agent_uuid)
            
            if not existing_agent:
                return None
                
            # Check if user has access to update this agent
            if not self._has_agent_access(existing_agent, updater):
                return None
            
            # Update metadata
            agent.id = agent_uuid
            agent.updated_by = updater.id
            agent.updated_at = datetime.utcnow()
            # Preserve original creation data
            agent.created_by = existing_agent.created_by
            agent.created_at = existing_agent.created_at
            agent.owner_id = existing_agent.owner_id
            
            updated_agent = await self.agent_repository.update(agent_uuid, agent)
            return updated_agent
        except ValueError:
            raise ValueError(f"Invalid agent ID format: {agent_id}")
        except Exception as e:
            logger.error(f"Error updating agent: {str(e)}")
            raise

    async def delete_agent(self, agent_id: str, deleter: User) -> bool:
        """Delete an agent"""
        try:
            await self._ensure_initialized()
            
            agent_uuid = UUID(agent_id)
            existing_agent = await self.agent_repository.get_by_id(agent_uuid)
            
            if not existing_agent:
                return False
                
            # Check if user has access to delete this agent
            if not self._has_agent_access(existing_agent, deleter):
                return False
            
            success = await self.agent_repository.delete(agent_uuid)
            return success
        except ValueError:
            raise ValueError(f"Invalid agent ID format: {agent_id}")
        except Exception as e:
            logger.error(f"Error deleting agent: {str(e)}")
            raise

    async def execute_query(self, agent: Agent, query: str, executor: User) -> Dict[str, Any]:
        """Execute a query using an agent"""
        try:
            # Check if user has access to execute this agent
            if not self._has_agent_access(agent, executor):
                raise ValueError("Access denied to execute this agent")
            
            # Execute the agent
            result = await self.execute_agent(agent, query)
            
            return {
                "agent_id": str(agent.id),
                "query": query,
                "result": result,
                "executed_by": str(executor.id),
                "executed_at": datetime.utcnow().isoformat()
            }
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            raise

    async def get_available_models(self, vendor: str) -> List[Dict[str, Any]]:
        """Get available models for a vendor"""
        try:
            models_map = {
                "openai": [
                    {"name": "gpt-4o", "description": "Latest GPT-4 Omni model"},
                    {"name": "gpt-4o-mini", "description": "Faster, cheaper GPT-4 Omni"},
                    {"name": "gpt-4-turbo", "description": "GPT-4 Turbo with 128K context"},
                    {"name": "gpt-3.5-turbo", "description": "ChatGPT model"}
                ],
                "anthropic": [
                    {"name": "claude-3-5-sonnet-20240620", "description": "Claude 3.5 Sonnet"},
                    {"name": "claude-3-opus-20240229", "description": "Claude 3 Opus"},
                    {"name": "claude-3-sonnet-20240229", "description": "Claude 3 Sonnet"},
                    {"name": "claude-3-haiku-20240307", "description": "Claude 3 Haiku"}
                ],
                "google": [
                    {"name": "gemini-1.5-pro", "description": "Gemini 1.5 Pro"},
                    {"name": "gemini-1.5-flash", "description": "Gemini 1.5 Flash"},
                    {"name": "gemini-pro", "description": "Gemini Pro"}
                ],
                "ollama": [
                    {"name": "llama3", "description": "Meta Llama 3"},
                    {"name": "llama2", "description": "Meta Llama 2"},
                    {"name": "codellama", "description": "Code Llama"},
                    {"name": "mistral", "description": "Mistral 7B"}
                ]
            }
            
            return models_map.get(vendor, [])
        except Exception as e:
            logger.error(f"Error getting available models: {str(e)}")
            raise

    def _has_agent_access(self, agent: Agent, user: User) -> bool:
        """Check if user has access to the agent"""
        # For now, simple owner check. In production, you might want
        # to check tenant membership, roles, etc.
        return agent.owner_id == user.id

    async def _create_agent_actions(self, agent: Agent) -> Dict:
        """Create actions based on agent configuration"""
        actions = {}
        
        # Get configured usecases for this agent
        for usecase_config in agent.usecase_configs:
            if not usecase_config.is_enabled:
                continue
                
            # Get the usecase definition
            usecase = await self._get_usecase(usecase_config.usecase_id)
            if not usecase:
                continue
            
            # Resolve parameters: usecase defaults + agent overrides
            resolved_params = usecase.default_parameters.copy()
            resolved_params.update(usecase_config.parameters)
                
            for action_id in usecase.action_ids:
                # Get the action definition from the action service
                action = await self.action_service.get_action(action_id)
                if not action:
                    continue
                
                # Get action-specific parameter overrides
                action_overrides = usecase_config.action_overrides.get(action_id, {})
                final_params = resolved_params.copy()
                final_params.update(action_overrides)
                    
                # Create action based on type
                if isinstance(action, KnowledgeBaseAnswerAction):
                    actions.update(self._create_knowledge_search_action(
                        f"tenant_{agent.tenant_id}",
                        action,
                        final_params
                    ))
                elif isinstance(action, APICallAction):
                    actions[action.name] = self._create_api_call_action(
                        action,
                        final_params
                    )
                elif isinstance(action, FunctionCallAction):
                    actions[action.name] = self._create_function_call_action(
                        action,
                        final_params
                    )
                elif isinstance(action, PromptTemplateAction):
                    actions[action.name] = self._create_prompt_template_action(
                        action,
                        final_params
                    )
        
        return actions
    
    async def _get_usecase(self, usecase_id: UUID) -> Optional[Usecase]:
        """Get usecase by ID"""
        try:
            await self._ensure_initialized()
            return await self.usecase_repository.get_by_id(usecase_id)
        except Exception as e:
            logger.error(f"Error getting usecase {usecase_id}: {str(e)}")
            return None

    def _create_knowledge_search_action(
        self, 
        index_name: str, 
        action: KnowledgeBaseAnswerAction,
        parameters: Dict[str, Any]
    ) -> Dict:
        """Create an action for searching knowledge base"""
        return {
            action.name: {
                "name": action.name,
                "description": action.description,
                "function": lambda q: self.resource_indexer.search(
                    index_name, 
                    q, 
                    max_results=parameters.get(
                        'max_results', 
                        action.max_retrieved_documents
                    )
                )
            }
        }

    def _create_api_call_action(
        self,
        action: APICallAction,
        parameters: Dict[str, Any]
    ) -> Dict:
        """Create an action for making API calls"""
        async def api_call_function(**kwargs):
            # Implement API call logic here using action.url, action.method, etc.
            # Use parameters to override defaults if needed
            pass
            
        return {
            "name": action.name,
            "description": action.description,
            "function": api_call_function
        }

    def _create_function_call_action(
        self,
        action: FunctionCallAction,
        parameters: Dict[str, Any]
    ) -> Dict:
        """Create an action for function calls"""
        return {
            "name": action.name,
            "description": action.description,
            "function": self._load_function(action.function_name)
        }

    def _load_function(self, function_name: str):
        """Load a function by its fully qualified name"""
        try:
            module_name, func_name = function_name.rsplit('.', 1)
            module = __import__(module_name, fromlist=[func_name])
            return getattr(module, func_name)
        except Exception as e:
            logger.error(f"Error loading function {function_name}: {str(e)}")
            # Return a dummy function that raises an error
            def error_function(*args, **kwargs):
                raise ValueError(f"Function {function_name} could not be loaded: {str(e)}")
            return error_function

    def _create_prompt_template_action(
        self,
        action: PromptTemplateAction,
        parameters: Dict[str, Any]
    ) -> Dict:
        """Create an action for prompt templates"""
        return {
            "name": action.name,
            "description": action.description,
            "function": lambda **kwargs: {
                "result": action.template_string.format(**kwargs)
            }
        }

    async def _select_actions(
        self, 
        query: str, 
        agent: Agent
    ) -> List[BaseAction]:
        """Select appropriate actions based on agent's usecases"""
        selected_actions = set()
        
        # Get all actions from agent's configured usecases
        for usecase_config in agent.usecase_configs:
            if not usecase_config.is_enabled:
                continue
                
            usecase = await self._get_usecase(usecase_config.usecase_id)
            if not usecase:
                continue
                
            for action_id in usecase.action_ids:
                # Get the action from the action service
                action = await self.action_service.get_action(action_id)
                if action:
                    selected_actions.add(action)
        
        return list(selected_actions)

    def _create_agent_prompt(self, agent: Agent) -> Union[SystemMessage, str]:
        """Create a prompt template based on agent configuration"""
        system_message = f"""You are {agent.name}. {agent.description or ''}
        
                Use the available actions to help users with their queries.
                Think step by step:
                1. Understand the user's request
                2. Select relevant actions
                3. Execute actions in a logical sequence
                4. Synthesize results into a coherent response

                If you're unsure about something, ask for clarification."""

        return system_message

    async def _get_llm(self, agent: Agent) -> BaseLanguageModel:
        """Get the language model based on agent's model configuration"""
        # Get the AI model configuration
        model = await self._get_ai_model(agent.model_id)
        if not model:
            raise ValueError(f"AI model not found for agent {agent.name}")
            
        if model.provider == "openai":
            return ChatOpenAI(
                model=model.model_name,
                temperature=model.temperature,
                **model.config
            )
        elif model.provider == "anthropic":
            return ChatAnthropic(
                model=model.model_name,
                temperature=model.temperature,
                **model.config
            )
        elif model.provider == "google":
            return ChatGoogleGenerativeAI(
                model=model.model_name,
                temperature=model.temperature,
                **model.config
            )
        else:
            raise ValueError(f"Unsupported model provider: {model.provider}")
    
    async def _get_ai_model(self, model_id: UUID) -> Optional[AIModel]:
        """Get AI model by ID"""
        try:
            await self._ensure_initialized()
            return await self.ai_model_repository.get_by_id(model_id)
        except Exception as e:
            logger.error(f"Error getting AI model {model_id}: {str(e)}")
            # Return a default model as fallback
            return AIModel(
                id=model_id,
                name="Default GPT-4",
                provider="openai",
                model_name="gpt-4",
                temperature=0.1,
                tenant_id=uuid4()  # This should come from the agent's tenant
            )

    async def create_agent_instance(self, agent: Agent) -> CompiledGraph:
        """Create an agent instance with specified configuration"""
        # Get language model
        llm = await self._get_llm(agent)
        
        # Get actions
        actions = await self._create_agent_actions(agent)
        
        # Create prompt
        prompt = self._create_agent_prompt(agent)
        
        # Create agent
        agent_instance = create_react_agent(llm, actions, prompt)
     
        return agent_instance

    async def execute_agent(self, agent: Agent, query: str, history: List[BaseMessage] = None) -> Dict:
        """Execute an agent instance with the given query"""
        # Create agent instance
        agent_instance = await self.create_agent_instance(agent)
        
        # Prepare message input format
        messages = [{"role": "user", "content": query}]
        if history:
            messages.extend([{
                "role": "user" if isinstance(msg, HumanMessage) else "assistant",
                "content": msg.content
            } for msg in history])
            
        # Prepare inputs in correct format
        inputs = {
            "messages": messages
        }
        
        try:
            # Execute with proper message format
            result = await agent_instance.ainvoke(inputs)
            
            # Update last active timestamp
            agent.last_active = datetime.utcnow()
            
            return result
        except Exception as e:
            logger.error(f"Error executing agent {agent.name}: {str(e)}")
            raise

    async def create_usecase(self, usecase: Usecase, creator: User) -> Usecase:
        """Create a new usecase"""
        try:
            await self._ensure_initialized()
            
            # Set metadata
            usecase.id = uuid4()
            usecase.created_by = creator.id
            usecase.updated_by = creator.id
            usecase.created_at = datetime.utcnow()
            usecase.updated_at = datetime.utcnow()
            
            # Save to database
            created_usecase = await self.usecase_repository.create(usecase)
            return created_usecase
        except Exception as e:
            logger.error(f"Error creating usecase: {str(e)}")
            raise

    async def create_ai_model(self, ai_model: AIModel, creator: User) -> AIModel:
        """Create a new AI model configuration"""
        try:
            await self._ensure_initialized()
            
            # Set metadata
            ai_model.id = uuid4()
            ai_model.created_by = creator.id
            ai_model.updated_by = creator.id
            ai_model.created_at = datetime.utcnow()
            ai_model.updated_at = datetime.utcnow()
            
            # Save to database
            created_model = await self.ai_model_repository.create(ai_model)
            return created_model
        except Exception as e:
            logger.error(f"Error creating AI model: {str(e)}")
            raise

    async def list_usecases(self, requester: User, tenant_id: UUID, skip: int = 0, limit: int = 100) -> List[Usecase]:
        """List usecases for a tenant"""
        try:
            await self._ensure_initialized()
            
            conditions = {"tenant_id": tenant_id}
            usecases = await self.usecase_repository.get_all(
                skip=skip,
                limit=limit,
                query_conditions=conditions
            )
            return usecases
        except Exception as e:
            logger.error(f"Error listing usecases: {str(e)}")
            raise

    async def list_ai_models(self, requester: User, tenant_id: UUID, skip: int = 0, limit: int = 100) -> List[AIModel]:
        """List AI models for a tenant"""
        try:
            await self._ensure_initialized()
            
            conditions = {"tenant_id": tenant_id}
            models = await self.ai_model_repository.get_all(
                skip=skip,
                limit=limit,
                query_conditions=conditions
            )
            return models
        except Exception as e:
            logger.error(f"Error listing AI models: {str(e)}")
            raise

# Create a singleton instance for backward compatibility
agent_service = AgentService()

# Services are now managed by ServiceManager - no singleton needed