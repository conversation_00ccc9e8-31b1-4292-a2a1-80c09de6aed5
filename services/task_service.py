from typing import List, Dict, Optional
from datetime import datetime
from models.task import Task, TaskStatus, TaskType
from data_access.factory import RepositoryFactory
from fastapi import HTT<PERSON>Exception, status
from uuid import UUID
from models.user import User
import logging

logger = logging.getLogger(__name__)

class TaskService:
    def __init__(self):
        self.task_repo = None
        
    async def initialize(self):
        """Initialize async components like repositories"""
        if self.task_repo is None:
            self.task_repo = await RepositoryFactory.create_task_repository()
    
    async def create_task(
        self,
        name: str,
        task_type: TaskType,
        tenant_id: str,
        user: User,
        schedule: Optional[str] = None,
        resource_id: Optional[str] = None,
        metadata: Optional[dict] = None
    ) -> Task:
        """Create a new task"""
        await self.initialize()
        task = Task(
            name=name,
            type=task_type,
            tenant_id=tenant_id,
            schedule=schedule,
            resource_id=resource_id,
            metadata=metadata or {},
            status=TaskStatus.PENDING,
            owner_id=user.id
        )
        
        return await self.task_repo.create(task, user)
    
    async def get_task(self, task_id: str, user: User) -> Optional[Task]:
        """Get a specific task"""
        await self.initialize()
        return await self.task_repo.get_by_id(task_id, user)
    
    async def list_tasks(
        self,
        user: User,
        status: Optional[TaskStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Task]:
        """List tasks with optional filtering"""
        await self.initialize()
        filter_dict = {}
        if status:
            filter_dict["status"] = status
            
        return await self.task_repo.get_all(skip=skip, limit=limit, query_conditions=filter_dict)
    
    async def update_task(
        self,
        task_id: str,
        update_data: Dict,
        user: User
    ) -> Optional[Task]:
        """Update a task"""
        await self.initialize()
        task = await self.task_repo.get_by_id(task_id, user)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Update fields
        for key, value in update_data.items():
            setattr(task, key, value)
            
        return await self.task_repo.update(task_id, task, user)
    
    async def delete_task(self, task_id: str, user: User) -> bool:
        """Delete a task"""
        await self.initialize()
        return await self.task_repo.delete(task_id, user)
    
    async def update_task_status(
        self,
        task_id: UUID,
        status: TaskStatus,
        user: Optional[User] = None,
        error_message: Optional[str] = None,
        next_run: Optional[datetime] = None
    ) -> Optional[Task]:
        """Update task status and related fields"""
        await self.initialize()
        task = await self.task_repo.get_by_id(task_id)
        if not task:
            return None
            
        task.status = status
        task.error_message = error_message
        
        if status == TaskStatus.RUNNING:
            task.started_at = datetime.utcnow()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            task.completed_at = datetime.utcnow()
            
        if next_run is not None:
            task.next_run = next_run
            
        return await self.task_repo.update(task_id, task, user)
        
# Create singleton instance
task_service = TaskService()
