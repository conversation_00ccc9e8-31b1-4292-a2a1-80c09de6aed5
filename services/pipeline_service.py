from typing import Dict, List, Any, Optional, Union, TypedDict, Annotated
from langgraph.graph import StateGraph, Graph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
import asyncio
import logging
from datetime import datetime, timezone
from uuid import UUID, uuid4

from models.pipeline import (
    Pipeline, PipelineNode, PipelineExecution, NodeExecution,
    NodeType, NodeStatus, PipelineStatus, DataSourceType,
    DataSourceNodeConfig, EntitySchemaNodeConfig, ProcessorNodeConfig,
    ParentChildIndexerConfig, EntityInserterConfig
)
from models.entity import EntityDefinition, EntityInstance
from services.resource_indexer import ResourceIndexer
from data_access.factory import RepositoryFactory
from utils.converters import convert_uuids_to_strings

logger = logging.getLogger(__name__)

# State definition for LangGraph
class PipelineState(TypedDict):
    pipeline_id: str
    execution_id: str
    tenant_id: str
    current_node: Optional[str]
    node_data: Dict[str, Any]  # Data passed between nodes
    processed_documents: List[any]  # LangChain documents
    entities: List[Dict[str, Any]]  # Entity instances
    metadata: Dict[str, Any]  # Pipeline metadata
    errors: List[str]  # Collected errors
    execution_context: Dict[str, Any]  # Execution context

class PipelineService:
    """Service for managing and executing data processing pipelines"""
    
    def __init__(self):
        self.pipeline_repo = None
        self.execution_repo = None
        self.entity_repo = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize repositories"""
        if self._initialized:
            return
        
        try:
            self.pipeline_repo = await RepositoryFactory.create_pipeline_repository()
            self.execution_repo = await RepositoryFactory.create_pipeline_execution_repository()
            self.entity_repo = await RepositoryFactory.create_entity_repository()
            self._initialized = True
            logger.info("PipelineService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PipelineService: {e}")
            raise
    
    async def _ensure_initialized(self):
        """Ensure service is initialized"""
        if not self._initialized:
            await self.initialize()
    
    # Pipeline Management
    async def create_pipeline(self, pipeline: Pipeline, user_id: UUID) -> Pipeline:
        """Create a new pipeline"""
        await self._ensure_initialized()
        
        # Validate pipeline
        self._validate_pipeline(pipeline)
        
        # Save pipeline
        saved_pipeline = await self.pipeline_repo.create(pipeline, str(pipeline.tenant_id))
        
        logger.info(f"Created pipeline {saved_pipeline.id} for tenant {pipeline.tenant_id}")
        return saved_pipeline
    
    async def get_pipeline(self, pipeline_id: UUID, tenant_id: UUID) -> Optional[Pipeline]:
        """Get a pipeline by ID"""
        await self._ensure_initialized()
        return await self.pipeline_repo.get_by_id(str(pipeline_id))
    
    async def list_pipelines(self, tenant_id: UUID, status: Optional[PipelineStatus] = None) -> List[Pipeline]:
        """List pipelines for a tenant"""
        await self._ensure_initialized()
        filters = {"tenant_id": str(tenant_id)}
        if status:
            filters["status"] = status
        return await self.pipeline_repo.find_by_filters(filters)
    
    async def update_pipeline(self, pipeline_id: UUID, updates: Dict[str, Any], user_id: UUID) -> Pipeline:
        """Update a pipeline"""
        await self._ensure_initialized()
        
        pipeline = await self.pipeline_repo.get_by_id(str(pipeline_id))
        if not pipeline:
            raise ValueError(f"Pipeline {pipeline_id} not found")
        
        # Update fields
        for key, value in updates.items():
            if hasattr(pipeline, key):
                setattr(pipeline, key, value)
        
        # Validate updated pipeline
        self._validate_pipeline(pipeline)
        
        # Save updates
        updated_pipeline = await self.pipeline_repo.update(str(pipeline_id), pipeline)
        
        logger.info(f"Updated pipeline {pipeline_id}")
        return updated_pipeline
    
    # Pipeline Execution
    async def execute_pipeline(
        self, 
        pipeline_id: UUID, 
        tenant_id: UUID,
        trigger_data: Optional[Dict[str, Any]] = None,
        user_id: Optional[UUID] = None
    ) -> PipelineExecution:
        """Execute a pipeline using LangGraph"""
        await self._ensure_initialized()
        
        # Get pipeline
        pipeline = await self.get_pipeline(pipeline_id, tenant_id)
        if not pipeline:
            raise ValueError(f"Pipeline {pipeline_id} not found")
        
        if not pipeline.is_active:
            raise ValueError(f"Pipeline {pipeline_id} is not active")
        
        # Create execution record
        execution = PipelineExecution(
            pipeline_id=pipeline_id,
            pipeline_version=pipeline.version,
            tenant_id=tenant_id,
            total_nodes=len(pipeline.nodes),
            trigger_data=trigger_data or {}
        )
        saved_execution = await self.execution_repo.create(execution, str(tenant_id))
        
        try:
            # Build and execute LangGraph
            graph = self._build_langgraph(pipeline)
            
            # Initialize state
            initial_state = PipelineState(
                pipeline_id=str(pipeline_id),
                execution_id=str(saved_execution.id),
                tenant_id=str(tenant_id),
                current_node=None,
                node_data={},
                processed_documents=[],
                entities=[],
                metadata={"user_id": str(user_id) if user_id else None},
                errors=[],
                execution_context=trigger_data or {}
            )
            
            # Execute graph
            logger.info(f"Starting pipeline execution {saved_execution.id}")
            final_state = await graph.ainvoke(initial_state)
            
            # Update execution record
            saved_execution.status = PipelineStatus.COMPLETED if not final_state["errors"] else PipelineStatus.FAILED
            saved_execution.completed_at = datetime.now(timezone.utc)
            saved_execution.nodes_completed = len([n for n in pipeline.nodes if n.id not in [e.split(":")[0] for e in final_state["errors"]]])
            saved_execution.nodes_failed = len(final_state["errors"])
            
            if final_state["errors"]:
                saved_execution.error_message = "; ".join(final_state["errors"])
            
            await self.execution_repo.update(str(saved_execution.id), saved_execution)
            
            logger.info(f"Pipeline execution {saved_execution.id} completed with status {saved_execution.status}")
            
        except Exception as e:
            # Update execution record with error
            saved_execution.status = PipelineStatus.FAILED
            saved_execution.completed_at = datetime.now(timezone.utc)
            saved_execution.error_message = str(e)
            await self.execution_repo.update(str(saved_execution.id), saved_execution)
            
            logger.error(f"Pipeline execution {saved_execution.id} failed: {str(e)}")
            raise
        
        return saved_execution
    
    def _build_langgraph(self, pipeline: Pipeline) -> Graph:
        """Build a LangGraph from pipeline definition"""
        
        # Create state graph
        graph = StateGraph(PipelineState)
        
        # Add nodes to graph
        for node in pipeline.nodes:
            node_function = self._create_node_function(node)
            graph.add_node(node.id, node_function)
        
        # Add edges based on node connections
        start_nodes = [node for node in pipeline.nodes if not node.input_nodes]
        end_nodes = [node for node in pipeline.nodes if not node.output_nodes]
        
        # Connect start nodes
        for node in start_nodes:
            graph.add_edge(START, node.id)
        
        # Connect end nodes
        for node in end_nodes:
            graph.add_edge(node.id, END)
        
        # Compile graph
        memory = MemorySaver()
        return graph.compile(checkpointer=memory)
    
    def _create_node_function(self, node: PipelineNode):
        """Create a function for a pipeline node"""
        
        async def node_function(state: PipelineState) -> PipelineState:
            """Execute a pipeline node"""
            logger.info(f"Executing node {node.id} of type {node.config.node_type}")
            
            try:
                # Update current node
                state["current_node"] = node.id
                
                # Execute based on node type
                if node.config.node_type == NodeType.DATA_SOURCE:
                    return await self._execute_data_source_node(node, state)
                elif node.config.node_type == NodeType.ENTITY_SCHEMA:
                    return await self._execute_entity_schema_node(node, state)
                elif node.config.node_type == NodeType.PROCESSOR:
                    return await self._execute_processor_node(node, state)
                elif node.config.node_type == NodeType.PARENT_CHILD_INDEXER:
                    return await self._execute_parent_child_indexer_node(node, state)
                elif node.config.node_type == NodeType.ENTITY_INSERTER:
                    return await self._execute_entity_inserter_node(node, state)
                else:
                    raise ValueError(f"Unknown node type: {node.config.node_type}")
                
            except Exception as e:
                error_msg = f"{node.id}: {str(e)}"
                state["errors"].append(error_msg)
                logger.error(f"Node {node.id} failed: {str(e)}")
                return state
        
        return node_function
    
    async def _execute_data_source_node(self, node: PipelineNode, state: PipelineState) -> PipelineState:
        """Execute a data source node"""
        config: DataSourceNodeConfig = node.config
        
        documents = []
        
        if config.source_type == DataSourceType.GOOGLE_DRIVE:
            documents = await self._load_from_google_drive(config, state)
        elif config.source_type == DataSourceType.ENTITY_COLLECTION:
            documents = await self._load_from_entity_collection(config, state)
        elif config.source_type == DataSourceType.LOCAL_FILES:
            documents = await self._load_from_local_files(config, state)
        else:
            raise ValueError(f"Unsupported data source type: {config.source_type}")
        
        state["processed_documents"].extend(documents)
        state["node_data"][node.id] = {
            "documents_loaded": len(documents),
            "source_type": config.source_type
        }
        
        logger.info(f"Data source node {node.id} loaded {len(documents)} documents")
        return state
    
    async def _execute_entity_schema_node(self, node: PipelineNode, state: PipelineState) -> PipelineState:
        """Execute an entity schema node - get collection by name and insert processed documents"""
        config: EntitySchemaNodeConfig = node.config
        
        try:
            # Get the collection name from entity_type
            collection_name = config.entity_type
            
            # Get the database connection (assuming we're using the vector database)
            from data_access.weaviatedb import get_client_async
            client = await get_client_async()
            
            if not client:
                raise Exception("Failed to get database client")
            
            # Process documents from previous nodes and insert them into the collection
            documents_inserted = 0
            inserted_documents = []
            
            # Handle different types of data in processed_documents
            data_to_process = state.get("processed_documents", [])
            
            for item in data_to_process:
                try:
                    document_data = {}
                    
                    # Check if item is a LangChain Document
                    if hasattr(item, 'page_content') and hasattr(item, 'metadata'):
                        # LangChain Document - extract content and metadata
                        document_data = {
                            "content": item.page_content,
                            "source": item.metadata.get("source", "pipeline"),
                            "metadata": item.metadata,
                            "tenant_id": state["tenant_id"]
                        }
                    elif isinstance(item, dict):
                        # JSON object that matches collection schema
                        document_data = {
                            "tenant_id": state["tenant_id"],
                            **item  # Spread the JSON object directly
                        }
                    elif isinstance(item, str):
                        # Plain text content
                        document_data = {
                            "content": item,
                            "source": "pipeline",
                            "tenant_id": state["tenant_id"]
                        }
                    else:
                        # Try to convert to dict if possible
                        if hasattr(item, 'dict'):
                            document_data = {
                                "tenant_id": state["tenant_id"],
                                **item.dict()
                            }
                        elif hasattr(item, '__dict__'):
                            document_data = {
                                "tenant_id": state["tenant_id"],
                                **item.__dict__
                            }
                        else:
                            # Fallback: convert to string
                            document_data = {
                                "content": str(item),
                                "source": "pipeline",
                                "tenant_id": state["tenant_id"]
                            }
                    
                    # Apply schema transformations if specified
                    if "schema_config" in config.__dict__ and config.schema_config:
                        schema_mappings = config.schema_config.get("mappings", {})
                        default_values = config.schema_config.get("defaults", {})
                        
                        # Apply field mappings
                        if schema_mappings:
                            mapped_data = {}
                            for target_field, source_field in schema_mappings.items():
                                if source_field in document_data:
                                    mapped_data[target_field] = document_data[source_field]
                            document_data.update(mapped_data)
                        
                        # Apply default values
                        for field, default_value in default_values.items():
                            if field not in document_data:
                                document_data[field] = default_value
                    
                    # Insert document into the specified collection
                    with client.batch as batch:
                        batch.add_data_object(
                            data_object=document_data,
                            class_name=collection_name,
                            tenant=state["tenant_id"]
                        )
                    
                    inserted_documents.append(document_data)
                    documents_inserted += 1
                    
                    logger.debug(f"Inserted document into collection {collection_name}")
                    
                except Exception as e:
                    logger.error(f"Failed to insert document into collection {collection_name}: {e}")
                    continue
            
            # Update state with results
            state["node_data"][node.id] = {
                "documents_inserted": documents_inserted,
                "collection_name": collection_name,
                "target_collection": collection_name
            }
            
            # Keep processed documents in state for downstream nodes
            state["collection_documents"] = state.get("collection_documents", [])
            state["collection_documents"].extend(inserted_documents)
            
            logger.info(f"Entity schema node {node.id} inserted {documents_inserted} documents into collection '{collection_name}'")
            return state
            
        except Exception as e:
            logger.error(f"Error in entity schema node {node.id}: {e}")
            state["errors"].append(f"Entity schema node {node.id} failed: {str(e)}")
            return state
    
    async def _execute_processor_node(self, node: PipelineNode, state: PipelineState) -> PipelineState:
        """Execute a processor node"""
        config: ProcessorNodeConfig = node.config
        
        if config.processor_type == "text_splitter":
            # Check if storage is configured
            if "storage" in config.processor_config:
                await self._process_text_splitter_with_storage(config, state)
            else:
                await self._process_text_splitter(config, state)
        elif config.processor_type == "text_splitter_with_storage":
            await self._process_text_splitter_with_storage(config, state)
        elif config.processor_type == "metadata_extractor":
            await self._process_metadata_extractor(config, state)
        elif config.processor_type == "content_filter":
            await self._process_content_filter(config, state)
        else:
            raise ValueError(f"Unknown processor type: {config.processor_type}")
        
        state["node_data"][node.id] = {
            "processor_type": config.processor_type,
            "documents_processed": len(state["processed_documents"])
        }
        
        return state
    
    async def _execute_parent_child_indexer_node(self, node: PipelineNode, state: PipelineState) -> PipelineState:
        """Execute a parent-child indexer node (inspired by Azure Search index projections)"""
        config: ParentChildIndexerConfig = node.config
        
        # Create text splitter for chunking
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.chunk_size,
            chunk_overlap=config.chunk_overlap,
            length_function=len
        )
        
        # Process documents to create parent-child relationships
        parent_documents = []
        child_chunks = []
        
        for doc in state["processed_documents"]:
            # Create parent document
            parent_id = str(uuid4())
            parent_doc = Document(
                page_content=doc.page_content,
                metadata={
                    **doc.metadata,
                    "document_type": "parent",
                    "parent_id": parent_id,
                    "tenant_id": state["tenant_id"]
                }
            )
            parent_documents.append(parent_doc)
            
            # Split into chunks
            chunks = text_splitter.split_documents([doc])
            
            for i, chunk in enumerate(chunks):
                # Create child chunk with parent reference
                chunk_metadata = {
                    **chunk.metadata,
                    "document_type": "chunk",
                    "parent_id": parent_id,
                    "chunk_index": i,
                    "chunk_id": str(uuid4()),
                    "tenant_id": state["tenant_id"]
                }
                
                # Copy specified parent metadata fields
                for field in config.parent_metadata_fields:
                    if field in doc.metadata:
                        chunk_metadata[field] = doc.metadata[field]
                
                chunk.metadata = chunk_metadata
                child_chunks.append(chunk)
        
        # Update state with processed documents
        state["processed_documents"] = parent_documents + child_chunks
        state["node_data"][node.id] = {
            "parent_documents": len(parent_documents),
            "child_chunks": len(child_chunks),
            "target_entity": config.target_chunk_entity
        }
        
        logger.info(f"Parent-child indexer {node.id} created {len(parent_documents)} parents and {len(child_chunks)} chunks")
        return state
    
    async def _execute_entity_inserter_node(self, node: PipelineNode, state: PipelineState) -> PipelineState:
        """Execute an entity inserter node"""
        config: EntityInserterConfig = node.config
        
        # Get entity definition
        entity_def_repo = await RepositoryFactory.create_entity_definition_repository()
        entity_def = await entity_def_repo.find_by_filters({
            "name": config.target_entity_type,
            "tenant_id": state["tenant_id"]
        })
        
        if not entity_def:
            raise ValueError(f"Entity definition not found: {config.target_entity_type}")
        
        entity_def = entity_def[0]
        
        # Create ResourceIndexer for vector indexing
        indexer = ResourceIndexer(
            tenant_id=state["tenant_id"],
            collection_name=f"entity_{config.target_entity_type}"
        )
        
        # Index documents to vector store
        if state["processed_documents"]:
            await indexer.index_documents(
                documents=state["processed_documents"],
                tenant_id=state["tenant_id"],
                resource_type="entity",
                metadata={
                    "entity_type": config.target_entity_type,
                    "pipeline_id": state["pipeline_id"],
                    "execution_id": state["execution_id"]
                }
            )
        
        # Create entity instances
        entity_repo = await RepositoryFactory.create_entity_instance_repository()
        entities_created = 0
        
        for doc in state["processed_documents"]:
            entity_data = {
                "content": doc.page_content,
                **doc.metadata
            }
            
            entity_instance = EntityInstance(
                entity_type=config.target_entity_type,
                data=entity_data,
                owner_id=UUID(state["metadata"]["user_id"]) if state["metadata"]["user_id"] else UUID("00000000-0000-0000-0000-000000000000"),
                tenant_id=UUID(state["tenant_id"])
            )
            
            if config.upsert_mode:
                # Try to find existing entity
                existing = await entity_repo.find_by_filters({
                    "entity_type": config.target_entity_type,
                    "data.parent_id": entity_data.get("parent_id"),
                    "data.chunk_id": entity_data.get("chunk_id"),
                    "tenant_id": state["tenant_id"]
                })
                
                if existing:
                    # Update existing
                    await entity_repo.update(str(existing[0].id), entity_instance)
                else:
                    # Create new
                    await entity_repo.create(entity_instance, state["tenant_id"])
                    entities_created += 1
            else:
                # Always create new
                await entity_repo.create(entity_instance, state["tenant_id"])
                entities_created += 1
        
        state["node_data"][node.id] = {
            "entities_created": entities_created,
            "documents_indexed": len(state["processed_documents"]),
            "target_entity_type": config.target_entity_type
        }
        
        logger.info(f"Entity inserter {node.id} created {entities_created} entities")
        return state
    
    # Helper methods for data loading
    async def _load_from_google_drive(self, config: DataSourceNodeConfig, state: PipelineState) -> List[Document]:
        """Load documents from Google Drive"""
        # Implementation would use Google Drive API
        # For now, return empty list
        logger.warning("Google Drive integration not yet implemented")
        return []
    
    async def _load_from_entity_collection(self, config: DataSourceNodeConfig, state: PipelineState) -> List[Document]:
        """Load documents from entity collection"""
        entity_repo = await RepositoryFactory.create_entity_instance_repository()
        
        filters = {"tenant_id": state["tenant_id"]}
        filters.update(config.filter_config)
        
        entities = await entity_repo.find_by_filters(filters)
        
        documents = []
        for entity in entities:
            doc = Document(
                page_content=str(entity.data),
                metadata={
                    "entity_id": str(entity.id),
                    "entity_type": entity.entity_type,
                    "source": "entity_collection",
                    **convert_uuids_to_strings(entity.data)
                }
            )
            documents.append(doc)
        
        return documents
    
    async def _load_from_local_files(self, config: DataSourceNodeConfig, state: PipelineState) -> List[Document]:
        """Load documents from local files"""
        # Implementation would load from local file system
        # For now, return empty list
        logger.warning("Local files integration not yet implemented")
        return []
    
    # Helper methods for processing
    async def _process_text_splitter(self, config: ProcessorNodeConfig, state: PipelineState):
        """Process documents with text splitter"""
        splitter_config = config.processor_config
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=splitter_config.get("chunk_size", 1000),
            chunk_overlap=splitter_config.get("chunk_overlap", 200),
            length_function=len
        )
        
        split_documents = []
        for doc in state["processed_documents"]:
            chunks = text_splitter.split_documents([doc])
            split_documents.extend(chunks)
        
        state["processed_documents"] = split_documents
    
    async def _process_metadata_extractor(self, config: ProcessorNodeConfig, state: PipelineState):
        """Extract metadata from documents"""
        extractor_config = config.processor_config
        
        for doc in state["processed_documents"]:
            # Add custom metadata based on configuration
            if "add_timestamp" in extractor_config and extractor_config["add_timestamp"]:
                doc.metadata["processed_at"] = datetime.now(timezone.utc).isoformat()
            
            if "add_word_count" in extractor_config and extractor_config["add_word_count"]:
                doc.metadata["word_count"] = len(doc.page_content.split())
    
    async def _process_content_filter(self, config: ProcessorNodeConfig, state: PipelineState):
        """Filter documents based on content"""
        filter_config = config.processor_config
        
        min_length = filter_config.get("min_length", 0)
        max_length = filter_config.get("max_length", float("inf"))
        required_keywords = filter_config.get("required_keywords", [])
        
        filtered_documents = []
        for doc in state["processed_documents"]:
            # Length filter
            if not (min_length <= len(doc.page_content) <= max_length):
                continue
            
            # Keyword filter
            if required_keywords:
                content_lower = doc.page_content.lower()
                if not any(keyword.lower() in content_lower for keyword in required_keywords):
                    continue
            
            filtered_documents.append(doc)
        
        state["processed_documents"] = filtered_documents
    
    async def _store_text_chunks(self, config: ProcessorNodeConfig, state: PipelineState, collection_name: str, field_mappings: dict = None):
        """Store text chunks using ResourceIndexer approach with configurable collection and field mapping"""
        try:
            # Initialize ResourceIndexer for this collection
            from services.resource_indexer import ResourceIndexer
            
            indexer = ResourceIndexer(
                tenant_id=state["tenant_id"],
                collection_name=collection_name
            )
            
            # Default field mappings if none provided
            if field_mappings is None:
                field_mappings = {
                    "content": "page_content",
                    "metadata": "metadata",
                    "source": "metadata.source",
                    "chunk_index": "metadata.chunk_index"
                }
            
            # Prepare documents for indexing with field mappings applied
            documents_to_index = []
            
            for idx, doc in enumerate(state["processed_documents"]):
                try:
                    # Start with original document
                    mapped_doc = Document(
                        page_content=doc.page_content if hasattr(doc, 'page_content') else str(doc),
                        metadata=doc.metadata if hasattr(doc, 'metadata') else {}
                    )
                    
                    # Apply field mappings to metadata
                    mapped_metadata = dict(mapped_doc.metadata)
                    
                    for target_field, source_path in field_mappings.items():
                        try:
                            if source_path == "page_content":
                                # Map content to a metadata field if needed
                                if target_field != "content":
                                    mapped_metadata[target_field] = mapped_doc.page_content
                            elif source_path == "metadata":
                                # Copy entire metadata
                                if target_field != "metadata":
                                    mapped_metadata[target_field] = mapped_doc.metadata
                            elif source_path.startswith("metadata."):
                                # Extract specific metadata field
                                metadata_key = source_path.replace("metadata.", "")
                                if metadata_key in mapped_doc.metadata:
                                    mapped_metadata[target_field] = mapped_doc.metadata[metadata_key]
                            elif hasattr(doc, source_path):
                                # Direct attribute mapping
                                mapped_metadata[target_field] = getattr(doc, source_path)
                        except Exception as e:
                            logger.warning(f"Failed to map field {target_field} from {source_path}: {e}")
                            continue
                    
                    # Add pipeline-specific metadata
                    mapped_metadata.update({
                        "pipeline_id": state["pipeline_id"],
                        "execution_id": state["execution_id"],
                        "processed_at": datetime.now(timezone.utc).isoformat(),
                        "chunk_length": len(mapped_doc.page_content),
                        "word_count": len(mapped_doc.page_content.split())
                    })
                    
                    # Update document metadata
                    mapped_doc.metadata = mapped_metadata
                    documents_to_index.append(mapped_doc)
                    
                except Exception as e:
                    logger.error(f"Failed to prepare document {idx} for indexing: {e}")
                    continue
            
            # Use ResourceIndexer to store chunks with embeddings
            if documents_to_index:
                result = await indexer.index_documents(
                    documents=documents_to_index,
                    cleanup_mode="incremental"
                )
                
                chunks_stored = result.get("chunks_added", 0) + result.get("chunks_updated", 0)
                
                # Update state with storage results
                state["stored_chunks"] = state.get("stored_chunks", [])
                state["stored_chunks"].extend([{
                    "collection": collection_name,
                    "chunks_added": result.get("chunks_added", 0),
                    "chunks_updated": result.get("chunks_updated", 0),
                    "chunks_skipped": result.get("chunks_skipped", 0)
                }])
                
                # Add to node data
                if "chunks_stored" not in state["node_data"]:
                    state["node_data"]["chunks_stored"] = {}
                
                state["node_data"]["chunks_stored"][collection_name] = {
                    "count": chunks_stored,
                    "collection": collection_name,
                    "field_mappings": field_mappings,
                    "indexer_result": result
                }
                
                logger.info(f"Successfully stored {chunks_stored} chunks in collection '{collection_name}' using ResourceIndexer")
                return chunks_stored
            else:
                logger.warning("No documents to index after field mapping")
                return 0
            
        except Exception as e:
            logger.error(f"Error storing chunks in collection {collection_name}: {e}")
            state["errors"].append(f"Failed to store chunks: {str(e)}")
            return 0

    async def _process_text_splitter_with_storage(self, config: ProcessorNodeConfig, state: PipelineState):
        """Process documents with text splitter and optionally store chunks"""
        # First do the text splitting
        await self._process_text_splitter(config, state)
        
        # Check if storage configuration is provided
        storage_config = config.processor_config.get("storage", {})
        if storage_config:
            collection_name = storage_config.get("collection_name")
            field_mappings = storage_config.get("field_mappings", {})
            
            if collection_name:
                # Store the chunks
                stored_count = await self._store_text_chunks(
                    config, state, collection_name, field_mappings
                )
                logger.info(f"Text splitter stored {stored_count} chunks in {collection_name}")
            else:
                logger.warning("Storage config provided but no collection_name specified")

    # ...existing code...
    def _validate_pipeline(self, pipeline: Pipeline):
        """Validate pipeline configuration"""
        if not pipeline.nodes:
            raise ValueError("Pipeline must have at least one node")
        
        # Check for cycles in the graph
        visited = set()
        rec_stack = set()
        
        def has_cycle(node_id: str) -> bool:
            visited.add(node_id)
            rec_stack.add(node_id)
            
            node = next((n for n in pipeline.nodes if n.id == node_id), None)
            if node:
                for output_id in node.output_nodes:
                    if output_id not in visited:
                        if has_cycle(output_id):
                            return True
                    elif output_id in rec_stack:
                        return True
            
            rec_stack.remove(node_id)
            return False
        
        for node in pipeline.nodes:
            if node.id not in visited:
                if has_cycle(node.id):
                    raise ValueError("Pipeline contains cycles")
