from typing import List, Optional
from uuid import UUID
from fastapi import <PERSON><PERSON><PERSON>Ex<PERSON>, status
from models.user import User
from models.role import Role
from data_access.factory import RepositoryFactory

class TeamService:
    def __init__(self):
        self.user_repo = None
        self.role_repo = None
    
    async def _ensure_repos(self):
        if self.user_repo is None:
            self.user_repo = await RepositoryFactory.create_user_repository()
        if self.role_repo is None:
            self.role_repo = await RepositoryFactory.create_role_repository()
    
    async def invite_member(self, email: str, role_name: str, tenant_id: UUID) -> User:
        """Invite a new member to the team"""
        await self._ensure_repos()
        
        # Get role by name
        role = await self.role_repo.get_by_name(role_name, tenant_id)
        if not role:
            raise ValueError(f"Role '{role_name}' not found")
            
        # Create user with role
        user = User(
            email=email,
            role_id=role.id,
            tenant_id=tenant_id
        )
        
        return await self.user_repo.create(user)
    
    async def remove_member(self, user_id: UUID, tenant_id: UUID) -> bool:
        """Remove a member from the team"""
        await self._ensure_repos()
        return await self.user_repo.delete_by_id_and_tenant(user_id, tenant_id)
    
    async def update_member_role(self, user_id: str, role_name: str, tenant_id: str) -> User:
        """Update a member's role"""
        # Get user and verify tenant
        user = await self.user_repo.get_by_id(user_id)
        if not user or user.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get role using get_all with tenant filter
        roles = await self.role_repo.get_all(
            query_conditions={
                "name": role_name,
                "tenant_id": tenant_id
            }
        )
        if not roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role {role_name} not found"
            )
        role = roles[0]
        
        # Update role
        user.role = role
        await self.user_repo.update(user.id, user)
        
        return user
    
    async def list_team_members(self, tenant_id: str) -> List[User]:
        """List all members in the team"""
        return await self.user_repo.get_all(
            query_conditions={"tenant_id": tenant_id}
        )

# Create a singleton instance
team_service = TeamService()