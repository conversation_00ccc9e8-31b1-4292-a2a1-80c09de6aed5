"""
Flexible file storage abstraction layer for Assivy Backend.

Supports multiple storage backends:
- Min<PERSON> (S3-compatible object storage)
- AWS S3 (cloud object storage)
- Local filesystem (development)

The storage interface allows easy switching between backends through configuration.
"""

import os
import io
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, BinaryIO, List, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import mimetypes
import tempfile
import shutil
import asyncio
from contextlib import asynccontextmanager

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from minio import Minio
from minio.error import S3Error
import aiofiles

from config import settings
import logging

logger = logging.getLogger(__name__)


class StorageException(Exception):
    """Base exception for storage operations"""
    pass


class FileNotFoundError(StorageException):
    """File not found in storage"""
    pass


class StorageConnectionError(StorageException):
    """Storage backend connection error"""
    pass


class FileMetadata:
    """File metadata container"""
    def __init__(
        self,
        filename: str,
        size: int,
        content_type: str,
        etag: Optional[str] = None,
        last_modified: Optional[datetime] = None,
        **kwargs
    ):
        self.filename = filename
        self.size = size
        self.content_type = content_type
        self.etag = etag
        self.last_modified = last_modified
        self.metadata = kwargs


class BaseStorageBackend(ABC):
    """Abstract base class for storage backends"""
    
    @abstractmethod
    async def upload_file(
        self,
        file_content: BinaryIO,
        key: str,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """Upload a file and return its storage key"""
        pass
    
    @abstractmethod
    async def download_file(self, key: str) -> bytes:
        """Download file content by key"""
        pass
    
    @abstractmethod
    async def get_file_stream(self, key: str) -> BinaryIO:
        """Get file as stream for efficient processing"""
        pass
    
    @abstractmethod
    async def delete_file(self, key: str) -> bool:
        """Delete a file by key"""
        pass
    
    @abstractmethod
    async def file_exists(self, key: str) -> bool:
        """Check if file exists"""
        pass
    
    @abstractmethod
    async def get_file_metadata(self, key: str) -> FileMetadata:
        """Get file metadata"""
        pass
    
    @abstractmethod
    async def list_files(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List files with optional prefix"""
        pass
    
    @abstractmethod
    async def generate_presigned_url(
        self,
        key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> str:
        """Generate presigned URL for file access"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Check storage backend health"""
        pass


class MinIOStorageBackend(BaseStorageBackend):
    """MinIO storage backend (S3-compatible)"""
    
    def __init__(self):
        self.client = Minio(
            endpoint=settings.minio_endpoint,
            access_key=settings.minio_access_key,
            secret_key=settings.minio_secret_key,
            secure=settings.minio_secure
        )
        self.bucket_name = settings.minio_bucket_name
        self._ensure_bucket()
    
    def _ensure_bucket(self):
        """Ensure the bucket exists"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"Created MinIO bucket: {self.bucket_name}")
        except S3Error as e:
            logger.error(f"Error creating MinIO bucket: {e}")
            raise StorageConnectionError(f"Failed to create bucket: {e}")
    
    async def upload_file(
        self,
        file_content: BinaryIO,
        key: str,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """Upload file to MinIO"""
        try:
            # Get file size
            file_content.seek(0, io.SEEK_END)
            file_size = file_content.tell()
            file_content.seek(0)
            
            # Auto-detect content type if not provided
            if not content_type:
                content_type = mimetypes.guess_type(key)[0] or 'application/octet-stream'
            
            # Upload file
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.put_object(
                    bucket_name=self.bucket_name,
                    object_name=key,
                    data=file_content,
                    length=file_size,
                    content_type=content_type,
                    metadata=metadata or {}
                )
            )
            
            logger.info(f"Uploaded file to MinIO: {key}")
            return key
            
        except S3Error as e:
            logger.error(f"MinIO upload error: {e}")
            raise StorageException(f"Upload failed: {e}")
    
    async def download_file(self, key: str) -> bytes:
        """Download file from MinIO"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.get_object(self.bucket_name, key)
            )
            content = response.read()
            response.close()
            response.release_conn()
            return content
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"MinIO download error: {e}")
            raise StorageException(f"Download failed: {e}")
    
    async def get_file_stream(self, key: str) -> BinaryIO:
        """Get file stream from MinIO"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.get_object(self.bucket_name, key)
            )
            return response
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"MinIO stream error: {e}")
            raise StorageException(f"Stream failed: {e}")
    
    async def delete_file(self, key: str) -> bool:
        """Delete file from MinIO"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.remove_object(self.bucket_name, key)
            )
            logger.info(f"Deleted file from MinIO: {key}")
            return True
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return False
            logger.error(f"MinIO delete error: {e}")
            raise StorageException(f"Delete failed: {e}")
    
    async def file_exists(self, key: str) -> bool:
        """Check if file exists in MinIO"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.stat_object(self.bucket_name, key)
            )
            return True
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return False
            logger.error(f"MinIO exists check error: {e}")
            raise StorageException(f"Exists check failed: {e}")
    
    async def get_file_metadata(self, key: str) -> FileMetadata:
        """Get file metadata from MinIO"""
        try:
            stat = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.stat_object(self.bucket_name, key)
            )
            
            return FileMetadata(
                filename=key.split('/')[-1],
                size=stat.size,
                content_type=stat.content_type,
                etag=stat.etag,
                last_modified=stat.last_modified,
                **stat.metadata
            )
            
        except S3Error as e:
            if e.code == 'NoSuchKey':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"MinIO metadata error: {e}")
            raise StorageException(f"Metadata failed: {e}")
    
    async def list_files(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List files in MinIO"""
        try:
            objects = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: list(self.client.list_objects(
                    self.bucket_name,
                    prefix=prefix,
                    recursive=True
                ))
            )
            
            return [obj.object_name for obj in objects[:limit]]
            
        except S3Error as e:
            logger.error(f"MinIO list error: {e}")
            raise StorageException(f"List failed: {e}")
    
    async def generate_presigned_url(
        self,
        key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> str:
        """Generate presigned URL for MinIO"""
        try:
            url = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.presigned_get_object(
                    self.bucket_name,
                    key,
                    expires=timedelta(seconds=expiration)
                )
            )
            return url
            
        except S3Error as e:
            logger.error(f"MinIO presigned URL error: {e}")
            raise StorageException(f"Presigned URL failed: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check MinIO health"""
        try:
            # Try to list bucket
            buckets = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.list_buckets()
            )
            
            bucket_exists = any(b.name == self.bucket_name for b in buckets)
            
            return {
                "status": "healthy",
                "backend": "minio",
                "endpoint": settings.minio_endpoint,
                "bucket_exists": bucket_exists,
                "bucket_name": self.bucket_name
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "minio",
                "error": str(e)
            }


class S3StorageBackend(BaseStorageBackend):
    """AWS S3 storage backend"""
    
    def __init__(self):
        try:
            self.client = boto3.client(
                's3',
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
                region_name=settings.aws_region
            )
            self.bucket_name = settings.aws_s3_bucket_name
            self._ensure_bucket()
            
        except NoCredentialsError:
            raise StorageConnectionError("AWS credentials not found")
    
    def _ensure_bucket(self):
        """Ensure the bucket exists"""
        try:
            self.client.head_bucket(Bucket=self.bucket_name)
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                try:
                    if settings.aws_region == 'us-east-1':
                        self.client.create_bucket(Bucket=self.bucket_name)
                    else:
                        self.client.create_bucket(
                            Bucket=self.bucket_name,
                            CreateBucketConfiguration={'LocationConstraint': settings.aws_region}
                        )
                    logger.info(f"Created S3 bucket: {self.bucket_name}")
                except ClientError as create_error:
                    logger.error(f"Error creating S3 bucket: {create_error}")
                    raise StorageConnectionError(f"Failed to create bucket: {create_error}")
    
    async def upload_file(
        self,
        file_content: BinaryIO,
        key: str,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """Upload file to S3"""
        try:
            # Auto-detect content type if not provided
            if not content_type:
                content_type = mimetypes.guess_type(key)[0] or 'application/octet-stream'
            
            # Upload file
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.upload_fileobj(
                    file_content,
                    self.bucket_name,
                    key,
                    ExtraArgs={
                        'ContentType': content_type,
                        'Metadata': metadata or {}
                    }
                )
            )
            
            logger.info(f"Uploaded file to S3: {key}")
            return key
            
        except ClientError as e:
            logger.error(f"S3 upload error: {e}")
            raise StorageException(f"Upload failed: {e}")
    
    async def download_file(self, key: str) -> bytes:
        """Download file from S3"""
        try:
            with io.BytesIO() as buffer:
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: self.client.download_fileobj(self.bucket_name, key, buffer)
                )
                return buffer.getvalue()
                
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"S3 download error: {e}")
            raise StorageException(f"Download failed: {e}")
    
    async def get_file_stream(self, key: str) -> BinaryIO:
        """Get file stream from S3"""
        try:
            # Download to temporary file for streaming
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.download_file(self.bucket_name, key, temp_file.name)
            )
            temp_file.close()
            
            # Return file handle
            return open(temp_file.name, 'rb')
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"S3 stream error: {e}")
            raise StorageException(f"Stream failed: {e}")
    
    async def delete_file(self, key: str) -> bool:
        """Delete file from S3"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.delete_object(Bucket=self.bucket_name, Key=key)
            )
            logger.info(f"Deleted file from S3: {key}")
            return True
            
        except ClientError as e:
            logger.error(f"S3 delete error: {e}")
            raise StorageException(f"Delete failed: {e}")
    
    async def file_exists(self, key: str) -> bool:
        """Check if file exists in S3"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.head_object(Bucket=self.bucket_name, Key=key)
            )
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error(f"S3 exists check error: {e}")
            raise StorageException(f"Exists check failed: {e}")
    
    async def get_file_metadata(self, key: str) -> FileMetadata:
        """Get file metadata from S3"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.head_object(Bucket=self.bucket_name, Key=key)
            )
            
            return FileMetadata(
                filename=key.split('/')[-1],
                size=response['ContentLength'],
                content_type=response['ContentType'],
                etag=response['ETag'].strip('"'),
                last_modified=response['LastModified'],
                **response.get('Metadata', {})
            )
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                raise FileNotFoundError(f"File not found: {key}")
            logger.error(f"S3 metadata error: {e}")
            raise StorageException(f"Metadata failed: {e}")
    
    async def list_files(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List files in S3"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=prefix,
                    MaxKeys=limit
                )
            )
            
            return [obj['Key'] for obj in response.get('Contents', [])]
            
        except ClientError as e:
            logger.error(f"S3 list error: {e}")
            raise StorageException(f"List failed: {e}")
    
    async def generate_presigned_url(
        self,
        key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> str:
        """Generate presigned URL for S3"""
        try:
            client_method = 'get_object' if method == 'GET' else 'put_object'
            url = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.generate_presigned_url(
                    client_method,
                    Params={'Bucket': self.bucket_name, 'Key': key},
                    ExpiresIn=expiration
                )
            )
            return url
            
        except ClientError as e:
            logger.error(f"S3 presigned URL error: {e}")
            raise StorageException(f"Presigned URL failed: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check S3 health"""
        try:
            # Try to list bucket
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.head_bucket(Bucket=self.bucket_name)
            )
            
            return {
                "status": "healthy",
                "backend": "s3",
                "region": settings.aws_region,
                "bucket_name": self.bucket_name
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "s3",
                "error": str(e)
            }


class LocalStorageBackend(BaseStorageBackend):
    """Local filesystem storage backend (for development)"""
    
    def __init__(self):
        self.storage_path = Path(settings.local_storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    def _get_file_path(self, key: str) -> Path:
        """Get full file path from key"""
        return self.storage_path / key
    
    async def upload_file(
        self,
        file_content: BinaryIO,
        key: str,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """Upload file to local storage"""
        try:
            file_path = self._get_file_path(key)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file content
            file_content.seek(0)
            async with aiofiles.open(file_path, 'wb') as f:
                content = file_content.read()
                await f.write(content)
            
            # Store metadata in a companion file
            if metadata:
                metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
                async with aiofiles.open(metadata_path, 'w') as f:
                    import json
                    await f.write(json.dumps(metadata))
            
            logger.info(f"Uploaded file to local storage: {key}")
            return key
            
        except Exception as e:
            logger.error(f"Local storage upload error: {e}")
            raise StorageException(f"Upload failed: {e}")
    
    async def download_file(self, key: str) -> bytes:
        """Download file from local storage"""
        try:
            file_path = self._get_file_path(key)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {key}")
            
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
                
        except FileNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Local storage download error: {e}")
            raise StorageException(f"Download failed: {e}")
    
    async def get_file_stream(self, key: str) -> BinaryIO:
        """Get file stream from local storage"""
        try:
            file_path = self._get_file_path(key)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {key}")
            
            return open(file_path, 'rb')
            
        except FileNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Local storage stream error: {e}")
            raise StorageException(f"Stream failed: {e}")
    
    async def delete_file(self, key: str) -> bool:
        """Delete file from local storage"""
        try:
            file_path = self._get_file_path(key)
            metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
            
            deleted = False
            if file_path.exists():
                file_path.unlink()
                deleted = True
            
            if metadata_path.exists():
                metadata_path.unlink()
            
            if deleted:
                logger.info(f"Deleted file from local storage: {key}")
            
            return deleted
            
        except Exception as e:
            logger.error(f"Local storage delete error: {e}")
            raise StorageException(f"Delete failed: {e}")
    
    async def file_exists(self, key: str) -> bool:
        """Check if file exists in local storage"""
        file_path = self._get_file_path(key)
        return file_path.exists()
    
    async def get_file_metadata(self, key: str) -> FileMetadata:
        """Get file metadata from local storage"""
        try:
            file_path = self._get_file_path(key)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {key}")
            
            stat = file_path.stat()
            content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
            # Load metadata if available
            metadata = {}
            metadata_path = file_path.with_suffix(file_path.suffix + '.meta')
            if metadata_path.exists():
                async with aiofiles.open(metadata_path, 'r') as f:
                    import json
                    content = await f.read()
                    metadata = json.loads(content)
            
            return FileMetadata(
                filename=file_path.name,
                size=stat.st_size,
                content_type=content_type,
                last_modified=datetime.fromtimestamp(stat.st_mtime),
                **metadata
            )
            
        except FileNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Local storage metadata error: {e}")
            raise StorageException(f"Metadata failed: {e}")
    
    async def list_files(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List files in local storage"""
        try:
            files = []
            for file_path in self.storage_path.rglob("*"):
                if file_path.is_file() and not file_path.name.endswith('.meta'):
                    relative_path = file_path.relative_to(self.storage_path)
                    key = str(relative_path).replace(os.sep, '/')
                    
                    if key.startswith(prefix):
                        files.append(key)
                        
                        if len(files) >= limit:
                            break
            
            return files
            
        except Exception as e:
            logger.error(f"Local storage list error: {e}")
            raise StorageException(f"List failed: {e}")
    
    async def generate_presigned_url(
        self,
        key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> str:
        """Generate presigned URL for local storage (not applicable)"""
        # Local storage doesn't support presigned URLs
        # Return a direct file path or implement a local file server
        file_path = self._get_file_path(key)
        return f"file://{file_path.absolute()}"
    
    async def health_check(self) -> Dict[str, Any]:
        """Check local storage health"""
        try:
            # Check if storage directory is accessible
            test_file = self.storage_path / '.health_check'
            test_file.touch()
            test_file.unlink()
            
            return {
                "status": "healthy",
                "backend": "local",
                "storage_path": str(self.storage_path.absolute()),
                "writable": True
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "local",
                "error": str(e)
            }


class StorageManager:
    """Storage manager that provides a unified interface to different storage backends"""
    
    def __init__(self):
        self._backend: Optional[BaseStorageBackend] = None
        self._current_provider = None
    
    def _get_backend(self) -> BaseStorageBackend:
        """Get the configured storage backend"""
        if self._backend is None or self._current_provider != settings.storage_provider:
            if settings.storage_provider == "minio":
                self._backend = MinIOStorageBackend()
            elif settings.storage_provider == "s3":
                self._backend = S3StorageBackend()
            elif settings.storage_provider == "local":
                self._backend = LocalStorageBackend()
            else:
                raise ValueError(f"Unsupported storage provider: {settings.storage_provider}")
            
            self._current_provider = settings.storage_provider
            logger.info(f"Initialized storage backend: {settings.storage_provider}")
        
        return self._backend
    
    def generate_file_key(self, tenant_id: str, filename: str, file_type: str = "files") -> str:
        """Generate a unique storage key for a file"""
        file_uuid = str(uuid.uuid4())
        file_ext = Path(filename).suffix
        timestamp = datetime.now().strftime("%Y%m%d")
        
        # Create hierarchical key: tenant/type/date/uuid_filename
        return f"{tenant_id}/{file_type}/{timestamp}/{file_uuid}{file_ext}"
    
    async def upload_file(
        self,
        file_content: BinaryIO,
        tenant_id: str,
        filename: str,
        file_type: str = "files",
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """Upload a file and return its storage key"""
        # Generate unique key
        key = self.generate_file_key(tenant_id, filename, file_type)
        
        # Add default metadata with proper capitalization and hyphens
        file_metadata = {
            "Tenant-Id": tenant_id,
            "Original-Filename": filename,
            "Upload-Timestamp": datetime.now().isoformat()
        }
        if metadata:
            file_metadata.update(metadata)
        
        # Upload file
        backend = self._get_backend()
        return await backend.upload_file(file_content, key, content_type, file_metadata)
    
    async def download_file(self, key: str) -> bytes:
        """Download file content by key"""
        backend = self._get_backend()
        return await backend.download_file(key)
    
    async def get_file_stream(self, key: str) -> BinaryIO:
        """Get file as stream for efficient processing"""
        backend = self._get_backend()
        return await backend.get_file_stream(key)
    
    async def delete_file(self, key: str) -> bool:
        """Delete a file by key"""
        backend = self._get_backend()
        return await backend.delete_file(key)
    
    async def file_exists(self, key: str) -> bool:
        """Check if file exists"""
        backend = self._get_backend()
        return await backend.file_exists(key)
    
    async def get_file_metadata(self, key: str) -> FileMetadata:
        """Get file metadata"""
        backend = self._get_backend()
        return await backend.get_file_metadata(key)
    
    async def list_files(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """List files with optional prefix"""
        backend = self._get_backend()
        return await backend.list_files(prefix, limit)
    
    async def list_tenant_files(self, tenant_id: str, file_type: str = "", limit: int = 1000) -> List[str]:
        """List files for a specific tenant"""
        prefix = f"{tenant_id}/"
        if file_type:
            prefix = f"{tenant_id}/{file_type}/"
        
        backend = self._get_backend()
        return await backend.list_files(prefix, limit)
    
    async def generate_presigned_url(
        self,
        key: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> str:
        """Generate presigned URL for file access"""
        backend = self._get_backend()
        return await backend.generate_presigned_url(key, expiration, method)
    
    async def health_check(self) -> Dict[str, Any]:
        """Check storage backend health"""
        try:
            backend = self._get_backend()
            health = await backend.health_check()
            health["provider"] = settings.storage_provider
            return health
        except Exception as e:
            return {
                "status": "unhealthy",
                "provider": settings.storage_provider,
                "error": str(e)
            }
    
    @asynccontextmanager
    async def temporary_file(self, key: str):
        """
        Context manager for temporary file access with proper extension.
        
        Creates a temporary file with the correct extension based on:
        1. Original filename from metadata
        2. Content type from metadata  
        3. Fallback to no extension
        
        Args:
            key: Storage key for the file
            
        Yields:
            str: Path to temporary file with appropriate extension
        """
        temp_file = None
        try:
            # Get file metadata to determine extension
            try:
                file_metadata = await self.get_file_metadata(key)
                extension = self._determine_file_extension(
                    file_metadata.filename, 
                    file_metadata.content_type
                )
            except Exception as e:
                logger.warning(f"Could not get file metadata for {key}: {str(e)}")
                extension = ""
            
            # Get file stream
            stream = await self.get_file_stream(key)
            
            # Create temporary file with proper extension
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=extension)
            temp_file.write(stream.read())
            temp_file.close()
            
            # Close original stream
            if hasattr(stream, 'close'):
                stream.close()
            
            yield temp_file.name
            
        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
    
    def _determine_file_extension(self, filename: Optional[str], content_type: Optional[str]) -> str:
        """
        Determine file extension from filename or content type.
        
        Args:
            filename: Original filename (may be None)
            content_type: MIME content type (may be None)
            
        Returns:
            str: File extension including dot (e.g., '.pdf', '.txt') or empty string
        """
        # Priority 1: Extract extension from original filename
        if filename:
            ext = Path(filename).suffix.lower()
            if ext:
                return ext
        
        # Priority 2: Determine extension from content type
        if content_type:
            # Common MIME type to extension mappings
            mime_to_ext = {
                'application/pdf': '.pdf',
                'text/plain': '.txt',
                'text/markdown': '.md',
                'application/json': '.json',
                'text/csv': '.csv',
                'application/xml': '.xml',
                'text/xml': '.xml',
                'text/html': '.html',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
                'application/msword': '.doc',
                'application/vnd.ms-excel': '.xls',
                'application/vnd.ms-powerpoint': '.ppt',
                'application/rtf': '.rtf',
                'application/vnd.oasis.opendocument.text': '.odt',
                'application/epub+zip': '.epub',
                'image/png': '.png',
                'image/jpeg': '.jpg',
                'image/gif': '.gif',
                'image/bmp': '.bmp',
                'image/tiff': '.tiff',
                'text/javascript': '.js',
                'text/css': '.css',
                'application/javascript': '.js',
                'text/x-python': '.py',
                'application/zip': '.zip',
                'application/x-tar': '.tar',
                'application/gzip': '.gz',
                'text/yaml': '.yaml',
                'application/yaml': '.yaml'
            }
            
            # Direct lookup
            if content_type in mime_to_ext:
                return mime_to_ext[content_type]
            
            # Try using mimetypes library for reverse lookup
            try:
                extensions = mimetypes.guess_all_extensions(content_type)
                if extensions:
                    # Prefer common extensions
                    preferred_extensions = ['.txt', '.html', '.pdf', '.json', '.csv', '.xml']
                    for pref_ext in preferred_extensions:
                        if pref_ext in extensions:
                            return pref_ext
                    # Return first available extension
                    return extensions[0]
            except Exception:
                pass
        
        # No extension could be determined
        return ""


# Global storage manager instance
storage_manager = StorageManager()
