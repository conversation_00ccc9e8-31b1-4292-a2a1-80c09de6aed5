from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import UUID
from models.action import (
    BaseAction, ActionType, APICallAction, FunctionCallAction,
    PromptTemplateAction, KnowledgeBaseAnswerAction
)
from data_access.base import BaseRepository
from data_access.factory import RepositoryFactory

class ActionService:
    def __init__(self):
        self.action_repo: Optional[BaseRepository[BaseAction]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize the repository"""
        if self._initialized:
            return
            
        if not self.action_repo:
            self.action_repo = await RepositoryFactory.create_repository(BaseAction)
        
        self._initialized = True

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self._ensure_initialized()

    async def create_action(
        self, 
        name: str,
        action_type: ActionType,
        description: Optional[str] = None,
        **action_params
    ) -> BaseAction:
        if action_type == ActionType.API_CALL:
            if not all(k in action_params for k in ['url', 'method']):
                raise ValueError("API actions must include url and method")
            action = APICallAction(
                name=name,
                description=description,
                url=action_params['url'],
                method=action_params['method'],
                headers_template=action_params.get('headers_template'),
                payload_template=action_params.get('payload_template')
            )
        elif action_type == ActionType.FUNCTION_CALL:
            if 'function_name' not in action_params:
                raise ValueError("Function actions must include function_name")
            action = FunctionCallAction(
                name=name,
                description=description,
                function_name=action_params['function_name'],
                parameters_schema=action_params.get('parameters_schema')
            )
        elif action_type == ActionType.PROMPT_TEMPLATE:
            if not all(k in action_params for k in ['template_string', 'input_variables']):
                raise ValueError("Prompt template actions must include template_string and input_variables")
            action = PromptTemplateAction(
                name=name,
                description=description,
                template_string=action_params['template_string'],
                input_variables=action_params['input_variables'],
                output_parser=action_params.get('output_parser')
            )
        elif action_type == ActionType.KNOWLEDGE_BASE_ANSWER:
            action = KnowledgeBaseAnswerAction(
                name=name,
                description=description,
                max_retrieved_documents=action_params.get('max_retrieved_documents', 5)
            )
        else:
            raise ValueError(f"Unsupported action type: {action_type}")
        
        await self._ensure_initialized()
        return await self.action_repo.create(action)

    async def get_action(self, action_id: UUID) -> Optional[BaseAction]:
        """Get action by ID"""
        await self._ensure_initialized()
        return await self.action_repo.find_by_id(action_id)

    async def get_action_by_name(self, name: str) -> Optional[BaseAction]:
        """Get action by name"""
        await self._ensure_initialized()
        actions = await self.action_repo.find_many(conditions={"name": name}, limit=1)
        return actions[0] if actions else None

    async def list_actions(self, filter_dict: Optional[Dict] = None) -> List[BaseAction]:
        await self._ensure_initialized()
        return await self.action_repo.find_many(conditions=filter_dict)

    async def update_action(self, action_id: UUID, update_data: Dict) -> Optional[BaseAction]:
        await self._ensure_initialized()
        existing_action = await self.action_repo.find_by_id(action_id)
        if not existing_action:
            return None
            
        action_type = existing_action.action_type
        
        # Convert update data to appropriate action type
        if action_type == ActionType.API_CALL:
            updated_action = APICallAction(**update_data)
        elif action_type == ActionType.FUNCTION_CALL:
            updated_action = FunctionCallAction(**update_data)
        elif action_type == ActionType.PROMPT_TEMPLATE:
            updated_action = PromptTemplateAction(**update_data)
        elif action_type == ActionType.KNOWLEDGE_BASE_ANSWER:
            updated_action = KnowledgeBaseAnswerAction(**update_data)
        else:
            return None
            
        updated_action.id = action_id
        updated_action.updated_at = datetime.utcnow()
        return await self.action_repo.update(action_id, updated_action)

    async def delete_action(self, action_id: UUID) -> bool:
        await self._ensure_initialized()
        return await self.action_repo.delete(action_id)

# Create a singleton instance for backward compatibility
action_service = ActionService()

# Services are now managed by ServiceManager - no singleton needed