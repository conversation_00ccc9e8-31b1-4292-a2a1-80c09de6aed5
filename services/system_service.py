"""
System Service
Handles the creation of system roles, system admin user, and host tenant setup.
"""

from models.tenant import Tenant
from services.role_service import RoleService
from services.tenant_service import TenantService
from config import settings
import logging

logger = logging.getLogger(__name__)

class SystemService:
    """Service to initialize the system with required roles, tenants, and admin users"""
    
    def __init__(self):
        self.role_service = None
        self.tenant_service = None
        self._initialized = False
    
    async def _ensure_services_initialized(self):
        """Initialize services if not already done"""
        if self._initialized:
            return
            
        self.role_service = RoleService()
        await self.role_service.initialize()
        self.tenant_service = TenantService()
        await self.tenant_service.initialize()
        self._initialized = True
        
    async def initialize_system(self) -> dict:
        """
        Initialize the entire system:
        1. Create system roles (predefined roles)
        2. Create Assivy Platform tenant
        3. Create Assivy Customer Test tenant
        4. Create system admin user (belongs to Assivy Platform)
        5. Create admin user for customer test tenant
        """
        result = {
            "system_roles_created": 0,
            "platform_tenant_created": False,
            "errors": [],
            "note": "Test tenant and user creation is handled by development endpoints in routes/system.py"
        }
        
        try:
            # Initialize services first
            await self._ensure_services_initialized()
            
            # Step 1: Initialize predefined roles (includes system and tenant roles)
            logger.info("Initializing predefined roles...")
            try:
                predefined_roles = await self.role_service.initialize_predefined_roles()
                result["system_roles_created"] = len(predefined_roles)
                logger.info(f"Created {len(predefined_roles)} predefined roles")
            except Exception as e:
                error_msg = f"Failed to initialize predefined roles: {str(e)}"
                logger.error(error_msg)
                result["errors"].append(error_msg)
            
            # Step 2: Create Assivy Platform tenant
            logger.info("Creating Assivy Platform tenant...")
            try:
                platform_tenant = await self._create_platform_tenant()
                result["platform_tenant_created"] = platform_tenant is not None
            except Exception as e:
                error_msg = f"Failed to create platform tenant: {str(e)}"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                platform_tenant = None
            
            # Note: Test tenant and user creation is now handled by development endpoints
            # in routes/system.py for better separation of concerns and to avoid duplication
            
            if not result["errors"]:
                logger.info("System initialization completed successfully")
            else:
                logger.warning(f"System initialization completed with {len(result['errors'])} errors")
            
        except Exception as e:
            error_msg = f"Critical error during system initialization: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            
        return result
    
    async def _create_platform_tenant(self) -> Tenant:
        """Create the Assivy Platform tenant"""
        platform_tenant_name = getattr(settings, 'platform_tenant_name', 'Assivy Platform')

        # Check if platform tenant already exists
        existing_tenants = await self.tenant_service.list_tenants()
        platform_tenant = next((t for t in existing_tenants if t.name == platform_tenant_name), None)

        if not platform_tenant:
            platform_tenant = await self.tenant_service.create_tenant(
                name=platform_tenant_name,
                description="Assivy Platform - System administrators tenant",
                created_by=None  # System created
            )
            logger.info(f"Created platform tenant: {platform_tenant.name}")
        else:
            logger.info(f"Platform tenant already exists: {platform_tenant.name}")

        return platform_tenant
    

    

    

    


# Singleton instance
system_service = SystemService()