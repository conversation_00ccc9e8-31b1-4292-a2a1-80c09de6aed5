from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timezone
from uuid import UUID
from fastapi import HTTP<PERSON>x<PERSON>, status
import logging

from models.library import Category
from models.resource import FileResource, ArticleResource, WebResource
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository

logger = logging.getLogger(__name__)

class CategoryService:
    """Service for managing categories with 2-level hierarchy support"""
    
    def __init__(self):
        """Initialize the category service - repositories will be created on first use"""
        self.category_repo: Optional[BaseRepository[Category]] = None
        self.file_repo: Optional[BaseRepository[FileResource]] = None
        self.article_repo: Optional[BaseRepository[ArticleResource]] = None
        self.web_repo: Optional[BaseRepository[WebResource]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize repositories asynchronously"""
        if self._initialized:
            return
            
        try:
            self.category_repo = await RepositoryFactory.create_category_repository()
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            self._initialized = True
            logger.info("CategoryService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize CategoryService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()

    async def create_category(self, category_data: dict, user_id: UUID) -> Category:
        """
        Create a new category with parent-child relationship validation
        
        Args:
            category_data: Dictionary containing category information
            user_id: ID of the user creating the category
            
        Returns:
            Created Category instance
            
        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        await self._ensure_initialized()
        
        try:
            # Validate required fields
            if not category_data.get('name'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Category name is required"
                )
            
            if not category_data.get('tenant_id'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tenant ID is required"
                )

            # Check for duplicate names at the same level within the same tenant
            query_conditions = {
                "name": category_data['name'],
                "tenant_id": str(category_data['tenant_id']),
                "is_deleted": False
            }

            existing_categories = await self.category_repo.get_all(query_conditions=query_conditions)
            
            if existing_categories:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"A category with name '{category_data['name']}' already exists"
                )

            # Create category instance
            category = Category(
                name=category_data['name'],
                description=category_data.get('description'),
                color=category_data.get('color'),
                icon=category_data.get('icon'),
                tenant_id=UUID(category_data['tenant_id']),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )

            # Save to database
            created_category = await self.category_repo.create(category, str(category_data['tenant_id']))
            logger.info(f"Created category '{category.name}' for user {user_id}")
            
            return created_category

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating category: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create category: {str(e)}"
            )

    async def get_category(self, category_id: UUID, user_id: UUID) -> Category:
        """
        Get a category by ID
        
        Args:
            category_id: ID of the category to retrieve
            user_id: ID of the requesting user
            
        Returns:
            Category instance
            
        Raises:
            HTTPException: If category not found or access denied
        """
        await self._ensure_initialized()
        
        try:
            category = await self.category_repo.get_by_id(str(category_id))
            
            if not category or category.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Category not found"
                )

            return category

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving category {category_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve category: {str(e)}"
            )

    async def update_category(self, category_id: UUID, updates: dict, user_id: UUID) -> Category:
        """
        Update a category
        
        Args:
            category_id: ID of the category to update
            updates: Dictionary of fields to update
            user_id: ID of the user making the update
            
        Returns:
            Updated Category instance
            
        Raises:
            HTTPException: If category not found or validation fails
        """
        await self._ensure_initialized()
        
        try:
            # Get existing category
            category = await self.get_category(category_id, user_id)

            # Update allowed fields (parent_id is not allowed to be updated to prevent hierarchy issues)
            allowed_fields = ['name', 'description', 'color', 'icon']
            for field in allowed_fields:
                if field in updates:
                    setattr(category, field, updates[field])
            
            # Update metadata
            category.updated_at = datetime.now(timezone.utc)
            category.updated_by = user_id

            # Check for name conflicts if name is being updated
            if 'name' in updates and updates['name'] != category.name:
                query_conditions = {
                    "name": updates['name'],
                    "tenant_id": str(category.tenant_id),
                    "is_deleted": False
                }

                existing_categories = await self.category_repo.get_all(query_conditions=query_conditions)
                
                # Filter out the current category
                existing_categories = [cat for cat in existing_categories if cat.id != category.id]
                
                if existing_categories:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"A category with name '{updates['name']}' already exists"
                    )

            # Save updates
            updated_category = await self.category_repo.update(str(category_id), category)
            
            if not updated_category:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update category"
                )

            logger.info(f"Updated category {category_id} for user {user_id}")
            return updated_category

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating category {category_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update category: {str(e)}"
            )

    async def delete_category(self, category_id: UUID, user_id: UUID, reassign_to_category_id: Optional[UUID] = None) -> bool:
        """
        Delete a category with resource reassignment handling
        
        Args:
            category_id: ID of the category to delete
            user_id: ID of the user performing the deletion
            reassign_to_category_id: Optional category ID to reassign resources to
            
        Returns:
            True if deletion was successful
            
        Raises:
            HTTPException: If category not found, has children, or reassignment fails
        """
        await self._ensure_initialized()
        
        try:
            # Get existing category
            category = await self.get_category(category_id, user_id)


            # Check for resources assigned to this category
            resources_with_category = await self._get_resources_in_category(category_id)
            
            if resources_with_category:
                if reassign_to_category_id is None:
                    resource_count = len(resources_with_category)
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Category has {resource_count} assigned resources. Please specify a category to reassign them to, or use reassign_to_category_id=null to remove category assignment."
                    )
                
                # Validate reassignment target if provided
                if reassign_to_category_id:
                    reassign_category = await self.get_category(reassign_to_category_id, user_id)
                    if not reassign_category:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Reassignment target category not found"
                        )

                # Reassign resources
                await self._reassign_resources(resources_with_category, reassign_to_category_id, user_id)

            # Perform soft delete
            category.is_deleted = True
            category.updated_at = datetime.now(timezone.utc)
            category.updated_by = user_id

            # Save changes
            updated_category = await self.category_repo.update(str(category_id), category)
            
            if not updated_category:
                return False

            logger.info(f"Deleted category {category_id} for user {user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting category {category_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete category: {str(e)}"
            )

    async def list_categories(self, tenant_id: UUID, skip: int = 0, limit: int = 100) -> List[Category]:
        """
        List categories with optional parent filtering
        
        Args:
            tenant_id: Tenant ID to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of Category instances
        """
        await self._ensure_initialized()
        
        try:
            query_conditions = {
                "tenant_id": str(tenant_id),
                "is_deleted": False
            }

            categories = await self.category_repo.get_all(
                skip=skip,
                limit=limit,
                query_conditions=query_conditions
            )

            logger.info(f"Retrieved {len(categories)} categories for tenant {tenant_id}")
            return categories

        except Exception as e:
            logger.error(f"Error listing categories for tenant {tenant_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list categories: {str(e)}"
            )

    async def get_category_hierarchy(self, tenant_id: UUID) -> Dict[str, Any]:
        """
        Get the complete category hierarchy for a tenant
        
        Args:
            tenant_id: Tenant ID to get hierarchy for
            
        Returns:
            Dictionary representing the flat category list
        """
        await self._ensure_initialized()
        
        try:
            # Get all categories for the tenant
            all_categories = await self.category_repo.get_all(
                query_conditions={
                    "tenant_id": str(tenant_id),
                    "is_deleted": False
                }
            )

            # Return flat list
            categories = [
                {
                    "id": str(cat.id),
                    "name": cat.name,
                    "description": cat.description,
                    "color": cat.color,
                    "icon": cat.icon,
                    "tenant_id": str(cat.tenant_id)
                }
                for cat in all_categories
            ]
            return {
                "tenant_id": str(tenant_id),
                "categories": categories,
                "total_count": len(all_categories)
            }

        except Exception as e:
            logger.error(f"Error getting category hierarchy for tenant {tenant_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get category hierarchy: {str(e)}"
            )

    async def assign_resource_to_category(self, resource_id: UUID, resource_type: str, category_id: UUID, user_id: UUID) -> Union[FileResource, ArticleResource, WebResource]:
        """
        Assign a resource to a category
        
        Args:
            resource_id: ID of the resource to assign
            resource_type: Type of resource ("file", "article", "web")
            category_id: ID of the category to assign to
            user_id: ID of the user making the assignment
            
        Returns:
            Updated resource instance
            
        Raises:
            HTTPException: If resource or category not found, or assignment fails
        """
        await self._ensure_initialized()
        
        try:
            # Validate category exists
            category = await self.get_category(category_id, user_id)
            
            # Get the appropriate repository and resource
            repo, resource = await self._get_resource_and_repo(resource_id, resource_type)
            
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"{resource_type.title()} resource not found"
                )

            # Update resource with category assignment
            resource.category_id = category_id
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id

            # Save the updated resource
            updated_resource = await repo.update(str(resource_id), resource)
            
            if not updated_resource:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to assign resource to category"
                )

            logger.info(f"Assigned {resource_type} resource {resource_id} to category {category_id}")
            return updated_resource

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error assigning resource {resource_id} to category {category_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to assign resource to category: {str(e)}"
            )

    async def remove_resource_from_category(self, resource_id: UUID, resource_type: str, user_id: UUID) -> Union[FileResource, ArticleResource, WebResource]:
        """
        Remove a resource from its current category
        
        Args:
            resource_id: ID of the resource to remove from category
            resource_type: Type of resource ("file", "article", "web")
            user_id: ID of the user making the change
            
        Returns:
            Updated resource instance
            
        Raises:
            HTTPException: If resource not found or removal fails
        """
        await self._ensure_initialized()
        
        try:
            # Get the appropriate repository and resource
            repo, resource = await self._get_resource_and_repo(resource_id, resource_type)
            
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"{resource_type.title()} resource not found"
                )

            # Remove category assignment
            resource.category_id = None
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id

            # Save the updated resource
            updated_resource = await repo.update(str(resource_id), resource)
            
            if not updated_resource:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to remove resource from category"
                )

            logger.info(f"Removed {resource_type} resource {resource_id} from category")
            return updated_resource

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error removing resource {resource_id} from category: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to remove resource from category: {str(e)}"
            )

    async def reassign_resources_to_category(self, from_category_id: UUID, to_category_id: Optional[UUID], user_id: UUID) -> int:
        """
        Reassign all resources from one category to another (or remove category assignment)
        
        Args:
            from_category_id: ID of the source category
            to_category_id: ID of the target category (None to remove category assignment)
            user_id: ID of the user performing the reassignment
            
        Returns:
            Number of resources reassigned
            
        Raises:
            HTTPException: If categories not found or reassignment fails
        """
        await self._ensure_initialized()
        
        try:
            # Validate source category exists
            await self.get_category(from_category_id, user_id)
            
            # Validate target category if provided
            if to_category_id:
                await self.get_category(to_category_id, user_id)

            # Get all resources in the source category
            resources_to_reassign = await self._get_resources_in_category(from_category_id)
            
            # Reassign resources
            await self._reassign_resources(resources_to_reassign, to_category_id, user_id)
            
            reassigned_count = len(resources_to_reassign)
            target_desc = f"category {to_category_id}" if to_category_id else "no category"
            logger.info(f"Reassigned {reassigned_count} resources from category {from_category_id} to {target_desc}")
            
            return reassigned_count

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error reassigning resources from category {from_category_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to reassign resources: {str(e)}"
            )

    # Private helper methods

    async def _get_resource_and_repo(self, resource_id: UUID, resource_type: str) -> tuple:
        """Get resource and its repository based on type"""
        if resource_type == "file":
            repo = self.file_repo
            resource = await repo.get_by_id(str(resource_id))
        elif resource_type == "article":
            repo = self.article_repo
            resource = await repo.get_by_id(str(resource_id))
        elif resource_type == "web":
            repo = self.web_repo
            resource = await repo.get_by_id(str(resource_id))
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid resource type: {resource_type}. Must be 'file', 'article', or 'web'"
            )
        
        return repo, resource

    async def _get_resources_in_category(self, category_id: UUID) -> List[Dict[str, Any]]:
        """Get all resources assigned to a specific category"""
        resources = []
        
        # Get file resources
        file_resources = await self.file_repo.get_all(
            query_conditions={
                "category_id": str(category_id),
                "is_deleted": False
            }
        )
        for resource in file_resources:
            resources.append({"id": resource.id, "type": "file", "resource": resource})
        
        # Get article resources
        article_resources = await self.article_repo.get_all(
            query_conditions={
                "category_id": str(category_id),
                "is_deleted": False
            }
        )
        for resource in article_resources:
            resources.append({"id": resource.id, "type": "article", "resource": resource})
        
        # Get web resources
        web_resources = await self.web_repo.get_all(
            query_conditions={
                "category_id": str(category_id),
                "is_deleted": False
            }
        )
        for resource in web_resources:
            resources.append({"id": resource.id, "type": "web", "resource": resource})
        
        return resources

    async def _reassign_resources(self, resources: List[Dict[str, Any]], target_category_id: Optional[UUID], user_id: UUID):
        """Reassign a list of resources to a new category"""
        for resource_info in resources:
            resource = resource_info["resource"]
            resource_type = resource_info["type"]
            
            # Update category assignment
            resource.category_id = target_category_id
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id
            
            # Get appropriate repository and save
            repo, _ = await self._get_resource_and_repo(resource.id, resource_type)
            await repo.update(str(resource.id), resource)