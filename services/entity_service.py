from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from uuid import UUID
from data_access.factory import RepositoryFactory
from models.entity import EntityDefinition, EntityField, EntityInstance
from fastapi import HTTPException

class EntityService:
    def __init__(self):
        self.entity_repo = None
        self.instance_repo = None

    async def initialize(self):
        """Initialize async components like repositories"""
        if self.entity_repo is None:
            self.entity_repo = await RepositoryFactory.create_entity_definition_repository()
        if self.instance_repo is None:
            self.instance_repo = await RepositoryFactory.create_entity_instance_repository()

    async def create_entity_definition(self, entity: EntityDefinition, current_user: Any, tenant_id: UUID) -> EntityDefinition:
        """Create a new entity definition with proper tenant context"""
        await self.initialize()
        # Validate field names are unique
        field_names = [field.name for field in entity.fields]
        if len(field_names) != len(set(field_names)):
            raise ValueError("Field names must be unique")
        
        return await self.entity_repo.create_with_tenant_id(
            data=entity,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def get_entity_definition(self, name: str, current_user: Any, tenant_id: UUID) -> Optional[EntityDefinition]:
        """Get an entity definition with tenant security"""
        await self.initialize()
        # Get by name with tenant filter
        filter_dict = {"name": name, "tenant_id": tenant_id}
        entities = await self.entity_repo.get_all_by_tenant(
            current_user=current_user,
            tenant_id=tenant_id,
            extra_filters=filter_dict
        )
        
        # Return first match if any
        return entities[0] if entities else None

    async def list_entity_definitions(self, current_user: Any, tenant_id: UUID) -> List[EntityDefinition]:
        """List entity definitions for a tenant"""
        await self.initialize()
        return await self.entity_repo.get_all_by_tenant(
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def update_entity_definition(self, name: str, update_data: Dict, current_user: Any, tenant_id: UUID) -> Optional[EntityDefinition]:
        """Update an entity definition with tenant security"""
        # Get the entity first to verify it exists and belongs to tenant
        entity = await self.get_entity_definition(name, current_user, tenant_id)
        if not entity:
            return None
            
        # Don't allow updating fields if there are existing instances
        if "fields" in update_data:
            # Get instances with tenant filter
            instances = await self.instance_repo.get_all_by_tenant(
                current_user=current_user,
                tenant_id=tenant_id,
                extra_filters={"entity_type": name}
            )
            
            if instances:
                raise ValueError("Cannot update fields when entity has existing instances")
        
        # Use ID for update
        return await self.entity_repo.update_by_id_and_tenant(
            id=entity.id,
            data=update_data,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def delete_entity_definition(self, name: str, current_user: Any, tenant_id: UUID) -> bool:
        """Delete an entity definition with tenant security"""
        # Get the entity first to verify it exists and belongs to tenant
        entity = await self.get_entity_definition(name, current_user, tenant_id)
        if not entity:
            return False
            
        # Check if there are any instances before deleting
        instances = await self.instance_repo.get_all_by_tenant(
            current_user=current_user,
            tenant_id=tenant_id,
            extra_filters={"entity_type": name}
        )
        
        if instances:
            raise ValueError("Cannot delete entity definition with existing instances")
            
        # Use ID for delete
        return await self.entity_repo.delete_by_id_and_tenant(
            id=entity.id,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def create_entity_instance(self, entity_type: str, data: Dict, current_user: Any, tenant_id: UUID) -> EntityInstance:
        """Create a new entity instance with tenant security"""
        # Validate entity type exists within tenant
        entity_def = await self.get_entity_definition(entity_type, current_user, tenant_id)
        if not entity_def:
            raise ValueError(f"Entity type '{entity_type}' does not exist")

        # Validate required fields
        for field in entity_def.fields:
            if field.required and field.name not in data:
                raise ValueError(f"Required field '{field.name}' is missing")

        # Create instance model
        instance = EntityInstance(
            id=UUID(),  # Will be overwritten by repository
            tenant_id=tenant_id,  # Will be overwritten by repository
            created_at=datetime.now(timezone.utc),  # Will be overwritten
            updated_at=datetime.now(timezone.utc),  # Will be overwritten
            entity_type=entity_type,
            data=data,
            owner_id=current_user.user_id,
            is_active=True
        )
        
        # Use repository to create with tenant context
        return await self.instance_repo.create_with_tenant_id(
            data=instance,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def get_entity_instance(self, instance_id: UUID, current_user: Any, tenant_id: UUID) -> Optional[EntityInstance]:
        """Get an entity instance with tenant security"""
        return await self.instance_repo.get_by_id_and_tenant(
            id=instance_id,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def list_entity_instances(self, entity_type: str, current_user: Any, tenant_id: UUID) -> List[EntityInstance]:
        """List entity instances with tenant security"""
        return await self.instance_repo.get_all_by_tenant(
            current_user=current_user,
            tenant_id=tenant_id,
            extra_filters={"entity_type": entity_type}
        )

    async def update_entity_instance(self, instance_id: UUID, update_data: Dict, current_user: Any, tenant_id: UUID) -> Optional[EntityInstance]:
        """Update an entity instance with tenant security"""
        # Get the instance first to validate it exists and verify entity type
        instance = await self.get_entity_instance(instance_id, current_user, tenant_id)
        if not instance:
            return None

        # Get entity definition to validate fields
        entity_def = await self.get_entity_definition(instance.entity_type, current_user, tenant_id)
        if not entity_def:
            raise ValueError(f"Entity type '{instance.entity_type}' no longer exists")
            
        # If data is being updated, validate required fields
        if "data" in update_data:
            new_data = update_data["data"]
            for field in entity_def.fields:
                if field.required and field.name not in new_data and field.name not in instance.data:
                    raise ValueError(f"Required field '{field.name}' cannot be removed")
                    
        return await self.instance_repo.update_by_id_and_tenant(
            id=instance_id,
            data=update_data,
            current_user=current_user,
            tenant_id=tenant_id
        )

    async def delete_entity_instance(self, instance_id: UUID, current_user: Any, tenant_id: UUID) -> bool:
        """Delete an entity instance with tenant security"""
        return await self.instance_repo.delete_by_id_and_tenant(
            id=instance_id,
            current_user=current_user,
            tenant_id=tenant_id
        )