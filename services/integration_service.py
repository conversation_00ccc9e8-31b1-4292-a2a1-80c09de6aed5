from typing import List, Dict, Any, Optional
from uuid import UUID, uuid4
from datetime import datetime
from models.integration import Integration, UserIntegrationConnection, IntegrationType, AuthType
from models.action import BaseAction
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository
import logging
from config import settings

logger = logging.getLogger(__name__)

class IntegrationService:
    def __init__(self):
        self.integration_repository: Optional[BaseRepository[Integration]] = None
        self.connection_repository: Optional[BaseRepository[UserIntegrationConnection]] = None
        self.action_repository: Optional[BaseRepository[BaseAction]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize the repositories"""
        if self._initialized:
            return
            
        try:
            self.integration_repository = await RepositoryFactory.create_repository(Integration)
            self.connection_repository = await RepositoryFactory.create_repository(UserIntegrationConnection)
            self.action_repository = await RepositoryFactory.create_repository(BaseAction)
            self._initialized = True
            logger.info("IntegrationService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize IntegrationService: {e}")
            raise

    # Integration Management
    async def create_integration(
        self,
        name: str,
        provider: str,
        integration_type: IntegrationType,
        auth_type: AuthType,
        tenant_id: UUID,
        description: Optional[str] = None,
        base_url: Optional[str] = None,
        auth_config: Optional[Dict[str, Any]] = None,
        icon_url: Optional[str] = None,
        color: Optional[str] = None,
        documentation_url: Optional[str] = None,
        support_url: Optional[str] = None
    ) -> Integration:
        """Create a new integration"""
        await self.initialize()
        
        integration = Integration(
            name=name,
            provider=provider,
            description=description,
            integration_type=integration_type,
            base_url=base_url,
            auth_type=auth_type,
            auth_config=auth_config or {},
            icon_url=icon_url,
            color=color,
            documentation_url=documentation_url,
            support_url=support_url,
            tenant_id=tenant_id
        )
        
        await self.integration_repository.save(integration)
        logger.info(f"Created integration: {integration.name} (ID: {integration.id})")
        return integration

    async def get_integration(self, integration_id: UUID) -> Optional[Integration]:
        """Get integration by ID"""
        await self.initialize()
        return await self.integration_repository.find_by_id(integration_id)

    async def list_integrations(
        self,
        tenant_id: UUID,
        integration_type: Optional[IntegrationType] = None,
        is_active: Optional[bool] = None
    ) -> List[Integration]:
        """List integrations with optional filters"""
        await self.initialize()
        
        query = {"tenant_id": tenant_id}
        if integration_type:
            query["integration_type"] = integration_type
        if is_active is not None:
            query["is_active"] = is_active
            
        return await self.integration_repository.find_by_criteria(query)

    async def update_integration(
        self,
        integration_id: UUID,
        **updates
    ) -> Optional[Integration]:
        """Update an integration"""
        await self.initialize()
        
        integration = await self.integration_repository.find_by_id(integration_id)
        if not integration:
            return None
            
        for key, value in updates.items():
            if hasattr(integration, key):
                setattr(integration, key, value)
        
        integration.updated_at = datetime.utcnow()
        await self.integration_repository.save(integration)
        return integration

    async def delete_integration(self, integration_id: UUID) -> bool:
        """Delete an integration"""
        await self.initialize()
        return await self.integration_repository.delete(integration_id)

    # User Connection Management
    async def create_user_connection(
        self,
        integration_id: UUID,
        user_id: UUID,
        tenant_id: UUID,
        auth_data: Dict[str, Any],
        enabled_actions: Optional[List[str]] = None
    ) -> UserIntegrationConnection:
        """Create a user connection to an integration"""
        await self.initialize()
        
        # Check if connection already exists
        existing = await self.get_user_connection(integration_id, user_id)
        if existing:
            raise ValueError(f"Connection already exists for user {user_id} and integration {integration_id}")
        
        connection = UserIntegrationConnection(
            integration_id=integration_id,
            user_id=user_id,
            tenant_id=tenant_id,
            auth_data=auth_data,  # Should be encrypted before storage
            enabled_actions=enabled_actions or [],
            is_connected=True,
            is_authorized=True,
            last_sync=datetime.utcnow()
        )
        
        await self.connection_repository.save(connection)
        logger.info(f"Created user connection: User {user_id} -> Integration {integration_id}")
        return connection

    async def get_user_connection(
        self,
        integration_id: UUID,
        user_id: UUID
    ) -> Optional[UserIntegrationConnection]:
        """Get user connection for an integration"""
        await self.initialize()
        
        query = {
            "integration_id": integration_id,
            "user_id": user_id
        }
        connections = await self.connection_repository.find_by_criteria(query)
        return connections[0] if connections else None

    async def list_user_connections(
        self,
        user_id: UUID,
        tenant_id: UUID,
        is_connected: Optional[bool] = None
    ) -> List[UserIntegrationConnection]:
        """List all connections for a user"""
        await self.initialize()
        
        query = {
            "user_id": user_id,
            "tenant_id": tenant_id
        }
        if is_connected is not None:
            query["is_connected"] = is_connected
            
        return await self.connection_repository.find_by_criteria(query)

    async def update_connection_status(
        self,
        connection_id: UUID,
        is_connected: bool,
        error_message: Optional[str] = None
    ) -> Optional[UserIntegrationConnection]:
        """Update connection status"""
        await self.initialize()
        
        connection = await self.connection_repository.find_by_id(connection_id)
        if not connection:
            return None
            
        connection.is_connected = is_connected
        if is_connected:
            connection.last_sync = datetime.utcnow()
            connection.error_count = 0
            connection.last_error = None
        else:
            connection.error_count += 1
            connection.last_error = error_message
            
        await self.connection_repository.save(connection)
        return connection

    async def enable_action_for_connection(
        self,
        connection_id: UUID,
        action_name: str
    ) -> bool:
        """Enable an action for a user connection"""
        await self.initialize()
        
        connection = await self.connection_repository.find_by_id(connection_id)
        if not connection:
            return False
            
        if action_name not in connection.enabled_actions:
            connection.enabled_actions.append(action_name)
            await self.connection_repository.save(connection)
        return True

    async def disable_action_for_connection(
        self,
        connection_id: UUID,
        action_name: str
    ) -> bool:
        """Disable an action for a user connection"""
        await self.initialize()
        
        connection = await self.connection_repository.find_by_id(connection_id)
        if not connection:
            return False
            
        if action_name in connection.enabled_actions:
            connection.enabled_actions.remove(action_name)
            await self.connection_repository.save(connection)
        return True

    # Integration Actions
    async def get_integration_actions(
        self,
        integration_id: UUID,
        is_enabled: Optional[bool] = None
    ) -> List[BaseAction]:
        """Get all actions for an integration"""
        await self.initialize()
        
        query = {"integration_id": integration_id}
        if is_enabled is not None:
            query["is_enabled"] = is_enabled
            
        return await self.action_repository.find_by_criteria(query)

    async def add_action_to_integration(
        self,
        integration_id: UUID,
        action_id: UUID
    ) -> bool:
        """Add an action to an integration"""
        await self.initialize()
        
        # Get the action and update its integration_id
        action = await self.action_repository.find_by_id(action_id)
        if not action:
            return False
            
        action.integration_id = integration_id
        await self.action_repository.save(action)
        
        # Update the integration's supported_actions list
        integration = await self.integration_repository.find_by_id(integration_id)
        if integration and action.name not in integration.supported_actions:
            integration.supported_actions.append(action.name)
            await self.integration_repository.save(integration)
            
        return True

    # System Integrations Setup
    async def setup_system_integrations(self, tenant_id: UUID) -> None:
        """Set up default system integrations"""
        await self.initialize()
        
        system_integrations = [
            {
                "name": "Gmail",
                "provider": "Google",
                "integration_type": IntegrationType.EMAIL,
                "auth_type": AuthType.OAUTH2,
                "description": "Send and manage emails through Gmail",
                "icon_url": "https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/gmail.svg",
                "color": "#EA4335",
                "auth_config": {
                    "scopes": ["https://www.googleapis.com/auth/gmail.send", "https://www.googleapis.com/auth/gmail.readonly"],
                    "oauth_url": "https://accounts.google.com/o/oauth2/auth",
                    "token_url": "https://oauth2.googleapis.com/token"
                }
            },
            {
                "name": "Google Calendar",
                "provider": "Google",
                "integration_type": IntegrationType.CALENDAR,
                "auth_type": AuthType.OAUTH2,
                "description": "Manage calendar events and schedules",
                "icon_url": "https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/googlecalendar.svg",
                "color": "#4285F4",
                "auth_config": {
                    "scopes": ["https://www.googleapis.com/auth/calendar"],
                    "oauth_url": "https://accounts.google.com/o/oauth2/auth",
                    "token_url": "https://oauth2.googleapis.com/token"
                }
            },
            {
                "name": "Salesforce",
                "provider": "Salesforce",
                "integration_type": IntegrationType.CRM,
                "auth_type": AuthType.OAUTH2,
                "description": "Manage CRM data and customer relationships",
                "icon_url": "https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/salesforce.svg",
                "color": "#00A1E0"
            },
            {
                "name": "Slack",
                "provider": "Slack",
                "integration_type": IntegrationType.COMMUNICATION,
                "auth_type": AuthType.OAUTH2,
                "description": "Send messages and manage Slack workspace",
                "icon_url": "https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/slack.svg",
                "color": "#4A154B"
            }
        ]
        
        for integration_data in system_integrations:
            # Check if integration already exists
            existing = await self.integration_repository.find_by_criteria({
                "name": integration_data["name"],
                "provider": integration_data["provider"],
                "tenant_id": tenant_id
            })
            
            if not existing:
                integration_data["tenant_id"] = tenant_id
                integration_data["is_system"] = True
                integration = Integration(**integration_data)
                await self.integration_repository.save(integration)
                logger.info(f"Created system integration: {integration.name}")
