"""
Weaviate setup and collection management for the resource indexing system.
"""

import weaviate
from weaviate.classes.config import Configure, Property, DataType, VectorDistances
from config import settings
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class WeaviateSetup:
    """Handles Weaviate collection setup and schema management"""
    
    def __init__(self):
        """Initialize Weaviate client"""
        try:
            if settings.weaviate_api_key:
                # Weaviate Cloud setup
                self.client = weaviate.connect_to_weaviate_cloud(
                    cluster_url=settings.weaviate_cluster_url,
                    auth_credentials=weaviate.auth.AuthApiKey(settings.weaviate_api_key),
                )
            else:
                # Local Weaviate setup
                self.client = weaviate.connect_to_local(
                    host=settings.weaviate_url.replace("http://", "").replace("https://", ""),
                )
            logger.info("Connected to Weaviate successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Weaviate: {str(e)}")
            raise

    def create_resource_collections(self) -> Dict[str, bool]:
        """Create a unified chunks collection for all resource types"""
        results = {}
        
        # Unified collection configuration for all chunks
        collection_config = {
            "chunks": {
                "description": "Unified chunks collection for all resource types (files, articles, web) with vector embeddings",
                "properties": [
                    # Core chunk properties
                    Property(name="content", data_type=DataType.TEXT),
                    Property(name="tenant_id", data_type=DataType.TEXT),
                    Property(name="resource_id", data_type=DataType.TEXT),
                    Property(name="resource_type", data_type=DataType.TEXT),  # file, article, web
                    Property(name="chunk_index", data_type=DataType.INT),
                    Property(name="processed_date", data_type=DataType.DATE),
                    
                    # Flexible metadata field (JSON object)
                    Property(name="metadata", data_type=DataType.OBJECT),
                    
                    # Legacy fields for backward compatibility and search optimization
                    Property(name="filename", data_type=DataType.TEXT),
                    Property(name="file_type", data_type=DataType.TEXT),
                    Property(name="title", data_type=DataType.TEXT),
                    Property(name="author", data_type=DataType.TEXT),
                    Property(name="url", data_type=DataType.TEXT),
                    Property(name="tags", data_type=DataType.TEXT_ARRAY),
                ]
            }
        }
        
        for collection_name, config in collection_config.items():
            try:
                # Check if collection already exists
                if self.client.collections.exists(collection_name):
                    logger.info(f"Collection {collection_name} already exists")
                    results[collection_name] = True
                    continue
                
                # Create collection with vector configuration
                collection = self.client.collections.create(
                    name=collection_name,
                    description=config["description"],
                    properties=config["properties"],
                    vectorizer_config=Configure.Vectorizer.none(),  # Use manual embeddings from Google Gemini
                    vector_index_config=Configure.VectorIndex.hnsw(
                        distance_metric=VectorDistances.COSINE
                    ),
                    # Enable multi-tenancy for data isolation
                    multi_tenancy_config=Configure.multi_tenancy(enabled=True)
                )
                
                logger.info(f"Created collection: {collection_name}")
                results[collection_name] = True
                
            except Exception as e:
                logger.error(f"Failed to create collection {collection_name}: {str(e)}")
                results[collection_name] = False
        
        return results

    def setup_tenants(self, tenant_ids: List[str]) -> Dict[str, bool]:
        """Setup tenants for the unified chunks collection"""
        results = {}
        collection_name = "chunks"
        
        try:
            if not self.client.collections.exists(collection_name):
                logger.warning(f"Collection {collection_name} does not exist")
                results[collection_name] = False
                return results
            
            collection = self.client.collections.get(collection_name)
            
            # Get existing tenants
            existing_tenants = set(tenant.name for tenant in collection.tenants.get())
            
            # Add new tenants
            new_tenants = []
            for tenant_id in tenant_ids:
                if tenant_id not in existing_tenants:
                    new_tenants.append(tenant_id)
            
            if new_tenants:
                from weaviate.classes.tenants import Tenant
                tenants_to_add = [Tenant(name=tid) for tid in new_tenants]
                collection.tenants.create(tenants_to_add)
                logger.info(f"Added tenants {new_tenants} to collection {collection_name}")
            
            results[collection_name] = True
            
        except Exception as e:
            logger.error(f"Failed to setup tenants for collection {collection_name}: {str(e)}")
            results[collection_name] = False
        
        return results

    def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get information about a collection"""
        try:
            if not self.client.collections.exists(collection_name):
                return {"exists": False}
            
            collection = self.client.collections.get(collection_name)
            
            # Get collection schema
            config = collection.config.get()
            
            # Get tenant count
            tenants = collection.tenants.get()
            
            # Get approximate object count (this might be slow for large collections)
            try:
                aggregate_result = collection.aggregate.over_all()
                total_count = aggregate_result.total_count or 0
            except:
                total_count = "unknown"
            
            return {
                "exists": True,
                "name": collection_name,
                "description": config.description,
                "properties": [prop.name for prop in config.properties],
                "vectorizer": config.vectorizer_config,
                "tenant_count": len(tenants),
                "tenants": [tenant.name for tenant in tenants[:10]],  # Show first 10 tenants
                "total_objects": total_count
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info for {collection_name}: {str(e)}")
            return {"exists": False, "error": str(e)}

    def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection (use with caution!)"""
        try:
            if self.client.collections.exists(collection_name):
                self.client.collections.delete(collection_name)
                logger.info(f"Deleted collection: {collection_name}")
                return True
            else:
                logger.warning(f"Collection {collection_name} does not exist")
                return False
        except Exception as e:
            logger.error(f"Failed to delete collection {collection_name}: {str(e)}")
            return False

    def close(self):
        """Close the Weaviate client connection"""
        try:
            self.client.close()
        except Exception as e:
            logger.error(f"Error closing Weaviate connection: {str(e)}")

# Utility functions
def setup_weaviate_for_tenant(tenant_id: str) -> bool:
    """Setup Weaviate collections and tenant for a new tenant"""
    try:
        weaviate_setup = WeaviateSetup()
        
        # Create collections if they don't exist
        collection_results = weaviate_setup.create_resource_collections()
        logger.info(f"Collection creation results: {collection_results}")
        
        # Setup tenant
        tenant_results = weaviate_setup.setup_tenants([tenant_id])
        logger.info(f"Tenant setup results: {tenant_results}")
        
        weaviate_setup.close()
        
        # Check if all operations were successful
        all_collections_ok = all(collection_results.values())
        all_tenants_ok = all(tenant_results.values())
        
        return all_collections_ok and all_tenants_ok
        
    except Exception as e:
        logger.error(f"Error setting up Weaviate for tenant {tenant_id}: {str(e)}")
        return False

def get_weaviate_status() -> Dict[str, Any]:
    """Get overall status of Weaviate setup"""
    try:
        weaviate_setup = WeaviateSetup()
        
        collection_names = ["chunks"]  # Use unified collection
        
        status = {
            "connected": True,
            "collections": {}
        }
        
        for collection_name in collection_names:
            status["collections"][collection_name] = weaviate_setup.get_collection_info(collection_name)
        
        weaviate_setup.close()
        return status
        
    except Exception as e:
        logger.error(f"Error getting Weaviate status: {str(e)}")
        return {
            "connected": False,
            "error": str(e),
            "collections": {}
        }
