from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import HTTPException, status
from models.tenant import Tenant
from models.role import Role
from data_access.factory import RepositoryFactory

class TenantServiceError(Exception):
    """Base exception for tenant service errors"""
    pass

class TenantNotFoundError(TenantServiceError):
    """Exception raised when a tenant is not found"""
    pass

class DuplicateTenantError(TenantServiceError):
    """Exception raised when attempting to create a tenant with a name that already exists"""
    pass

class TenantService:
    def __init__(self):
        self.tenant_repo = None
        self.role_repo = None
        self._initialized = False

    async def initialize(self):
        """Initialize repositories"""
        if not self._initialized:
            self.tenant_repo = await RepositoryFactory.create_tenant_repository()
            self.role_repo = await RepositoryFactory.create_role_repository()
            self._initialized = True

    async def _ensure_repos_initialized(self):
        """Ensure repositories are initialized"""
        if not self._initialized:
            await self.initialize()

    async def create_tenant(self, name: str, description: Optional[str] = None, created_by: Optional[UUID] = None) -> Tenant:
        """Create a new tenant with default roles"""
        if not name or len(name.strip()) == 0:
            raise ValueError("Tenant name cannot be empty")

        if len(name) > 100:
            raise ValueError("Tenant name cannot be longer than 100 characters")

        await self._ensure_repos_initialized()
        
        try:
            # Create tenant
            tenant = Tenant(
                name=name.strip(),
                description=description or "",
                created_by=created_by
            )
            # Check if tenant with the same name already exists
            tenants = await self.tenant_repo.get_all()
            existing_tenant = next((t for t in tenants if t.name.lower() == name.lower()), None)

            if existing_tenant:
                raise DuplicateTenantError("Tenant with this name already exists")

            tenant = await self.tenant_repo.create(tenant)
            return tenant
        except DuplicateTenantError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant with this name already exists"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create tenant: {str(e)}"
            )

    async def get_tenant(self, tenant_id: UUID) -> Optional[Tenant]:
        """Get a tenant by ID"""
        if not tenant_id:
            raise ValueError("Tenant ID cannot be empty")

        await self._ensure_tenant_repo()
        try:
            tenant = await self.tenant_repo.get_by_id(tenant_id)
            if not tenant:
                raise TenantNotFoundError(f"Tenant with ID {tenant_id} not found")
            return tenant
        except TenantNotFoundError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get tenant: {str(e)}"
            )

    async def get_tenant_by_name(self, name: str) -> Optional[Tenant]:
        """
        Get a tenant by name (case-insensitive)
        
        Args:
            name (str): The name of the tenant to find
            
        Returns:
            Optional[Tenant]: The found tenant or None
            
        Raises:
            ValueError: If name is empty
            HTTPException: If tenant not found or other errors occur
        """
        if not name or len(name.strip()) == 0:
            raise ValueError("Tenant name cannot be empty")

        await self._ensure_tenant_repo()
        
        try:
            # Get all tenants and find matching name
            tenants = await self.tenant_repo.get_all()
            tenant = next(
                (t for t in tenants if t.name.lower() == name.lower()),
                None
            )
                
            return tenant
            
        except TenantNotFoundError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get tenant by name: {str(e)}"
            )

    async def list_tenants(self) -> List[Tenant]:
        """List all tenants"""
        await self._ensure_repos_initialized()
        try:
            return await self.tenant_repo.get_all()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list tenants: {str(e)}"
            )

    async def update_tenant(self, tenant_id: UUID, name: Optional[str] = None, description: Optional[str] = None, settings: Optional[Dict] = None) -> Tenant:
        """Update a tenant"""
        if not tenant_id:
            raise ValueError("Tenant ID cannot be empty")

        await self._ensure_tenant_repo()
        
        try:
            tenant = await self.get_tenant(tenant_id)
            
            if name:
                if len(name.strip()) == 0:
                    raise ValueError("Tenant name cannot be empty")
                if len(name) > 100:
                    raise ValueError("Tenant name cannot be longer than 100 characters")
                    
                # Check for name conflicts
                tenants = await self.tenant_repo.get_all()
                existing_tenant = next((t for t in tenants if t.name.lower() == name.lower() and t._id != tenant_id), None)
                if existing_tenant:
                    raise DuplicateTenantError("Another tenant with this name already exists")
                    
                tenant.name = name.strip()
                
            if description is not None:
                tenant.description = description
                
            if settings:
                tenant.settings = {**(tenant.settings or {}), **settings}

            return await self.tenant_repo.update(tenant_id, tenant)
            
        except TenantNotFoundError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        except DuplicateTenantError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update tenant: {str(e)}"
            )

    async def delete_tenant(self, tenant_id: UUID) -> bool:
        """Delete a tenant"""
        if not tenant_id:
            raise ValueError("Tenant ID cannot be empty")
            
        await self._ensure_tenant_repo()
        
        try:
            # Verify tenant exists first
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                raise TenantNotFoundError(f"Tenant with ID {tenant_id} not found")
                
            success = await self.tenant_repo.delete(tenant_id)
            if not success:
                raise Exception("Failed to delete tenant")
            return True
            
        except TenantNotFoundError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete tenant: {str(e)}"
            )

    async def get_tenant_analytics(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get analytics for a tenant"""
        if not tenant_id:
            raise ValueError("Tenant ID cannot be empty")
            
        await self._ensure_tenant_repo()
        
        try:
            # Verify tenant exists
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                raise TenantNotFoundError(f"Tenant with ID {tenant_id} not found")
                
            # TODO: Implement tenant analytics
            return {
                "users_count": 0,
                "resources_count": 0,
                "tasks_count": 0
            }
        except TenantNotFoundError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get tenant analytics: {str(e)}"
            )

# Create singleton instance
tenant_service = TenantService()
