from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from uuid import UUID, uuid4
from fastapi import HTT<PERSON>Ex<PERSON>, status
import logging

from models.library import Library
from models.resource import FileResource, ArticleResource, WebResource, Shelf
from models.user import User
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository

logger = logging.getLogger(__name__)

class LibraryService:
    """Core library management service"""
    
    def __init__(self):
        """Initialize the library service - repositories will be created on first use"""
        self.library_repo: Optional[BaseRepository[Library]] = None
        self.shelf_repo: Optional[BaseRepository[Shelf]] = None
        self.file_repo: Optional[BaseRepository[FileResource]] = None
        self.article_repo: Optional[BaseRepository[ArticleResource]] = None
        self.web_repo: Optional[BaseRepository[WebResource]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize repositories asynchronously"""
        if self._initialized:
            return
            
        try:
            self.library_repo = await RepositoryFactory.create_library_repository()
            self.shelf_repo = await RepositoryFactory.create_shelf_repository()
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            self._initialized = True
            logger.info("LibraryService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LibraryService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()

    # Library CRUD Operations
    async def create_library(self, library_data: dict, user_id: UUID) -> Library:
        """Create a new library"""
        await self._ensure_initialized()
        
        try:
            # Validate required fields
            if not library_data.get('name'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Library name is required"
                )
            
            if not library_data.get('tenant_id'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tenant ID is required"
                )

            # Check if library with same name exists for this tenant
            existing_libraries = await self.library_repo.get_all(
                query_conditions={
                    "name": library_data['name'],
                    "tenant_id": str(library_data['tenant_id']),
                    "is_deleted": False
                }
            )
            
            if existing_libraries:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Library with name '{library_data['name']}' already exists"
                )

            # Create library instance
            library = Library(
                name=library_data['name'],
                description=library_data.get('description'),
                owner_id=user_id,
                tenant_id=UUID(library_data['tenant_id']),
                settings=library_data.get('settings', {}),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )

            # Save to database
            created_library = await self.library_repo.create(library, str(library_data['tenant_id']))

            # Create default shelves for each resource type
            await self._create_default_shelves(created_library, user_id, str(library_data['tenant_id']))

            logger.info(f"Created library '{library.name}' with default shelves for user {user_id}")

            return created_library

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating library: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create library: {str(e)}"
            )

    async def get_library(self, library_id: UUID, user_id: UUID) -> Library:
        """Get a library by ID with access control"""
        await self._ensure_initialized()
        
        try:
            library = await self.library_repo.get_by_id(str(library_id))
            
            if not library:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Library not found"
                )
            
            if library.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Library not found"
                )

            # Check access permissions (owner or shared access)
            if library.owner_id != user_id:
                # TODO: Add shared access check when sharing is implemented
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this library"
                )

            # Calculate and update statistics
            await self._update_library_statistics(library)
            
            return library

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving library {library_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve library: {str(e)}"
            )

    async def update_library(self, library_id: UUID, updates: dict, user_id: UUID) -> Library:
        """Update a library"""
        await self._ensure_initialized()
        
        try:
            # Get existing library
            library = await self.get_library(library_id, user_id)
            
            # Update allowed fields
            allowed_fields = ['name', 'description', 'settings']
            for field in allowed_fields:
                if field in updates:
                    setattr(library, field, updates[field])
            
            # Update metadata
            library.updated_at = datetime.now(timezone.utc)
            library.updated_by = user_id

            # Check for name conflicts if name is being updated
            if 'name' in updates and updates['name'] != library.name:
                existing_libraries = await self.library_repo.get_all(
                    query_conditions={
                        "name": updates['name'],
                        "tenant_id": str(library.tenant_id),
                        "is_deleted": False
                    }
                )
                
                # Filter out the current library
                existing_libraries = [lib for lib in existing_libraries if lib.id != library.id]
                
                if existing_libraries:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"Library with name '{updates['name']}' already exists"
                    )

            # Save updates
            updated_library = await self.library_repo.update(str(library_id), library)
            
            if not updated_library:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update library"
                )

            logger.info(f"Updated library {library_id} for user {user_id}")
            return updated_library

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating library {library_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update library: {str(e)}"
            )

    async def delete_library(self, library_id: UUID, user_id: UUID) -> bool:
        """Delete a library (soft delete)"""
        await self._ensure_initialized()
        
        try:
            # Get existing library and verify ownership
            library = await self.get_library(library_id, user_id)
            
            # Perform soft delete
            library.is_deleted = True
            library.updated_at = datetime.now(timezone.utc)
            library.updated_by = user_id

            # Save changes
            updated_library = await self.library_repo.update(str(library_id), library)
            
            if not updated_library:
                return False

            logger.info(f"Deleted library {library_id} for user {user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting library {library_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete library: {str(e)}"
            )

    async def list_user_libraries(self, user_id: UUID, tenant_id: UUID, skip: int = 0, limit: int = 100) -> List[Library]:
        """List libraries accessible to a user"""
        await self._ensure_initialized()
        
        try:
            # Get libraries owned by user
            libraries = await self.library_repo.get_all(
                skip=skip,
                limit=limit,
                query_conditions={
                    "owner_id": str(user_id),
                    "tenant_id": str(tenant_id),
                    "is_deleted": False
                }
            )

            # Update statistics for each library
            for library in libraries:
                await self._update_library_statistics(library)

            logger.info(f"Retrieved {len(libraries)} libraries for user {user_id}")
            return libraries

        except Exception as e:
            logger.error(f"Error listing libraries for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list libraries: {str(e)}"
            )



    # Resource Organization
    async def add_resource_to_shelf(self, resource_id: UUID, shelf_id: UUID, user_id: UUID) -> bool:
        """Add a resource to a specific shelf"""
        await self._ensure_initialized()

        try:
            # Get the shelf and verify access through library ownership
            shelf = await self.shelf_repo.get_by_id(str(shelf_id))
            if not shelf:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shelf not found"
                )

            # Verify library access
            library = await self.get_library(shelf.library_id, user_id)

            # Determine resource type and get the resource
            resource = None
            resource_type = None

            # Try to find the resource in each repository
            try:
                resource = await self.file_repo.get_by_id(str(resource_id))
                if resource:
                    resource_type = 'file'
            except:
                pass

            if not resource:
                try:
                    resource = await self.article_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_type = 'article'
                except:
                    pass

            if not resource:
                try:
                    resource = await self.web_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_type = 'web'
                except:
                    pass

            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Resource {resource_id} not found"
                )

            # Verify resource belongs to same tenant
            if resource.tenant_id != library.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Cannot add resource from different tenant"
                )

            # Verify resource type matches shelf type
            from models.resource import ResourceType
            expected_type = ResourceType(resource_type.upper())
            if shelf.resource_type != expected_type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource type '{resource_type}' does not match shelf type '{shelf.resource_type.value}'"
                )

            # Check if resource is already in a shelf
            if resource.shelf_id:
                if resource.shelf_id == shelf_id:
                    return True  # Already in this shelf
                else:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail="Resource is already assigned to another shelf"
                    )

            # Assign resource to shelf
            resource.shelf_id = shelf_id
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id

            # Update resource in appropriate repository
            if resource_type == 'file':
                await self.file_repo.update(str(resource_id), resource)
            elif resource_type == 'article':
                await self.article_repo.update(str(resource_id), resource)
            elif resource_type == 'web':
                await self.web_repo.update(str(resource_id), resource)

            # Update library statistics
            await self._update_library_statistics(library)

            # Recalculate statistics
            await self._update_library_statistics(library)

            # Save the updated library
            updated_library = await self.library_repo.update(str(library.id), library)

            if not updated_library:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update library"
                )

            logger.info(f"Added {resource_type} resource {resource_id} to shelf {shelf_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to add resource {resource_id} to shelf {shelf_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to add resource to shelf: {str(e)}"
            )

    async def add_resource_to_library(self, resource_id: UUID, library_id: UUID, user_id: UUID) -> bool:
        """Add a resource to a library by finding the appropriate shelf"""
        await self._ensure_initialized()

        try:
            # Get the library and verify ownership
            library = await self.get_library(library_id, user_id)

            # Determine resource type
            resource = None
            resource_type = None

            # Try to find the resource in each repository
            try:
                resource = await self.file_repo.get_by_id(str(resource_id))
                if resource:
                    resource_type = 'file'
            except:
                pass

            if not resource:
                try:
                    resource = await self.article_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_type = 'article'
                except:
                    pass

            if not resource:
                try:
                    resource = await self.web_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_type = 'web'
                except:
                    pass

            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Resource {resource_id} not found"
                )

            # Find the appropriate shelf for this resource type
            from models.resource import ResourceType
            target_resource_type = ResourceType(resource_type.upper())

            shelves = await self.shelf_repo.get_all(
                query_conditions={
                    "library_id": str(library_id),
                    "resource_type": target_resource_type.value,
                    "is_deleted": False
                }
            )

            if not shelves:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No shelf found for resource type '{resource_type}' in library"
                )

            # Use the first matching shelf
            target_shelf = shelves[0]

            # Add resource to the shelf
            return await self.add_resource_to_shelf(resource_id, target_shelf.id, user_id)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to add resource {resource_id} to library {library_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to add resource to library: {str(e)}"
            )

    async def bulk_add_resources_to_library(self, resource_ids: List[UUID], library_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """Add multiple resources to a library"""
        await self._ensure_initialized()

        results = {
            'added': [],
            'skipped': [],
            'errors': []
        }

        for resource_id in resource_ids:
            try:
                success = await self.add_resource_to_library(resource_id, library_id, user_id)
                if success:
                    results['added'].append(str(resource_id))
                else:
                    results['skipped'].append(str(resource_id))
            except HTTPException as e:
                results['errors'].append({
                    'resource_id': str(resource_id),
                    'error': e.detail
                })
            except Exception as e:
                results['errors'].append({
                    'resource_id': str(resource_id),
                    'error': str(e)
                })

        logger.info(f"Bulk add to library {library_id}: {len(results['added'])} added, {len(results['skipped'])} skipped, {len(results['errors'])} errors")
        return results

    async def remove_resource_from_library(self, resource_id: UUID, library_id: UUID, user_id: UUID) -> bool:
        """Remove a resource from a library"""
        await self._ensure_initialized()

        try:
            # Get the library and verify ownership
            library = await self.get_library(library_id, user_id)

            # Remove resource from appropriate list
            removed = False
            if library.file_ids and resource_id in library.file_ids:
                library.file_ids.remove(resource_id)
                removed = True
            elif library.article_ids and resource_id in library.article_ids:
                library.article_ids.remove(resource_id)
                removed = True
            elif library.webpage_ids and resource_id in library.webpage_ids:
                library.webpage_ids.remove(resource_id)
                removed = True

            if not removed:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Resource not found in library"
                )

            # Update library metadata
            library.updated_at = datetime.now(timezone.utc)
            library.updated_by = user_id

            # Recalculate statistics
            await self._update_library_statistics(library)

            # Save the updated library
            updated_library = await self.library_repo.update(str(library_id), library)

            if not updated_library:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update library"
                )

            logger.info(f"Removed resource {resource_id} from library {library_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to remove resource {resource_id} from library {library_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to remove resource from library: {str(e)}"
            )

    async def get_library_resources(self, library_id: UUID, user_id: UUID, skip: int = 0, limit: int = 20,
                                  resource_type: Optional[str] = None, search: Optional[str] = None) -> Dict[str, Any]:
        """Get resources in a library with pagination and filtering"""
        await self._ensure_initialized()

        try:
            # Get the library and verify ownership
            library = await self.get_library(library_id, user_id)

            all_resources = []

            # Collect resources based on type filter
            if not resource_type or resource_type == 'file':
                if library.file_ids:
                    file_resources = await self.file_repo.get_all(
                        query_conditions={
                            "id": {"$in": [str(fid) for fid in library.file_ids]},
                            "is_deleted": False
                        }
                    )
                    for resource in file_resources:
                        all_resources.append({
                            'id': str(resource.id),
                            'name': resource.title,
                            'type': 'file',
                            'description': resource.description,
                            'status': resource.status,
                            'created_at': resource.created_at.isoformat(),
                            'updated_at': resource.updated_at.isoformat(),
                            'file_size': getattr(resource, 'file_size', None),
                            'file_type': getattr(resource, 'file_type', None)
                        })

            if not resource_type or resource_type == 'article':
                if library.article_ids:
                    article_resources = await self.article_repo.get_all(
                        query_conditions={
                            "id": {"$in": [str(aid) for aid in library.article_ids]},
                            "is_deleted": False
                        }
                    )
                    for resource in article_resources:
                        all_resources.append({
                            'id': str(resource.id),
                            'name': resource.title,
                            'type': 'article',
                            'description': resource.description,
                            'status': resource.status,
                            'created_at': resource.created_at.isoformat(),
                            'updated_at': resource.updated_at.isoformat(),
                            'content': getattr(resource, 'content', '')[:200] + '...' if len(getattr(resource, 'content', '')) > 200 else getattr(resource, 'content', '')
                        })

            if not resource_type or resource_type == 'web':
                if library.webpage_ids:
                    web_resources = await self.web_repo.get_all(
                        query_conditions={
                            "id": {"$in": [str(wid) for wid in library.webpage_ids]},
                            "is_deleted": False
                        }
                    )
                    for resource in web_resources:
                        all_resources.append({
                            'id': str(resource.id),
                            'name': resource.title,
                            'type': 'web',
                            'description': resource.description,
                            'status': resource.status,
                            'created_at': resource.created_at.isoformat(),
                            'updated_at': resource.updated_at.isoformat(),
                            'url': resource.url
                        })

            # Apply search filter
            if search:
                search_lower = search.lower()
                all_resources = [
                    resource for resource in all_resources
                    if search_lower in resource['name'].lower() or
                       (resource.get('description') and search_lower in resource['description'].lower())
                ]

            # Sort by created_at (newest first)
            all_resources.sort(key=lambda x: x['created_at'], reverse=True)

            # Apply pagination
            total = len(all_resources)
            paginated_resources = all_resources[skip:skip + limit]

            return {
                'resources': paginated_resources,
                'total': total,
                'skip': skip,
                'limit': limit
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get resources for library {library_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get library resources: {str(e)}"
            )



    # Private helper methods
    async def _update_library_statistics(self, library: Library) -> None:
        """Update library statistics (resource count and total size) based on shelves"""
        try:
            total_count = 0
            total_size = 0

            # Get all shelves in this library
            shelves = await self.shelf_repo.get_all(
                query_conditions={
                    "library_id": str(library.id),
                    "is_deleted": False
                }
            )

            # For each shelf, count resources and calculate size
            for shelf in shelves:
                shelf_resources = []

                # Get resources from each repository based on shelf_id
                file_resources = await self.file_repo.get_all(
                    query_conditions={
                        "shelf_id": str(shelf.id),
                        "is_deleted": False
                    }
                )
                shelf_resources.extend(file_resources)

                article_resources = await self.article_repo.get_all(
                    query_conditions={
                        "shelf_id": str(shelf.id),
                        "is_deleted": False
                    }
                )
                shelf_resources.extend(article_resources)

                web_resources = await self.web_repo.get_all(
                    query_conditions={
                        "shelf_id": str(shelf.id),
                        "is_deleted": False
                    }
                )
                shelf_resources.extend(web_resources)

                # Update shelf resource count
                shelf.resource_count = len(shelf_resources)
                await self.shelf_repo.update(str(shelf.id), shelf)

                # Add to library totals
                total_count += len(shelf_resources)

                # Calculate size (only file resources have size)
                for resource in file_resources:
                    total_size += getattr(resource, 'size', 0)

            # Update library statistics
            library.resource_count = total_count
            library.total_size = total_size

        except Exception as e:
            logger.warning(f"Failed to update library statistics for {library.id}: {str(e)}")
            # Don't raise exception, just log warning as this is not critical

    async def _create_default_shelves(self, library: Library, user_id: UUID, tenant_id: str) -> None:
        """Create default shelves for each resource type when a library is created"""
        try:
            from models.resource import ResourceType

            default_shelves = [
                {
                    "name": "Documents",
                    "description": "File-based resources like PDFs, Word documents, etc.",
                    "resource_type": ResourceType.FILE
                },
                {
                    "name": "Articles",
                    "description": "Text articles and written content",
                    "resource_type": ResourceType.ARTICLE
                },
                {
                    "name": "Web Pages",
                    "description": "Web-crawled content and online resources",
                    "resource_type": ResourceType.WEB
                }
            ]

            for shelf_data in default_shelves:
                shelf = Shelf(
                    library_id=library.id,
                    name=shelf_data["name"],
                    description=shelf_data["description"],
                    resource_type=shelf_data["resource_type"],
                    resource_count=0,
                    tenant_id=UUID(tenant_id),
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    created_by=user_id
                )

                await self.shelf_repo.create(shelf, tenant_id)
                logger.info(f"Created default shelf '{shelf.name}' for library {library.id}")

        except Exception as e:
            logger.error(f"Failed to create default shelves for library {library.id}: {str(e)}")
            # Don't raise exception as library creation should still succeed