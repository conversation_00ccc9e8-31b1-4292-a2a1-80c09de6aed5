"""
Service Manager for centralized service lifecycle management.

This module provides a centralized way to manage all services in the application,
ensuring proper initialization, dependency injection, and singleton behavior.
"""

import asyncio
import logging
from typing import Optional, Dict, Type, TypeVar, Generic
from abc import ABC, abstractmethod

# Import all service types
from .user_service import UserService
from .resource_service import ResourceService
from .action_service import ActionService
from .agent_service import AgentService
from .entity_service import EntityService
from .task_service import TaskService
from .role_service import RoleService
from .team_service import TeamService
from .tenant_service import TenantService

logger = logging.getLogger(__name__)

T = TypeVar('T')

class BaseService(ABC):
    """Base class for all services with async initialization support"""
    
    def __init__(self):
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service asynchronously"""
        pass
    
    async def ensure_initialized(self) -> None:
        """Ensure the service is initialized"""
        if not self._initialized:
            await self.initialize()
            self._initialized = True

class ServiceManager:
    """Centralized service manager for the application"""
    
    _instance: Optional['ServiceManager'] = None
    _services: Dict[Type, object] = {}
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def initialize(self) -> None:
        """Initialize all services"""
        if self._initialized:
            return
            
        logger.info("Initializing service manager...")
        
        # Initialize user_service first for dependency injection
        user_service = UserService()
        await user_service.initialize()
        self._services[UserService] = user_service
        logger.info("Initialized user_service")

        # Initialize unified auth service
        try:
            from .unified_auth_service import unified_auth_service
            await unified_auth_service.initialize()
            logger.info("Initialized unified_auth_service")
        except Exception as e:
            logger.error(f"Failed to initialize unified_auth_service: {e}")
            raise

        # Initialize other services as before
        services_to_init = [
            (ResourceService, 'resource_service'),
            (ActionService, 'action_service'),
            (AgentService, 'agent_service'),
            (EntityService, 'entity_service'),
            (TaskService, 'task_service'),
            (RoleService, 'role_service'),
            (TeamService, 'team_service'),
            (TenantService, 'tenant_service'),
        ]
        for service_class, service_name in services_to_init:
            try:
                service = service_class()
                if hasattr(service, 'initialize'):
                    await service.initialize()
                self._services[service_class] = service
                logger.info(f"Initialized {service_name}")
            except Exception as e:
                logger.error(f"Failed to initialize {service_name}: {e}")
                # Continue with other services

        self._initialized = True
        logger.info("Service manager initialization complete")
    
    async def get_service(self, service_class: Type[T]) -> T:
        """Get a service instance, initializing if necessary"""
        # Check if service already exists
        if service_class in self._services:
            return self._services[service_class]
        
        # If not initialized yet, trigger initialization
        if not self._initialized:
            await self.initialize()
        
        # Check again after initialization
        if service_class in self._services:
            return self._services[service_class]
        
        # Lazy initialization for services not yet created
        try:
            service = service_class()
            if hasattr(service, 'initialize'):
                await service.initialize()
            self._services[service_class] = service
            logger.info(f"Lazy initialized {service_class.__name__}")
            return service
        except Exception as e:
            logger.error(f"Failed to lazy initialize {service_class.__name__}: {e}")
            raise
    
    def get_service_sync(self, service_class: Type[T]) -> Optional[T]:
        """Get a service instance synchronously (only if already initialized)"""
        return self._services.get(service_class)
    
    async def shutdown(self) -> None:
        """Shutdown all services"""
        logger.info("Shutting down service manager...")
        
        for service_class, service in self._services.items():
            try:
                if hasattr(service, 'shutdown'):
                    await service.shutdown()
                logger.info(f"Shutdown {service_class.__name__}")
            except Exception as e:
                logger.error(f"Error shutting down {service_class.__name__}: {e}")
        
        self._services.clear()
        self._initialized = False
        logger.info("Service manager shutdown complete")

# Global service manager instance
service_manager = ServiceManager()

# Convenience functions for getting services
async def get_auth_service():
    """Get the unified auth service instance"""
    from services.unified_auth_service import unified_auth_service
    return unified_auth_service

async def get_user_service() -> UserService:
    """Get the user service instance"""
    return await service_manager.get_service(UserService)

async def get_resource_service() -> ResourceService:
    """Get the resource service instance"""
    return await service_manager.get_service(ResourceService)

async def get_action_service() -> ActionService:
    """Get the action service instance"""
    return await service_manager.get_service(ActionService)

async def get_agent_service() -> AgentService:
    """Get the agent service instance"""
    return await service_manager.get_service(AgentService)

async def get_entity_service() -> EntityService:
    """Get the entity service instance"""
    return await service_manager.get_service(EntityService)

async def get_task_service() -> TaskService:
    """Get the task service instance"""
    return await service_manager.get_service(TaskService)

async def get_role_service() -> RoleService:
    """Get the role service instance"""
    return await service_manager.get_service(RoleService)

async def get_team_service() -> TeamService:
    """Get the team service instance"""
    return await service_manager.get_service(TeamService)

async def get_tenant_service() -> TenantService:
    """Get the tenant service instance"""
    return await service_manager.get_service(TenantService)
