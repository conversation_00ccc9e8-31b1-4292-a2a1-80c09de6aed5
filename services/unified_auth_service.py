"""
Unified Authentication Service

This service consolidates unified_unified_auth_service.py and secure_unified_unified_auth_service.py into a single,
secure authentication service following JWT best practices with the Refresh Token Pattern.

Features:
- Stateless JWT access tokens (15-30 minutes)
- Database-stored refresh tokens (7-30 days) with rotation
- Token family tracking for theft detection
- Comprehensive security logging and rate limiting
- API key authentication support
- Secure token blacklisting for logout scenarios
"""

import secrets
import hashlib
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, List, Tuple, Union
from uuid import UUID, uuid4
import logging

from fastapi import HTTPException, status, Response, Request
from passlib.context import CryptContext

from security.jwt_manager import jwt_manager, JWTClaims
from security.auth_middleware import StatelessUser
from data_access.factory import RepositoryFactory
from models.user import User
from models.role import Permission
from models.api_key import APIKey
from models.refresh_token import RefreshToken, TokenBlacklist
from services.user_service import UserService
from services.role_service import RoleService
from config import settings

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Cookie configuration
COOKIE_NAME = "assivy_auth_token"
REFRESH_COOKIE_NAME = "assivy_refresh_token"
COOKIE_SECURE = settings.environment == "production"
COOKIE_HTTPONLY = True
COOKIE_SAMESITE = "lax"

# Token configuration
ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Short-lived access tokens
REFRESH_TOKEN_EXPIRE_DAYS = 30    # Long-lived refresh tokens
MAX_REFRESH_ATTEMPTS_PER_HOUR = 10  # Rate limiting


class UnifiedAuthService:
    """
    Unified authentication service implementing secure JWT + refresh token pattern
    
    This service provides:
    1. Stateless JWT access tokens with embedded permissions (no DB lookups)
    2. Database-stored refresh tokens with rotation and family tracking
    3. Token theft detection and automatic revocation
    4. Secure logout with token blacklisting
    5. API key authentication support
    6. Comprehensive security logging
    """
    
    def __init__(self):
        self.user_service = UserService()
        self.role_service = RoleService()
        self._refresh_token_repo = None
        self._token_blacklist_repo = None
        self._api_key_repo = None
        self._user_repo = None
        self._initialized = False
    
    async def _ensure_initialized(self):
        """Initialize repositories lazily"""
        if not self._refresh_token_repo:
            self._refresh_token_repo = await RepositoryFactory.create_repository(RefreshToken)
        if not self._token_blacklist_repo:
            self._token_blacklist_repo = await RepositoryFactory.create_repository(TokenBlacklist)
        if not self._api_key_repo:
            self._api_key_repo = await RepositoryFactory.create_api_key_repository()
        if not self._user_repo:
            self._user_repo = await RepositoryFactory.create_user_repository()
        self._initialized = True

    async def initialize(self):
        """
        Initialize the unified authentication service

        This method ensures all repositories are created and ready for use.
        Should be called during application startup.
        """
        try:
            await self._ensure_initialized()
            logger.info("Unified authentication service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize unified authentication service: {e}")
            raise
    
    # ============================================================================
    # CORE AUTHENTICATION METHODS
    # ============================================================================
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password

        Args:
            email: User email address
            password: Plain text password

        Returns:
            User: Authenticated user or None if invalid credentials
        """
        try:
            # Initialize user service if needed
            if not hasattr(self.user_service, '_initialized') or not self.user_service._initialized:
                await self.user_service.initialize()

            user = await self.user_service.get_user_by_email(email)
            if not user or not user.is_active:
                return None

            # Verify password
            if not user.hashed_password:
                return None  # OAuth users don't have passwords

            if not pwd_context.verify(password, user.hashed_password):
                return None

            logger.info(f"User {user.id} authenticated successfully via password")
            return user

        except Exception as e:
            logger.error(f"Authentication error for {email}: {e}")
            return None
    
    async def authenticate_api_key(self, api_key_value: str) -> Optional[User]:
        """
        Authenticate user with API key

        Args:
            api_key_value: API key value

        Returns:
            User: Authenticated user or None if invalid key
        """
        try:
            await self._ensure_initialized()

            if not api_key_value:
                return None

            # Hash the API key for lookup
            api_key_hash = self._hash_token(api_key_value)

            # Find API key by hash
            api_keys = await self._api_key_repo.get_all(
                query_conditions={"key_hash": api_key_hash, "is_active": True}
            )

            if not api_keys:
                return None

            api_key_doc = api_keys[0]  # Should be unique due to hash uniqueness

            # Check expiration
            if api_key_doc.expires_at and api_key_doc.expires_at < datetime.now(timezone.utc):
                logger.warning(f"Expired API key used: {api_key_doc.id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="API Key has expired"
                )

            # Get associated user
            user = await self._user_repo.get_by_id(api_key_doc.user_id)
            if not user or not user.is_active:
                logger.warning(f"API key {api_key_doc.id} associated with inactive user")
                return None

            # Update last used timestamp
            api_key_doc.last_used_at = datetime.now(timezone.utc)
            await self._api_key_repo.update(api_key_doc.id, api_key_doc)

            logger.info(f"User {user.id} authenticated successfully via API key")
            return user

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"API key authentication error: {e}")
            return None
    
    # ============================================================================
    # TOKEN MANAGEMENT
    # ============================================================================
    
    async def create_token_pair(
        self, 
        user: User, 
        request: Optional[Request] = None
    ) -> Dict[str, Any]:
        """
        Create access and refresh token pair with embedded permissions
        
        Args:
            user: Authenticated user
            request: Optional request for metadata collection
            
        Returns:
            dict: Token pair with user information
        """
        try:
            await self._ensure_initialized()
            
            # Get user role and permissions
            role = await self.role_service.get_role_with_default(user.role_id)
            permissions = [perm.value for perm in role.permissions] if role else []
            role_name = role.name if role else "Unknown"
            
            # Create access token with embedded permissions (stateless)
            access_token = jwt_manager.create_access_token(
                user_id=user.id,
                email=user.email,
                tenant_id=user.tenant_id,
                permissions=permissions,
                role=role_name,
                expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            )
            
            # Create and store refresh token in database
            refresh_token_data = await self._create_refresh_token(user, request)
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token_data["token"],
                "token_type": "bearer",
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "refresh_expires_in": REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "username": getattr(user, 'username', user.email.split('@')[0]),
                    "tenant_id": str(user.tenant_id) if user.tenant_id else None,
                    "role": role_name,
                    "permissions": permissions,
                    "is_active": user.is_active
                }
            }
            
        except Exception as e:
            logger.error(f"Token creation error for user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create authentication tokens"
            )

    async def refresh_access_token(
        self,
        refresh_token: str,
        request: Optional[Request] = None
    ) -> Dict[str, Any]:
        """
        Create new access token using refresh token with rotation

        Args:
            refresh_token: Valid refresh token
            request: Optional request for metadata collection

        Returns:
            dict: New token pair
        """
        try:
            await self._ensure_initialized()

            # Verify and decode refresh token
            claims = jwt_manager.verify_token(refresh_token, token_type="refresh")

            # Hash the refresh token for database lookup
            token_hash = self._hash_token(refresh_token)

            # Find refresh token in database
            stored_tokens = await self._refresh_token_repo.get_all(0, 1, {"token_hash": token_hash})
            stored_token = stored_tokens[0] if stored_tokens else None
            if not stored_token:
                logger.warning(f"Refresh token not found in database for user {claims.sub}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )

            # Check if token is valid
            if not stored_token.is_valid():
                logger.warning(f"Invalid refresh token used for user {claims.sub}: expired={stored_token.is_expired()}, revoked={stored_token.is_revoked}")

                # If token is part of a compromised family, revoke all tokens in family
                if stored_token.is_revoked:
                    await self._revoke_token_family(stored_token.family_id, "Compromised token family")

                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Refresh token expired or revoked"
                )

            # Get current user data
            user = await self.user_service.get_user_by_id(UUID(claims.sub))
            if not user or not user.is_active:
                logger.warning(f"Refresh token used for inactive user {claims.sub}")
                await self._revoke_token_family(stored_token.family_id, "User inactive")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive"
                )

            # Update last used timestamp
            stored_token.update_last_used()
            await self._refresh_token_repo.update(stored_token.id, stored_token)

            # Revoke the old refresh token (token rotation)
            stored_token.revoke("Token rotation")
            await self._refresh_token_repo.update(stored_token.id, stored_token)

            # Create new token pair
            new_token_data = await self.create_token_pair(user, request)

            logger.info(f"Access token refreshed successfully for user {user.id}")
            return new_token_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token refresh failed"
            )

    async def logout(
        self,
        access_token: str,
        refresh_token: Optional[str] = None,
        request: Optional[Request] = None
    ) -> bool:
        """
        Logout user by blacklisting tokens and revoking refresh tokens

        Args:
            access_token: Current access token to blacklist
            refresh_token: Optional refresh token to revoke
            request: Optional request for metadata collection

        Returns:
            bool: True if logout successful
        """
        try:
            await self._ensure_initialized()

            # Decode access token to get claims
            access_claims = jwt_manager.verify_token(access_token, token_type="access")

            # Blacklist the access token
            await self._blacklist_token(
                jti=access_claims.jti,
                user_id=UUID(access_claims.sub),
                token_type="access",
                expires_at=access_claims.exp,  # exp is already a datetime object
                reason="User logout",
                request=request
            )

            # Revoke refresh token if provided
            if refresh_token:
                try:
                    refresh_claims = jwt_manager.verify_token(refresh_token, token_type="refresh")
                    token_hash = self._hash_token(refresh_token)

                    stored_tokens = await self._refresh_token_repo.get_all(0, 1, {"token_hash": token_hash})
                    stored_token = stored_tokens[0] if stored_tokens else None
                    if stored_token and stored_token.is_valid():
                        stored_token.revoke("User logout")
                        await self._refresh_token_repo.update(stored_token.id, stored_token)

                        # Also blacklist the refresh token JTI
                        await self._blacklist_token(
                            jti=refresh_claims.jti,
                            user_id=UUID(refresh_claims.sub),
                            token_type="refresh",
                            expires_at=refresh_claims.exp,  # exp is already a datetime object
                            reason="User logout",
                            request=request
                        )

                except Exception as e:
                    logger.warning(f"Error revoking refresh token during logout: {e}")

            logger.info(f"User {access_claims.sub} logged out successfully")
            return True

        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False

    # ============================================================================
    # TOKEN VALIDATION AND SECURITY
    # ============================================================================

    async def is_token_blacklisted(self, jti: str) -> bool:
        """
        Check if a token JTI is blacklisted

        Args:
            jti: JWT ID to check

        Returns:
            bool: True if token is blacklisted
        """
        try:
            await self._ensure_initialized()

            blacklisted_tokens = await self._token_blacklist_repo.get_all(0, 1, {"jti": jti})
            blacklisted_token = blacklisted_tokens[0] if blacklisted_tokens else None
            if not blacklisted_token:
                return False

            # Clean up expired blacklist entries
            if blacklisted_token.is_expired():
                await self._token_blacklist_repo.delete(blacklisted_token.id)
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking token blacklist: {e}")
            return False  # Fail open for availability

    async def revoke_all_user_tokens(self, user_id: UUID, reason: str = "Security revocation") -> int:
        """
        Revoke all refresh tokens for a user (e.g., on password change)

        Args:
            user_id: User ID
            reason: Reason for revocation

        Returns:
            int: Number of tokens revoked
        """
        try:
            await self._ensure_initialized()

            # Get all active refresh tokens for user
            user_tokens = await self._refresh_token_repo.get_all(
                query_conditions={"user_id": user_id, "is_revoked": False}
            )

            revoked_count = 0
            for token in user_tokens:
                if token.is_valid():
                    token.revoke(reason)
                    await self._refresh_token_repo.update(token.id, token)
                    revoked_count += 1

            logger.info(f"Revoked {revoked_count} refresh tokens for user {user_id}")
            return revoked_count

        except Exception as e:
            logger.error(f"Error revoking user tokens: {e}")
            return 0

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    async def _create_refresh_token(
        self,
        user: User,
        request: Optional[Request] = None,
        parent_token_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create and store refresh token in database

        Args:
            user: User to create token for
            request: Optional request for metadata
            parent_token_id: Optional parent token for family tracking

        Returns:
            dict: Token data with database ID
        """
        await self._ensure_initialized()

        # Generate token family ID (new family or inherit from parent)
        if parent_token_id:
            parent_tokens = await self._refresh_token_repo.get_all(0, 1, {"token_id": parent_token_id})
            parent_token = parent_tokens[0] if parent_tokens else None
            family_id = parent_token.family_id if parent_token else str(uuid4())
        else:
            family_id = str(uuid4())

        # Create JWT refresh token
        token_id = secrets.token_urlsafe(32)
        refresh_token = jwt_manager.create_refresh_token(
            user_id=user.id,
            email=user.email,
            tenant_id=user.tenant_id,
            expires_delta=timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        )

        # Hash token for secure storage
        token_hash = self._hash_token(refresh_token)

        # Collect client metadata
        client_info = {}
        ip_address = None
        user_agent = None

        if request:
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent", "")
            client_info = {
                "method": request.method,
                "path": str(request.url.path),
                "query": str(request.url.query) if request.url.query else None
            }

        # Create database record
        refresh_token_record = RefreshToken(
            token_id=token_id,
            user_id=user.id,
            token_hash=token_hash,
            expires_at=datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS),
            family_id=family_id,
            parent_token_id=parent_token_id,
            client_info=client_info,
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Store in database
        stored_token = await self._refresh_token_repo.create(refresh_token_record)

        return {
            "token": refresh_token,
            "token_id": token_id,
            "family_id": family_id,
            "database_id": str(stored_token.id)
        }

    async def _revoke_token_family(self, family_id: str, reason: str) -> int:
        """
        Revoke all tokens in a token family (for theft detection)

        Args:
            family_id: Token family ID
            reason: Reason for revocation

        Returns:
            int: Number of tokens revoked
        """
        try:
            await self._ensure_initialized()

            family_tokens = await self._refresh_token_repo.get_all(
                query_conditions={"family_id": family_id, "is_revoked": False}
            )

            revoked_count = 0
            for token in family_tokens:
                token.revoke(reason)
                await self._refresh_token_repo.update(token.id, token)
                revoked_count += 1

            logger.warning(f"Revoked {revoked_count} tokens in family {family_id}: {reason}")
            return revoked_count

        except Exception as e:
            logger.error(f"Error revoking token family: {e}")
            return 0

    async def _blacklist_token(
        self,
        jti: str,
        user_id: UUID,
        token_type: str,
        expires_at: datetime,
        reason: str,
        request: Optional[Request] = None
    ) -> None:
        """
        Add token to blacklist

        Args:
            jti: JWT ID to blacklist
            user_id: User ID
            token_type: Type of token (access/refresh)
            expires_at: Original token expiration
            reason: Reason for blacklisting
            request: Optional request for metadata
        """
        try:
            await self._ensure_initialized()

            ip_address = None
            if request and request.client:
                ip_address = request.client.host

            blacklist_entry = TokenBlacklist(
                jti=jti,
                user_id=user_id,
                token_type=token_type,
                expires_at=expires_at,
                reason=reason,
                ip_address=ip_address
            )

            await self._token_blacklist_repo.create(blacklist_entry)
            logger.info(f"Token {jti} blacklisted for user {user_id}: {reason}")

        except Exception as e:
            logger.error(f"Error blacklisting token: {e}")

    def _hash_token(self, token: str) -> str:
        """
        Hash token for secure database storage

        Args:
            token: Token to hash

        Returns:
            str: SHA-256 hash of token
        """
        return hashlib.sha256(token.encode()).hexdigest()

    # ============================================================================
    # COOKIE MANAGEMENT
    # ============================================================================

    def set_auth_cookies(self, response: Response, access_token: str, refresh_token: str) -> None:
        """
        Set secure authentication cookies

        Args:
            response: FastAPI response object
            access_token: Access token to set
            refresh_token: Refresh token to set
        """
        # Set access token cookie
        response.set_cookie(
            key=COOKIE_NAME,
            value=access_token,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

        # Set refresh token cookie
        response.set_cookie(
            key=REFRESH_COOKIE_NAME,
            value=refresh_token,
            max_age=REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

    def clear_auth_cookies(self, response: Response) -> None:
        """
        Clear authentication cookies

        Args:
            response: FastAPI response object
        """
        response.delete_cookie(
            key=COOKIE_NAME,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

        response.delete_cookie(
            key=REFRESH_COOKIE_NAME,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

    # ============================================================================
    # MAINTENANCE AND CLEANUP
    # ============================================================================

    async def cleanup_expired_tokens(self) -> Dict[str, int]:
        """
        Clean up expired refresh tokens and blacklist entries

        Returns:
            dict: Cleanup statistics
        """
        try:
            await self._ensure_initialized()

            now = datetime.now(timezone.utc)

            # Clean up expired refresh tokens
            expired_refresh_tokens = await self._refresh_token_repo.get_all(
                query_conditions={"expires_at": {"$lt": now}}
            )

            refresh_deleted = 0
            for token in expired_refresh_tokens:
                await self._refresh_token_repo.delete(token.id)
                refresh_deleted += 1

            # Clean up expired blacklist entries
            expired_blacklist_entries = await self._token_blacklist_repo.get_all(
                query_conditions={"expires_at": {"$lt": now}}
            )

            blacklist_deleted = 0
            for entry in expired_blacklist_entries:
                await self._token_blacklist_repo.delete(entry.id)
                blacklist_deleted += 1

            stats = {
                "refresh_tokens_deleted": refresh_deleted,
                "blacklist_entries_deleted": blacklist_deleted,
                "cleanup_timestamp": now.isoformat()
            }

            if refresh_deleted > 0 or blacklist_deleted > 0:
                logger.info(f"Token cleanup completed: {stats}")

            return stats

        except Exception as e:
            logger.error(f"Token cleanup error: {e}")
            return {"error": str(e)}

    # ============================================================================
    # EMAIL VERIFICATION AND API KEY MANAGEMENT
    # ============================================================================

    def create_verification_token(self) -> str:
        """
        Create email verification token

        Returns:
            str: Secure verification token
        """
        return secrets.token_urlsafe(32)

    def send_verification_email(self, email: str, token: str) -> bool:
        """
        Send verification email to user

        Args:
            email: User email address
            token: Verification token

        Returns:
            bool: True if email sent successfully
        """
        try:
            # In development, just log the token
            if settings.environment == "development":
                logger.info(f"Verification token for {email}: {token}")
                return True

            # In production, implement actual email sending
            # This would integrate with your email service (SendGrid, SES, etc.)
            logger.info(f"Verification email sent to {email}")
            return True

        except Exception as e:
            logger.error(f"Error sending verification email: {e}")
            return False

    async def verify_email(self, token: str) -> Optional[User]:
        """
        Verify email using verification token

        Args:
            token: Verification token

        Returns:
            User: Verified user or None if token invalid
        """
        try:
            await self._ensure_initialized()

            # Find user with matching verification token
            users = await self._user_repo.get_all(0, 1, {"verification_token": token})
            user = users[0] if users else None
            if not user:
                return None

            # Check token expiration
            if (user.verification_token_expires and
                user.verification_token_expires < datetime.now(timezone.utc)):
                return None

            # Mark email as verified
            user.is_email_verified = True
            user.is_active = True
            user.verification_token = None
            user.verification_token_expires = None

            await self._user_repo.update(user.id, user)

            logger.info(f"Email verified for user {user.id}")
            return user

        except Exception as e:
            logger.error(f"Error verifying email: {e}")
            return None

    async def generate_api_key(self, user_email: str) -> str:
        """
        Generate API key for user

        Args:
            user_email: User email address

        Returns:
            str: Generated API key
        """
        try:
            await self._ensure_initialized()

            # Find user by email
            users = await self._user_repo.get_all(0, 1, {"email": user_email})
            user = users[0] if users else None
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Generate secure API key
            api_key_value = f"ak_{secrets.token_urlsafe(32)}"
            api_key_hash = self._hash_token(api_key_value)

            # Create API key record
            from models.api_key import APIKey
            api_key = APIKey(
                id=uuid4(),
                user_id=user.id,
                key_hash=api_key_hash,
                name=f"API Key for {user.email}",
                expires_at=datetime.now(timezone.utc) + timedelta(days=365),  # 1 year
                is_active=True
            )

            await self._api_key_repo.create(api_key)

            logger.info(f"API key generated for user {user.id}")
            return api_key_value

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating API key: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate API key"
            )

    def set_auth_cookie(self, response: Response, token: str) -> None:
        """
        Set single authentication cookie (legacy method)

        Args:
            response: FastAPI response object
            token: Token to set
        """
        response.set_cookie(
            key=COOKIE_NAME,
            value=token,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

    def clear_auth_cookie(self, response: Response) -> None:
        """
        Clear single authentication cookie (legacy method)

        Args:
            response: FastAPI response object
        """
        response.delete_cookie(
            key=COOKIE_NAME,
            httponly=COOKIE_HTTPONLY,
            secure=COOKIE_SECURE,
            samesite=COOKIE_SAMESITE,
            path="/"
        )

    async def create_access_token(
        self,
        user_id: Union[str, UUID],
        email: str,
        tenant_id: Union[str, UUID],
        permissions: List[str],
        role: str,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create access token (legacy method for compatibility)

        Args:
            user_id: User identifier
            email: User email
            tenant_id: Tenant identifier
            permissions: List of permissions
            role: User role
            expires_delta: Custom expiration

        Returns:
            str: Access token
        """
        return jwt_manager.create_access_token(
            user_id=user_id,
            email=email,
            tenant_id=tenant_id,
            permissions=permissions,
            role=role,
            expires_delta=expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )

    # ============================================================================
    # TESTING AND DEVELOPMENT UTILITIES
    # ============================================================================

    async def create_test_token(
        self,
        user_id: Union[str, UUID],
        email: str,
        tenant_id: Union[str, UUID],
        permissions: List[str],
        role: str,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create test access token (for development/testing only)

        Args:
            user_id: User identifier
            email: User email
            tenant_id: Tenant identifier
            permissions: List of permissions
            role: User role
            expires_delta: Custom expiration

        Returns:
            str: Test access token
        """
        if settings.environment == "production":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Test tokens not allowed in production"
            )

        return jwt_manager.create_access_token(
            user_id=user_id,
            email=email,
            tenant_id=tenant_id,
            permissions=permissions,
            role=role,
            expires_delta=expires_delta,
            additional_claims={"test_token": True}
        )


# Create singleton instance
unified_auth_service = UnifiedAuthService()
