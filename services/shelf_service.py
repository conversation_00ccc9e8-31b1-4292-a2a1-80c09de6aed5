from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from uuid import UUID, uuid4
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
import logging

from models.resource import Shelf, ResourceType, FileResource, ArticleResource, WebResource
from models.library import Library
from models.user import User
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository

logger = logging.getLogger(__name__)

class ShelfService:
    """Core shelf management service"""
    
    def __init__(self):
        """Initialize the shelf service - repositories will be created on first use"""
        self.shelf_repo: Optional[BaseRepository[Shelf]] = None
        self.library_repo: Optional[BaseRepository[Library]] = None
        self.file_repo: Optional[BaseRepository[FileResource]] = None
        self.article_repo: Optional[BaseRepository[ArticleResource]] = None
        self.web_repo: Optional[BaseRepository[WebResource]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize repositories asynchronously"""
        if self._initialized:
            return
            
        try:
            self.shelf_repo = await RepositoryFactory.create_shelf_repository()
            self.library_repo = await RepositoryFactory.create_library_repository()
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            self._initialized = True
            logger.info("ShelfService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ShelfService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()

    async def create_shelf(self, shelf_data: dict, user_id: UUID) -> Shelf:
        """Create a new shelf in a library"""
        await self._ensure_initialized()
        
        try:
            # Validate library exists and user has access
            library = await self.library_repo.get_by_id(str(shelf_data['library_id']))
            if not library:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Library not found"
                )
            
            if library.owner_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this library"
                )

            # Check if shelf with same name already exists in library (no resource type restriction)
            existing_shelves = await self.shelf_repo.get_all(
                query_conditions={
                    "library_id": str(shelf_data['library_id']),
                    "name": shelf_data['name'],
                    "tenant_id": str(shelf_data['tenant_id']),
                    "is_deleted": False
                }
            )

            if existing_shelves:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Shelf '{shelf_data['name']}' already exists in this library"
                )

            # Create shelf instance (supports mixed resource types)
            shelf = Shelf(
                library_id=UUID(shelf_data['library_id']),
                name=shelf_data['name'],
                description=shelf_data.get('description'),
                resource_count=0,
                tenant_id=UUID(shelf_data['tenant_id']),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )

            # Save to database
            created_shelf = await self.shelf_repo.create(shelf, str(shelf_data['tenant_id']))
            logger.info(f"Created shelf '{shelf.name}' in library {shelf_data['library_id']} for user {user_id}")
            
            return created_shelf

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create shelf: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create shelf: {str(e)}"
            )

    async def get_shelf(self, shelf_id: UUID, user_id: UUID) -> Shelf:
        """Get a shelf by ID with access control"""
        await self._ensure_initialized()
        
        try:
            shelf = await self.shelf_repo.get_by_id(str(shelf_id))
            
            if not shelf:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shelf not found"
                )
            
            if shelf.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shelf not found"
                )

            # Check access permissions through library ownership
            library = await self.library_repo.get_by_id(str(shelf.library_id))
            if not library or library.owner_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this shelf"
                )

            return shelf

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving shelf {shelf_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve shelf: {str(e)}"
            )

    async def update_shelf(self, shelf_id: UUID, updates: dict, user_id: UUID) -> Shelf:
        """Update a shelf"""
        await self._ensure_initialized()
        
        try:
            # Get existing shelf and verify access
            shelf = await self.get_shelf(shelf_id, user_id)
            
            # Update allowed fields
            allowed_fields = ['name', 'description']
            for field in allowed_fields:
                if field in updates:
                    setattr(shelf, field, updates[field])
            
            # Update metadata
            shelf.updated_at = datetime.now(timezone.utc)
            shelf.updated_by = user_id

            # Save changes
            updated_shelf = await self.shelf_repo.update(str(shelf_id), shelf)
            
            if not updated_shelf:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Shelf not found"
                )

            logger.info(f"Updated shelf {shelf_id} for user {user_id}")
            return updated_shelf

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update shelf {shelf_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update shelf: {str(e)}"
            )

    async def delete_shelf(self, shelf_id: UUID, user_id: UUID) -> bool:
        """Delete a shelf (soft delete) - only if it's empty"""
        await self._ensure_initialized()
        
        try:
            # Get existing shelf and verify access
            shelf = await self.get_shelf(shelf_id, user_id)
            
            # Check if shelf has any resources
            resource_count = await self._count_shelf_resources(shelf_id)
            if resource_count > 0:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Cannot delete shelf with {resource_count} resources. Move or delete resources first."
                )
            
            # Perform soft delete
            shelf.is_deleted = True
            shelf.updated_at = datetime.now(timezone.utc)
            shelf.updated_by = user_id

            # Save changes
            updated_shelf = await self.shelf_repo.update(str(shelf_id), shelf)
            
            if not updated_shelf:
                return False

            logger.info(f"Deleted shelf {shelf_id} for user {user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to delete shelf {shelf_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete shelf: {str(e)}"
            )

    async def list_library_shelves(self, library_id: UUID, user_id: UUID) -> List[Shelf]:
        """List shelves in a library - supports mixed resource types"""
        await self._ensure_initialized()

        try:
            # Verify library access
            library = await self.library_repo.get_by_id(str(library_id))
            if not library or library.owner_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this library"
                )

            # Build query conditions (no resource type filtering)
            query_conditions = {
                "library_id": str(library_id),
                "is_deleted": False
            }

            # Get shelves
            shelves = await self.shelf_repo.get_all(query_conditions=query_conditions)

            # Update resource counts for each shelf
            for shelf in shelves:
                shelf.resource_count = await self._count_shelf_resources(shelf.id)

            logger.info(f"Retrieved {len(shelves)} mixed-type shelves for library {library_id}")
            return shelves

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to list shelves for library {library_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list shelves: {str(e)}"
            )

    async def _count_shelf_resources(self, shelf_id: UUID) -> int:
        """Count resources in a shelf - supports mixed resource types"""
        try:
            total_count = 0
            shelf_id_str = str(shelf_id)

            # Count resources in each repository that have this shelf_id in their shelf_ids list
            for repo in [self.file_repo, self.article_repo, self.web_repo]:
                try:
                    resources = await repo.get_all(
                        query_conditions={"is_deleted": False}
                    )
                    for resource in resources:
                        if resource.shelf_ids and shelf_id_str in [str(sid) for sid in resource.shelf_ids]:
                            total_count += 1
                except Exception as e:
                    logger.warning(f"Error counting resources in repository: {e}")
                    continue

            return total_count

        except Exception as e:
            logger.error(f"Failed to count resources for shelf {shelf_id}: {e}")
            return 0

    async def add_resource_to_shelf(self, shelf_id: UUID, resource_id: UUID, user_id: UUID) -> bool:
        """Add a resource to a shelf - supports mixed resource types"""
        await self._ensure_initialized()

        try:
            # Get existing shelf and verify access
            shelf = await self.get_shelf(shelf_id, user_id)

            # Try to find the resource in all repositories since shelves support mixed types
            resource = None
            resource_repo = None
            resource_type = None

            # Check file repository
            try:
                resource = await self.file_repo.get_by_id(str(resource_id))
                if resource:
                    resource_repo = self.file_repo
                    resource_type = "file"
            except:
                pass

            # Check article repository if not found
            if not resource:
                try:
                    resource = await self.article_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_repo = self.article_repo
                        resource_type = "article"
                except:
                    pass

            # Check web repository if not found
            if not resource:
                try:
                    resource = await self.web_repo.get_by_id(str(resource_id))
                    if resource:
                        resource_repo = self.web_repo
                        resource_type = "web"
                except:
                    pass

            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Resource not found"
                )

            # Add shelf_id to the resource's shelf_ids list
            if not resource.shelf_ids:
                resource.shelf_ids = []

            if shelf_id not in resource.shelf_ids:
                resource.shelf_ids.append(shelf_id)
                resource.updated_at = datetime.now(timezone.utc)
                resource.updated_by = user_id

                # Save the updated resource
                updated_resource = await resource_repo.update(str(resource_id), resource)

                if not updated_resource:
                    return False

                # Update shelf resource count
                shelf.resource_count = await self._count_shelf_resources(shelf_id)
                shelf.updated_at = datetime.now(timezone.utc)
                shelf.updated_by = user_id
                await self.shelf_repo.update(str(shelf_id), shelf)

                logger.info(f"Added {resource_type} resource {resource_id} to mixed-type shelf {shelf_id} for user {user_id}")
            else:
                logger.info(f"Resource {resource_id} already in shelf {shelf_id}")

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to add resource {resource_id} to shelf {shelf_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to add resource to shelf: {str(e)}"
            )

    async def get_shelf_resources(self, shelf_id: UUID, user_id: UUID, search: str = None, limit: int = 50) -> List[dict]:
        """Get all resources in a shelf - supports mixed resource types"""
        await self._ensure_initialized()

        try:
            # Get existing shelf and verify access
            shelf = await self.get_shelf(shelf_id, user_id)

            # Get resources from all repositories since shelves support mixed types
            all_resources = []

            # Query condition to find resources that have this shelf_id in their shelf_ids list
            shelf_id_str = str(shelf_id)

            # Get file resources
            try:
                file_resources = await self.file_repo.get_all(
                    query_conditions={"is_deleted": False},
                    limit=limit
                )
                for resource in file_resources:
                    if resource.shelf_ids and shelf_id_str in [str(sid) for sid in resource.shelf_ids]:
                        resource_dict = {
                            'id': str(resource.id),
                            'name': getattr(resource, 'filename', None) or getattr(resource, 'title', 'Untitled'),
                            'title': getattr(resource, 'title', None) or getattr(resource, 'filename', 'Untitled'),
                            'description': getattr(resource, 'description', ''),
                            'type': 'file',
                            'created_at': resource.created_at.isoformat() if resource.created_at else None,
                            'updated_at': resource.updated_at.isoformat() if resource.updated_at else None,
                            'file_path': getattr(resource, 'file_path', ''),
                            'file_size': getattr(resource, 'size', 0),
                            'file_type': getattr(resource, 'file_type', ''),
                            'shelf_ids': [str(sid) for sid in resource.shelf_ids] if resource.shelf_ids else []
                        }
                        all_resources.append(resource_dict)
            except Exception as e:
                logger.warning(f"Error fetching file resources for shelf {shelf_id}: {e}")

            # Get article resources
            try:
                article_resources = await self.article_repo.get_all(
                    query_conditions={"is_deleted": False},
                    limit=limit
                )
                for resource in article_resources:
                    if resource.shelf_ids and shelf_id_str in [str(sid) for sid in resource.shelf_ids]:
                        resource_dict = {
                            'id': str(resource.id),
                            'name': getattr(resource, 'title', 'Untitled'),
                            'title': getattr(resource, 'title', 'Untitled'),
                            'description': getattr(resource, 'description', ''),
                            'type': 'article',
                            'created_at': resource.created_at.isoformat() if resource.created_at else None,
                            'updated_at': resource.updated_at.isoformat() if resource.updated_at else None,
                            'author': getattr(resource, 'author', ''),
                            'content': getattr(resource, 'content', ''),
                            'shelf_ids': [str(sid) for sid in resource.shelf_ids] if resource.shelf_ids else []
                        }
                        all_resources.append(resource_dict)
            except Exception as e:
                logger.warning(f"Error fetching article resources for shelf {shelf_id}: {e}")

            # Get web resources
            try:
                web_resources = await self.web_repo.get_all(
                    query_conditions={"is_deleted": False},
                    limit=limit
                )
                for resource in web_resources:
                    if resource.shelf_ids and shelf_id_str in [str(sid) for sid in resource.shelf_ids]:
                        resource_dict = {
                            'id': str(resource.id),
                            'name': getattr(resource, 'title', 'Untitled'),
                            'title': getattr(resource, 'title', 'Untitled'),
                            'description': getattr(resource, 'description', ''),
                            'type': 'web',
                            'created_at': resource.created_at.isoformat() if resource.created_at else None,
                            'updated_at': resource.updated_at.isoformat() if resource.updated_at else None,
                            'url': getattr(resource, 'url', ''),
                            'content': getattr(resource, 'content', ''),
                            'shelf_ids': [str(sid) for sid in resource.shelf_ids] if resource.shelf_ids else []
                        }
                        all_resources.append(resource_dict)
            except Exception as e:
                logger.warning(f"Error fetching web resources for shelf {shelf_id}: {e}")

            # Apply search filter if provided
            if search:
                search_lower = search.lower()
                filtered_resources = []
                for resource_dict in all_resources:
                    if (search_lower in resource_dict['name'].lower() or
                        search_lower in resource_dict.get('description', '').lower()):
                        filtered_resources.append(resource_dict)
                all_resources = filtered_resources

            # Sort by created_at (newest first)
            all_resources.sort(key=lambda x: x.get('created_at', ''), reverse=True)

            # Apply limit
            if limit and len(all_resources) > limit:
                all_resources = all_resources[:limit]

            logger.info(f"Retrieved {len(all_resources)} mixed-type resources for shelf {shelf_id}")
            return all_resources

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get resources for shelf {shelf_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get shelf resources: {str(e)}"
            )
