from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timezone
from uuid import UUID
from fastapi import HTTP<PERSON>x<PERSON>, status
import logging

from models.library import Tag
from models.resource import FileResource, ArticleResource, WebResource
from data_access.factory import RepositoryFactory
from data_access.base import BaseRepository

logger = logging.getLogger(__name__)

class TagService:
    """Service for managing tags and resource tagging"""
    
    def __init__(self):
        """Initialize the tag service - repositories will be created on first use"""
        self.tag_repo: Optional[BaseRepository[Tag]] = None
        self.file_repo: Optional[BaseRepository[FileResource]] = None
        self.article_repo: Optional[BaseRepository[ArticleResource]] = None
        self.web_repo: Optional[BaseRepository[WebResource]] = None
        self._initialized = False

    async def initialize(self):
        """Initialize repositories asynchronously"""
        if self._initialized:
            return
            
        try:
            self.tag_repo = await RepositoryFactory.create_tag_repository()
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            self._initialized = True
            logger.info("TagService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize TagService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()

    async def create_tag(self, tag_data: dict, user_id: UUID) -> Tag:
        """
        Create a new tag
        
        Args:
            tag_data: Dictionary containing tag information
            user_id: ID of the user creating the tag
            
        Returns:
            Created Tag instance
            
        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        await self._ensure_initialized()
        
        try:
            # Validate required fields
            if not tag_data.get('name'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tag name is required"
                )
            
            if not tag_data.get('tenant_id'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tenant ID is required"
                )

            # Check if tag with same name exists for this tenant
            existing_tags = await self.tag_repo.get_all(
                query_conditions={
                    "name": tag_data['name'],
                    "tenant_id": str(tag_data['tenant_id']),
                    "is_deleted": False
                }
            )
            
            if existing_tags:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Tag with name '{tag_data['name']}' already exists"
                )

            # Create tag instance
            tag = Tag(
                name=tag_data['name'],
                color=tag_data.get('color'),
                usage_count=0,
                tenant_id=UUID(tag_data['tenant_id']),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )

            # Save to database
            created_tag = await self.tag_repo.create(tag, str(tag_data['tenant_id']))
            logger.info(f"Created tag '{tag.name}' for user {user_id}")
            
            return created_tag

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating tag: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create tag: {str(e)}"
            )

    async def get_tag(self, tag_id: UUID, user_id: UUID) -> Tag:
        """
        Get a tag by ID
        
        Args:
            tag_id: ID of the tag to retrieve
            user_id: ID of the requesting user
            
        Returns:
            Tag instance
            
        Raises:
            HTTPException: If tag not found
        """
        await self._ensure_initialized()
        
        try:
            tag = await self.tag_repo.get_by_id(str(tag_id))
            
            if not tag or tag.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Tag not found"
                )

            return tag

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error retrieving tag {tag_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve tag: {str(e)}"
            )

    async def update_tag(self, tag_id: UUID, updates: dict, user_id: UUID) -> Tag:
        """
        Update a tag
        
        Args:
            tag_id: ID of the tag to update
            updates: Dictionary of fields to update
            user_id: ID of the user making the update
            
        Returns:
            Updated Tag instance
            
        Raises:
            HTTPException: If tag not found or validation fails
        """
        await self._ensure_initialized()
        
        try:
            # Get existing tag
            tag = await self.get_tag(tag_id, user_id)

            # Update allowed fields
            allowed_fields = ['name', 'color']
            for field in allowed_fields:
                if field in updates:
                    setattr(tag, field, updates[field])
            
            # Update metadata
            tag.updated_at = datetime.now(timezone.utc)
            tag.updated_by = user_id

            # Check for name conflicts if name is being updated
            if 'name' in updates and updates['name'] != tag.name:
                existing_tags = await self.tag_repo.get_all(
                    query_conditions={
                        "name": updates['name'],
                        "tenant_id": str(tag.tenant_id),
                        "is_deleted": False
                    }
                )
                
                # Filter out the current tag
                existing_tags = [t for t in existing_tags if t.id != tag.id]
                
                if existing_tags:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"Tag with name '{updates['name']}' already exists"
                    )

            # Save updates
            updated_tag = await self.tag_repo.update(str(tag_id), tag)
            
            if not updated_tag:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update tag"
                )

            logger.info(f"Updated tag {tag_id} for user {user_id}")
            return updated_tag

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating tag {tag_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update tag: {str(e)}"
            )

    async def delete_tag(self, tag_id: UUID, user_id: UUID) -> bool:
        """
        Delete a tag (soft delete)
        
        Args:
            tag_id: ID of the tag to delete
            user_id: ID of the user performing the deletion
            
        Returns:
            True if deletion was successful
            
        Raises:
            HTTPException: If tag not found
        """
        await self._ensure_initialized()
        
        try:
            # Get existing tag
            tag = await self.get_tag(tag_id, user_id)
            
            # Perform soft delete
            tag.is_deleted = True
            tag.updated_at = datetime.now(timezone.utc)
            tag.updated_by = user_id

            # Save changes
            updated_tag = await self.tag_repo.update(str(tag_id), tag)
            
            if not updated_tag:
                return False

            # Remove tag from resources (asynchronously)
            await self._remove_tag_from_all_resources(tag_id, user_id)

            logger.info(f"Deleted tag {tag_id} for user {user_id}")
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting tag {tag_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete tag: {str(e)}"
            )

    async def list_tags(self, tenant_id: UUID, search_term: Optional[str] = None, 
                        sort_by: str = "name", skip: int = 0, limit: int = 100) -> List[Tag]:
        """
        List tags with optional filtering and sorting
        
        Args:
            tenant_id: Tenant ID to filter by
            search_term: Optional search term to filter tags by name
            sort_by: Field to sort by ("name", "usage_count", "created_at")
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of Tag instances
        """
        await self._ensure_initialized()
        
        try:
            query_conditions = {
                "tenant_id": str(tenant_id),
                "is_deleted": False
            }
            
            # Add name search if provided
            if search_term:
                # Simple contains search - in a real implementation, this would use
                # a more sophisticated search mechanism based on the database
                query_conditions["name"] = {"$regex": search_term, "$options": "i"}

            # Get tags
            tags = await self.tag_repo.get_all(
                skip=skip,
                limit=limit,
                query_conditions=query_conditions
            )

            # Sort tags (in-memory sorting as a fallback)
            if sort_by == "usage_count":
                tags.sort(key=lambda tag: tag.usage_count, reverse=True)
            elif sort_by == "created_at":
                tags.sort(key=lambda tag: tag.created_at, reverse=True)
            else:
                # Default sort by name
                tags.sort(key=lambda tag: tag.name.lower())

            logger.info(f"Retrieved {len(tags)} tags for tenant {tenant_id}")
            return tags

        except Exception as e:
            logger.error(f"Error listing tags for tenant {tenant_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list tags: {str(e)}"
            )

    async def get_tag_suggestions(self, tenant_id: UUID, partial_name: str, limit: int = 10) -> List[Tag]:
        """
        Get tag suggestions based on partial name input
        
        Args:
            tenant_id: Tenant ID to filter by
            partial_name: Partial tag name to match
            limit: Maximum number of suggestions to return
            
        Returns:
            List of matching Tag instances
        """
        await self._ensure_initialized()
        
        try:
            if not partial_name:
                # Return most used tags if no input provided
                return await self.list_tags(tenant_id, sort_by="usage_count", limit=limit)
            
            # Query for tags that start with the partial name
            query_conditions = {
                "tenant_id": str(tenant_id),
                "is_deleted": False,
                "name": {"$regex": f"^{partial_name}", "$options": "i"}
            }
            
            matching_tags = await self.tag_repo.get_all(
                limit=limit,
                query_conditions=query_conditions
            )
            
            # Sort by usage count to prioritize popular tags
            matching_tags.sort(key=lambda tag: tag.usage_count, reverse=True)
            
            logger.info(f"Found {len(matching_tags)} tag suggestions for '{partial_name}'")
            return matching_tags

        except Exception as e:
            logger.error(f"Error getting tag suggestions for '{partial_name}': {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get tag suggestions: {str(e)}"
            )

    async def assign_tags_to_resource(self, resource_id: UUID, resource_type: str, 
                                     tag_ids: List[UUID], user_id: UUID) -> Union[FileResource, ArticleResource, WebResource]:
        """
        Assign tags to a resource
        
        Args:
            resource_id: ID of the resource to tag
            resource_type: Type of resource ("file", "article", "web")
            tag_ids: List of tag IDs to assign
            user_id: ID of the user making the assignment
            
        Returns:
            Updated resource instance
            
        Raises:
            HTTPException: If resource not found, tags not found, or assignment fails
        """
        await self._ensure_initialized()
        
        try:
            # Get the appropriate repository and resource
            repo, resource = await self._get_resource_and_repo(resource_id, resource_type)
            
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"{resource_type.title()} resource not found"
                )

            # Validate that all tags exist
            for tag_id in tag_ids:
                tag = await self.get_tag(tag_id, user_id)
                if not tag:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Tag with ID {tag_id} not found"
                    )

            # Update resource with tag assignments
            resource.tags = tag_ids
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id

            # Save the updated resource
            updated_resource = await repo.update(str(resource_id), resource)
            
            if not updated_resource:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to assign tags to resource"
                )

            # Update usage count for each tag
            for tag_id in tag_ids:
                await self._increment_tag_usage(tag_id)

            logger.info(f"Assigned {len(tag_ids)} tags to {resource_type} resource {resource_id}")
            return updated_resource

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error assigning tags to resource {resource_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to assign tags to resource: {str(e)}"
            )

    async def add_tags_to_resource(self, resource_id: UUID, resource_type: str, 
                                  tag_ids: List[UUID], user_id: UUID) -> Union[FileResource, ArticleResource, WebResource]:
        """
        Add tags to a resource (preserving existing tags)
        
        Args:
            resource_id: ID of the resource to tag
            resource_type: Type of resource ("file", "article", "web")
            tag_ids: List of tag IDs to add
            user_id: ID of the user making the assignment
            
        Returns:
            Updated resource instance
            
        Raises:
            HTTPException: If resource not found, tags not found, or assignment fails
        """
        await self._ensure_initialized()
        
        try:
            # Get the appropriate repository and resource
            repo, resource = await self._get_resource_and_repo(resource_id, resource_type)
            
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"{resource_type.title()} resource not found"
                )

            # Validate that all tags exist
            for tag_id in tag_ids:
                tag = await self.get_tag(tag_id, user_id)
                if not tag:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Tag with ID {tag_id} not found"
                    )

            # Get existing tags and add new ones (avoiding duplicates)
            existing_tags = resource.tags or []
            combined_tags = list(set(existing_tags + tag_ids))
            
            # Update resource with combined tags
            resource.tags = combined_tags
            resource.updated_at = datetime.now(timezone.utc)
            resource.updated_by = user_id

            # Save the updated resource
            updated_resource = await repo.update(str(resource_id), resource)
            
            if not updated_resource:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to add tags to resource"
                )

            # Update usage count for each new tag
            for tag_id in tag_ids:
                if tag_id not in existing_tags:
                    await self._increment_tag_usage(tag_id)

            logger.info(f"Added {len(tag_ids)} tags to {resource_type} resource {resource_id}")
            return updated_resource

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error adding tags to resource {resource_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to add tags to resource: {str(e)}"
            )

    async def remove_tag_from_resource(self, resource_id: UUID, resource_type: str, 
                                      tag_id: UUID, user_id: UUID) -> Union[FileResource, ArticleResource, WebResource]:
        """
        Remove a tag from a resource
        
        Args:
            resource_id: ID of the resource to update
            resource_type: Type of resource ("file", "article", "web")
            tag_id: ID of the tag to remove
            user_id: ID of the user making the change
            
        Returns:
            Updated resource instance
            
        Raises:
            HTTPException: If resource not found or removal fails
        """
        await self._ensure_initialized()
        
        try:
            # Get the appropriate repository and resource
            repo, resource = await self._get_resource_and_repo(resource_id, resource_type)
            
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"{resource_type.title()} resource not found"
                )

            # Remove tag from resource's tag list
            if resource.tags and tag_id in resource.tags:
                resource.tags = [t for t in resource.tags if t != tag_id]
                resource.updated_at = datetime.now(timezone.utc)
                resource.updated_by = user_id

                # Save the updated resource
                updated_resource = await repo.update(str(resource_id), resource)
                
                if not updated_resource:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to remove tag from resource"
                    )

                # Decrement tag usage count
                await self._decrement_tag_usage(tag_id)
                
                logger.info(f"Removed tag {tag_id} from {resource_type} resource {resource_id}")
                return updated_resource
            else:
                # Tag not found on resource, return unchanged
                return resource

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error removing tag from resource {resource_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to remove tag from resource: {str(e)}"
            )

    async def get_resources_by_tag(self, tag_id: UUID, tenant_id: UUID, 
                                  resource_type: Optional[str] = None,
                                  skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get resources that have a specific tag
        
        Args:
            tag_id: ID of the tag to filter by
            tenant_id: Tenant ID to filter by
            resource_type: Optional resource type filter ("file", "article", "web")
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of resources with the specified tag
        """
        await self._ensure_initialized()
        
        try:
            # Validate tag exists
            tag = await self.tag_repo.get_by_id(str(tag_id))
            if not tag or tag.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Tag not found"
                )

            resources = []
            
            # Query conditions for all resource types
            query_conditions = {
                "tenant_id": str(tenant_id),
                "is_deleted": False,
                "tags": {"$contains": str(tag_id)}
            }
            
            # Get resources by type
            if resource_type is None or resource_type == "file":
                file_resources = await self.file_repo.get_all(
                    skip=skip if resource_type == "file" else 0,
                    limit=limit if resource_type == "file" else limit,
                    query_conditions=query_conditions
                )
                for resource in file_resources:
                    resources.append({
                        "id": resource.id,
                        "type": "file",
                        "title": resource.title,
                        "description": resource.description,
                        "resource": resource
                    })
            
            if resource_type is None or resource_type == "article":
                article_resources = await self.article_repo.get_all(
                    skip=skip if resource_type == "article" else 0,
                    limit=limit if resource_type == "article" else limit,
                    query_conditions=query_conditions
                )
                for resource in article_resources:
                    resources.append({
                        "id": resource.id,
                        "type": "article",
                        "title": resource.title,
                        "description": resource.description,
                        "resource": resource
                    })
            
            if resource_type is None or resource_type == "web":
                web_resources = await self.web_repo.get_all(
                    skip=skip if resource_type == "web" else 0,
                    limit=limit if resource_type == "web" else limit,
                    query_conditions=query_conditions
                )
                for resource in web_resources:
                    resources.append({
                        "id": resource.id,
                        "type": "web",
                        "title": resource.title,
                        "description": resource.description,
                        "resource": resource
                    })
            
            # If no specific resource type, we need to handle pagination manually
            if resource_type is None:
                # Sort by updated_at to get most recently updated resources first
                resources.sort(key=lambda r: getattr(r["resource"], "updated_at", datetime.min), reverse=True)
                
                # Apply skip and limit
                resources = resources[skip:skip+limit]
            
            logger.info(f"Found {len(resources)} resources with tag {tag_id}")
            return resources

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting resources by tag {tag_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get resources by tag: {str(e)}"
            )

    # Private helper methods

    async def _get_resource_and_repo(self, resource_id: UUID, resource_type: str) -> tuple:
        """Get resource and its repository based on type"""
        if resource_type == "file":
            repo = self.file_repo
            resource = await repo.get_by_id(str(resource_id))
        elif resource_type == "article":
            repo = self.article_repo
            resource = await repo.get_by_id(str(resource_id))
        elif resource_type == "web":
            repo = self.web_repo
            resource = await repo.get_by_id(str(resource_id))
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid resource type: {resource_type}. Must be 'file', 'article', or 'web'"
            )
        
        return repo, resource

    async def _increment_tag_usage(self, tag_id: UUID) -> None:
        """Increment the usage count for a tag"""
        try:
            tag = await self.tag_repo.get_by_id(str(tag_id))
            if tag and not tag.is_deleted:
                tag.usage_count += 1
                await self.tag_repo.update(str(tag_id), tag)
        except Exception as e:
            logger.warning(f"Failed to increment usage count for tag {tag_id}: {str(e)}")

    async def _decrement_tag_usage(self, tag_id: UUID) -> None:
        """Decrement the usage count for a tag"""
        try:
            tag = await self.tag_repo.get_by_id(str(tag_id))
            if tag and not tag.is_deleted and tag.usage_count > 0:
                tag.usage_count -= 1
                await self.tag_repo.update(str(tag_id), tag)
        except Exception as e:
            logger.warning(f"Failed to decrement usage count for tag {tag_id}: {str(e)}")

    async def _remove_tag_from_all_resources(self, tag_id: UUID, user_id: UUID) -> None:
        """Remove a tag from all resources that have it"""
        try:
            # Process file resources
            file_resources = await self.file_repo.get_all(
                query_conditions={
                    "tags": {"$contains": str(tag_id)},
                    "is_deleted": False
                }
            )
            
            for resource in file_resources:
                resource.tags = [t for t in resource.tags if t != tag_id]
                resource.updated_at = datetime.now(timezone.utc)
                resource.updated_by = user_id
                await self.file_repo.update(str(resource.id), resource)
            
            # Process article resources
            article_resources = await self.article_repo.get_all(
                query_conditions={
                    "tags": {"$contains": str(tag_id)},
                    "is_deleted": False
                }
            )
            
            for resource in article_resources:
                resource.tags = [t for t in resource.tags if t != tag_id]
                resource.updated_at = datetime.now(timezone.utc)
                resource.updated_by = user_id
                await self.article_repo.update(str(resource.id), resource)
            
            # Process web resources
            web_resources = await self.web_repo.get_all(
                query_conditions={
                    "tags": {"$contains": str(tag_id)},
                    "is_deleted": False
                }
            )
            
            for resource in web_resources:
                resource.tags = [t for t in resource.tags if t != tag_id]
                resource.updated_at = datetime.now(timezone.utc)
                resource.updated_by = user_id
                await self.web_repo.update(str(resource.id), resource)
                
            logger.info(f"Removed tag {tag_id} from all resources")
            
        except Exception as e:
            logger.warning(f"Error removing tag {tag_id} from resources: {str(e)}")