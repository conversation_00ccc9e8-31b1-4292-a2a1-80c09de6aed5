from typing import List, Dict, Any, Optional, Literal, Union
from langchain_weaviate import WeaviateVectorStore
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain_mongodb.indexes import MongoDBRecordManager
from langchain.indexes import index
from models.resource import Chunk
from data_access.factory import RepositoryFactory
from datetime import datetime, timezone
from langchain_community.document_loaders import (
    # PDF loaders
    PyPDFLoader, UnstructuredPDFLoader, PDFMinerLoader, PDFPlumberLoader,
    # Text and document loaders
    TextLoader, UnstructuredFileLoader, UnstructuredMarkdownLoader, 
    UnstructuredRTFLoader, UnstructuredODTLoader, UnstructuredEPubLoader,
    # Office document loaders
    UnstructuredWordDocumentLoader, UnstructuredPowerPointLoader,
    UnstructuredExcelLoader, Docx2txtLoader,
    # Data file loaders
    CSVLoader, JSONLoader, UnstructuredTSVLoader, UnstructuredXMLLoader,
    # Web loaders
    WebBaseLoader, AsyncHtmlLoader, SitemapLoader, RecursiveUrlLoader,
    UnstructuredHTMLLoader, BSHTMLLoader, UnstructuredURLLoader,
    # Email loaders
    UnstructuredEmailLoader, OutlookMessageLoader, # UnstructuredEMLLoader,
    # Code loaders
    PythonLoader, NotebookLoader,
    # Image loaders (with OCR)
    UnstructuredImageLoader,
    # Audio loaders
    # OpenAIWhisperParser,  # May not be available in all versions
    # Generic loaders
    DirectoryLoader, UnstructuredAPIFileLoader
)
from langchain.document_loaders.base import BaseLoader
from fastapi import UploadFile
import weaviate
from config import settings
import logging
import aiofiles
import tempfile
import os
from pathlib import Path
import mimetypes
import asyncio
from urllib.robotparser import RobotFileParser
import time
import requests
from urllib.parse import urljoin, urlparse
import chardet  # For encoding detection
import re  # For text cleaning in sitemap extraction
import threading
from services.storage import storage_manager
import ssl
import urllib.request
import urllib3
import uuid
from uuid import UUID

# Disable SSL warnings for development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# WARNING: SSL verification is disabled in this module for development purposes.
# In production, consider:
# 1. Using proper SSL certificates
# 2. Adding custom CA bundles if needed
# 3. Setting up proper certificate verification
# 4. Using environment variables to control SSL behavior

def convert_uuids_to_strings(obj):
    """Recursively convert UUID objects and datetime objects to strings in dictionaries and lists"""
    if isinstance(obj, dict):
        return {k: convert_uuids_to_strings(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_uuids_to_strings(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_uuids_to_strings(item) for item in obj)
    elif isinstance(obj, set):
        return {convert_uuids_to_strings(item) for item in obj}
    elif isinstance(obj, (uuid.UUID, UUID)):
        return str(obj)
    elif isinstance(obj, datetime):
        return obj.isoformat()
    else:
        return obj

logger = logging.getLogger(__name__)

class ResourceIndexer:
    def __init__(self, tenant_id: str, collection_name: str):
        # Store tenant ID and collection name
        self.tenant_id = tenant_id
        self.collection_name = collection_name
        
        # Create tenant-specific namespace: tenant_id.collection_name
        self._namespace = f"{tenant_id}.{collection_name}"
        
        # Create tenant-specific Weaviate index name
        self._chunk_index_name = "Chunk"
        
        # Initialize chunk repository for database operations
        self.chunk_repository = None  # Will be initialized when needed
        
        # Initialize with safe defaults
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001",
                google_api_key=settings.google_ai_key
            )
        except Exception as e:
            logger.warning(f"Could not initialize GoogleGenerativeAIEmbeddings: {e}")
            self.embeddings = None
        
        # Configurable text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            length_function=len,
        )
        
        # Initialize MongoDB record manager with connection pool
        try:
            # Get client from pool
            self.record_manager = MongoDBRecordManager.from_connection_string(
                connection_string=settings.mongo_url,
                namespace=self._namespace
            )                

        except Exception as e:
            logger.warning(f"Could not initialize MongoDB record manager: {e}")
            self.record_manager = None
        
        # Initialize Weaviate client based on configuration - safely
        try:
            if hasattr(settings, 'weaviate_use_cloud') and settings.weaviate_use_cloud and settings.weaviate_api_key:
                self.weaviate_client = weaviate.connect_to_weaviate_cloud(
                    cluster_url=settings.weaviate_cluster_url if hasattr(settings, 'weaviate_cluster_url') else settings.weaviate_url,
                    auth_credentials=weaviate.auth.AuthApiKey(settings.weaviate_api_key),
                )
            else:
                # Local Weaviate
                host = settings.weaviate_url.replace("http://", "").replace("https://", "")
                port = int(host.split(":")[-1]) if ":" in host else 8080
                host = host.split(":")[0] if ":" in host else host
                
                self.weaviate_client = weaviate.connect_to_local(
                    host=host,
                    port=port,
                )
        except Exception as e:
            logger.warning(f"Could not connect to Weaviate: {e}")
            self.weaviate_client = None
        
        # Initialize Weaviate vector store - safely
        try:
            if self.weaviate_client and self.embeddings:
                self.vector_store = WeaviateVectorStore(
                    client=self.weaviate_client,
                    index_name=self._chunk_index_name,  # Use tenant-specific index name
                    text_key="text",
                    embedding=self.embeddings,
                    use_multi_tenancy=True
                )
            else:
                self.vector_store = None
        except Exception as e:
            logger.warning(f"Could not initialize Weaviate vector store: {e}")
            self.vector_store = None
        
        logger.info(f"ResourceIndexer initialized for tenant: {tenant_id}, collection: {collection_name} (vector_store: {'available' if self.vector_store else 'unavailable'})")
    
    def _get_vector_store(self) -> WeaviateVectorStore:
        """Get the vector store for this collection"""
        if not self.vector_store:
            raise ValueError("Vector store is not available. Check Weaviate and Google AI configuration.")
        return self.vector_store
    
    def _detect_file_type_and_encoding(self, file_path: str, content_type: Optional[str] = None, 
                                       file_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        Detect file type and encoding using Content-Type and file extension.
        
        This method prioritizes information sources in the following order:
        1. Explicit content_type parameter (from upload or storage)
        2. Content type from file_metadata (from storage metadata)
        3. MIME type guessed from file extension using mimetypes library
        4. Fallback mapping for common extensions
        5. Default to 'application/octet-stream' for unknown types
        
        Args:
            file_path: Path to the file
            content_type: Explicit content type (e.g., from HTTP upload headers)
            file_metadata: File metadata dict that may contain 'content_type' and 'encoding'
            
        Returns:
            Dict containing:
            - mime_type: Detected MIME type
            - encoding: Text encoding (for text files)
            - file_extension: File extension from path
            - detected_type: Same as mime_type (for backward compatibility)
            
        Examples:
            # From upload with content type
            result = indexer._detect_file_type_and_encoding(
                "/tmp/doc.pdf", 
                content_type="application/pdf"
            )
            # Returns: {"mime_type": "application/pdf", "encoding": "utf-8", ...}
            
            # From storage metadata
            result = indexer._detect_file_type_and_encoding(
                "/tmp/data.json",
                file_metadata={"content_type": "application/json", "encoding": "utf-8"}
            )
            # Returns: {"mime_type": "application/json", "encoding": "utf-8", ...}
            
            # Extension-based detection
            result = indexer._detect_file_type_and_encoding("/tmp/readme.txt")
            # Returns: {"mime_type": "text/plain", "encoding": "ascii", ...}
        """
        result = {
            "mime_type": None,
            "encoding": None,
            "file_extension": Path(file_path).suffix.lower(),
            "detected_type": None
        }
        
        try:
            # Priority 1: Use provided content_type (from storage or upload)
            if content_type:
                result["mime_type"] = content_type
                logger.debug(f"Using provided content type: {content_type}")
            
            # Priority 2: Check file metadata for content type
            elif file_metadata and "content_type" in file_metadata:
                result["mime_type"] = file_metadata["content_type"]
                logger.debug(f"Using metadata content type: {file_metadata['content_type']}")
            
            # Priority 3: Use mimetypes library based on file extension
            else:
                guessed_type, _ = mimetypes.guess_type(file_path)
                if guessed_type:
                    result["mime_type"] = guessed_type
                    logger.debug(f"Guessed content type from extension: {guessed_type}")
                else:
                    # Fallback mapping for common extensions
                    extension_mapping = {
                        '.pdf': 'application/pdf',
                        '.txt': 'text/plain',
                        '.md': 'text/markdown',
                        '.json': 'application/json',
                        '.csv': 'text/csv',
                        '.xml': 'application/xml',
                        '.html': 'text/html',
                        '.htm': 'text/html',
                        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        '.rtf': 'application/rtf',
                        '.odt': 'application/vnd.oasis.opendocument.text',
                        '.epub': 'application/epub+zip',
                        '.png': 'image/png',
                        '.jpg': 'image/jpeg',
                        '.jpeg': 'image/jpeg',
                        '.gif': 'image/gif',
                        '.bmp': 'image/bmp',
                        '.tiff': 'image/tiff',
                        '.py': 'text/x-python',
                        '.js': 'text/javascript',
                        '.css': 'text/css',
                        '.yaml': 'text/yaml',
                        '.yml': 'text/yaml',
                        '.log': 'text/plain'
                    }
                    result["mime_type"] = extension_mapping.get(result["file_extension"], 'application/octet-stream')
                    logger.debug(f"Using fallback content type: {result['mime_type']}")
            
            # Detect encoding for text files
            if result["mime_type"] and (result["mime_type"].startswith('text/') or 
                                      result["mime_type"] in ['application/json', 'application/xml', 'application/csv']):
                # Check if encoding is provided in metadata
                if file_metadata and "encoding" in file_metadata:
                    result["encoding"] = file_metadata["encoding"]
                else:
                    # Try to detect encoding from file content
                    try:
                        with open(file_path, 'rb') as f:
                            raw_data = f.read(10000)  # Read first 10KB
                            detected = chardet.detect(raw_data)
                            result["encoding"] = detected.get('encoding', 'utf-8') if detected else 'utf-8'
                    except Exception as e:
                        logger.warning(f"Could not detect encoding for {file_path}: {str(e)}")
                        result["encoding"] = 'utf-8'  # Default encoding
            else:
                result["encoding"] = 'utf-8'  # Default for non-text files
                
            # Set detected_type for backward compatibility
            result["detected_type"] = result["mime_type"]
            
        except Exception as e:
            logger.warning(f"Error detecting file type for {file_path}: {str(e)}")
            # Ultimate fallback
            result["mime_type"] = 'application/octet-stream'
            result["encoding"] = 'utf-8'
            result["detected_type"] = result["mime_type"]
        
        return result
    
    def _get_comprehensive_file_loader(self, file_path: str, file_info: Optional[Dict] = None, 
                                      content_type: Optional[str] = None, 
                                      file_metadata: Optional[Dict[str, Any]] = None) -> BaseLoader:
        """Get the best document loader based on comprehensive file analysis"""
        
        if file_info is None:
            file_info = self._detect_file_type_and_encoding(file_path, content_type, file_metadata)
        
        mime_type = file_info.get("mime_type", "")
        file_extension = file_info.get("file_extension", "")
        encoding = file_info.get("encoding", "utf-8")
        
        # Comprehensive loader mapping with multiple options per type
        loader_options = {
            # PDF files - multiple loaders for different scenarios
            'application/pdf': [PyPDFLoader, UnstructuredPDFLoader, PDFMinerLoader, PDFPlumberLoader],
            '.pdf': [PyPDFLoader, UnstructuredPDFLoader, PDFMinerLoader, PDFPlumberLoader],
            
            # Text files - handle all text variations
            'text/plain': [TextLoader],
            '.txt': [TextLoader],
            '.text': [TextLoader],
            '.log': [TextLoader],
            '.cfg': [TextLoader],
            '.conf': [TextLoader],
            '.ini': [TextLoader],
            '.yaml': [TextLoader],
            '.yml': [TextLoader],
            '.toml': [TextLoader],
            
            # Markdown files
            'text/markdown': [UnstructuredMarkdownLoader, TextLoader],
            '.md': [UnstructuredMarkdownLoader, TextLoader],
            '.markdown': [UnstructuredMarkdownLoader, TextLoader],
            '.mdown': [UnstructuredMarkdownLoader, TextLoader],
            '.mkd': [UnstructuredMarkdownLoader, TextLoader],
            
            # Code files - treat as text
            '.py': [PythonLoader, TextLoader],
            '.js': [TextLoader],
            '.ts': [TextLoader],
            '.jsx': [TextLoader],
            '.tsx': [TextLoader],
            '.java': [TextLoader],
            '.cpp': [TextLoader],
            '.c': [TextLoader],
            '.h': [TextLoader],
            '.hpp': [TextLoader],
            '.cs': [TextLoader],
            '.php': [TextLoader],
            '.rb': [TextLoader],
            '.go': [TextLoader],
            '.rs': [TextLoader],
            '.sql': [TextLoader],
            '.sh': [TextLoader],
            '.bash': [TextLoader],
            '.zsh': [TextLoader],
            '.ps1': [TextLoader],
            '.bat': [TextLoader],
            '.cmd': [TextLoader],
            
            # Data files
            'text/csv': [CSVLoader],
            '.csv': [CSVLoader],
            '.tsv': [UnstructuredTSVLoader, TextLoader],
            'application/json': [JSONLoader, TextLoader],
            '.json': [JSONLoader, TextLoader],
            '.jsonl': [TextLoader],
            'application/xml': [UnstructuredXMLLoader, TextLoader],
            '.xml': [UnstructuredXMLLoader, TextLoader],
            
            # Office documents
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [UnstructuredWordDocumentLoader, Docx2txtLoader],
            '.docx': [UnstructuredWordDocumentLoader, Docx2txtLoader],
            '.doc': [UnstructuredWordDocumentLoader],
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': [UnstructuredPowerPointLoader],
            '.pptx': [UnstructuredPowerPointLoader],
            '.ppt': [UnstructuredPowerPointLoader],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [UnstructuredExcelLoader],
            '.xlsx': [UnstructuredExcelLoader],
            '.xls': [UnstructuredExcelLoader],
            
            # Rich text and document formats
            'application/rtf': [UnstructuredRTFLoader, TextLoader],
            '.rtf': [UnstructuredRTFLoader, TextLoader],
            'application/vnd.oasis.opendocument.text': [UnstructuredODTLoader],
            '.odt': [UnstructuredODTLoader],
            'application/epub+zip': [UnstructuredEPubLoader],
            '.epub': [UnstructuredEPubLoader],
            
            # HTML files
            'text/html': [BSHTMLLoader, UnstructuredHTMLLoader, TextLoader],
            '.html': [BSHTMLLoader, UnstructuredHTMLLoader, TextLoader],
            '.htm': [BSHTMLLoader, UnstructuredHTMLLoader, TextLoader],
            
            # Email files
            'message/rfc822': [UnstructuredEmailLoader],
            '.eml': [UnstructuredEmailLoader],
            '.msg': [OutlookMessageLoader],
            
            # Notebook files
            '.ipynb': [NotebookLoader],
            
            # Image files (with OCR)
            'image/png': [UnstructuredImageLoader],
            'image/jpeg': [UnstructuredImageLoader],
            'image/jpg': [UnstructuredImageLoader],
            'image/gif': [UnstructuredImageLoader],
            'image/bmp': [UnstructuredImageLoader],
            'image/tiff': [UnstructuredImageLoader],
            '.png': [UnstructuredImageLoader],
            '.jpg': [UnstructuredImageLoader],
            '.jpeg': [UnstructuredImageLoader],
            '.gif': [UnstructuredImageLoader],
            '.bmp': [UnstructuredImageLoader],
            '.tiff': [UnstructuredImageLoader],
            '.tif': [UnstructuredImageLoader],
        }
        
        # Get loader options, prioritizing MIME type over extension
        loaders = loader_options.get(mime_type) or loader_options.get(file_extension)
        
        if not loaders:
            # Fallback strategy for unknown file types
            if mime_type and mime_type.startswith('text/'):
                loaders = [TextLoader]
            else:
                # Try unstructured loader as last resort
                loaders = [UnstructuredFileLoader, TextLoader]
        
        # Try loaders in order until one works
        for loader_class in loaders:
            try:
                # Special handling for different loader requirements
                if loader_class == JSONLoader:
                    return loader_class(file_path, jq_schema='.', text_content=False)
                elif loader_class == CSVLoader:
                    return loader_class(file_path, encoding=encoding)
                elif loader_class == TextLoader:
                    return loader_class(file_path, encoding=encoding)
                elif loader_class in [PythonLoader]:
                    return loader_class(file_path)
                else:
                    return loader_class(file_path)
            except Exception as e:
                logger.warning(f"Failed to initialize {loader_class.__name__} for {file_path}: {str(e)}")
                continue
        
        # Ultimate fallback
        logger.warning(f"No suitable loader found for {file_path}, using TextLoader")
        return TextLoader(file_path, encoding=encoding)
    
    def _should_process_in_background(self, file_size: int, file_type: str = None) -> bool:
        """Determine if file should be processed in background based on size and type"""
        size_mb = file_size / (1024 * 1024)
        
        # Always process large files in background
        if size_mb > settings.large_file_threshold_mb:
            return True
        
        # Process immediately if small
        if size_mb < settings.small_file_threshold_mb:
            return False
        
        # For medium files, decide based on file type
        complex_types = ['.pdf', '.docx', '.pptx', '.xlsx', '.epub', '.msg']
        if file_type and any(file_type.endswith(ext) for ext in complex_types):
            return True
        
        return False
    
    def _create_ssl_context(self):
        """Create SSL context that handles certificate verification issues"""
        ssl_context = ssl.create_default_context()
        # For development/testing, you might want to disable SSL verification
        # In production, consider using proper certificates or custom CA bundles
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        return ssl_context

    def _check_robots_txt(self, url: str) -> bool:
        """Check if URL is allowed by robots.txt"""
        if not settings.web_crawl_respect_robots:
            return True
        
        try:
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            rp = RobotFileParser()
            rp.set_url(robots_url)
            
            # Create SSL context for HTTPS requests
            if parsed_url.scheme == 'https':
                ssl_context = self._create_ssl_context()
                # Create a custom opener with SSL context
                opener = urllib.request.build_opener(urllib.request.HTTPSHandler(context=ssl_context))
                urllib.request.install_opener(opener)
            
            rp.read()
            
            # Check if our user agent is allowed
            return rp.can_fetch("*", url)
        except Exception as e:
            logger.warning(f"Could not check robots.txt for {url}: {str(e)}")
            return True  # Allow by default if we can't check
    
    async def index_file(
        self,
        file_path: str,
        tenant_id: str,
        resource_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental",
        process_in_background: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Index a single file with smart processing decisions"""
        try:
            # Analyze file
            file_info = self._detect_file_type_and_encoding(file_path, 
                                                          content_type=metadata.get("content_type") if metadata else None,
                                                          file_metadata=metadata)
            file_size = os.path.getsize(file_path)
            
            # Decide on processing mode
            if process_in_background is None:
                process_in_background = self._should_process_in_background(
                    file_size, file_info.get("file_extension")
                )
            
            # Get appropriate loader
            loader = self._get_comprehensive_file_loader(file_path, file_info, 
                                                       content_type=metadata.get("content_type") if metadata else None,
                                                       file_metadata=metadata)
            
            if process_in_background:
                # For large files, process in chunks to avoid memory issues
                return await self._process_large_file(
                    loader, file_path, file_info, tenant_id, resource_id, metadata, cleanup_mode
                )
            else:
                # For small files, process immediately
                return await self._process_small_file(
                    loader, file_path, file_info, tenant_id, resource_id, metadata, cleanup_mode
                )
            
        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {str(e)}")
            raise
    
    async def _process_small_file(
        self,
        loader: BaseLoader,
        file_path: str,
        file_info: Dict,
        tenant_id: str,
        resource_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: str = "incremental"
    ) -> Dict[str, Any]:
        """Process small files immediately"""
        try:
            # Load documents
            documents = loader.load()
            
            # Convert UUIDs to strings in metadata before processing
            safe_metadata = convert_uuids_to_strings(metadata or {})
            
            # Add comprehensive metadata
            file_metadata = {
                "file_path": file_path,
                "filename": Path(file_path).name,
                "file_type": file_info.get("mime_type"),
                "file_extension": file_info.get("file_extension"),
                "file_size": os.path.getsize(file_path),
                "encoding": file_info.get("encoding"),
                "processing_mode": "immediate",
                "loader_used": loader.__class__.__name__,
                **safe_metadata
            }
            
            for doc in documents:
                # Convert any UUIDs in existing document metadata
                safe_doc_metadata = convert_uuids_to_strings(doc.metadata or {})
                doc.metadata = {**safe_doc_metadata, **file_metadata}
            
            # Index documents with resource info
            result = await self.index_documents(
                documents=documents,
                cleanup_mode=cleanup_mode
            )
            
            result["processing_mode"] = "immediate"
            result["file_info"] = file_info
            return result
            
        except Exception as e:
            logger.error(f"Error processing small file {file_path}: {str(e)}")
            raise
    
    async def _process_large_file(
        self,
        loader: BaseLoader,
        file_path: str,
        file_info: Dict,
        tenant_id: str,
        resource_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: str = "incremental"
    ) -> Dict[str, Any]:
        """Process large files with memory optimization"""
        try:
            # Use larger chunk size for large files
            large_text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=min(settings.max_chunk_size, settings.chunk_size * 2),
                chunk_overlap=settings.chunk_overlap,
                length_function=len,
            )
            
            # Load documents
            logger.info(f"Processing large file {file_path} in background mode")
            documents = loader.load()
            
            # Convert UUIDs to strings in metadata before processing
            safe_metadata = convert_uuids_to_strings(metadata or {})
            
            # Process in batches to avoid memory issues
            batch_size = 10  # Process 10 documents at a time
            total_chunks = 0
            
            file_metadata = {
                "file_path": file_path,
                "file_name": Path(file_path).name,
                "file_type": file_info.get("mime_type"),
                "file_extension": file_info.get("file_extension"),
                "file_size": os.path.getsize(file_path),
                "encoding": file_info.get("encoding"),
                "processing_mode": "background",
                "loader_used": loader.__class__.__name__,
                **safe_metadata
            }
            
            # Process documents in batches
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                # Add metadata to batch
                for doc in batch:
                    # Convert any UUIDs in existing document metadata
                    safe_doc_metadata = convert_uuids_to_strings(doc.metadata or {})
                    doc.metadata = {**safe_doc_metadata, **file_metadata}
                    doc.metadata["batch_index"] = i // batch_size
                
                # Split into chunks
                chunks = large_text_splitter.split_documents(batch)
                total_chunks += len(chunks)
                
                # Index this batch
                await self.index_documents(
                    documents=chunks,
                    tenant_id=tenant_id,
                    resource_id=resource_id,
                    resource_type="file",
                    metadata=safe_metadata,
                    cleanup_mode="none"  # Don't cleanup between batches
                )
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)
            
            return {
                "status": "success",
                "processing_mode": "background",
                "total_documents": len(documents),
                "total_chunks": total_chunks,
                "batches_processed": (len(documents) - 1) // batch_size + 1,
                "file_info": file_info
            }
            
        except Exception as e:
            logger.error(f"Error processing large file {file_path}: {str(e)}")
            raise
    
    async def index_upload_file(
        self,
        file: UploadFile,
        tenant_id: str,
        resource_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental"
    ) -> Dict[str, Any]:
        """Index an uploaded file with automatic processing mode selection"""
        temp_file_path = None
        try:
            # Read file content and determine size
            content = await file.read()
            file_size = len(content)
            
            # Check file size limits
            max_size_bytes = settings.max_file_size_mb * 1024 * 1024
            if file_size > max_size_bytes:
                raise ValueError(f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({settings.max_file_size_mb}MB)")
            
            # Create temporary file with proper extension
            file_extension = Path(file.filename or "temp").suffix or ".txt"
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file.write(content)
                temp_file.flush()
                temp_file_path = temp_file.name
                
                # Determine processing mode
                process_in_background = self._should_process_in_background(file_size, file_extension)
                
                # Enhanced metadata
                file_metadata = {
                    "original_filename": file.filename,
                    "content_type": file.content_type,
                    "file_size_mb": round(file_size / 1024 / 1024, 2),
                    "upload_source": True,
                    "processing_mode": "background" if process_in_background else "immediate",
                    **(metadata or {})
                }
                
                try:
                    # Index using temporary file with content type information
                    result = await self.index_file(
                        file_path=temp_file_path,
                        tenant_id=tenant_id,
                        resource_id=resource_id,
                        metadata=file_metadata,
                        cleanup_mode=cleanup_mode,
                        process_in_background=process_in_background
                    )
                    
                    result["upload_info"] = {
                        "original_filename": file.filename,
                        "content_type": file.content_type,
                        "file_size_mb": round(file_size / 1024 / 1024, 2),
                        "processing_mode": "background" if process_in_background else "immediate"
                    }
                    
                    return result
                    
                finally:
                    # Clean up temporary file
                    try:
                        if temp_file_path and os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)
                    except OSError as e:
                        logger.warning(f"Could not delete temporary file {temp_file_path}: {str(e)}")
                        
        except Exception as e:
            # Clean up on error
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass
            logger.error(f"Error indexing uploaded file {file.filename}: {str(e)}")
            raise
    
    async def index_text(
        self,
        text: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental"
    ) -> Dict[str, Any]:
        """Index plain text content (articles)"""
        try:
            # Create document from text
            document = Document(
                page_content=text,
                metadata=convert_uuids_to_strings(metadata or {})
            )
            
            # Index document
            return await self.index_documents(
                documents=[document],
                cleanup_mode=cleanup_mode
            )
            
        except Exception as e:
            logger.error(f"Error indexing text: {str(e)}")
            raise
    
    async def index_web_content(
        self,
        url: str,
        tenant_id: str,
        resource_id: str,
        load_type: Literal["single", "recursive", "sitemap"] = "single",
        max_depth: Optional[int] = 2,
        exclude_dirs: Optional[List[str]] = None,
        sitemap_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental"
    ) -> Dict[str, Any]:
        """Index web content using different loading strategies"""
        try:
            documents = await self._load_web_content(
                url=url,
                load_type=load_type,
                max_depth=max_depth,
                exclude_dirs=exclude_dirs,
                sitemap_url=sitemap_url
            )
            
            # Convert UUIDs to strings in metadata
            safe_metadata = convert_uuids_to_strings(metadata or {})
            
            # Add web metadata
            web_metadata = {
                "source_url": url,
                "load_type": load_type,
                "max_depth": max_depth,
                "pages_loaded": len(documents),
                **safe_metadata
            }
            
            for doc in documents:
                # Convert any UUIDs in existing document metadata
                safe_doc_metadata = convert_uuids_to_strings(doc.metadata or {})
                doc.metadata = {**safe_doc_metadata, **web_metadata}
            
            # Index documents
            return await self.index_documents(
                documents=documents,
                tenant_id=tenant_id,
                resource_id=resource_id,
                resource_type="web",
                metadata=safe_metadata,
                cleanup_mode=cleanup_mode
            )
            
        except Exception as e:
            logger.error(f"Error indexing web content from {url}: {str(e)}")
            raise
    
    async def _load_web_content(
        self,
        url: str,
        load_type: Literal["single", "recursive", "sitemap"] = "single",
        max_depth: Optional[int] = 2,
        exclude_dirs: Optional[List[str]] = None,
        sitemap_url: Optional[str] = None
    ) -> List[Document]:
        """Load web content using different strategies with rate limiting and robots.txt respect"""
        try:
            # Check robots.txt compliance
            if not self._check_robots_txt(url):
                raise ValueError(f"URL {url} is disallowed by robots.txt")
            
            documents = []
            
            if load_type == "single":
                # Enhanced single page loading with better error handling
                documents = await self._load_single_page(url)
                
            elif load_type == "recursive":
                # Enhanced recursive loading with rate limiting
                documents = await self._load_recursive_pages(
                    url=url,
                    max_depth=max_depth or 2,
                    exclude_dirs=exclude_dirs or []
                )
                
            elif load_type == "sitemap":
                # Enhanced sitemap loading
                if not sitemap_url:
                    # Try to find sitemap automatically
                    sitemap_url = await self._find_sitemap(url)
                    if not sitemap_url:
                        raise ValueError("No sitemap URL provided and could not auto-detect sitemap")
                
                documents = await self._load_from_sitemap(sitemap_url, url)
                
            else:
                raise ValueError(f"Invalid load_type: {load_type}")
            
            # Limit total pages processed
            if len(documents) > settings.web_crawl_max_pages:
                logger.warning(f"Limiting results to {settings.web_crawl_max_pages} pages")
                documents = documents[:settings.web_crawl_max_pages]
            
            return documents
            
        except Exception as e:
            logger.error(f"Error loading web content: {str(e)}")
            raise
    
    async def _load_single_page(self, url: str) -> List[Document]:
        """Load content from a single web page with enhanced error handling"""
        try:
            # Use WebBaseLoader for better content extraction
            loader = WebBaseLoader(
                web_paths=[url],
                requests_kwargs={
                    "timeout": settings.web_crawl_timeout,
                    "headers": {
                        "User-Agent": "Mozilla/5.0 (compatible; AssivyBot/1.0; +https://assivy.com/bot)"
                    },
                    "verify": False  # Disable SSL verification
                }
            )
            
            documents = loader.load()
            
            # Enhance metadata
            for doc in documents:
                # Convert any UUIDs in existing metadata to strings
                safe_metadata = convert_uuids_to_strings(doc.metadata or {})
                safe_metadata.update({
                    "load_type": "single",
                    "page_title": safe_metadata.get("title", ""),
                    "content_length": len(doc.page_content)
                })
                doc.metadata = safe_metadata
            
            return documents
            
        except Exception as e:
            logger.error(f"Error loading single page {url}: {str(e)}")
            raise
    
    async def _load_recursive_pages(
        self,
        url: str,
        max_depth: int,
        exclude_dirs: List[str]
    ) -> List[Document]:
        """Load pages recursively with rate limiting and smart filtering"""
        try:
            # Use RecursiveUrlLoader with enhanced configuration
            loader = RecursiveUrlLoader(
                url=url,
                max_depth=max_depth,
                exclude_dirs=exclude_dirs,
                use_async=True,
                prevent_outside=True,
                timeout=settings.web_crawl_timeout,
                check_response_status=True,
                requests_kwargs={
                    "headers": {
                        "User-Agent": "Mozilla/5.0 (compatible; AssivyBot/1.0; +https://assivy.com/bot)"
                    },
                    "verify": False  # Disable SSL verification
                }
            )
            
            # Load with progress tracking
            logger.info(f"Starting recursive crawl of {url} with max_depth={max_depth}")
            documents = await loader.aload()
            
            # Add rate limiting between requests
            if settings.web_crawl_delay > 0:
                await asyncio.sleep(settings.web_crawl_delay)
            
            # Enhance metadata for all documents
            for i, doc in enumerate(documents):
                # Convert any UUIDs in existing metadata to strings
                safe_metadata = convert_uuids_to_strings(doc.metadata or {})
                safe_metadata.update({
                    "load_type": "recursive",
                    "max_depth": max_depth,
                    "crawl_order": i,
                    "page_title": safe_metadata.get("title", ""),
                    "content_length": len(doc.page_content)
                })
                doc.metadata = safe_metadata
            
            logger.info(f"Recursive crawl completed. Found {len(documents)} pages")
            return documents
            
        except Exception as e:
            logger.error(f"Error in recursive crawl of {url}: {str(e)}")
            raise
    
    async def _load_from_sitemap(self, sitemap_url: str, filter_url: Optional[str] = None) -> List[Document]:
        """Load pages from sitemap using LangChain's SitemapLoader with enhanced parsing"""
        try:
            from bs4 import BeautifulSoup, SoupStrainer

            def simple_extractor(html: str | BeautifulSoup) -> str:
                if isinstance(html, str):
                    soup = BeautifulSoup(html, "lxml")
                elif isinstance(html, BeautifulSoup):
                    soup = html
                else:
                    raise ValueError(
                        "Input should be either BeautifulSoup object or an HTML string"
                    )
                return re.sub(r"\n\n+", "\n\n", soup.text).strip()

            def metadata_extractor(meta: dict, soup: BeautifulSoup, title_suffix: str = "") -> dict:
                """Extract enhanced metadata from HTML soup"""
                try:
                    enhanced_meta = meta.copy()

                    # Extract title with fallbacks
                    title = None
                    title_tag = soup.find('title')
                    if title_tag and title_tag.string:
                        title = title_tag.string.strip()

                    # Try Open Graph title
                    if not title:
                        og_title = soup.find('meta', property='og:title')
                        if og_title and og_title.get('content'):
                            title = og_title['content'].strip()

                    # Try h1 as fallback
                    if not title:
                        h1 = soup.find('h1')
                        if h1:
                            title = h1.get_text(strip=True)

                    if title:
                        # Remove title suffix if provided
                        if title_suffix and title.endswith(title_suffix):
                            title = title[:-len(title_suffix)].strip()
                        enhanced_meta['title'] = title

                    # Extract description
                    description = None
                    desc_meta = soup.find('meta', attrs={'name': 'description'})
                    if desc_meta and desc_meta.get('content'):
                        description = desc_meta['content'].strip()

                    # Try Open Graph description
                    if not description:
                        og_desc = soup.find('meta', property='og:description')
                        if og_desc and og_desc.get('content'):
                            description = og_desc['content'].strip()

                    if description:
                        enhanced_meta['description'] = description

                    # Extract keywords
                    keywords_meta = soup.find('meta', attrs={'name': 'keywords'})
                    if keywords_meta and keywords_meta.get('content'):
                        enhanced_meta['keywords'] = keywords_meta['content'].strip()

                    # Extract author
                    author_meta = soup.find('meta', attrs={'name': 'author'})
                    if author_meta and author_meta.get('content'):
                        enhanced_meta['author'] = author_meta['content'].strip()

                    # Extract publication date
                    date_selectors = [
                        'meta[property="article:published_time"]',
                        'meta[name="date"]',
                        'meta[name="publish-date"]',
                        'time[datetime]'
                    ]

                    for selector in date_selectors:
                        date_elem = soup.select_one(selector)
                        if date_elem:
                            date_value = date_elem.get('content') or date_elem.get('datetime')
                            if date_value:
                                enhanced_meta['published_date'] = date_value.strip()
                                break

                    return enhanced_meta

                except Exception as e:
                    logger.warning(f"Error in metadata_extractor: {str(e)}")
                    return meta

            # Use LangChain's SitemapLoader with custom parsing functions
            logger.info(f"Loading sitemap using LangChain SitemapLoader: {sitemap_url}")

            loader = SitemapLoader(
                sitemap_url,
                parsing_function=simple_extractor,
                default_parser="lxml",
                bs_kwargs={"parse_only": SoupStrainer(name=("article", "main", "div", "section", "title", "meta"))},
                meta_function=lambda meta, soup: metadata_extractor(meta, soup),
                continue_on_failure=True,  # Continue loading even if some URLs fail due to SSL issues
                requests_kwargs={
                    "timeout": settings.web_crawl_timeout,
                    "headers": {
                        "User-Agent": "Mozilla/5.0 (compatible; AssivyBot/1.0; +https://assivy.com/bot)"
                    },
                    "verify": False  # Disable SSL verification
                }
            )

            # Load documents in thread pool to avoid event loop conflicts
            # SitemapLoader.load() is synchronous but internally uses async operations
            import concurrent.futures

            def load_sitemap():
                # Create SSL context that doesn't verify certificates
                ssl_context = self._create_ssl_context()

                # Monkey patch aiohttp to use our SSL context
                import aiohttp
                original_connector = aiohttp.TCPConnector

                def patched_connector(*args, **kwargs):
                    kwargs['ssl'] = ssl_context
                    return original_connector(*args, **kwargs)

                # Temporarily replace the connector
                aiohttp.TCPConnector = patched_connector

                try:
                    return loader.load()
                finally:
                    # Restore original connector
                    aiohttp.TCPConnector = original_connector

            # Run in thread pool to avoid "asyncio.run() cannot be called from a running event loop" error
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                documents = await loop.run_in_executor(executor, load_sitemap)

            if not documents:
                logger.warning(f"No documents loaded from sitemap: {sitemap_url}")
                return []

            logger.info(f"LangChain SitemapLoader found {len(documents)} documents from sitemap: {sitemap_url}")

            # Filter URLs if filter_url is provided
            if filter_url:
                filtered_docs = [doc for doc in documents if filter_url in doc.metadata.get('source', '')]
                logger.info(f"Filtered to {len(filtered_docs)} documents matching filter: {filter_url}")
                documents = filtered_docs

            # Enhance metadata for all documents
            enhanced_documents = []
            for i, doc in enumerate(documents):
                try:
                    # Convert any UUIDs in existing metadata to strings
                    safe_metadata = convert_uuids_to_strings(doc.metadata or {})

                    # Add sitemap-specific metadata
                    sitemap_metadata = {
                        "load_type": "sitemap",
                        "sitemap_url": sitemap_url,
                        "is_root": False,  # LangChain loader doesn't distinguish root pages
                        "sitemap_depth": 0,  # LangChain loader doesn't provide depth info
                        "discovered_at": datetime.now(timezone.utc).isoformat(),
                        "page_title": safe_metadata.get("title", ""),
                        "content_length": len(doc.page_content),
                        "crawl_order": i
                    }

                    # Add rate limiting between requests if configured
                    if settings.web_crawl_delay > 0 and i > 0:
                        await asyncio.sleep(settings.web_crawl_delay)

                    safe_metadata.update(sitemap_metadata)
                    doc.metadata = safe_metadata
                    enhanced_documents.append(doc)

                except Exception as e:
                    logger.error(f"Error enhancing document metadata: {str(e)}")
                    enhanced_documents.append(doc)  # Add document even if metadata enhancement fails

            logger.info(f"Sitemap crawl completed using LangChain. Successfully loaded {len(enhanced_documents)} pages")
            return enhanced_documents

        except Exception as e:
            logger.error(f"Error loading from sitemap {sitemap_url} using LangChain: {str(e)}")
            raise



    async def _find_sitemap(self, url: str) -> Optional[str]:
        """Try to automatically find sitemap for a domain"""
        try:
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Common sitemap locations
            sitemap_paths = [
                "/sitemap.xml",
                "/sitemap_index.xml",
                "/sitemaps.xml",
                "/sitemap.txt"
            ]
            
            for path in sitemap_paths:
                sitemap_url = urljoin(base_url, path)
                try:
                    response = requests.head(
                        sitemap_url,
                        timeout=settings.web_crawl_timeout,
                        headers={
                            "User-Agent": "Mozilla/5.0 (compatible; AssivyBot/1.0; +https://assivy.com/bot)"
                        },
                        verify=False  # Disable SSL verification
                    )
                    if response.status_code == 200:
                        logger.info(f"Found sitemap at: {sitemap_url}")
                        return sitemap_url
                except:
                    continue
            
            logger.warning(f"No sitemap found for {url}")
            return None
            
        except Exception as e:
            logger.error(f"Error finding sitemap for {url}: {str(e)}")
            return None
    
    async def index_documents(
        self,
        documents: List[Document],
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental"
    ) -> Dict[str, Any]:
        """Index documents with tenant context and save chunks to database"""
        try:
            vector_store = self._get_vector_store()
            
            # Add tenant context to metadata for vector store
            all_chunks = []
            for doc in documents:
                # Split documents into chunks
                langchain_chunks = self.text_splitter.split_documents([doc])
                
                # Add tenant context and chunk metadata to each chunk
                for idx, chunk in enumerate(langchain_chunks):
                    if not hasattr(chunk, "metadata") or chunk.metadata is None:
                        chunk.metadata = {}
                    
                    # Add tenant context
                    chunk.metadata["tenant_id"] = str(self.tenant_id)
                    chunk.metadata["chunk_index"] = idx
                
                all_chunks.extend(langchain_chunks)
            
            # Use LangChain's indexing API for vector store
            result = index(
                all_chunks,
                self.record_manager,
                vector_store,
                cleanup=cleanup_mode,
                source_id_key="resource_id"
            )
            
            return {
                "status": "success",
                "collection": self.collection_name,
                "chunks_added": result.get("num_added", 0),
                "chunks_updated": result.get("num_updated", 0),
                "chunks_skipped": result.get("num_skipped", 0),
                "chunks_deleted": result.get("num_deleted", 0),
            }
            
        except Exception as e:
            logger.error(f"Error indexing documents: {str(e)}")
            raise
    
    async def search_documents(
        self,
        query: str,
        tenant_id: Optional[str] = None,
        k: int = 4,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """Search documents with tenant context"""
        try:
            vector_store = self._get_vector_store()
            
            # Use instance tenant_id if not provided
            tenant_id = tenant_id or self.tenant_id
            
            # Add tenant filter
            search_filters = {
                "tenant_id": {"equal": tenant_id},
                **(filters or {})
            }
            
            # Convert filters to Weaviate format if needed
            weaviate_filters = self._convert_filters_to_weaviate(search_filters)
            
            return await vector_store.asimilarity_search(
                query,
                k=k,
                where_filter=weaviate_filters
            )
            
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            raise
    
    def _convert_filters_to_weaviate(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Convert generic filters to Weaviate-specific format"""
        weaviate_filters = {}
        
        for key, value in filters.items():
            if isinstance(value, dict):
                # Handle nested filter operations
                weaviate_filters[key] = value
            else:
                # Simple equality filter
                weaviate_filters[key] = {"equal": value}
        
        return weaviate_filters
    
    async def delete_documents(
        self,
        tenant_id: Optional[str] = None,
        doc_ids: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Delete documents with tenant context"""
        try:
            vector_store = self._get_vector_store()
            
            # Use instance tenant_id if not provided
            tenant_id = tenant_id or self.tenant_id
            
            # Add tenant filter
            delete_filters = {
                "tenant_id": {"equal": tenant_id},
                **(filters or {})
            }
            
            if doc_ids:
                delete_filters["source"] = {"valueText": doc_ids}
            
            # Convert filters to Weaviate format
            weaviate_filters = self._convert_filters_to_weaviate(delete_filters)
            
            # Delete from vector store using Weaviate client directly
            collection = self.weaviate_client.collections.get(self._weaviate_index_name)
            
            if doc_ids:
                # Delete specific documents
                for doc_id in doc_ids:
                    try:
                        collection.data.delete_by_id(doc_id)
                    except Exception as e:
                        logger.warning(f"Could not delete document {doc_id}: {str(e)}")
            else:
                # Delete by filters
                collection.data.delete_many(where=weaviate_filters)
            
            # Delete from record manager
            if doc_ids:
                await self.record_manager.delete_keys(doc_ids)
            
            return {
                "status": "success",
                "tenant_id": tenant_id,
                "collection": self.collection_name,
                "message": "Documents deleted successfully"
            }
            
        except Exception as e:
            logger.error(f"Error deleting documents: {str(e)}")
            raise
    
    async def get_collection_stats(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics for the collection"""
        try:
            # Use instance tenant_id if not provided
            tenant_id = tenant_id or self.tenant_id
            
            collection = self.weaviate_client.collections.get(self._weaviate_index_name)
            
            # Get total count for tenant
            result = collection.aggregate.over_all(
                where={"tenant_id": {"equal": tenant_id}}
            )
            
            return {
                "collection_name": self.collection_name,
                "tenant_id": tenant_id,
                "total_documents": result.total_count or 0,
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            return {
                "collection_name": self.collection_name,
                "tenant_id": tenant_id or self.tenant_id,
                "total_documents": 0,
                "error": str(e)
            }
    
    def close(self):
        """Explicitly close all connections"""
        try:
            # Close Weaviate client
            if hasattr(self, 'weaviate_client') and self.weaviate_client:
                self.weaviate_client.close()
                logger.debug(f"Closed Weaviate client for tenant: {self.tenant_id}, collection: {self.collection_name}")
        except Exception as e:
            logger.warning(f"Error closing Weaviate client: {e}")

    def __del__(self):
        """Clean up connections when object is destroyed"""
        self.close()

    async def index_storage_file(
        self,
        storage_key: str,
        tenant_id: str,
        resource_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        cleanup_mode: Literal["none", "incremental", "full"] = "incremental",
        process_in_background: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        Index a file from storage using storage key.
        
        This method fetches the file and its metadata from storage automatically,
        making it more self-contained than index_file which requires a file path.
        
        Args:
            storage_key: Key identifying the file in storage
            tenant_id: Tenant identifier for multi-tenancy
            metadata: Additional metadata to include
            cleanup_mode: How to handle existing documents
            process_in_background: Force background processing (auto-detected if None)
            
        Returns:
            Dict with indexing results
        """
        try:
            # Get basic file info from storage for processing decisions
            try:
                file_metadata = await storage_manager.get_file_metadata(storage_key)
                file_size = file_metadata.size or 0
                filename = file_metadata.filename
                content_type = file_metadata.content_type
                logger.debug(f"Retrieved basic file info for {storage_key}")
            except Exception as e:
                logger.warning(f"Could not get file metadata from storage for {storage_key}: {str(e)}")
                file_size = 0
                filename = None
                content_type = None
            
            # Simple metadata following text resource pattern
            simple_metadata = {
                "resource_id": resource_id,
                "resource_type": "file",
                "filename": filename,
                "file_type": content_type,
                **(metadata or {})
            }
            
            # Decide on processing mode based on file size
            if process_in_background is None:
                # Extract extension from filename for decision making
                file_extension = Path(filename).suffix.lower() if filename else ""
                process_in_background = self._should_process_in_background(file_size, file_extension)
            
            # Use storage manager's temporary file context manager
            async with storage_manager.temporary_file(storage_key) as temp_file_path:
                # Get file info using basic metadata
                file_info = self._detect_file_type_and_encoding(
                    temp_file_path,
                    content_type=content_type,
                    file_metadata={"filename": filename, "content_type": content_type}
                )
                
                # Get appropriate loader
                loader = self._get_comprehensive_file_loader(
                    temp_file_path, 
                    file_info,
                    content_type=content_type,
                    file_metadata={"filename": filename, "content_type": content_type}
                )
                
                if process_in_background:
                    # For large files, process in chunks to avoid memory issues
                    result = await self._process_large_file(
                        loader, temp_file_path, file_info, tenant_id, resource_id, simple_metadata, cleanup_mode
                    )
                else:
                    # For small files, process immediately
                    result = await self._process_small_file(
                        loader, temp_file_path, file_info, tenant_id, resource_id, simple_metadata, cleanup_mode
                    )
                
                # Add minimal result info
                result["storage_key"] = storage_key
                result["filename"] = filename
                result["processing_mode"] = "background" if process_in_background else "immediate"
                
                return result
                
        except Exception as e:
            logger.error(f"Error indexing storage file {storage_key}: {str(e)}")
            raise

    def _extract_chunk_metadata(self, langchain_metadata: Dict[str, Any], resource_type: str, base_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant metadata for chunk based on resource type following the model guidelines"""
        metadata = {}
        
        if resource_type == "file":
            # FileResource metadata (stored in chunk.metadata) - simplified
            metadata["filename"] = base_metadata.get("filename")
            metadata["file_type"] = base_metadata.get("file_type")
            # Add page_number if available (for PDF, Word documents)
            if langchain_metadata.get("page_number"):
                metadata["page_number"] = langchain_metadata.get("page_number")
            # Add source info if available
            if langchain_metadata.get("source"):
                metadata["source"] = langchain_metadata.get("source")
                
        elif resource_type == "article":
            # ArticleResource metadata (stored in chunk.metadata)
            if base_metadata.get("title"):
                metadata["title"] = base_metadata.get("title")
            if base_metadata.get("author"):
                metadata["author"] = base_metadata.get("author")
            if base_metadata.get("tags"):
                metadata["tags"] = base_metadata.get("tags")
                
        elif resource_type == "web":
            # WebResource metadata (stored in chunk.metadata)
            metadata["url"] = base_metadata.get("url") or base_metadata.get("source_url")
            if base_metadata.get("title") or base_metadata.get("page_title"):
                metadata["title"] = base_metadata.get("title") or base_metadata.get("page_title")
            if base_metadata.get("crawl_depth"):
                metadata["crawl_depth"] = base_metadata.get("crawl_depth")
        
        # Remove None values to keep metadata clean
        return {k: v for k, v in metadata.items() if v is not None}

# Users should create instances with specific tenant_id and collection_name where needed