from typing import List, Optional, Set
from uuid import UUID
from fastapi import HTT<PERSON>Ex<PERSON>, status
from models.role import Role, Permission
from models.user import User
from data_access.factory import RepositoryFactory

class RoleService:
    """Service for managing roles and permissions"""
    
    def __init__(self):
        self.role_repo = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the role repository"""
        if not self._initialized:
            self.role_repo = await RepositoryFactory.create_role_repository()
            self._initialized = True
    
    async def _ensure_initialized(self):
        """Ensure the service is initialized"""
        if not self._initialized:
            await self.initialize()
    
    async def create_role(
        self,
        name: str,
        description: str,
        permissions: Set[Permission],
        tenant_id: Optional[UUID] = None
    ) -> Role:
        """Create a new role - predefined (tenant_id=None) or tenant-specific (tenant_id=UUID)"""
        await self._ensure_initialized()
        
        # Validate system permissions - only roles with system permissions should be predefined
        system_permissions = {perm for perm in permissions if perm.value.startswith("system:")}
        if system_permissions and tenant_id is not None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Roles with system permissions must be predefined (tenant_id=None)"
            )
        
        role = Role(
            name=name,
            description=description,
            permissions=permissions,
            tenant_id=tenant_id
        )
        
        return await self.role_repo.create(role, tenant_id_for_creation=tenant_id)
    
    async def get_roles_with_system_permissions(self) -> List[Role]:
        """Get all roles that have system-level permissions"""
        await self._ensure_initialized()
        all_roles = await self.role_repo.get_all()
        return [role for role in all_roles if any(perm.value.startswith("system:") for perm in role.permissions)]
    
    async def list_tenant_roles(self, tenant_id: UUID) -> List[Role]:
        """Get roles available for a specific tenant (alias for get_tenant_roles)"""
        return await self.get_tenant_roles(tenant_id)
    
    async def list_system_roles(self) -> List[Role]:
        """Get roles with system permissions (alias for get_roles_with_system_permissions)"""
        return await self.get_roles_with_system_permissions()
    
    async def update_role(
        self,
        role_id: UUID,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[Set[Permission]] = None
    ) -> Role:
        """Update a role"""
        role = await self.get_role(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        update_data = {}
        if name is not None:
            update_data["name"] = name
        if description is not None:
            update_data["description"] = description
        if permissions is not None:
            # Validate system permissions
            system_permissions = {perm for perm in permissions if perm.value.startswith("system:")}
            if system_permissions and role.is_tenant_specific():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot add system permissions to tenant-specific role"
                )
            update_data["permissions"] = permissions
        
        updated_role = await self.role_repo.update(role_id, update_data)
        return updated_role
    
    async def delete_role(self, role_id: UUID) -> bool:
        """Delete a role"""
        role = await self.get_role(role_id)
        if not role:
            return False
        
        if role.is_predefined():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete predefined roles"
            )
        
        # Check if any users are using this role
        user_repo = RepositoryFactory.create_user_repository()
        users_with_role = await user_repo.get_all(
            query_conditions={"role_id": role_id}
        )
        
        if users_with_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete role that is assigned to users"
            )
        
        await self.role_repo.delete(role_id)
        return True
    
    async def initialize_predefined_roles(self) -> List[Role]:
        """Initialize all predefined roles (should be called once during setup)"""
        await self._ensure_initialized()
        predefined_roles = Role.get_predefined_roles()
        created_roles = []
        
        for role in predefined_roles:
            # Check if role already exists by getting all roles and filtering by name
            all_roles = await self.role_repo.get_all()
            existing_roles = [r for r in all_roles if r.name == role.name and r.tenant_id is None]
            
            if not existing_roles:
                created_role = await self.role_repo.create(role, tenant_id_for_creation=None)
                created_roles.append(created_role)
        
        return created_roles
    
    async def get_predefined_roles(self) -> List[Role]:
        """Get all predefined roles (from Role model, not DB)"""
        await self._ensure_initialized()
        return Role.get_predefined_roles()
    
    async def get_tenant_roles(self, tenant_id: UUID) -> List[Role]:
        """Get roles available for a tenant (predefined + tenant-specific)"""
        await self._ensure_initialized()
        # Get predefined roles
        predefined_roles = await self.get_predefined_roles()
        
        # Get tenant-specific roles
        tenant_specific_roles = await self.role_repo.get_all(
            query_conditions={"tenant_id": tenant_id}
        )
        
        return predefined_roles + tenant_specific_roles
    
    async def get_user_permissions(self, user: User) -> Set[Permission]:
        """Get all permissions for a user"""
        role = await self.get_role(user.role_id)
        return role.permissions if role else set()
    
    async def user_has_permission(self, user: User, permission: Permission) -> bool:
        """Check if a user has a specific permission"""
        user_permissions = await self.get_user_permissions(user)
        return permission in user_permissions
    
    async def user_has_any_permission(self, user: User, permissions: List[Permission]) -> bool:
        """Check if a user has any of the specified permissions"""
        user_permissions = await self.get_user_permissions(user)
        return bool(set(permissions).intersection(user_permissions))
    
    async def assign_role_to_user(self, user_id: UUID, role_id: UUID, tenant_id: Optional[UUID]) -> bool:
        """Assign a role to a user"""
        # Validate role exists and belongs to the same tenant (or is a system role)
        role = await self.get_role(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        if role.tenant_id and role.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot assign role from different tenant"
            )
        
        # Update user's role
        user_repo = RepositoryFactory.create_user_repository()
        await user_repo.update(user_id, {"role_id": role_id})
        return True
    
    async def delete_role(self, tenant_id: str, role_id: str) -> None:
        """Delete a role"""
        role = await self.role_repo.get_by_id(role_id)
        if not role or role.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Cannot delete predefined roles
        if role.is_predefined():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete predefined roles"
            )
        
        await self.role_repo.delete(role_id)
    
    async def list_roles(self, tenant_id: str) -> List[Role]:
        """List all roles available to the tenant"""
        return await self.get_tenant_roles(UUID(tenant_id))
    
    async def get_role(self, role_id: UUID) -> Optional[Role]:
        """Get a role by ID"""
        role = await self.role_repo.get_by_id(role_id)
        if not role:
            return None
        return role

    async def get_role_with_default(self, role_id: UUID, default_role_name: str = "Member") -> Optional[Role]:
        """Get a role by ID, with fallback to a default role if not found"""
        await self._ensure_initialized()
        
        # Try to get the role by ID
        role = await self.get_role(role_id)
        if role:
            return role
        
        # If role not found, get the default role from predefined roles in the model
        predefined_roles = Role.get_predefined_roles()
        default_role = next(
            (role for role in predefined_roles if role.name == default_role_name),
            None
        )
        
        return default_role


# Create a singleton instance
role_service = RoleService()