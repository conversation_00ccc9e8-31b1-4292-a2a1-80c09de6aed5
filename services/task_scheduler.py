from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from datetime import datetime
from models.task import Task, TaskType, TaskStatus
import logging
from uuid import UUID
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)

class TaskScheduler:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()

    async def start(self):
        """Start the scheduler and restore existing tasks"""
        if not self.scheduler.running:
            self.scheduler.start()
            await self._restore_tasks()
            
    async def stop(self):
        """Stop the scheduler"""
        if self.scheduler.running:
            self.scheduler.shutdown()
            
    async def _restore_tasks(self):
        """Restore existing scheduled tasks from database"""
        tasks = await task_service.list_tasks(
            user=None,
            skip=0,
            limit=1000,
            status=TaskStatus.PENDING
        )
        for task in tasks:
            if task.schedule:
                await self.schedule_task(task.id, task.schedule, restore=True)
    
    async def schedule_task(self, task_id: UUID, cron_expression: str, restore: bool = False):
        """Schedule a task with a cron expression"""
        task = await task_service.get_task(task_id, None)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )
            
        job_id = str(task.id)
        
        try:
            # Remove existing job if it exists
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # Schedule new job
            self.scheduler.add_job(
                self._execute_task,
                CronTrigger.from_crontab(cron_expression),
                id=job_id,
                args=[task_id],
                replace_existing=True
            )
            
            if not restore:
                # Update task schedule info
                next_run = self.scheduler.get_job(job_id).next_run_time
                await task_service.update_task(
                    task_id,
                    {"schedule": cron_expression, "next_run": next_run},
                    None
                )
            
            return True
        except Exception as e:
            logger.error(f"Error scheduling task {task_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to schedule task: {str(e)}"
            )
    
    async def _execute_task(self, task_id: UUID):
        """Execute a scheduled task"""
        try:
            task = await task_service.get_task(task_id, None)
            if not task:
                logger.error(f"Task {task_id} not found")
                return
            
            # Update task status to running
            await task_service.update_task_status(
                task_id,
                TaskStatus.RUNNING,
                None,
                last_run=datetime.utcnow()
            )
            
            if task.type == TaskType.RESOURCE_INDEXING:
                await self._handle_resource_indexing(task)
            # Add other task type handlers here
            
            # Update next run time and status
            next_run = self.scheduler.get_job(str(task_id)).next_run_time
            await task_service.update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                None,
                error_message=None,
                next_run=next_run
            )
            
        except Exception as e:
            logger.error(f"Error executing task {task_id}: {str(e)}")
            try:
                await task_service.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    None,
                    error_message=str(e)
                )
            except Exception as update_error:
                logger.error(f"Failed to update task status: {str(update_error)}")
    
    async def _handle_resource_indexing(self, task: Task):
        """Handle resource indexing task"""
        if not task.resource_id:
            raise ValueError("Resource ID not found in task")
            
        resource = await resource_service.get_resource(task.resource_type, task.resource_id)
        if not resource:
            raise ValueError(f"Resource {task.resource_id} not found")
            
        metadata = {
            "resource_id": str(resource.id),
            "tenant_id": resource.tenant_id,
            "title": resource.title,
            "type": resource.type
        }
        
        if hasattr(resource, "file_path"):
            await resource_service.process_file(
                file_path=resource.file_path,
                resource_id=resource.id,
                tenant_id=resource.tenant_id,
                metadata=metadata
            )
        elif hasattr(resource, "content"):
            await resource_service.process_text(
                text=resource.content,
                resource_id=resource.id,
                tenant_id=resource.tenant_id,
                metadata=metadata
            )
        else:
            raise ValueError("Unsupported resource type")
    
    async def cancel_task(self, task_id: UUID):
        """Cancel a scheduled task"""
        try:
            task = await task_service.get_task(task_id, None)
            if not task:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Task {task_id} not found"
                )
                
            if self.scheduler.get_job(str(task_id)):
                self.scheduler.remove_job(str(task_id))
            
            await task_service.update_task(
                task_id,
                {"schedule": None, "next_run": None},
                None
            )
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error canceling task {task_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to cancel task: {str(e)}"
            )