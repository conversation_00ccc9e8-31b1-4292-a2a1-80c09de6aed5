"""
Simple embeddings implementation that doesn't require external APIs or heavy dependencies
"""
import hashlib
import numpy as np
from typing import List, Dict, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import logging

logger = logging.getLogger(__name__)

class SimpleEmbeddings:
    """
    A simple embedding class that uses TF-IDF for text vectorization
    This provides a free alternative to OpenAI embeddings
    """
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=384,  # Standard embedding dimension
            stop_words='english',
            lowercase=True,
            ngram_range=(1, 2)  # Include bigrams for better context
        )
        self.is_fitted = False
        
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of documents"""
        try:
            if not self.is_fitted:
                # Fit the vectorizer on the input texts
                vectors = self.vectorizer.fit_transform(texts)
                self.is_fitted = True
            else:
                vectors = self.vectorizer.transform(texts)
            
            # Convert to dense format and return as list of lists
            dense_vectors = vectors.toarray()
            return [vector.tolist() for vector in dense_vectors]
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            # Fallback: return zero vectors
            return [[0.0] * 384 for _ in texts]
    
    def embed_query(self, text: str) -> List[float]:
        """Generate embedding for a single query"""
        if not self.is_fitted:
            # If not fitted, return a simple hash-based vector
            return self._hash_embedding(text)
        
        try:
            vector = self.vectorizer.transform([text])
            return vector.toarray()[0].tolist()
        except Exception as e:
            logger.error(f"Error generating query embedding: {e}")
            return self._hash_embedding(text)
    
    def _hash_embedding(self, text: str) -> List[float]:
        """Create a simple hash-based embedding as fallback"""
        # Create a deterministic hash-based vector
        hash_obj = hashlib.md5(text.encode())
        hash_bytes = hash_obj.digest()
        
        # Convert to float values between -1 and 1
        vector = []
        for i in range(0, len(hash_bytes), 4):
            chunk = hash_bytes[i:i+4]
            if len(chunk) == 4:
                # Convert 4 bytes to int, then normalize
                val = int.from_bytes(chunk, byteorder='big')
                normalized = (val / (2**32 - 1)) * 2 - 1
                vector.append(normalized)
        
        # Pad or trim to 384 dimensions
        while len(vector) < 384:
            vector.extend(vector[:min(384 - len(vector), len(vector))])
        
        return vector[:384]

# Simple compatibility wrapper that mimics langchain embeddings interface
class LangChainCompatibleEmbeddings:
    """
    Wrapper to make SimpleEmbeddings compatible with LangChain's embedding interface
    Handles both sync and async calls gracefully.
    """
    
    def __init__(self):
        self.embeddings = SimpleEmbeddings()
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """Async version of embed_documents"""
        # Run in a thread to avoid blocking
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.embeddings.embed_documents, texts)
    
    async def aembed_query(self, text: str) -> List[float]:
        """Async version of embed_query"""
        # Run in a thread to avoid blocking
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.embeddings.embed_query, text)
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Synchronous version of embed_documents"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # In async context, run async version
                return loop.run_until_complete(self.aembed_documents(texts))
        except RuntimeError:
            pass
        return self.embeddings.embed_documents(texts)
    
    def embed_query(self, text: str) -> List[float]:
        """Synchronous version of embed_query"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                return loop.run_until_complete(self.aembed_query(text))
        except RuntimeError:
            pass
        return self.embeddings.embed_query(text)

    # Add async aliases for compatibility with vector store expectations
    async def embed_documents_async(self, texts: List[str]) -> List[List[float]]:
        return await self.aembed_documents(texts)

    async def embed_query_async(self, text: str) -> List[float]:
        return await self.aembed_query(text)

    # Some vector stores may call embed_documents or embed_query as async
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return await self.aembed_documents(texts)

    async def embed_query(self, text: str) -> List[float]:
        return await self.aembed_query(text)
