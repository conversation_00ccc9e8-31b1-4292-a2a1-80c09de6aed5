from typing import List, Optional
from uuid import UUID, uuid4
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from models.user import User
from models.role import Role, Permission
from data_access.factory import RepositoryFactory
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self, user_repo=None, role_repo=None):
        self.user_repo = user_repo
        self.role_repo = role_repo
    
    async def initialize(self):
        """Initialize the repositories for UserService"""
        if not self.user_repo:
            self.user_repo = await RepositoryFactory.create_user_repository()
        if not self.role_repo:
            self.role_repo = await RepositoryFactory.create_role_repository()

    async def create_user(
        self, 
        email: str, 
        password: str, 
        tenant_id: UUID,
        role_id: UUID,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None
    ) -> User:    
        # Check if user exists
        existing_users = await self.user_repo.get_all(
            query_conditions={"email": email}
        )
        if existing_users:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"User with email {email} already exists"
            )
        
        # Create user with required role
        user = User(
            email=email,
            hashed_password=password,  # Note: Password should be hashed before reaching this point
            first_name=first_name,
            last_name=last_name,
            role_id=role_id,
            tenant_id=tenant_id
        )
        
        return await self.user_repo.create(user, tenant_id_for_creation=tenant_id)
        
    async def register_first_user(
        self,
        email: str,
        password: str,
        tenant_id: UUID,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None
    ) -> User:
        """Register the first user in a tenant with admin role"""
        try:
            # Validate inputs
            if not email or not password or not tenant_id:
                raise ValueError("Email, password and tenant_id are required")

            # Check if any users exist in the tenant
            try:
                existing_users = await self.user_repo.get_all(
                    query_conditions={"tenant_id": tenant_id}
                )
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Database error while checking existing users: {str(e)}"
                )

            if existing_users:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot register first user - users already exist in this tenant"
                )
            
            # Get predefined roles and find Admin role
            try:
                predefined_roles = Role.get_predefined_roles()
                admin_role = next((role for role in predefined_roles if role.name == "Admin"), None)
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error getting predefined roles: {str(e)}"
                )

            if not admin_role:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Admin role not found in predefined roles"
                )
            
            # Create user with admin role
            try:
                return await self.create_user(
                    email=email,
                    password=password,
                    tenant_id=tenant_id,
                    role_id=admin_role.id,
                    first_name=first_name,
                    last_name=last_name
                )
            except HTTPException as he:
                # Re-raise HTTP exceptions from create_user
                raise he
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error creating user: {str(e)}"
                )

        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(ve)
            )
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            # Catch any other unexpected errors
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error during user registration: {str(e)}"
            )

    async def get_user(self, user_id: str, requester: User) -> User:
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Ensure requester can only access users in their own tenant unless they have system access
        # Check if requester has system-level permissions
        requester_role = await self.role_repo.get_by_id(requester.role_id)
        has_system_permission = any(perm.value.startswith("system:") for perm in requester_role.permissions) if requester_role else False
        if requester.tenant_id != user.tenant_id and not has_system_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to user from different tenant"
            )
        
        return user
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email - used for authentication"""
        users = await self.user_repo.get_all(
            query_conditions={"email": email}
        )
        return users[0] if users else None
    
    async def update_user(self, user_data: User) -> User:
        """
        Update user information using a user object
        
        Args:
            user_id: The ID of the user to update
            user_data: User object containing the updated fields
            
        Returns:
            Updated User object
            
        Raises:
            HTTPException: If user not found or update fails
        """
        # Get existing user
        existing_user = await self.user_repo.get_by_id(user_data.id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update only allowed fields
        updateable_fields = [
            'first_name',
            'last_name',
            'is_active',
            'email',
            'phone_number',
            'profile_image'
        ]
        
        for field in updateable_fields:
            new_value = getattr(user_data, field, None)
            if new_value is not None:
                setattr(existing_user, field, new_value)
        
        # Save updates
        try:
            return await self.user_repo.update(existing_user.id, existing_user)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update user: {str(e)}"
            )

    async def update_user_role(self, user_id: str, role_name: str) -> User:
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get role using get_all with name filter for user's tenant
        roles = await self.role_repo.get_all(
            query_conditions={
                "tenant_id": user.tenant_id,  # Use user's existing tenant
                "name": role_name
            }
        )
        if not roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role {role_name} not found in user's tenant"
            )
        role = roles[0]
        
        user.role = role
        return await self.user_repo.update(user_id, user)

    async def update_password(self, user_id: str, current_password: str, 
                            new_password: str, updater: User) -> None:
        """Update user's password"""
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Note: Password verification should happen here
        # if not verify_password(current_password, user.hashed_password):
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail="Current password is incorrect"
        #     )
        
        user.hashed_password = new_password  # Note: Password should be hashed before reaching this point
        await self.user_repo.update(user_id, user, updater)
    
    async def list_users(self, tenant_id: str) -> List[User]:        
        return await self.user_repo.get_all(
            query_conditions={"tenant_id": tenant_id}
        )
    
    async def delete_user(self, user_id: str) -> None:
        user = await self.user_repo.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user has system admin role (cannot delete system admins)
        role_repo = RepositoryFactory.create_role_repository()
        role = await role_repo.get_by_id(user.role_id)
        
        if role and any(perm.value.startswith("system:") for perm in role.permissions) and Permission.SYSTEM_ADMIN in role.permissions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system administrator accounts"
            )
        
        await self.user_repo.delete(user_id)

    async def get_or_create_oauth_user(self, email: str, oauth_data: dict) -> User:
        """Get or create a user from OAuth data"""
        # Try to get existing user
        existing_user = await self.get_user_by_email(email)
        if existing_user:
            return existing_user
        
        # Get default role for new users
        roles = await self.role_repo.get_all(
            query_conditions={"name": "user"}
        )
        if not roles:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Default user role not found"
            )
        
        # Create new user with OAuth profile
        user = User(
            email=email,
            first_name=oauth_data.get("profile", {}).get("given_name"),
            last_name=oauth_data.get("profile", {}).get("family_name"),
            role_id=roles[0].id,
            oauth_provider=oauth_data["provider"],
            oauth_id=oauth_data.get("profile", {}).get("sub"),
            is_active=True
        )
        
        return await self.user_repo.create(user)

    def serialize_user(self, user: User) -> dict:
        """Serialize a user object for API responses"""
        if not user:
            return None
        
        return {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": user.phone,
            "job_title": user.job_title,
            "location": user.location,
            "bio": user.bio,
            "timezone": user.timezone,
            "profile_image_url": user.profile_image_url,
            "is_active": user.is_active,
            "is_email_verified": user.is_email_verified,
            "role_id": str(user.role_id) if user.role_id else None,
            "role_name": user.role_name if hasattr(user, 'role_name') else None,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }

    # Profile Management Methods
    async def update_user_profile(self, user_id: UUID, update_data: dict) -> User:
        """Update user profile information"""
        try:
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Update user fields with provided data
            for field, value in update_data.items():
                if hasattr(user, field):
                    setattr(user, field, value)
            
            # Update timestamp with RFC3339 format for Weaviate compatibility
            user.updated_at = datetime.now(timezone.utc)
            
            # Save updates
            updated_user = await self.user_repo.update(user_id, user)
            return updated_user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user profile: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user profile"
            )

    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID"""
        try:
            return await self.user_repo.get_by_id(user_id)
        except Exception as e:
            logger.error(f"Error getting user by ID: {str(e)}")
            return None