"""
Testing utilities package for <PERSON><PERSON>vy Backend.

This package provides comprehensive testing utilities optimized for AI agents
to fully automate testing and debugging of protected routes with tokens.
"""

from .auth_test_helpers import (
    RoleBasedAuthTestHelper,
    AsyncRoleBasedAuthTestHelper,
    TestContextManager,
    generate_test_report,
    validate_authentication_setup,
    auth_helper,
    system_admin_token,
    tenant_admin_token,
    manager_token,
    member_token,
    system_admin_headers,
    tenant_admin_headers,
    manager_headers,
    member_headers,
    expired_token,
    invalid_tokens,
    async_auth_helper,
    test_context
)

__all__ = [
    "RoleBasedAuthTestHelper",
    "AsyncRoleBasedAuthTestHelper", 
    "TestContextManager",
    "generate_test_report",
    "validate_authentication_setup",
    "auth_helper",
    "system_admin_token",
    "tenant_admin_token",
    "manager_token",
    "member_token",
    "system_admin_headers",
    "tenant_admin_headers",
    "manager_headers",
    "member_headers",
    "expired_token",
    "invalid_tokens",
    "async_auth_helper",
    "test_context"
] 