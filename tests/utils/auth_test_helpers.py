"""
Authentication Testing Utilities for AI Agents

This module provides comprehensive testing utilities for protected routes with tokens.
Optimized for AI agents like Cursor to fully automate testing and debugging.

Best Practices:
1. Use fixtures for consistent token generation based on roles/permissions
2. Test all authentication scenarios (valid, invalid, expired, missing)
3. Test permission-based access control with different roles
4. Test multi-tenant isolation
5. Provide clear error messages for debugging
6. Support both unit and integration testing
7. No superuser concept - everything is role/permission based
"""

import pytest
import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from uuid import uuid4, UUID
from fastapi.testclient import TestClient
from httpx import AsyncClient
import jwt
from jose import jwt as jose_jwt

from models.user import User
from models.role import Role, Permission
from config import settings
from utils.test_auth import create_test_superuser, create_test_jwt_token
from session.running_context import RunningContext, set_context, clear_context


class RoleBasedAuthTestHelper:
    """
    Comprehensive role-based authentication testing helper for AI agents.
    
    This class provides all the utilities needed to test protected routes
    with various roles and permissions, making it easy for AI agents
    to write comprehensive tests.
    """
    
    def __init__(self, test_client: TestClient, api_base_url: str = "/api"):
        self.test_client = test_client
        self.api_base_url = api_base_url
        self._cached_tokens = {}
    
    def get_system_admin_token(self, tenant_id: Optional[str] = None) -> str:
        """
        Get a system admin token for testing.
        
        Args:
            tenant_id: Optional tenant ID for multi-tenant testing
            
        Returns:
            Valid JWT token for system admin access
        """
        cache_key = f"system_admin_{tenant_id or 'default'}"
        if cache_key in self._cached_tokens:
            return self._cached_tokens[cache_key]
        
        # Create system admin user with all permissions
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        user.email = "<EMAIL>"
        
        # Create token with system admin permissions
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": "SystemAdmin",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": [perm.value for perm in Permission],  # All permissions
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        token = jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
        self._cached_tokens[cache_key] = token
        return token
    
    def get_tenant_admin_token(self, tenant_id: Optional[str] = None) -> str:
        """
        Get a tenant admin token for testing.
        
        Args:
            tenant_id: Optional tenant ID for multi-tenant testing
            
        Returns:
            Valid JWT token for tenant admin access
        """
        cache_key = f"tenant_admin_{tenant_id or 'default'}"
        if cache_key in self._cached_tokens:
            return self._cached_tokens[cache_key]
        
        # Create tenant admin user with tenant-level permissions
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        user.email = "<EMAIL>"
        
        # Create token with tenant admin permissions (all except system permissions)
        tenant_permissions = [perm.value for perm in Permission if not perm.value.startswith("system:")]
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": "TenantAdmin",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": tenant_permissions,
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        token = jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
        self._cached_tokens[cache_key] = token
        return token
    
    def get_manager_token(self, tenant_id: Optional[str] = None) -> str:
        """
        Get a manager token for testing.
        
        Args:
            tenant_id: Optional tenant ID for multi-tenant testing
            
        Returns:
            Valid JWT token for manager access
        """
        cache_key = f"manager_{tenant_id or 'default'}"
        if cache_key in self._cached_tokens:
            return self._cached_tokens[cache_key]
        
        # Create manager user with manager permissions
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        user.email = "<EMAIL>"
        
        # Manager permissions from Role.get_predefined_roles()
        manager_permissions = [
            "create_agent", "read_agent", "update_agent", "delete_agent", "execute_agent",
            "create_resource", "read_resource", "update_resource", "delete_resource", "search_resource",
            "create_task", "read_task", "update_task", "delete_task", "manage_tasks", "read_tasks",
            "view_tenant_analytics", "manage_team", "invite_member", "remove_member"
        ]
        
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": "Manager",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": manager_permissions,
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        token = jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
        self._cached_tokens[cache_key] = token
        return token
    
    def get_member_token(self, tenant_id: Optional[str] = None) -> str:
        """
        Get a member token for testing.
        
        Args:
            tenant_id: Optional tenant ID for multi-tenant testing
            
        Returns:
            Valid JWT token for member access
        """
        cache_key = f"member_{tenant_id or 'default'}"
        if cache_key in self._cached_tokens:
            return self._cached_tokens[cache_key]
        
        # Create member user with member permissions
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        user.email = "<EMAIL>"
        
        # Member permissions from Role.get_predefined_roles()
        member_permissions = [
            "read_agent", "execute_agent", "read_resource", "search_resource", "read_task", "read_tasks"
        ]
        
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": "Member",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": member_permissions,
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        token = jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
        self._cached_tokens[cache_key] = token
        return token
    
    def create_custom_role_token(self, 
                               permissions: List[str],
                               user_id: Optional[str] = None,
                               email: str = "<EMAIL>",
                               tenant_id: Optional[str] = None,
                               expires_minutes: int = 60) -> str:
        """
        Create a custom token with specific permissions.
        
        Args:
            permissions: List of permission strings
            user_id: Custom user ID
            email: User email
            tenant_id: Tenant ID for multi-tenant testing
            expires_minutes: Token expiration time
            
        Returns:
            Custom JWT token with specified permissions
        """
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        if user_id:
            user.id = UUID(user_id)
        user.email = email
        
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": email.split('@')[0],
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": permissions,
            "exp": datetime.utcnow() + timedelta(minutes=expires_minutes)
        }
        
        return jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
    
    def get_auth_headers(self, token: str) -> Dict[str, str]:
        """
        Get authentication headers with token.
        
        Args:
            token: JWT token
            
        Returns:
            Headers dictionary with Authorization header
        """
        return {"Authorization": f"Bearer {token}"}
    
    def get_role_headers(self, role: str, tenant_id: Optional[str] = None) -> Dict[str, str]:
        """
        Get authentication headers for a specific role.
        
        Args:
            role: Role name ("system_admin", "tenant_admin", "manager", "member")
            tenant_id: Optional tenant ID
            
        Returns:
            Headers dictionary with role-based token
        """
        token_getters = {
            "system_admin": self.get_system_admin_token,
            "tenant_admin": self.get_tenant_admin_token,
            "manager": self.get_manager_token,
            "member": self.get_member_token
        }
        
        if role not in token_getters:
            raise ValueError(f"Unknown role: {role}. Available roles: {list(token_getters.keys())}")
        
        token = token_getters[role](tenant_id)
        return self.get_auth_headers(token)
    
    def create_expired_token(self, role: str = "member", user_id: Optional[str] = None) -> str:
        """
        Create an expired token for testing.
        
        Args:
            role: Role to use for permissions
            user_id: Custom user ID
            
        Returns:
            Expired JWT token
        """
        user = create_test_superuser()
        if user_id:
            user.id = UUID(user_id)
        
        # Get permissions based on role
        role_permissions = {
            "system_admin": [perm.value for perm in Permission],
            "tenant_admin": [perm.value for perm in Permission if not perm.value.startswith("system:")],
            "manager": ["create_agent", "read_agent", "update_agent", "delete_agent", "execute_agent",
                       "create_resource", "read_resource", "update_resource", "delete_resource", "search_resource",
                       "create_task", "read_task", "update_task", "delete_task", "manage_tasks", "read_tasks",
                       "view_tenant_analytics", "manage_team", "invite_member", "remove_member"],
            "member": ["read_agent", "execute_agent", "read_resource", "search_resource", "read_task", "read_tasks"]
        }
        
        permissions = role_permissions.get(role, role_permissions["member"])
        
        # Create token that expired 1 hour ago
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": user.first_name or "test",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": permissions,
            "exp": datetime.utcnow() - timedelta(hours=1)
        }
        
        return jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
    
    def create_invalid_token(self, token_type: str = "malformed") -> str:
        """
        Create various types of invalid tokens for testing.
        
        Args:
            token_type: Type of invalid token ("malformed", "wrong_signature", "empty")
            
        Returns:
            Invalid token string
        """
        if token_type == "malformed":
            return "invalid.token.format"
        elif token_type == "wrong_signature":
            payload = {
                "sub": "test-user-123",
                "exp": datetime.utcnow() + timedelta(hours=1)
            }
            return jose_jwt.encode(payload, "wrong-secret", algorithm="HS256")
        elif token_type == "empty":
            return ""
        else:
            raise ValueError(f"Unknown invalid token type: {token_type}")
    
    def test_protected_endpoint(self, 
                               method: str,
                               endpoint: str,
                               expected_status: int = 200,
                               headers: Optional[Dict[str, str]] = None,
                               data: Optional[Dict[str, Any]] = None,
                               json_data: Optional[Dict[str, Any]] = None,
                               params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Test a protected endpoint with comprehensive validation.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path
            expected_status: Expected HTTP status code
            headers: Request headers
            data: Form data
            json_data: JSON data
            params: Query parameters
            
        Returns:
            Response data and validation results
        """
        url = f"{self.api_base_url}{endpoint}"
        
        # Make request
        response = getattr(self.test_client, method.lower())(
            url,
            headers=headers,
            data=data,
            json=json_data,
            params=params
        )
        
        # Validate response
        result = {
            "status_code": response.status_code,
            "expected_status": expected_status,
            "success": response.status_code == expected_status,
            "response_data": response.json() if response.content else None,
            "headers": dict(response.headers),
            "url": url,
            "method": method.upper()
        }
        
        # Add validation details
        if response.status_code != expected_status:
            result["error"] = {
                "message": f"Expected status {expected_status}, got {response.status_code}",
                "response_text": response.text,
                "suggestion": self._get_error_suggestion(response.status_code, expected_status)
            }
        
        return result
    
    def test_role_based_access(self, 
                              method: str,
                              endpoint: str,
                              test_data: Optional[Dict[str, Any]] = None,
                              expected_results: Optional[Dict[str, int]] = None) -> Dict[str, Any]:
        """
        Test endpoint access with different roles.
        
        Args:
            method: HTTP method
            endpoint: API endpoint path
            test_data: Optional test data for the request
            expected_results: Expected status codes for each role
            
        Returns:
            Role-based access test results
        """
        roles = ["system_admin", "tenant_admin", "manager", "member"]
        
        # Default expected results if not provided
        if expected_results is None:
            expected_results = {
                "system_admin": 200,  # System admin can access everything
                "tenant_admin": 200,  # Tenant admin can access tenant resources
                "manager": 200,       # Manager can access most resources
                "member": 200         # Member can access read operations
            }
        
        results = {}
        
        for role in roles:
            expected_status = expected_results.get(role, 403)  # Default to forbidden
            headers = self.get_role_headers(role)
            
            results[role] = self.test_protected_endpoint(
                method, endpoint, expected_status,
                headers=headers, json_data=test_data
            )
        
        return {
            "endpoint": endpoint,
            "method": method.upper(),
            "role_results": results,
            "summary": self._generate_role_summary(results, expected_results)
        }
    
    def test_authentication_scenarios(self, 
                                    method: str,
                                    endpoint: str,
                                    test_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Test all authentication scenarios for an endpoint.
        
        Args:
            method: HTTP method
            endpoint: API endpoint path
            test_data: Optional test data for the request
            
        Returns:
            Comprehensive test results for all scenarios
        """
        scenarios = {}
        
        # 1. Test with valid system admin token
        scenarios["valid_system_admin"] = self.test_protected_endpoint(
            method, endpoint, 200, 
            headers=self.get_role_headers("system_admin"),
            json_data=test_data
        )
        
        # 2. Test without authentication
        scenarios["no_auth"] = self.test_protected_endpoint(
            method, endpoint, 401
        )
        
        # 3. Test with expired token
        expired_token = self.create_expired_token("system_admin")
        scenarios["expired_token"] = self.test_protected_endpoint(
            method, endpoint, 401,
            headers=self.get_auth_headers(expired_token),
            json_data=test_data
        )
        
        # 4. Test with malformed token
        malformed_token = self.create_invalid_token("malformed")
        scenarios["malformed_token"] = self.test_protected_endpoint(
            method, endpoint, 401,
            headers=self.get_auth_headers(malformed_token),
            json_data=test_data
        )
        
        # 5. Test with wrong signature token
        wrong_sig_token = self.create_invalid_token("wrong_signature")
        scenarios["wrong_signature"] = self.test_protected_endpoint(
            method, endpoint, 401,
            headers=self.get_auth_headers(wrong_sig_token),
            json_data=test_data
        )
        
        # 6. Test with empty token
        scenarios["empty_token"] = self.test_protected_endpoint(
            method, endpoint, 401,
            headers={"Authorization": "Bearer "},
            json_data=test_data
        )
        
        # 7. Test with wrong auth scheme
        scenarios["wrong_scheme"] = self.test_protected_endpoint(
            method, endpoint, 401,
            headers={"Authorization": "Basic dGVzdDp0ZXN0"},
            json_data=test_data
        )
        
        # 8. Test with insufficient permissions (member trying to access admin endpoint)
        scenarios["insufficient_permissions"] = self.test_protected_endpoint(
            method, endpoint, 403,
            headers=self.get_role_headers("member"),
            json_data=test_data
        )
        
        return {
            "endpoint": endpoint,
            "method": method.upper(),
            "scenarios": scenarios,
            "summary": self._generate_scenario_summary(scenarios)
        }
    
    def test_multi_tenant_isolation(self, 
                                   method: str,
                                   endpoint: str,
                                   test_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Test multi-tenant isolation for an endpoint.
        
        Args:
            method: HTTP method
            endpoint: API endpoint path
            test_data: Optional test data
            
        Returns:
            Multi-tenant isolation test results
        """
        tenant_a_id = str(uuid4())
        tenant_b_id = str(uuid4())
        
        # Test with tenant A
        tenant_a_headers = self.get_role_headers("tenant_admin", tenant_a_id)
        tenant_a_result = self.test_protected_endpoint(
            method, endpoint, 200,
            headers=tenant_a_headers,
            json_data=test_data
        )
        
        # Test with tenant B
        tenant_b_headers = self.get_role_headers("tenant_admin", tenant_b_id)
        tenant_b_result = self.test_protected_endpoint(
            method, endpoint, 200,
            headers=tenant_b_headers,
            json_data=test_data
        )
        
        return {
            "endpoint": endpoint,
            "method": method.upper(),
            "tenant_a": {
                "tenant_id": tenant_a_id,
                "result": tenant_a_result
            },
            "tenant_b": {
                "tenant_id": tenant_b_id,
                "result": tenant_b_result
            },
            "isolation_verified": True  # Add specific isolation checks as needed
        }
    
    def _get_error_suggestion(self, actual_status: int, expected_status: int) -> str:
        """Get helpful suggestion for authentication errors."""
        if actual_status == 401 and expected_status != 401:
            return "Authentication required. Check if token is valid and properly formatted."
        elif actual_status == 403 and expected_status != 403:
            return "Insufficient permissions. Check if user has required role/permissions."
        elif actual_status == 404 and expected_status != 404:
            return "Endpoint not found. Check URL and HTTP method."
        elif actual_status == 422 and expected_status != 422:
            return "Validation error. Check request data format and required fields."
        else:
            return f"Unexpected status code. Expected {expected_status}, got {actual_status}."
    
    def _generate_scenario_summary(self, scenarios: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of authentication scenario results."""
        total_scenarios = len(scenarios)
        successful_scenarios = sum(1 for s in scenarios.values() if s.get("success", False))
        failed_scenarios = total_scenarios - successful_scenarios
        
        return {
            "total_scenarios": total_scenarios,
            "successful": successful_scenarios,
            "failed": failed_scenarios,
            "success_rate": f"{(successful_scenarios/total_scenarios)*100:.1f}%",
            "issues": [name for name, scenario in scenarios.items() if not scenario.get("success", False)]
        }
    
    def _generate_role_summary(self, results: Dict[str, Any], expected_results: Dict[str, int]) -> Dict[str, Any]:
        """Generate summary of role-based test results."""
        total_roles = len(results)
        successful_roles = sum(1 for role, result in results.items() 
                             if result.get("success", False))
        failed_roles = total_roles - successful_roles
        
        role_details = {}
        for role, result in results.items():
            expected = expected_results.get(role, 403)
            actual = result.get("status_code", 0)
            role_details[role] = {
                "expected": expected,
                "actual": actual,
                "success": result.get("success", False),
                "status": "✅ PASS" if result.get("success") else "❌ FAIL"
            }
        
        return {
            "total_roles": total_roles,
            "successful": successful_roles,
            "failed": failed_roles,
            "success_rate": f"{(successful_roles/total_roles)*100:.1f}%",
            "role_details": role_details
        }


# Pytest fixtures for easy use in tests
@pytest.fixture
def auth_helper(test_client: TestClient, api_base_url: str) -> RoleBasedAuthTestHelper:
    """
    Fixture providing RoleBasedAuthTestHelper instance.
    
    Usage:
        def test_protected_endpoint(auth_helper):
            result = auth_helper.test_role_based_access("GET", "/resources")
            assert result["summary"]["success_rate"] == "100.0%"
    """
    return RoleBasedAuthTestHelper(test_client, api_base_url)


@pytest.fixture
def system_admin_token(auth_helper: RoleBasedAuthTestHelper) -> str:
    """Get a system admin token for testing."""
    return auth_helper.get_system_admin_token()


@pytest.fixture
def tenant_admin_token(auth_helper: RoleBasedAuthTestHelper) -> str:
    """Get a tenant admin token for testing."""
    return auth_helper.get_tenant_admin_token()


@pytest.fixture
def manager_token(auth_helper: RoleBasedAuthTestHelper) -> str:
    """Get a manager token for testing."""
    return auth_helper.get_manager_token()


@pytest.fixture
def member_token(auth_helper: RoleBasedAuthTestHelper) -> str:
    """Get a member token for testing."""
    return auth_helper.get_member_token()


@pytest.fixture
def system_admin_headers(auth_helper: RoleBasedAuthTestHelper) -> Dict[str, str]:
    """Get authentication headers with system admin token."""
    return auth_helper.get_role_headers("system_admin")


@pytest.fixture
def tenant_admin_headers(auth_helper: RoleBasedAuthTestHelper) -> Dict[str, str]:
    """Get authentication headers with tenant admin token."""
    return auth_helper.get_role_headers("tenant_admin")


@pytest.fixture
def manager_headers(auth_helper: RoleBasedAuthTestHelper) -> Dict[str, str]:
    """Get authentication headers with manager token."""
    return auth_helper.get_role_headers("manager")


@pytest.fixture
def member_headers(auth_helper: RoleBasedAuthTestHelper) -> Dict[str, str]:
    """Get authentication headers with member token."""
    return auth_helper.get_role_headers("member")


@pytest.fixture
def expired_token(auth_helper: RoleBasedAuthTestHelper) -> str:
    """Get an expired token for testing."""
    return auth_helper.create_expired_token("system_admin")


@pytest.fixture
def invalid_tokens(auth_helper: RoleBasedAuthTestHelper) -> Dict[str, str]:
    """Get various invalid tokens for testing."""
    return {
        "malformed": auth_helper.create_invalid_token("malformed"),
        "wrong_signature": auth_helper.create_invalid_token("wrong_signature"),
        "empty": auth_helper.create_invalid_token("empty")
    }


# Async testing utilities
class AsyncRoleBasedAuthTestHelper:
    """Async version of RoleBasedAuthTestHelper for async testing."""
    
    def __init__(self, async_client: AsyncClient, api_base_url: str = "/api"):
        self.async_client = async_client
        self.api_base_url = api_base_url
        self._cached_tokens = {}
    
    async def get_system_admin_token(self, tenant_id: Optional[str] = None) -> str:
        """Get system admin token asynchronously."""
        cache_key = f"system_admin_{tenant_id or 'default'}"
        if cache_key in self._cached_tokens:
            return self._cached_tokens[cache_key]
        
        # For async testing, we'll create the token directly
        user = create_test_superuser(UUID(tenant_id) if tenant_id else None)
        user.email = "<EMAIL>"
        
        payload = {
            "sub": str(user.id),
            "user_id": str(user.id),
            "username": "SystemAdmin",
            "email": user.email,
            "tid": str(user.tenant_id),
            "rol": [perm.value for perm in Permission],
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        token = jose_jwt.encode(payload, settings.secret_key, algorithm="HS256")
        self._cached_tokens[cache_key] = token
        return token
    
    async def test_protected_endpoint_async(self,
                                          method: str,
                                          endpoint: str,
                                          expected_status: int = 200,
                                          headers: Optional[Dict[str, str]] = None,
                                          data: Optional[Dict[str, Any]] = None,
                                          json_data: Optional[Dict[str, Any]] = None,
                                          params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Test protected endpoint asynchronously."""
        url = f"{self.api_base_url}{endpoint}"
        
        response = await getattr(self.async_client, method.lower())(
            url,
            headers=headers,
            data=data,
            json=json_data,
            params=params
        )
        
        return {
            "status_code": response.status_code,
            "expected_status": expected_status,
            "success": response.status_code == expected_status,
            "response_data": response.json() if response.content else None,
            "headers": dict(response.headers),
            "url": url,
            "method": method.upper()
        }


@pytest.fixture
async def async_auth_helper(async_client: AsyncClient, api_base_url: str) -> AsyncRoleBasedAuthTestHelper:
    """Async fixture providing AsyncRoleBasedAuthTestHelper instance."""
    return AsyncRoleBasedAuthTestHelper(async_client, api_base_url)


# Context management utilities for testing
class TestContextManager:
    """Manage test context for authentication testing."""
    
    @staticmethod
    def create_test_context(user: User, **metadata) -> RunningContext:
        """Create a test context with the given user."""
        context = RunningContext(
            user=user,
            request_id=str(uuid4()),
            metadata=metadata
        )
        set_context(context)
        return context
    
    @staticmethod
    def clear_test_context():
        """Clear the test context."""
        clear_context()
    
    @staticmethod
    @pytest.fixture
    def test_context():
        """Fixture for managing test context."""
        context = None
        try:
            user = create_test_superuser()
            context = TestContextManager.create_test_context(user)
            yield context
        finally:
            TestContextManager.clear_test_context()


# Utility functions for AI agents
def generate_test_report(test_results: Dict[str, Any]) -> str:
    """
    Generate a human-readable test report for AI agents.
    
    Args:
        test_results: Results from authentication tests
        
    Returns:
        Formatted test report
    """
    report = []
    report.append("🔐 Role-Based Authentication Test Report")
    report.append("=" * 60)
    
    if "role_results" in test_results:
        role_results = test_results["role_results"]
        summary = test_results.get("summary", {})
        
        report.append(f"Endpoint: {test_results['endpoint']}")
        report.append(f"Method: {test_results['method']}")
        report.append(f"Success Rate: {summary.get('success_rate', 'N/A')}")
        report.append("")
        
        for role_name, result in role_results.items():
            status = "✅ PASS" if result.get("success") else "❌ FAIL"
            report.append(f"{status} {role_name}: {result['status_code']}")
            
            if not result.get("success") and "error" in result:
                report.append(f"    Error: {result['error']['message']}")
                report.append(f"    Suggestion: {result['error']['suggestion']}")
    
    elif "scenarios" in test_results:
        scenarios = test_results["scenarios"]
        summary = test_results.get("summary", {})
        
        report.append(f"Endpoint: {test_results['endpoint']}")
        report.append(f"Method: {test_results['method']}")
        report.append(f"Success Rate: {summary.get('success_rate', 'N/A')}")
        report.append("")
        
        for scenario_name, result in scenarios.items():
            status = "✅ PASS" if result.get("success") else "❌ FAIL"
            report.append(f"{status} {scenario_name}: {result['status_code']}")
            
            if not result.get("success") and "error" in result:
                report.append(f"    Error: {result['error']['message']}")
                report.append(f"    Suggestion: {result['error']['suggestion']}")
    
    return "\n".join(report)


def validate_authentication_setup(test_client: TestClient, api_base_url: str = "/api") -> Dict[str, Any]:
    """
    Validate that authentication system is properly set up for testing.
    
    Args:
        test_client: FastAPI test client
        api_base_url: API base URL
        
    Returns:
        Validation results
    """
    helper = RoleBasedAuthTestHelper(test_client, api_base_url)
    
    # Test basic authentication endpoints
    validation_results = {
        "auth_test_endpoint": helper.test_protected_endpoint("GET", "/resources/test/auth", 401),  # Should require auth
        "auth_test_with_system_admin": helper.test_protected_endpoint(
            "GET", "/resources/test/auth", 200,
            headers=helper.get_role_headers("system_admin")
        ),
        "auth_test_with_member": helper.test_protected_endpoint(
            "GET", "/resources/test/auth", 200,
            headers=helper.get_role_headers("member")
        )
    }
    
    # Check if all validations passed
    all_valid = all(result.get("success", False) for result in validation_results.values())
    
    return {
        "setup_valid": all_valid,
        "results": validation_results,
        "recommendations": _get_setup_recommendations(validation_results)
    }


def _get_setup_recommendations(validation_results: Dict[str, Any]) -> List[str]:
    """Get recommendations for fixing authentication setup issues."""
    recommendations = []
    
    if validation_results["auth_test_endpoint"].get("success"):
        recommendations.append("Auth test endpoint should require authentication but doesn't.")
    
    if not validation_results["auth_test_with_system_admin"].get("success"):
        recommendations.append("Auth test endpoint should work with system admin token but doesn't.")
    
    if not validation_results["auth_test_with_member"].get("success"):
        recommendations.append("Auth test endpoint should work with member token but doesn't.")
    
    return recommendations 