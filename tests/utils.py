"""
Test utilities and helper functions.

This module provides common utilities used across multiple test modules,
including mock creators, data generators, and assertion helpers.
"""
import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from uuid import uuid4


class TestDataGenerator:
    """Generate test data for various scenarios."""
    
    @staticmethod
    def random_string(length: int = 10) -> str:
        """Generate a random string of specified length."""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    @staticmethod
    def random_email() -> str:
        """Generate a random email address."""
        username = TestDataGenerator.random_string(8).lower()
        domain = TestDataGenerator.random_string(6).lower()
        return f"{username}@{domain}.com"
    
    @staticmethod
    def random_uuid() -> str:
        """Generate a random UUID string."""
        return str(uuid4())
    
    @staticmethod
    def future_datetime(hours: int = 24) -> datetime:
        """Generate a datetime in the future."""
        return datetime.utcnow() + timedelta(hours=hours)
    
    @staticmethod
    def past_datetime(hours: int = 24) -> datetime:
        """Generate a datetime in the past."""
        return datetime.utcnow() - timedelta(hours=hours)
    
    @staticmethod
    def generate_article_data(
        title: Optional[str] = None,
        content_length: int = 500,
        include_optional_fields: bool = True
    ) -> Dict[str, Any]:
        """Generate sample article data."""
        data = {
            "title": title or f"Test Article {TestDataGenerator.random_string(5)}",
            "content": TestDataGenerator.random_string(content_length),
            "author": f"Test Author {TestDataGenerator.random_string(3)}"
        }
        
        if include_optional_fields:
            data.update({
                "description": f"Description for {data['title']}",
                "tags": ",".join([
                    TestDataGenerator.random_string(5).lower() 
                    for _ in range(3)
                ])
            })
        
        return data
    
    @staticmethod
    def generate_user_data(
        is_active: bool = True,
        role_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate sample user data."""
        return {
            "id": TestDataGenerator.random_uuid(),
            "email": TestDataGenerator.random_email(),
            "username": TestDataGenerator.random_string(8).lower(),
            "is_active": is_active,
            "tenant_id": TestDataGenerator.random_uuid(),
            "role_id": role_id or TestDataGenerator.random_uuid(),
            "created_at": TestDataGenerator.past_datetime(hours=random.randint(1, 720))
        }


class MockFactory:
    """Factory for creating mock objects and responses."""
    
    @staticmethod
    def create_mock_response(
        status_code: int = 200,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        """Create a mock HTTP response."""
        from unittest.mock import Mock
        
        mock_response = Mock()
        mock_response.status_code = status_code
        mock_response.json.return_value = json_data or {}
        mock_response.headers = headers or {}
        
        return mock_response
    
    @staticmethod
    def create_mock_database():
        """Create a mock database connection."""
        from unittest.mock import AsyncMock, Mock
        
        mock_db = AsyncMock()
        mock_db.collection = Mock()
        mock_db.collection.return_value = AsyncMock()
        
        return mock_db
    
    @staticmethod
    def create_mock_file_storage():
        """Create a mock file storage service."""
        from unittest.mock import AsyncMock
        
        mock_storage = AsyncMock()
        mock_storage.upload_file = AsyncMock(return_value="mock-file-url")
        mock_storage.delete_file = AsyncMock(return_value=True)
        mock_storage.get_file_url = AsyncMock(return_value="mock-file-url")
        
        return mock_storage


class AssertionHelpers:
    """Helper methods for common test assertions."""
    
    @staticmethod
    def assert_valid_uuid(value: str) -> None:
        """Assert that a string is a valid UUID."""
        import uuid
        try:
            uuid.UUID(value)
        except ValueError:
            pytest.fail(f"'{value}' is not a valid UUID")
    
    @staticmethod
    def assert_valid_datetime(value: str) -> None:
        """Assert that a string is a valid ISO datetime."""
        try:
            datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            pytest.fail(f"'{value}' is not a valid ISO datetime")
    
    @staticmethod
    def assert_valid_email(value: str) -> None:
        """Assert that a string is a valid email address."""
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            pytest.fail(f"'{value}' is not a valid email address")
    
    @staticmethod
    def assert_response_structure(
        response_data: Dict[str, Any], 
        required_fields: List[str],
        optional_fields: Optional[List[str]] = None
    ) -> None:
        """Assert that a response has the expected structure."""
        optional_fields = optional_fields or []
        
        # Check required fields
        for field in required_fields:
            assert field in response_data, f"Required field '{field}' missing from response"
        
        # Check that no unexpected fields are present (optional check)
        all_expected_fields = set(required_fields + optional_fields)
        actual_fields = set(response_data.keys())
        unexpected_fields = actual_fields - all_expected_fields
        
        if unexpected_fields:
            print(f"Warning: Unexpected fields in response: {unexpected_fields}")
    
    @staticmethod
    def assert_error_response(
        response_data: Dict[str, Any],
        expected_error_type: Optional[str] = None
    ) -> None:
        """Assert that a response is a properly formatted error response."""
        # Common error response fields
        expected_fields = ["detail"]  # FastAPI standard
        
        for field in expected_fields:
            assert field in response_data, f"Error response missing '{field}' field"
        
        if expected_error_type:
            # Check if error type matches (implementation depends on your error format)
            assert "type" in response_data and response_data["type"] == expected_error_type


class TestContext:
    """Context manager for test setup and teardown."""
    
    def __init__(self, cleanup_functions: Optional[List[callable]] = None):
        self.cleanup_functions = cleanup_functions or []
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        for cleanup_func in self.cleanup_functions:
            try:
                cleanup_func()
            except Exception as e:
                print(f"Error during cleanup: {e}")
    
    def add_cleanup(self, func: callable):
        """Add a cleanup function to be called on exit."""
        self.cleanup_functions.append(func)
