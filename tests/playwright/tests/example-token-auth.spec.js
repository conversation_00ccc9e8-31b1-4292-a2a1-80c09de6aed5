/**
 * Example Test: Token-based Authentication
 * 
 * This test demonstrates how to use direct token injection
 * for faster authentication in Playwright tests.
 */

const { test, expect } = require('@playwright/test');
const { 
  tokenBasedAuth, 
  generateJWTToken,
  verifyAuthentication,
  cleanupAuth
} = require('../helpers/auth-helpers');

test.describe('Token-based Authentication Examples', () => {
  
  test('should authenticate using direct token injection', async ({ page }) => {
    // Perform token-based authentication
    const { token, user } = await tokenBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Verify token was created
    expect(token).toBeTruthy();
    expect(user.role).toBe('SystemAdmin');
    
    // Navigate to protected page
    await page.goto('/dashboard');
    
    // Should not be redirected to login
    await expect(page).not.toHaveURL(/\/auth\/login/);
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Verify authentication
    await verifyAuthentication(page, 'SystemAdmin');
  });
  
  test('should work with different user roles', async ({ page }) => {
    // Test with Manager role
    await tokenBasedAuth(page, 'MANAGER');
    await page.goto('/team');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    await verifyAuthentication(page, 'Manager');
    
    // Test with Member role (new page context)
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    await tokenBasedAuth(page, 'MEMBER');
    await page.goto('/dashboard');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    await verifyAuthentication(page, 'Member');
  });
  
  test('should handle token generation failures', async ({ page }) => {
    // Mock the backend to return error
    await page.route('**/system/v1/test/tokens/permanent', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Token generation failed' })
      });
    });
    
    // Token authentication should fail
    await expect(tokenBasedAuth(page, 'SYSTEM_ADMIN')).rejects.toThrow();
  });
  
  test('should persist authentication across page reloads', async ({ page }) => {
    // Authenticate with token
    await tokenBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Navigate to protected page
    await page.goto('/agents');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Reload page
    await page.reload();
    
    // Should still be authenticated
    await expect(page).not.toHaveURL(/\/auth\/login/);
    await expect(page).toHaveURL(/\/agents/);
  });
  
  test('should work with custom token expiration', async ({ page }) => {
    // Generate token with custom expiration
    const { token, user } = await generateJWTToken('SYSTEM_ADMIN', false); // Non-permanent token
    
    // Manually inject token
    await page.context().addCookies([{
      name: 'assivy_auth_token',
      value: token,
      domain: new URL(page.url() || 'http://localhost:4029').hostname,
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'Lax'
    }]);
    
    await page.addInitScript((data) => {
      localStorage.setItem('assivy_token', data.token);
      localStorage.setItem('assivy_user', JSON.stringify(data.user));
    }, { token, user });
    
    // Navigate to protected page
    await page.goto('/dashboard');
    await expect(page).not.toHaveURL(/\/auth\/login/);
  });
  
  test('should handle multiple concurrent token authentications', async ({ browser }) => {
    const contexts = [];
    const pages = [];
    
    try {
      // Create multiple contexts with different roles
      const roles = ['SYSTEM_ADMIN', 'MANAGER', 'MEMBER'];
      
      for (const role of roles) {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // Authenticate each with token
        await tokenBasedAuth(page, role);
        
        contexts.push(context);
        pages.push(page);
      }
      
      // Verify all are authenticated correctly
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const expectedRole = ['SystemAdmin', 'Manager', 'Member'][i];
        
        await page.goto('/dashboard');
        await expect(page).not.toHaveURL(/\/auth\/login/);
        await verifyAuthentication(page, expectedRole);
      }
      
    } finally {
      // Clean up contexts
      for (const context of contexts) {
        await context.close();
      }
    }
  });
  
});

test.describe('Token Authentication Performance', () => {
  
  test('should be faster than login-based authentication', async ({ page }) => {
    // Measure token authentication time
    const tokenStart = Date.now();
    await tokenBasedAuth(page, 'SYSTEM_ADMIN');
    await page.goto('/dashboard');
    const tokenTime = Date.now() - tokenStart;
    
    // Clear authentication
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
    
    // Measure login authentication time
    const loginStart = Date.now();
    const { loginBasedAuth } = require('../helpers/auth-helpers');
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    const loginTime = Date.now() - loginStart;
    
    console.log(`Token auth: ${tokenTime}ms, Login auth: ${loginTime}ms`);
    
    // Token auth should be significantly faster
    expect(tokenTime).toBeLessThan(loginTime);
  });
  
  test('should handle rapid successive authentications', async ({ browser }) => {
    const startTime = Date.now();
    const contexts = [];
    
    try {
      // Create 5 contexts rapidly
      for (let i = 0; i < 5; i++) {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        await tokenBasedAuth(page, 'SYSTEM_ADMIN');
        await page.goto('/dashboard');
        await expect(page).not.toHaveURL(/\/auth\/login/);
        
        contexts.push(context);
      }
      
      const totalTime = Date.now() - startTime;
      console.log(`Created 5 authenticated contexts in ${totalTime}ms`);
      
      // Should complete in reasonable time
      expect(totalTime).toBeLessThan(30000); // 30 seconds
      
    } finally {
      for (const context of contexts) {
        await context.close();
      }
    }
  });
  
});

test.describe('Token Authentication Security', () => {
  
  test('should use secure token structure', async ({ page }) => {
    const { token, user } = await tokenBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Token should be a valid JWT (3 parts separated by dots)
    const tokenParts = token.split('.');
    expect(tokenParts).toHaveLength(3);
    
    // Decode header and payload (not signature for security)
    const header = JSON.parse(Buffer.from(tokenParts[0], 'base64url').toString());
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64url').toString());
    
    // Verify token structure
    expect(header.alg).toBe('HS256');
    expect(payload.sub).toBe(user.id);
    expect(payload.email).toBeTruthy();
    expect(payload.permissions).toBeInstanceOf(Array);
    expect(payload.iss).toBe('assivy-api');
    expect(payload.aud).toBe('assivy-client');
  });
  
  test('should handle invalid tokens gracefully', async ({ page }) => {
    // Inject invalid token
    await page.context().addCookies([{
      name: 'assivy_auth_token',
      value: 'invalid.token.here',
      domain: new URL('http://localhost:4029').hostname,
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'Lax'
    }]);
    
    // Try to access protected page
    await page.goto('/dashboard');
    
    // Should be redirected to login due to invalid token
    await expect(page).toHaveURL(/\/auth\/login/);
  });
  
  test('should include proper permissions in token', async ({ page }) => {
    const { token, user } = await tokenBasedAuth(page, 'MANAGER');
    
    // Decode token payload
    const tokenParts = token.split('.');
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64url').toString());
    
    // Verify manager permissions are included
    expect(payload.permissions).toContain('create_agent');
    expect(payload.permissions).toContain('manage_team');
    expect(payload.permissions).not.toContain('system_admin');
    
    // Verify role
    expect(payload.role).toBe('Manager');
  });
  
});

// Cleanup after tests
test.afterAll(async () => {
  // Clean up any stored authentication contexts if needed
  if (process.env.PLAYWRIGHT_CLEANUP_AFTER_TESTS === 'true') {
    cleanupAuth();
  }
});
