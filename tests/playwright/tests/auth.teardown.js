/**
 * Authentication Teardown for Playwright Tests
 * 
 * This teardown file cleans up authentication contexts and test data
 * after test execution is complete.
 */

const { test as teardown } = require('@playwright/test');
const { authConfig, USER_ROLES } = require('../config/auth-config');
const { cleanupAuth } = require('../helpers/auth-helpers');

teardown('cleanup authentication contexts', async () => {
  console.log('🧹 Starting authentication cleanup...');
  
  // Check if cleanup is enabled
  const shouldCleanup = process.env.PLAYWRIGHT_CLEANUP_CONTEXTS === 'true' || 
                       process.env.CI === 'true' ||
                       process.env.PLAYWRIGHT_CLEANUP_AFTER_TESTS === 'true';
  
  if (!shouldCleanup) {
    console.log('💾 Cleanup disabled, keeping authentication contexts for next run');
    return;
  }
  
  try {
    // Clean up all authentication contexts
    cleanupAuth();
    
    console.log('✅ Authentication contexts cleaned up successfully');
    
  } catch (error) {
    console.warn('⚠️ Failed to clean up authentication contexts:', error.message);
  }
});

teardown('cleanup test data', async () => {
  console.log('🧹 Cleaning up test data...');
  
  const backendURL = authConfig.config.backendURL;
  
  try {
    // Clean up test resources
    const resourcesResponse = await fetch(`${backendURL}/system/v1/test/cleanup-resources`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (resourcesResponse.ok) {
      console.log('✅ Test resources cleaned up');
    } else {
      console.warn('⚠️ Failed to clean up test resources');
    }
    
    // Clean up test tasks
    const tasksResponse = await fetch(`${backendURL}/system/v1/test/cleanup-tasks`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (tasksResponse.ok) {
      console.log('✅ Test tasks cleaned up');
    } else {
      console.warn('⚠️ Failed to clean up test tasks');
    }
    
    // Clean up test agents
    const agentsResponse = await fetch(`${backendURL}/system/v1/test/cleanup-agents`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (agentsResponse.ok) {
      console.log('✅ Test agents cleaned up');
    } else {
      console.warn('⚠️ Failed to clean up test agents');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to clean up test data:', error.message);
  }
});

teardown('generate test summary', async () => {
  console.log('📊 Generating test execution summary...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Check if results file exists
    const resultsPath = path.join(__dirname, '..', 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log('📈 Test Execution Summary:');
      console.log('=' * 30);
      console.log(`Total tests: ${results.stats?.total || 'Unknown'}`);
      console.log(`✅ Passed: ${results.stats?.passed || 'Unknown'}`);
      console.log(`❌ Failed: ${results.stats?.failed || 'Unknown'}`);
      console.log(`⏭️ Skipped: ${results.stats?.skipped || 'Unknown'}`);
      console.log(`⏱️ Duration: ${results.stats?.duration || 'Unknown'}ms`);
      
      // Log failed tests if any
      if (results.stats?.failed > 0) {
        console.log('\n❌ Failed Tests:');
        results.suites?.forEach(suite => {
          suite.specs?.forEach(spec => {
            spec.tests?.forEach(test => {
              if (test.results?.some(result => result.status === 'failed')) {
                console.log(`  - ${suite.title}: ${spec.title}`);
              }
            });
          });
        });
      }
      
      // Log authentication method used
      console.log(`\n🔐 Authentication method: ${authConfig.getAuthMethod()}`);
      console.log(`👤 Default role: ${authConfig.config.defaultRole}`);
      
    } else {
      console.log('📝 No test results file found');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to generate test summary:', error.message);
  }
});
