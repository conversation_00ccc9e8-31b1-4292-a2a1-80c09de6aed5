/**
 * Authentication Setup for Playwright Tests
 * 
 * This setup file creates authenticated browser contexts for all user roles
 * and saves them for reuse in actual tests.
 */

const { test as setup, expect } = require('@playwright/test');
const { authConfig, USER_ROLES } = require('../config/auth-config');
const { setupAuthenticatedContext, verifyAuthentication } = require('../helpers/auth-helpers');

// Create authentication contexts for each user role
Object.keys(USER_ROLES).forEach(role => {
  setup(`authenticate as ${role}`, async ({ browser }) => {
    console.log(`🔐 Setting up authentication for ${role}...`);
    
    try {
      // Create authenticated context
      const { context, page } = await setupAuthenticatedContext(browser, role);
      
      // Verify authentication by navigating to dashboard
      await page.goto('/dashboard', { waitUntil: 'networkidle' });
      
      // Verify we're not redirected to login
      await expect(page).not.toHaveURL(/\/auth\/login/);
      
      // Verify user authentication
      await verifyAuthentication(page, USER_ROLES[role].role);
      
      // Save the storage state
      await context.storageState({ 
        path: authConfig.getStorageStatePath(role) 
      });
      
      console.log(`✅ Authentication setup complete for ${role}`);
      
      await context.close();
      
    } catch (error) {
      console.error(`❌ Authentication setup failed for ${role}:`, error.message);
      throw error;
    }
  });
});

// Verify all authentication contexts are working
setup('verify all authentication contexts', async ({ browser }) => {
  console.log('🔍 Verifying all authentication contexts...');
  
  const roles = Object.keys(USER_ROLES);
  const results = {};
  
  for (const role of roles) {
    try {
      const storageStatePath = authConfig.getStorageStatePath(role);
      
      // Create context with stored state
      const context = await browser.newContext({
        storageState: storageStatePath,
        baseURL: authConfig.config.baseURL
      });
      
      const page = await context.newPage();
      
      // Test navigation to protected page
      await page.goto('/dashboard', { waitUntil: 'networkidle', timeout: 15000 });
      
      // Verify not redirected to login
      const isAuthenticated = !page.url().includes('/auth/login');
      results[role] = isAuthenticated;
      
      if (isAuthenticated) {
        console.log(`✅ ${role} context is valid`);
      } else {
        console.log(`❌ ${role} context is invalid`);
      }
      
      await context.close();
      
    } catch (error) {
      console.error(`❌ Failed to verify ${role} context:`, error.message);
      results[role] = false;
    }
  }
  
  // Ensure at least one context is valid
  const validContexts = Object.values(results).filter(Boolean).length;
  expect(validContexts).toBeGreaterThan(0);
  
  console.log(`📊 Authentication verification complete: ${validContexts}/${roles.length} contexts valid`);
});
