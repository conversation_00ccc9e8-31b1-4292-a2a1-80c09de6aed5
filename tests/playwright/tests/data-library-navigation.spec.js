/**
 * Data Library Navigation Test
 * 
 * This test demonstrates navigating to the data library page
 * using the Assivy Playwright authentication system.
 */

const { test, expect } = require('@playwright/test');
const { authenticate, verifyAuthentication } = require('../helpers/auth-helpers');

test.describe('Data Library Navigation', () => {
  
  test('should navigate to data library page as System Admin', async ({ page }) => {
    // Authenticate as System Admin (has full access)
    await authenticate(page, 'SYSTEM_ADMIN');
    
    // Verify authentication
    await verifyAuthentication(page, 'SystemAdmin');
    
    // Navigate to data library page
    await page.goto('/data-library');
    
    // Verify we're not redirected to login
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Verify we're on the data library page
    await expect(page).toHaveURL(/\/data-library/);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Look for common data library elements
    const pageTitle = page.locator('h1, [data-testid="page-title"], .page-title').first();
    await expect(pageTitle).toBeVisible({ timeout: 10000 });
    
    // Check for data library specific content
    const possibleSelectors = [
      '[data-testid="data-library"]',
      '.data-library',
      '[data-testid="file-list"]',
      '.file-list',
      '[data-testid="resource-list"]',
      '.resource-list',
      'table', // If data is displayed in a table
      '.grid', // If data is displayed in a grid
      '[role="grid"]'
    ];
    
    let foundContent = false;
    for (const selector of possibleSelectors) {
      try {
        const element = page.locator(selector).first();
        if (await element.isVisible({ timeout: 2000 })) {
          console.log(`✅ Found data library content: ${selector}`);
          foundContent = true;
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }
    
    if (!foundContent) {
      console.log('⚠️ No specific data library content found, but page loaded successfully');
    }
    
    // Take a screenshot for verification
    await page.screenshot({ 
      path: 'test-results/data-library-page.png',
      fullPage: true 
    });
    
    console.log('✅ Successfully navigated to data library page');
  });
  
  test('should navigate to data library via menu/navigation', async ({ page }) => {
    // Authenticate as System Admin
    await authenticate(page, 'SYSTEM_ADMIN');
    
    // Start from dashboard
    await page.goto('/dashboard');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Look for navigation menu items that might lead to data library
    const navigationSelectors = [
      'a[href="/data-library"]',
      'a[href*="data-library"]',
      'a:has-text("Data Library")',
      'a:has-text("Data")',
      'a:has-text("Library")',
      'a:has-text("Files")',
      'a:has-text("Resources")',
      '[data-testid="data-library-link"]',
      '[data-testid="nav-data-library"]'
    ];
    
    let navigationFound = false;
    
    for (const selector of navigationSelectors) {
      try {
        const navLink = page.locator(selector).first();
        if (await navLink.isVisible({ timeout: 2000 })) {
          console.log(`✅ Found navigation link: ${selector}`);
          await navLink.click();
          navigationFound = true;
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }
    
    if (!navigationFound) {
      console.log('⚠️ No navigation link found, using direct URL navigation');
      await page.goto('/data-library');
    }
    
    // Wait for navigation to complete
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the data library page
    await expect(page).toHaveURL(/\/data-library/);
    
    console.log('✅ Successfully navigated to data library via menu');
  });
  
  test('should check data library access for different user roles', async ({ page }) => {
    const roles = [
      { role: 'SYSTEM_ADMIN', expectedAccess: true, roleName: 'SystemAdmin' },
      { role: 'TENANT_ADMIN', expectedAccess: true, roleName: 'TenantAdmin' },
      { role: 'MANAGER', expectedAccess: true, roleName: 'Manager' },
      { role: 'MEMBER', expectedAccess: false, roleName: 'Member' } // Assuming members have limited access
    ];
    
    for (const { role, expectedAccess, roleName } of roles) {
      console.log(`🔍 Testing data library access for ${role}...`);
      
      // Clear previous authentication
      await page.context().clearCookies();
      await page.evaluate(() => localStorage.clear());
      
      // Authenticate with current role
      await authenticate(page, role);
      await verifyAuthentication(page, roleName);
      
      // Try to access data library
      await page.goto('/data-library');
      
      if (expectedAccess) {
        // Should have access
        await expect(page).not.toHaveURL(/\/auth\/login/);
        await expect(page).toHaveURL(/\/data-library/);
        console.log(`✅ ${role} has access to data library`);
      } else {
        // May be redirected or see access denied
        // This depends on your application's permission handling
        const currentUrl = page.url();
        if (currentUrl.includes('/auth/login')) {
          console.log(`⚠️ ${role} was redirected to login (no access)`);
        } else if (currentUrl.includes('/data-library')) {
          // Check for access denied message
          const accessDenied = page.locator('.access-denied, .unauthorized, .forbidden, [data-testid="access-denied"]').first();
          if (await accessDenied.isVisible({ timeout: 2000 })) {
            console.log(`⚠️ ${role} sees access denied message`);
          } else {
            console.log(`⚠️ ${role} has unexpected access to data library`);
          }
        } else {
          console.log(`⚠️ ${role} was redirected to: ${currentUrl}`);
        }
      }
    }
  });
  
  test('should interact with data library features', async ({ page }) => {
    // Authenticate as System Admin for full access
    await authenticate(page, 'SYSTEM_ADMIN');
    
    // Navigate to data library
    await page.goto('/data-library');
    await expect(page).toHaveURL(/\/data-library/);
    await page.waitForLoadState('networkidle');
    
    // Look for common data library features
    const features = [
      {
        name: 'Search functionality',
        selectors: ['input[type="search"]', '[data-testid="search"]', '.search-input', 'input[placeholder*="search" i]']
      },
      {
        name: 'Upload button',
        selectors: ['button:has-text("Upload")', '[data-testid="upload-button"]', '.upload-btn', 'input[type="file"]']
      },
      {
        name: 'Filter options',
        selectors: ['select', '.filter', '[data-testid="filter"]', '.dropdown']
      },
      {
        name: 'Data items/files',
        selectors: ['.file-item', '.data-item', '[data-testid="file"]', 'tr', '.card']
      }
    ];
    
    for (const feature of features) {
      let found = false;
      for (const selector of feature.selectors) {
        try {
          const element = page.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            console.log(`✅ Found ${feature.name}: ${selector}`);
            found = true;
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (!found) {
        console.log(`⚠️ ${feature.name} not found`);
      }
    }
    
    // Take a screenshot of the data library interface
    await page.screenshot({ 
      path: 'test-results/data-library-interface.png',
      fullPage: true 
    });
    
    console.log('✅ Data library feature exploration completed');
  });
  
  test('should handle data library loading states', async ({ page }) => {
    // Authenticate
    await authenticate(page, 'SYSTEM_ADMIN');
    
    // Navigate to data library and monitor loading
    const navigationPromise = page.goto('/data-library');
    
    // Look for loading indicators
    const loadingSelectors = [
      '.loading',
      '.spinner',
      '[data-testid="loading"]',
      '.skeleton',
      '.loading-spinner'
    ];
    
    let loadingFound = false;
    for (const selector of loadingSelectors) {
      try {
        const loading = page.locator(selector).first();
        if (await loading.isVisible({ timeout: 1000 })) {
          console.log(`✅ Found loading indicator: ${selector}`);
          loadingFound = true;
          
          // Wait for loading to disappear
          await loading.waitFor({ state: 'hidden', timeout: 30000 });
          console.log(`✅ Loading completed`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }
    
    if (!loadingFound) {
      console.log('⚠️ No loading indicators found (page may load very quickly)');
    }
    
    // Wait for navigation to complete
    await navigationPromise;
    await page.waitForLoadState('networkidle');
    
    // Verify final state
    await expect(page).toHaveURL(/\/data-library/);
    
    console.log('✅ Data library loading test completed');
  });
  
});
