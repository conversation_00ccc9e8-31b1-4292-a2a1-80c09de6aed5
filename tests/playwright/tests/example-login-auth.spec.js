/**
 * Example Test: Login-based Authentication
 * 
 * This test demonstrates how to use login-based authentication
 * in Playwright tests for the Assivy application.
 */

const { test, expect } = require('@playwright/test');
const { 
  loginBasedAuth, 
  verifyAuthentication, 
  logout, 
  switchUserRole 
} = require('../helpers/auth-helpers');

test.describe('Login-based Authentication Examples', () => {
  
  test('should authenticate using login form', async ({ page }) => {
    // Perform login-based authentication
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Verify we're on the dashboard
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Verify authentication
    const userInfo = await verifyAuthentication(page, 'SystemAdmin');
    expect(userInfo.role).toBe('SystemAdmin');
    
    // Test navigation to protected pages
    await page.goto('/agents');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    await page.goto('/resources');
    await expect(page).not.toHaveURL(/\/auth\/login/);
  });
  
  test('should handle different user roles via login', async ({ page }) => {
    // Test Manager role
    await loginBasedAuth(page, 'MANAGER');
    await verifyAuthentication(page, 'Manager');
    
    // Navigate to team management (should be accessible for managers)
    await page.goto('/team');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Switch to Member role
    await switchUserRole(page, 'MEMBER');
    await verifyAuthentication(page, 'Member');
    
    // Try to access team management (should be restricted for members)
    await page.goto('/team');
    // Member might be redirected or see access denied message
    // This depends on your application's permission handling
  });
  
  test('should handle login failures gracefully', async ({ page }) => {
    const { authConfig } = require('../config/auth-config');
    
    // Navigate to login page
    await page.goto('/auth/login');
    
    // Try invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    const loginButton = page.locator('button[type="submit"]').first();
    await loginButton.click();
    
    // Should remain on login page or show error
    await expect(page).toHaveURL(/\/auth\/login/);
    
    // Look for error message
    const errorMessage = page.locator('.error, .alert-error, [role="alert"]').first();
    await expect(errorMessage).toBeVisible({ timeout: 5000 });
  });
  
  test('should maintain session across page reloads', async ({ page }) => {
    // Login as system admin
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Navigate to a protected page
    await page.goto('/agents');
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Reload the page
    await page.reload();
    
    // Should still be authenticated
    await expect(page).not.toHaveURL(/\/auth\/login/);
    await expect(page).toHaveURL(/\/agents/);
  });
  
  test('should logout successfully', async ({ page }) => {
    // Login first
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Perform logout
    await logout(page);
    
    // Should be redirected to login page
    await expect(page).toHaveURL(/\/auth\/login/);
    
    // Try to access protected page - should redirect to login
    await page.goto('/dashboard');
    await expect(page).toHaveURL(/\/auth\/login/);
  });
  
  test('should handle concurrent user sessions', async ({ browser }) => {
    // Create two browser contexts for different users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    try {
      // Login as different users in each context
      await loginBasedAuth(page1, 'SYSTEM_ADMIN');
      await loginBasedAuth(page2, 'MANAGER');
      
      // Verify both are authenticated with correct roles
      await verifyAuthentication(page1, 'SystemAdmin');
      await verifyAuthentication(page2, 'Manager');
      
      // Both should be able to access their respective pages
      await page1.goto('/system/analytics');
      await expect(page1).not.toHaveURL(/\/auth\/login/);
      
      await page2.goto('/team');
      await expect(page2).not.toHaveURL(/\/auth\/login/);
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });
  
  test('should handle session timeout', async ({ page }) => {
    // This test would require manipulating token expiration
    // For demonstration, we'll simulate by clearing storage
    
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Simulate session timeout by clearing auth storage
    await page.evaluate(() => {
      localStorage.removeItem('assivy_token');
      localStorage.removeItem('assivy_user');
    });
    
    // Clear cookies
    await page.context().clearCookies();
    
    // Try to access protected page
    await page.goto('/agents');
    
    // Should be redirected to login
    await expect(page).toHaveURL(/\/auth\/login/);
  });
  
});

test.describe('Login Authentication Error Handling', () => {
  
  test('should handle network errors during login', async ({ page }) => {
    // Navigate to login page
    await page.goto('/auth/login');
    
    // Intercept login request to simulate network error
    await page.route('**/auth/login', route => {
      route.abort('failed');
    });
    
    // Fill credentials
    const user = require('../config/auth-config').authConfig.getUserCredentials('SYSTEM_ADMIN');
    await page.fill('input[type="email"]', user.email);
    await page.fill('input[type="password"]', user.password);
    
    // Submit form
    const loginButton = page.locator('button[type="submit"]').first();
    await loginButton.click();
    
    // Should show error or remain on login page
    await expect(page).toHaveURL(/\/auth\/login/);
  });
  
  test('should handle slow login responses', async ({ page }) => {
    // Navigate to login page
    await page.goto('/auth/login');
    
    // Intercept login request to add delay
    await page.route('**/auth/login', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      route.continue();
    });
    
    // Perform login
    await loginBasedAuth(page, 'SYSTEM_ADMIN');
    
    // Should eventually succeed
    await expect(page).toHaveURL(/\/dashboard/);
  });
  
});
