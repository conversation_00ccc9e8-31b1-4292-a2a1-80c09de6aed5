/**
 * Playwright Configuration for Assivy Application
 * 
 * This configuration supports both login-based and token-based authentication
 * with persistent browser contexts and multi-role testing capabilities.
 */

const { defineConfig, devices } = require('@playwright/test');
const { authConfig } = require('./config/auth-config');

/**
 * Environment-based configuration
 */
const config = {
  // Base URL for the application
  baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4029',
  
  // Backend API URL
  backendURL: process.env.PLAYWRIGHT_BACKEND_URL || 'http://localhost:8000',
  
  // Authentication method: 'login' or 'token'
  authMethod: process.env.PLAYWRIGHT_AUTH_METHOD || 'token',
  
  // Default user role for tests
  defaultRole: process.env.PLAYWRIGHT_DEFAULT_ROLE || 'SYSTEM_ADMIN',
  
  // Test execution settings
  workers: process.env.CI ? 2 : 4,
  retries: process.env.CI ? 2 : 1,
  timeout: 60000,
  
  // Headless mode
  headless: process.env.PLAYWRIGHT_HEADLESS !== 'false',
  
  // Video and screenshot settings
  video: process.env.PLAYWRIGHT_VIDEO || 'retain-on-failure',
  screenshot: process.env.PLAYWRIGHT_SCREENSHOT || 'only-on-failure'
};

module.exports = defineConfig({
  // Test directory
  testDir: './tests',
  
  // Global test timeout
  timeout: config.timeout,
  
  // Expect timeout for assertions
  expect: {
    timeout: 10000
  },
  
  // Test execution settings
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: config.retries,
  workers: config.workers,
  
  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    process.env.CI ? ['github'] : ['list']
  ],
  
  // Global test settings
  use: {
    // Base URL
    baseURL: config.baseURL,
    
    // Browser settings
    headless: config.headless,
    viewport: { width: 1280, height: 720 },
    
    // Media settings
    video: config.video,
    screenshot: config.screenshot,
    
    // Network settings
    ignoreHTTPSErrors: true,
    
    // Timeouts
    actionTimeout: 15000,
    navigationTimeout: 30000,
    
    // Trace settings
    trace: 'retain-on-failure',
    
    // Custom properties for auth helpers
    authMethod: config.authMethod,
    defaultRole: config.defaultRole,
    backendURL: config.backendURL
  },
  
  // Test output directory
  outputDir: 'test-results/artifacts',
  
  // Global setup and teardown
  globalSetup: require.resolve('./setup/global-setup.js'),
  globalTeardown: require.resolve('./setup/global-teardown.js'),
  
  // Projects for different browsers and authentication methods
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.js/,
      teardown: 'cleanup'
    },
    
    // Cleanup project
    {
      name: 'cleanup',
      testMatch: /.*\.teardown\.js/
    },
    
    // Chrome with token authentication
    {
      name: 'chromium-token',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'token',
        storageState: authConfig.getStorageStatePath('SYSTEM_ADMIN')
      },
      dependencies: ['setup']
    },
    
    // Chrome with login authentication
    {
      name: 'chromium-login',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'login'
      },
      dependencies: ['setup']
    },
    
    // Firefox with token authentication
    {
      name: 'firefox-token',
      use: { 
        ...devices['Desktop Firefox'],
        authMethod: 'token',
        storageState: authConfig.getStorageStatePath('SYSTEM_ADMIN')
      },
      dependencies: ['setup']
    },
    
    // Safari with token authentication (macOS only)
    {
      name: 'webkit-token',
      use: { 
        ...devices['Desktop Safari'],
        authMethod: 'token',
        storageState: authConfig.getStorageStatePath('SYSTEM_ADMIN')
      },
      dependencies: ['setup']
    },
    
    // Mobile Chrome
    {
      name: 'mobile-chrome',
      use: { 
        ...devices['Pixel 5'],
        authMethod: 'token',
        storageState: authConfig.getStorageStatePath('SYSTEM_ADMIN')
      },
      dependencies: ['setup']
    },
    
    // Role-specific projects
    {
      name: 'system-admin',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'token',
        defaultRole: 'SYSTEM_ADMIN',
        storageState: authConfig.getStorageStatePath('SYSTEM_ADMIN')
      },
      dependencies: ['setup']
    },
    
    {
      name: 'tenant-admin',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'token',
        defaultRole: 'TENANT_ADMIN',
        storageState: authConfig.getStorageStatePath('TENANT_ADMIN')
      },
      dependencies: ['setup']
    },
    
    {
      name: 'manager',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'token',
        defaultRole: 'MANAGER',
        storageState: authConfig.getStorageStatePath('MANAGER')
      },
      dependencies: ['setup']
    },
    
    {
      name: 'member',
      use: { 
        ...devices['Desktop Chrome'],
        authMethod: 'token',
        defaultRole: 'MEMBER',
        storageState: authConfig.getStorageStatePath('MEMBER')
      },
      dependencies: ['setup']
    }
  ],
  
  // Web server configuration (optional - for local development)
  webServer: process.env.PLAYWRIGHT_SKIP_SERVER ? undefined : [
    {
      command: 'npm run dev',
      port: 4029,
      reuseExistingServer: !process.env.CI,
      timeout: 120000,
      env: {
        NODE_ENV: 'test'
      }
    }
  ]
});
