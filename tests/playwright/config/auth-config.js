/**
 * Playwright Authentication Configuration
 * 
 * This module provides configurable authentication for Playwright tests
 * supporting both login-based and direct token injection approaches.
 */

const path = require('path');
const fs = require('fs');

// Authentication methods
const AUTH_METHODS = {
  LOGIN: 'login',
  TOKEN: 'token'
};

// User roles with their corresponding test credentials
const USER_ROLES = {
  SYSTEM_ADMIN: {
    id: '00000000-0000-0000-0000-000000000001',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'SystemAdmin',
    tenantId: '00000000-0000-0000-0000-000000000001',
    permissions: [
      'system_admin', 'tenant_admin', 'create_agent', 'read_agent', 'update_agent', 
      'delete_agent', 'execute_agent', 'create_resource', 'read_resource', 
      'update_resource', 'delete_resource', 'search_resource', 'create_task', 
      'read_task', 'update_task', 'delete_task', 'manage_tasks', 'read_tasks'
    ]
  },
  TENANT_ADMIN: {
    id: '00000000-0000-0000-0000-000000000002',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'TenantAdmin',
    tenantId: '00000000-0000-0000-0000-000000000002',
    permissions: [
      'tenant_admin', 'create_agent', 'read_agent', 'update_agent', 'delete_agent',
      'execute_agent', 'create_resource', 'read_resource', 'update_resource',
      'delete_resource', 'search_resource', 'create_task', 'read_task',
      'update_task', 'delete_task', 'manage_tasks', 'read_tasks'
    ]
  },
  MANAGER: {
    id: '00000000-0000-0000-0000-000000000003',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'Manager',
    tenantId: '00000000-0000-0000-0000-000000000002',
    permissions: [
      'create_agent', 'read_agent', 'update_agent', 'delete_agent', 'execute_agent',
      'create_resource', 'read_resource', 'update_resource', 'delete_resource',
      'search_resource', 'create_task', 'read_task', 'update_task', 'delete_task',
      'manage_tasks', 'read_tasks', 'view_tenant_analytics', 'manage_team',
      'invite_member', 'remove_member'
    ]
  },
  MEMBER: {
    id: '00000000-0000-0000-0000-000000000004',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'Member',
    tenantId: '00000000-0000-0000-0000-000000000002',
    permissions: [
      'read_agent', 'execute_agent', 'read_resource', 'search_resource',
      'read_task', 'read_tasks'
    ]
  }
};

class AuthConfig {
  constructor() {
    this.baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4029';
    this.backendURL = process.env.PLAYWRIGHT_BACKEND_URL || 'http://localhost:8000';
    this.authMethod = process.env.PLAYWRIGHT_AUTH_METHOD || AUTH_METHODS.TOKEN;
    this.defaultRole = process.env.PLAYWRIGHT_DEFAULT_ROLE || 'SYSTEM_ADMIN';
    this.storageDir = path.join(__dirname, '..', 'storage');
    this.configFile = path.join(__dirname, 'test-config.json');
    
    // Ensure storage directory exists
    if (!fs.existsSync(this.storageDir)) {
      fs.mkdirSync(this.storageDir, { recursive: true });
    }
    
    this.loadConfig();
  }

  /**
   * Load configuration from file or create default
   */
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        const config = JSON.parse(fs.readFileSync(this.configFile, 'utf8'));
        this.config = { ...this.getDefaultConfig(), ...config };
      } else {
        this.config = this.getDefaultConfig();
        this.saveConfig();
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error.message);
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * Get default configuration
   */
  getDefaultConfig() {
    return {
      authMethod: this.authMethod,
      defaultRole: this.defaultRole,
      baseURL: this.baseURL,
      backendURL: this.backendURL,
      timeout: {
        login: 30000,
        navigation: 30000,
        assertion: 10000
      },
      retries: {
        login: 3,
        tokenGeneration: 3
      },
      storage: {
        persistContext: true,
        contextTimeout: 24 * 60 * 60 * 1000, // 24 hours
        clearOnFailure: true
      },
      security: {
        cookieName: 'assivy_auth_token',
        tokenStorageKey: 'assivy_token',
        userStorageKey: 'assivy_user'
      }
    };
  }

  /**
   * Save configuration to file
   */
  saveConfig() {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.warn('Failed to save config:', error.message);
    }
  }

  /**
   * Get user credentials by role
   */
  getUserCredentials(role = this.defaultRole) {
    const user = USER_ROLES[role];
    if (!user) {
      throw new Error(`Unknown user role: ${role}. Available roles: ${Object.keys(USER_ROLES).join(', ')}`);
    }
    return user;
  }

  /**
   * Get storage state file path for a specific role
   */
  getStorageStatePath(role = this.defaultRole) {
    return path.join(this.storageDir, `auth-state-${role.toLowerCase()}.json`);
  }

  /**
   * Check if stored authentication state exists and is valid
   */
  hasValidStorageState(role = this.defaultRole) {
    const storagePath = this.getStorageStatePath(role);
    
    if (!fs.existsSync(storagePath)) {
      return false;
    }

    try {
      const state = JSON.parse(fs.readFileSync(storagePath, 'utf8'));
      const now = Date.now();
      
      // Check if state has required properties
      if (!state.cookies || !state.origins) {
        return false;
      }

      // Check if any auth cookies exist and are not expired
      const authCookies = state.cookies.filter(cookie => 
        cookie.name === this.config.security.cookieName
      );

      if (authCookies.length === 0) {
        return false;
      }

      // Check cookie expiration
      for (const cookie of authCookies) {
        if (cookie.expires && cookie.expires * 1000 < now) {
          return false;
        }
      }

      // Check storage state age
      const stateAge = now - (state.timestamp || 0);
      if (stateAge > this.config.storage.contextTimeout) {
        return false;
      }

      return true;
    } catch (error) {
      console.warn(`Invalid storage state for ${role}:`, error.message);
      return false;
    }
  }

  /**
   * Clear storage state for a specific role
   */
  clearStorageState(role = this.defaultRole) {
    const storagePath = this.getStorageStatePath(role);
    try {
      if (fs.existsSync(storagePath)) {
        fs.unlinkSync(storagePath);
        console.log(`Cleared storage state for ${role}`);
      }
    } catch (error) {
      console.warn(`Failed to clear storage state for ${role}:`, error.message);
    }
  }

  /**
   * Update configuration
   */
  updateConfig(updates) {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }

  /**
   * Get authentication method
   */
  getAuthMethod() {
    return this.config.authMethod;
  }

  /**
   * Set authentication method
   */
  setAuthMethod(method) {
    if (!Object.values(AUTH_METHODS).includes(method)) {
      throw new Error(`Invalid auth method: ${method}. Use: ${Object.values(AUTH_METHODS).join(', ')}`);
    }
    this.config.authMethod = method;
    this.saveConfig();
  }

  /**
   * Get all available user roles
   */
  getAvailableRoles() {
    return Object.keys(USER_ROLES);
  }

  /**
   * Validate role
   */
  isValidRole(role) {
    return Object.keys(USER_ROLES).includes(role);
  }
}

// Export singleton instance
const authConfig = new AuthConfig();

module.exports = {
  authConfig,
  AUTH_METHODS,
  USER_ROLES
};
