/**
 * Playwright Authentication Helpers
 * 
 * This module provides helper functions for both login-based and token-based
 * authentication approaches in Playwright tests.
 */

const { expect } = require('@playwright/test');
const { authConfig, AUTH_METHODS } = require('../config/auth-config');

/**
 * Generate a secure JWT token using the backend API
 */
async function generateJWTToken(userRole = 'SYSTEM_ADMIN', permanent = true) {
  const user = authConfig.getUserCredentials(userRole);
  const backendURL = authConfig.config.backendURL;
  
  const tokenRequest = {
    user_id: user.id,
    tenant_id: user.tenantId,
    role: user.role,
    permissions: user.permissions,
    permanent: permanent,
    expires_in_hours: permanent ? 24 * 365 : 24 // 1 year or 24 hours
  };

  try {
    const response = await fetch(`${backendURL}/system/v1/test/tokens/permanent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tokenRequest)
    });

    if (!response.ok) {
      throw new Error(`Token generation failed: ${response.status} ${response.statusText}`);
    }

    const tokenData = await response.json();
    return {
      token: tokenData.token,
      user: {
        id: tokenData.user_id,
        email: user.email,
        role: tokenData.role,
        tenantId: tokenData.tenant_id,
        permissions: tokenData.permissions
      }
    };
  } catch (error) {
    console.error('Failed to generate JWT token:', error);
    throw error;
  }
}

/**
 * Perform login-based authentication
 */
async function loginBasedAuth(page, userRole = 'SYSTEM_ADMIN') {
  const user = authConfig.getUserCredentials(userRole);
  const baseURL = authConfig.config.baseURL;
  const timeout = authConfig.config.timeout.login;

  console.log(`Performing login-based authentication for ${userRole}...`);

  try {
    // Navigate to login page
    await page.goto(`${baseURL}/auth/login`, { 
      waitUntil: 'networkidle',
      timeout: authConfig.config.timeout.navigation 
    });

    // Wait for login form to be visible
    await page.waitForSelector('input[type="email"], input[name="email"]', { timeout });
    await page.waitForSelector('input[type="password"], input[name="password"]', { timeout });

    // Fill in credentials
    await page.fill('input[type="email"], input[name="email"]', user.email);
    await page.fill('input[type="password"], input[name="password"]', user.password);

    // Submit login form
    const loginButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await loginButton.click();

    // Wait for successful login (redirect or dashboard)
    await page.waitForURL(url => !url.pathname.includes('/auth/login'), { timeout });

    // Verify authentication success
    await expect(page).not.toHaveURL(/\/auth\/login/);

    // Wait for any loading states to complete
    await page.waitForLoadState('networkidle');

    console.log(`✅ Login successful for ${userRole}`);
    return true;

  } catch (error) {
    console.error(`❌ Login failed for ${userRole}:`, error.message);
    throw new Error(`Login authentication failed for ${userRole}: ${error.message}`);
  }
}

/**
 * Perform token-based authentication
 */
async function tokenBasedAuth(page, userRole = 'SYSTEM_ADMIN') {
  console.log(`Performing token-based authentication for ${userRole}...`);

  try {
    // Generate JWT token
    const { token, user } = await generateJWTToken(userRole, true);

    // Set authentication cookie
    await page.context().addCookies([{
      name: authConfig.config.security.cookieName,
      value: token,
      domain: new URL(authConfig.config.baseURL).hostname,
      path: '/',
      httpOnly: true,
      secure: authConfig.config.baseURL.startsWith('https'),
      sameSite: 'Lax'
    }]);

    // Set localStorage tokens for frontend
    await page.addInitScript((data) => {
      localStorage.setItem(data.tokenKey, data.token);
      localStorage.setItem(data.userKey, JSON.stringify(data.user));
    }, {
      tokenKey: authConfig.config.security.tokenStorageKey,
      userKey: authConfig.config.security.userStorageKey,
      token: token,
      user: user
    });

    console.log(`✅ Token injection successful for ${userRole}`);
    return { token, user };

  } catch (error) {
    console.error(`❌ Token authentication failed for ${userRole}:`, error.message);
    throw new Error(`Token authentication failed for ${userRole}: ${error.message}`);
  }
}

/**
 * Authenticate user based on configured method
 */
async function authenticate(page, userRole = 'SYSTEM_ADMIN') {
  const authMethod = authConfig.getAuthMethod();
  
  console.log(`🔐 Authenticating ${userRole} using ${authMethod} method...`);

  try {
    if (authMethod === AUTH_METHODS.LOGIN) {
      return await loginBasedAuth(page, userRole);
    } else if (authMethod === AUTH_METHODS.TOKEN) {
      return await tokenBasedAuth(page, userRole);
    } else {
      throw new Error(`Unknown authentication method: ${authMethod}`);
    }
  } catch (error) {
    console.error(`Authentication failed:`, error.message);
    
    // Clear storage state on failure if configured
    if (authConfig.config.storage.clearOnFailure) {
      authConfig.clearStorageState(userRole);
    }
    
    throw error;
  }
}

/**
 * Setup authenticated browser context
 */
async function setupAuthenticatedContext(browser, userRole = 'SYSTEM_ADMIN') {
  const storageStatePath = authConfig.getStorageStatePath(userRole);
  let context;

  // Try to reuse existing context if valid
  if (authConfig.config.storage.persistContext && authConfig.hasValidStorageState(userRole)) {
    console.log(`📁 Reusing stored authentication context for ${userRole}`);
    
    try {
      context = await browser.newContext({
        storageState: storageStatePath,
        baseURL: authConfig.config.baseURL
      });
      
      // Verify the context is still valid by checking a protected page
      const page = await context.newPage();
      await page.goto('/dashboard', { waitUntil: 'networkidle', timeout: 10000 });
      
      // If we can access dashboard without redirect to login, context is valid
      if (!page.url().includes('/auth/login')) {
        console.log(`✅ Stored context is valid for ${userRole}`);
        return { context, page, fromStorage: true };
      } else {
        console.log(`⚠️ Stored context expired for ${userRole}, creating new one`);
        await context.close();
      }
    } catch (error) {
      console.log(`⚠️ Failed to reuse stored context for ${userRole}:`, error.message);
      if (context) await context.close();
    }
  }

  // Create new authenticated context
  console.log(`🆕 Creating new authenticated context for ${userRole}`);
  
  context = await browser.newContext({
    baseURL: authConfig.config.baseURL
  });

  const page = await context.newPage();
  
  // Perform authentication
  await authenticate(page, userRole);

  // Save storage state if persistence is enabled
  if (authConfig.config.storage.persistContext) {
    try {
      const storageState = await context.storageState();
      storageState.timestamp = Date.now(); // Add timestamp for expiration checking
      
      require('fs').writeFileSync(storageStatePath, JSON.stringify(storageState, null, 2));
      console.log(`💾 Saved authentication context for ${userRole}`);
    } catch (error) {
      console.warn(`Failed to save storage state for ${userRole}:`, error.message);
    }
  }

  return { context, page, fromStorage: false };
}

/**
 * Verify user is authenticated and has correct role
 */
async function verifyAuthentication(page, expectedRole = null) {
  try {
    // Check if we're not on login page
    await expect(page).not.toHaveURL(/\/auth\/login/);

    // Try to access user info from localStorage or API
    const userInfo = await page.evaluate(() => {
      const userStr = localStorage.getItem('assivy_user');
      return userStr ? JSON.parse(userStr) : null;
    });

    if (expectedRole && userInfo) {
      expect(userInfo.role).toBe(expectedRole);
    }

    console.log(`✅ Authentication verified${expectedRole ? ` for role ${expectedRole}` : ''}`);
    return userInfo;

  } catch (error) {
    console.error('❌ Authentication verification failed:', error.message);
    throw error;
  }
}

/**
 * Logout user and clear authentication
 */
async function logout(page) {
  try {
    // Try to find and click logout button
    const logoutSelectors = [
      'button:has-text("Logout")',
      'button:has-text("Sign Out")',
      '[data-testid="logout-button"]',
      '.logout-button'
    ];

    for (const selector of logoutSelectors) {
      try {
        const logoutButton = page.locator(selector).first();
        if (await logoutButton.isVisible({ timeout: 2000 })) {
          await logoutButton.click();
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    // Clear localStorage
    await page.evaluate(() => {
      localStorage.removeItem('assivy_token');
      localStorage.removeItem('assivy_user');
      localStorage.clear();
    });

    // Wait for redirect to login page
    await page.waitForURL(/\/auth\/login/, { timeout: 10000 });
    
    console.log('✅ Logout successful');

  } catch (error) {
    console.warn('⚠️ Logout may have failed:', error.message);
    // Force navigation to login page
    await page.goto('/auth/login');
  }
}

/**
 * Switch user role (re-authenticate with different role)
 */
async function switchUserRole(page, newRole) {
  console.log(`🔄 Switching to user role: ${newRole}`);

  // Logout current user
  await logout(page);

  // Authenticate with new role
  await authenticate(page, newRole);

  // Verify new authentication
  await verifyAuthentication(page, newRole);

  console.log(`✅ Successfully switched to ${newRole}`);
}

/**
 * Clean up authentication contexts and storage
 */
async function cleanupAuth(userRole = null) {
  if (userRole) {
    authConfig.clearStorageState(userRole);
  } else {
    // Clear all stored contexts
    const roles = authConfig.getAvailableRoles();
    roles.forEach(role => authConfig.clearStorageState(role));
  }
  console.log(`🧹 Cleaned up authentication storage${userRole ? ` for ${userRole}` : ''}`);
}

module.exports = {
  generateJWTToken,
  loginBasedAuth,
  tokenBasedAuth,
  authenticate,
  setupAuthenticatedContext,
  verifyAuthentication,
  logout,
  switchUserRole,
  cleanupAuth
};
