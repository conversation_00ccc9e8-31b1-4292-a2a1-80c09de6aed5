#!/usr/bin/env node
/**
 * Data Library Test Runner
 * 
 * Quick script to test navigation to the data library page
 */

const { spawn } = require('child_process');
const path = require('path');

async function runDataLibraryTest() {
  console.log('🚀 Running Data Library Navigation Test');
  console.log('=' * 50);
  
  const testFile = 'tests/data-library-navigation.spec.js';
  const playwrightDir = path.join(__dirname, '..');
  
  // Set environment variables for the test
  const env = {
    ...process.env,
    PLAYWRIGHT_AUTH_METHOD: 'token', // Use fast token authentication
    PLAYWRIGHT_DEFAULT_ROLE: 'SYSTEM_ADMIN',
    PLAYWRIGHT_HEADLESS: 'false', // Run in headed mode to see the navigation
    PLAYWRIGHT_BASE_URL: 'http://localhost:4029',
    PLAYWRIGHT_BACKEND_URL: 'http://localhost:8000'
  };
  
  console.log('🔧 Test Configuration:');
  console.log(`  Authentication: ${env.PLAYWRIGHT_AUTH_METHOD}`);
  console.log(`  User Role: ${env.PLAYWRIGHT_DEFAULT_ROLE}`);
  console.log(`  Headless: ${env.PLAYWRIGHT_HEADLESS}`);
  console.log(`  Frontend URL: ${env.PLAYWRIGHT_BASE_URL}`);
  console.log(`  Backend URL: ${env.PLAYWRIGHT_BACKEND_URL}`);
  console.log('');
  
  return new Promise((resolve, reject) => {
    const playwright = spawn('npx', ['playwright', 'test', testFile, '--reporter=list'], {
      cwd: playwrightDir,
      env: env,
      stdio: 'inherit'
    });
    
    playwright.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ Data library test completed successfully!');
        console.log('\n📸 Screenshots saved to test-results/');
        console.log('📊 Full report available at: test-results/html-report/index.html');
        resolve();
      } else {
        console.log(`\n❌ Test failed with exit code ${code}`);
        reject(new Error(`Test failed with exit code ${code}`));
      }
    });
    
    playwright.on('error', (error) => {
      console.error('❌ Failed to start test:', error);
      reject(error);
    });
  });
}

// Quick test options
const commands = {
  async quick() {
    console.log('🏃‍♂️ Running quick data library access test...');
    
    const env = {
      ...process.env,
      PLAYWRIGHT_AUTH_METHOD: 'token',
      PLAYWRIGHT_DEFAULT_ROLE: 'SYSTEM_ADMIN',
      PLAYWRIGHT_HEADLESS: 'false'
    };
    
    const playwright = spawn('npx', [
      'playwright', 'test', 
      'tests/data-library-navigation.spec.js',
      '--grep', 'should navigate to data library page as System Admin',
      '--reporter=list'
    ], {
      cwd: path.join(__dirname, '..'),
      env: env,
      stdio: 'inherit'
    });
    
    return new Promise((resolve, reject) => {
      playwright.on('close', (code) => {
        code === 0 ? resolve() : reject(new Error(`Test failed: ${code}`));
      });
    });
  },
  
  async roles() {
    console.log('👥 Testing data library access for all user roles...');
    
    const env = {
      ...process.env,
      PLAYWRIGHT_AUTH_METHOD: 'token',
      PLAYWRIGHT_HEADLESS: 'false'
    };
    
    const playwright = spawn('npx', [
      'playwright', 'test', 
      'tests/data-library-navigation.spec.js',
      '--grep', 'should check data library access for different user roles',
      '--reporter=list'
    ], {
      cwd: path.join(__dirname, '..'),
      env: env,
      stdio: 'inherit'
    });
    
    return new Promise((resolve, reject) => {
      playwright.on('close', (code) => {
        code === 0 ? resolve() : reject(new Error(`Test failed: ${code}`));
      });
    });
  },
  
  async features() {
    console.log('🔍 Testing data library features and interface...');
    
    const env = {
      ...process.env,
      PLAYWRIGHT_AUTH_METHOD: 'token',
      PLAYWRIGHT_DEFAULT_ROLE: 'SYSTEM_ADMIN',
      PLAYWRIGHT_HEADLESS: 'false'
    };
    
    const playwright = spawn('npx', [
      'playwright', 'test', 
      'tests/data-library-navigation.spec.js',
      '--grep', 'should interact with data library features',
      '--reporter=list'
    ], {
      cwd: path.join(__dirname, '..'),
      env: env,
      stdio: 'inherit'
    });
    
    return new Promise((resolve, reject) => {
      playwright.on('close', (code) => {
        code === 0 ? resolve() : reject(new Error(`Test failed: ${code}`));
      });
    });
  },
  
  help() {
    console.log('📚 Data Library Test Runner');
    console.log('Usage: node test-data-library.js [command]');
    console.log('');
    console.log('Commands:');
    console.log('  (no command)  Run all data library tests');
    console.log('  quick         Run quick navigation test only');
    console.log('  roles         Test access for all user roles');
    console.log('  features      Test data library features');
    console.log('  help          Show this help');
    console.log('');
    console.log('Examples:');
    console.log('  node test-data-library.js');
    console.log('  node test-data-library.js quick');
    console.log('  node test-data-library.js roles');
  }
};

// Main execution
async function main() {
  const command = process.argv[2];
  
  if (command === 'help' || command === '--help' || command === '-h') {
    commands.help();
    return;
  }
  
  try {
    if (command && commands[command]) {
      await commands[command]();
    } else if (command) {
      console.error(`❌ Unknown command: ${command}`);
      commands.help();
      process.exit(1);
    } else {
      await runDataLibraryTest();
    }
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
