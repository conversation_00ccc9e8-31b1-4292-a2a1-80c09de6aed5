#!/usr/bin/env node
/**
 * Authentication Manager Script
 * 
 * This utility script helps manage Playwright authentication contexts
 * and configuration for the Assivy application.
 */

const { authConfig, USER_ROLES } = require('../config/auth-config');
const { cleanupAuth, generateJWTToken } = require('../helpers/auth-helpers');

const commands = {
  
  /**
   * Show current configuration
   */
  async config() {
    console.log('🔧 Current Playwright Authentication Configuration');
    console.log('=' * 50);
    console.log(`Authentication method: ${authConfig.getAuthMethod()}`);
    console.log(`Default role: ${authConfig.config.defaultRole}`);
    console.log(`Frontend URL: ${authConfig.config.baseURL}`);
    console.log(`Backend URL: ${authConfig.config.backendURL}`);
    console.log(`Context persistence: ${authConfig.config.storage.persistContext}`);
    console.log(`Context timeout: ${authConfig.config.storage.contextTimeout / 1000 / 60} minutes`);
    console.log('\n📋 Available user roles:');
    Object.keys(USER_ROLES).forEach(role => {
      const user = USER_ROLES[role];
      console.log(`  - ${role}: ${user.email} (${user.role})`);
    });
  },
  
  /**
   * Set authentication method
   */
  async setMethod(method) {
    if (!method) {
      console.error('❌ Please specify authentication method: login or token');
      process.exit(1);
    }
    
    try {
      authConfig.setAuthMethod(method);
      console.log(`✅ Authentication method set to: ${method}`);
    } catch (error) {
      console.error(`❌ Failed to set authentication method: ${error.message}`);
      process.exit(1);
    }
  },
  
  /**
   * Show authentication context status
   */
  async status() {
    console.log('📊 Authentication Context Status');
    console.log('=' * 40);
    
    const roles = Object.keys(USER_ROLES);
    
    for (const role of roles) {
      const isValid = authConfig.hasValidStorageState(role);
      const status = isValid ? '✅ Valid' : '❌ Invalid/Missing';
      const path = authConfig.getStorageStatePath(role);
      
      console.log(`${role}: ${status}`);
      console.log(`  Path: ${path}`);
      
      if (isValid) {
        try {
          const fs = require('fs');
          const state = JSON.parse(fs.readFileSync(path, 'utf8'));
          const age = Date.now() - (state.timestamp || 0);
          const ageMinutes = Math.floor(age / 1000 / 60);
          console.log(`  Age: ${ageMinutes} minutes`);
        } catch (error) {
          console.log(`  Age: Unknown`);
        }
      }
      console.log('');
    }
  },
  
  /**
   * Clean up authentication contexts
   */
  async cleanup(role) {
    if (role) {
      if (!authConfig.isValidRole(role)) {
        console.error(`❌ Invalid role: ${role}`);
        console.log(`Available roles: ${authConfig.getAvailableRoles().join(', ')}`);
        process.exit(1);
      }
      
      cleanupAuth(role);
      console.log(`✅ Cleaned up authentication context for ${role}`);
    } else {
      cleanupAuth();
      console.log('✅ Cleaned up all authentication contexts');
    }
  },
  
  /**
   * Test token generation
   */
  async testToken(role = 'SYSTEM_ADMIN') {
    if (!authConfig.isValidRole(role)) {
      console.error(`❌ Invalid role: ${role}`);
      console.log(`Available roles: ${authConfig.getAvailableRoles().join(', ')}`);
      process.exit(1);
    }
    
    console.log(`🔐 Testing token generation for ${role}...`);
    
    try {
      const { token, user } = await generateJWTToken(role, true);
      
      console.log('✅ Token generated successfully');
      console.log(`User ID: ${user.id}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Tenant ID: ${user.tenantId}`);
      console.log(`Permissions: ${user.permissions.length} permissions`);
      console.log(`Token length: ${token.length} characters`);
      
      // Decode token header and payload for inspection
      const tokenParts = token.split('.');
      const header = JSON.parse(Buffer.from(tokenParts[0], 'base64url').toString());
      const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64url').toString());
      
      console.log('\n🔍 Token Details:');
      console.log(`Algorithm: ${header.alg}`);
      console.log(`Issuer: ${payload.iss}`);
      console.log(`Audience: ${payload.aud}`);
      console.log(`Expires: ${new Date(payload.exp * 1000).toISOString()}`);
      
    } catch (error) {
      console.error(`❌ Token generation failed: ${error.message}`);
      process.exit(1);
    }
  },
  
  /**
   * Test backend connectivity
   */
  async testBackend() {
    console.log('🔍 Testing backend connectivity...');
    
    const backendURL = authConfig.config.backendURL;
    
    try {
      const response = await fetch(`${backendURL}/system/v1/status`, {
        method: 'GET',
        timeout: 10000
      });
      
      if (response.ok) {
        const status = await response.json();
        console.log('✅ Backend is accessible');
        console.log(`Environment: ${status.environment}`);
        console.log(`Version: ${status.version || 'Unknown'}`);
        console.log(`Status: ${status.status}`);
      } else {
        console.error(`❌ Backend returned error: ${response.status} ${response.statusText}`);
        process.exit(1);
      }
      
    } catch (error) {
      console.error(`❌ Failed to connect to backend: ${error.message}`);
      console.log(`Backend URL: ${backendURL}`);
      process.exit(1);
    }
  },
  
  /**
   * Initialize test environment
   */
  async init() {
    console.log('🚀 Initializing Playwright test environment...');
    
    // Test backend connectivity
    await commands.testBackend();
    
    // Test token generation for all roles
    console.log('\n🔐 Testing token generation for all roles...');
    const roles = Object.keys(USER_ROLES);
    
    for (const role of roles) {
      try {
        await generateJWTToken(role, false);
        console.log(`✅ ${role}: Token generation successful`);
      } catch (error) {
        console.error(`❌ ${role}: Token generation failed - ${error.message}`);
      }
    }
    
    // Show current configuration
    console.log('\n');
    await commands.config();
    
    console.log('\n✅ Environment initialization complete');
    console.log('\n🚀 Ready to run tests:');
    console.log('  npx playwright test');
    console.log('  npm run test:e2e');
  },
  
  /**
   * Show help
   */
  async help() {
    console.log('🔧 Playwright Authentication Manager');
    console.log('=' * 40);
    console.log('Usage: node auth-manager.js <command> [options]');
    console.log('');
    console.log('Commands:');
    console.log('  config              Show current configuration');
    console.log('  status              Show authentication context status');
    console.log('  cleanup [role]      Clean up authentication contexts');
    console.log('  set-method <method> Set authentication method (login|token)');
    console.log('  test-token [role]   Test token generation for role');
    console.log('  test-backend        Test backend connectivity');
    console.log('  init                Initialize test environment');
    console.log('  help                Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  node auth-manager.js config');
    console.log('  node auth-manager.js set-method token');
    console.log('  node auth-manager.js cleanup SYSTEM_ADMIN');
    console.log('  node auth-manager.js test-token MANAGER');
    console.log('  node auth-manager.js init');
  }
};

// Main execution
async function main() {
  const command = process.argv[2];
  const args = process.argv.slice(3);
  
  if (!command || !commands[command.replace('-', '')]) {
    await commands.help();
    process.exit(1);
  }
  
  try {
    const commandName = command.replace('-', '');
    await commands[commandName](...args);
  } catch (error) {
    console.error(`❌ Command failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = commands;
