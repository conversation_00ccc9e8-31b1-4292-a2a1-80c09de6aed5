/**
 * Global Setup for Playwright Tests
 * 
 * This setup ensures the backend is ready and creates authentication
 * contexts for all user roles before running tests.
 */

const { chromium } = require('@playwright/test');
const { authConfig, USER_ROLES } = require('../config/auth-config');
const { setupAuthenticatedContext, cleanupAuth } = require('../helpers/auth-helpers');

/**
 * Wait for backend to be ready
 */
async function waitForBackend(maxRetries = 30, retryDelay = 2000) {
  const backendURL = authConfig.config.backendURL;
  
  console.log(`🔍 Waiting for backend at ${backendURL}...`);
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(`${backendURL}/system/v1/status`, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        const status = await response.json();
        console.log(`✅ Backend is ready (environment: ${status.environment})`);
        return true;
      }
    } catch (error) {
      console.log(`⏳ Backend not ready, attempt ${i + 1}/${maxRetries}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, retryDelay));
  }
  
  throw new Error(`❌ Backend not ready after ${maxRetries} attempts`);
}

/**
 * Wait for frontend to be ready
 */
async function waitForFrontend(maxRetries = 30, retryDelay = 2000) {
  const frontendURL = authConfig.config.baseURL;
  
  console.log(`🔍 Waiting for frontend at ${frontendURL}...`);
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(frontendURL, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        console.log(`✅ Frontend is ready`);
        return true;
      }
    } catch (error) {
      console.log(`⏳ Frontend not ready, attempt ${i + 1}/${maxRetries}...`);
    }
    
    await new Promise(resolve => setTimeout(resolve, retryDelay));
  }
  
  throw new Error(`❌ Frontend not ready after ${maxRetries} attempts`);
}

/**
 * Initialize test users in the backend
 */
async function initializeTestUsers() {
  const backendURL = authConfig.config.backendURL;
  
  console.log('👥 Initializing test users...');
  
  try {
    // Initialize test tenants
    const tenantsResponse = await fetch(`${backendURL}/system/v1/test/init-tenants`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!tenantsResponse.ok) {
      console.warn('⚠️ Failed to initialize test tenants, they may already exist');
    } else {
      console.log('✅ Test tenants initialized');
    }
    
    // Initialize test users
    const usersResponse = await fetch(`${backendURL}/system/v1/test/init-users`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!usersResponse.ok) {
      console.warn('⚠️ Failed to initialize test users, they may already exist');
    } else {
      console.log('✅ Test users initialized');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to initialize test data:', error.message);
    // Don't fail setup if test data initialization fails
  }
}

/**
 * Create authentication contexts for all user roles
 */
async function createAuthenticationContexts() {
  if (!authConfig.config.storage.persistContext) {
    console.log('📝 Context persistence disabled, skipping context creation');
    return;
  }
  
  console.log('🔐 Creating authentication contexts for all user roles...');
  
  const browser = await chromium.launch({ headless: true });
  const roles = Object.keys(USER_ROLES);
  
  try {
    for (const role of roles) {
      console.log(`📝 Creating context for ${role}...`);
      
      try {
        // Skip if valid context already exists
        if (authConfig.hasValidStorageState(role)) {
          console.log(`✅ Valid context already exists for ${role}`);
          continue;
        }
        
        // Create new authenticated context
        const { context, page } = await setupAuthenticatedContext(browser, role);
        
        // Verify the context works by navigating to a protected page
        await page.goto('/dashboard', { waitUntil: 'networkidle', timeout: 30000 });
        
        if (page.url().includes('/auth/login')) {
          throw new Error('Authentication failed - redirected to login');
        }
        
        console.log(`✅ Authentication context created for ${role}`);
        await context.close();
        
      } catch (error) {
        console.error(`❌ Failed to create context for ${role}:`, error.message);
        // Clear any partial context
        authConfig.clearStorageState(role);
      }
    }
  } finally {
    await browser.close();
  }
}

/**
 * Verify authentication contexts
 */
async function verifyAuthenticationContexts() {
  console.log('🔍 Verifying authentication contexts...');
  
  const roles = Object.keys(USER_ROLES);
  const validContexts = [];
  const invalidContexts = [];
  
  for (const role of roles) {
    if (authConfig.hasValidStorageState(role)) {
      validContexts.push(role);
    } else {
      invalidContexts.push(role);
    }
  }
  
  console.log(`✅ Valid contexts: ${validContexts.join(', ')}`);
  if (invalidContexts.length > 0) {
    console.log(`⚠️ Invalid contexts: ${invalidContexts.join(', ')}`);
  }
  
  return validContexts.length > 0;
}

/**
 * Main global setup function
 */
async function globalSetup() {
  console.log('🚀 Starting Playwright Global Setup');
  console.log('=' * 50);
  
  try {
    // Step 1: Wait for services to be ready
    await waitForBackend();
    await waitForFrontend();
    
    // Step 2: Initialize test data
    await initializeTestUsers();
    
    // Step 3: Create authentication contexts
    await createAuthenticationContexts();
    
    // Step 4: Verify contexts
    const hasValidContexts = await verifyAuthenticationContexts();
    
    if (!hasValidContexts && authConfig.config.storage.persistContext) {
      console.warn('⚠️ No valid authentication contexts found');
    }
    
    console.log('=' * 50);
    console.log('✅ Global setup completed successfully');
    console.log(`🔐 Authentication method: ${authConfig.getAuthMethod()}`);
    console.log(`👤 Default role: ${authConfig.config.defaultRole}`);
    console.log(`🌐 Frontend URL: ${authConfig.config.baseURL}`);
    console.log(`🔧 Backend URL: ${authConfig.config.backendURL}`);
    
  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    throw error;
  }
}

module.exports = globalSetup;
