/**
 * Global Teardown for Playwright Tests
 * 
 * This teardown cleans up authentication contexts and test data
 * after all tests have completed.
 */

const { authConfig } = require('../config/auth-config');
const { cleanupAuth } = require('../helpers/auth-helpers');

/**
 * Clean up test data from backend
 */
async function cleanupTestData() {
  const backendURL = authConfig.config.backendURL;
  
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Clean up test resources
    const resourcesResponse = await fetch(`${backendURL}/system/v1/test/cleanup-resources`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (resourcesResponse.ok) {
      console.log('✅ Test resources cleaned up');
    } else {
      console.warn('⚠️ Failed to clean up test resources');
    }
    
    // Clean up test tasks
    const tasksResponse = await fetch(`${backendURL}/system/v1/test/cleanup-tasks`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (tasksResponse.ok) {
      console.log('✅ Test tasks cleaned up');
    } else {
      console.warn('⚠️ Failed to clean up test tasks');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to clean up test data:', error.message);
    // Don't fail teardown if cleanup fails
  }
}

/**
 * Clean up authentication contexts
 */
async function cleanupAuthenticationContexts() {
  console.log('🧹 Cleaning up authentication contexts...');
  
  try {
    // Clean up all stored authentication contexts
    cleanupAuth();
    console.log('✅ Authentication contexts cleaned up');
    
  } catch (error) {
    console.warn('⚠️ Failed to clean up authentication contexts:', error.message);
  }
}

/**
 * Generate test report summary
 */
async function generateTestSummary() {
  console.log('📊 Test Execution Summary');
  console.log('=' * 30);
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Check if results file exists
    const resultsPath = path.join(__dirname, '..', 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log(`📈 Total tests: ${results.stats?.total || 'Unknown'}`);
      console.log(`✅ Passed: ${results.stats?.passed || 'Unknown'}`);
      console.log(`❌ Failed: ${results.stats?.failed || 'Unknown'}`);
      console.log(`⏭️ Skipped: ${results.stats?.skipped || 'Unknown'}`);
      console.log(`⏱️ Duration: ${results.stats?.duration || 'Unknown'}ms`);
      
      if (results.stats?.failed > 0) {
        console.log('\n❌ Failed Tests:');
        results.suites?.forEach(suite => {
          suite.specs?.forEach(spec => {
            spec.tests?.forEach(test => {
              if (test.results?.some(result => result.status === 'failed')) {
                console.log(`  - ${suite.title}: ${spec.title}`);
              }
            });
          });
        });
      }
    } else {
      console.log('📝 No test results file found');
    }
    
  } catch (error) {
    console.warn('⚠️ Failed to generate test summary:', error.message);
  }
}

/**
 * Main global teardown function
 */
async function globalTeardown() {
  console.log('🏁 Starting Playwright Global Teardown');
  console.log('=' * 50);
  
  try {
    // Step 1: Generate test summary
    await generateTestSummary();
    
    // Step 2: Clean up test data
    await cleanupTestData();
    
    // Step 3: Clean up authentication contexts (optional)
    const shouldCleanupContexts = process.env.PLAYWRIGHT_CLEANUP_CONTEXTS === 'true' || process.env.CI;
    
    if (shouldCleanupContexts) {
      await cleanupAuthenticationContexts();
    } else {
      console.log('💾 Keeping authentication contexts for next run');
    }
    
    console.log('=' * 50);
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error.message);
    // Don't throw error in teardown to avoid masking test failures
  }
}

module.exports = globalTeardown;
