# Playwright Authentication System for Assivy

This directory contains a comprehensive Playwright testing framework with configurable authentication for the Assivy application. The system supports both login-based and direct token injection authentication methods.

## 🏗️ Architecture Overview

```
tests/playwright/
├── config/
│   ├── auth-config.js          # Authentication configuration
│   └── test-config.json        # Runtime configuration (auto-generated)
├── helpers/
│   └── auth-helpers.js         # Authentication helper functions
├── setup/
│   ├── global-setup.js         # Global test setup
│   └── global-teardown.js      # Global test cleanup
├── storage/                    # Persistent browser contexts (auto-generated)
│   ├── auth-state-system_admin.json
│   ├── auth-state-tenant_admin.json
│   ├── auth-state-manager.json
│   └── auth-state-member.json
├── tests/
│   ├── auth.setup.js           # Authentication setup tests
│   ├── example-login-auth.spec.js    # Login-based auth examples
│   └── example-token-auth.spec.js    # Token-based auth examples
├── test-results/               # Test outputs (auto-generated)
├── playwright.config.js        # Main Playwright configuration
└── README.md                   # This file
```

## 🔐 Authentication Methods

### 1. Login-based Authentication

**Use Case**: Integration tests that need to verify the complete login flow.

**How it works**:
- Navigates to `/auth/login`
- Fills in username/password from configuration
- Submits the form and waits for successful authentication
- Captures resulting JWT tokens and cookies

**Example**:
```javascript
const { loginBasedAuth } = require('../helpers/auth-helpers');

test('login authentication example', async ({ page }) => {
  await loginBasedAuth(page, 'SYSTEM_ADMIN');
  await page.goto('/dashboard');
  await expect(page).not.toHaveURL(/\/auth\/login/);
});
```

### 2. Direct Token Injection

**Use Case**: Fast test execution where login UI testing is not required.

**How it works**:
- Generates JWT token using backend API
- Injects token directly into browser cookies and localStorage
- Bypasses login UI entirely for faster test execution

**Example**:
```javascript
const { tokenBasedAuth } = require('../helpers/auth-helpers');

test('token authentication example', async ({ page }) => {
  await tokenBasedAuth(page, 'SYSTEM_ADMIN');
  await page.goto('/dashboard');
  await expect(page).not.toHaveURL(/\/auth\/login/);
});
```

## 👥 User Roles

The system supports four predefined user roles with different permission sets:

| Role | Permissions | Use Case |
|------|-------------|----------|
| **SYSTEM_ADMIN** | All permissions including system-level access | Testing admin features, system management |
| **TENANT_ADMIN** | All tenant-level permissions | Testing tenant management, user administration |
| **MANAGER** | Team and resource management permissions | Testing team features, resource management |
| **MEMBER** | Read-only permissions for basic features | Testing user-level functionality |

## ⚙️ Configuration

### Environment Variables

```bash
# Application URLs
PLAYWRIGHT_BASE_URL=http://localhost:4029          # Frontend URL
PLAYWRIGHT_BACKEND_URL=http://localhost:8000       # Backend API URL

# Authentication method: 'login' or 'token'
PLAYWRIGHT_AUTH_METHOD=token

# Default user role for tests
PLAYWRIGHT_DEFAULT_ROLE=SYSTEM_ADMIN

# Browser settings
PLAYWRIGHT_HEADLESS=false                          # Run in headed mode
PLAYWRIGHT_VIDEO=retain-on-failure                 # Video recording
PLAYWRIGHT_SCREENSHOT=only-on-failure              # Screenshot capture

# Cleanup settings
PLAYWRIGHT_CLEANUP_CONTEXTS=false                  # Keep auth contexts between runs
PLAYWRIGHT_CLEANUP_AFTER_TESTS=false               # Clean up after individual tests

# Server settings
PLAYWRIGHT_SKIP_SERVER=false                       # Skip starting dev server
```

### Configuration File

The system automatically creates `config/test-config.json` with runtime settings:

```json
{
  "authMethod": "token",
  "defaultRole": "SYSTEM_ADMIN",
  "baseURL": "http://localhost:4029",
  "backendURL": "http://localhost:8000",
  "timeout": {
    "login": 30000,
    "navigation": 30000,
    "assertion": 10000
  },
  "retries": {
    "login": 3,
    "tokenGeneration": 3
  },
  "storage": {
    "persistContext": true,
    "contextTimeout": 86400000,
    "clearOnFailure": true
  },
  "security": {
    "cookieName": "assivy_auth_token",
    "tokenStorageKey": "assivy_token",
    "userStorageKey": "assivy_user"
  }
}
```

## 🚀 Getting Started

### 1. Installation

```bash
# Install Playwright
npm install -D @playwright/test

# Install browsers
npx playwright install
```

### 2. Basic Usage

```bash
# Run all tests with default settings
npx playwright test

# Run with specific authentication method
PLAYWRIGHT_AUTH_METHOD=login npx playwright test

# Run with specific user role
PLAYWRIGHT_DEFAULT_ROLE=MANAGER npx playwright test

# Run in headed mode
PLAYWRIGHT_HEADLESS=false npx playwright test

# Run specific test file
npx playwright test example-token-auth.spec.js
```

### 3. Project-specific Runs

```bash
# Run tests for specific browser and auth method
npx playwright test --project=chromium-token

# Run tests for specific user role
npx playwright test --project=manager

# Run setup only
npx playwright test --project=setup
```

## 📝 Writing Tests

### Basic Test Structure

```javascript
const { test, expect } = require('@playwright/test');
const { authenticate, verifyAuthentication } = require('../helpers/auth-helpers');

test.describe('My Feature Tests', () => {
  
  test('should test protected feature', async ({ page }) => {
    // Authenticate (method determined by configuration)
    await authenticate(page, 'SYSTEM_ADMIN');
    
    // Navigate to feature
    await page.goto('/my-feature');
    
    // Verify not redirected to login
    await expect(page).not.toHaveURL(/\/auth\/login/);
    
    // Test your feature
    await page.click('[data-testid="my-button"]');
    await expect(page.locator('.result')).toBeVisible();
  });
  
});
```

### Using Stored Authentication Context

```javascript
// This automatically uses stored authentication context
test.use({ storageState: 'storage/auth-state-system_admin.json' });

test('should use stored authentication', async ({ page }) => {
  // No need to authenticate - context is already authenticated
  await page.goto('/dashboard');
  await expect(page).not.toHaveURL(/\/auth\/login/);
});
```

### Testing Different User Roles

```javascript
test.describe('Role-based Access Tests', () => {
  
  test('admin should access system settings', async ({ page }) => {
    await authenticate(page, 'SYSTEM_ADMIN');
    await page.goto('/system/settings');
    await expect(page).not.toHaveURL(/\/auth\/login/);
  });
  
  test('member should not access system settings', async ({ page }) => {
    await authenticate(page, 'MEMBER');
    await page.goto('/system/settings');
    // Should be redirected or show access denied
    // Implementation depends on your app's behavior
  });
  
});
```

### Switching User Roles

```javascript
const { switchUserRole } = require('../helpers/auth-helpers');

test('should handle role switching', async ({ page }) => {
  // Start as admin
  await authenticate(page, 'SYSTEM_ADMIN');
  await verifyAuthentication(page, 'SystemAdmin');
  
  // Switch to manager
  await switchUserRole(page, 'MANAGER');
  await verifyAuthentication(page, 'Manager');
});
```

## 🔧 Helper Functions

### Authentication Helpers

```javascript
// Generate JWT token
const { token, user } = await generateJWTToken('SYSTEM_ADMIN', true);

// Login-based authentication
await loginBasedAuth(page, 'MANAGER');

// Token-based authentication
await tokenBasedAuth(page, 'MEMBER');

// Generic authentication (uses configured method)
await authenticate(page, 'SYSTEM_ADMIN');

// Setup authenticated browser context
const { context, page } = await setupAuthenticatedContext(browser, 'MANAGER');

// Verify authentication
await verifyAuthentication(page, 'SystemAdmin');

// Logout
await logout(page);

// Switch user role
await switchUserRole(page, 'MEMBER');

// Clean up authentication storage
cleanupAuth('SYSTEM_ADMIN'); // Specific role
cleanupAuth(); // All roles
```

## 🎯 Best Practices

### 1. Choose the Right Authentication Method

- **Use Token Authentication** for most tests (faster execution)
- **Use Login Authentication** when testing the login flow specifically

### 2. Leverage Persistent Contexts

- Enable `persistContext: true` in configuration
- Reuse authentication contexts across test runs
- Clean up contexts only when necessary

### 3. Test Different User Roles

- Create role-specific test suites
- Use the project-based configuration for parallel role testing
- Verify permission boundaries

### 4. Handle Authentication Failures

```javascript
test('should handle auth failures gracefully', async ({ page }) => {
  try {
    await authenticate(page, 'INVALID_ROLE');
  } catch (error) {
    expect(error.message).toContain('Unknown user role');
  }
});
```

### 5. Clean Up Resources

```javascript
test.afterEach(async ({ page }) => {
  // Clean up test data
  await page.evaluate(() => {
    // Clear any test-specific data
  });
});

test.afterAll(async () => {
  // Clean up authentication contexts if needed
  if (process.env.CI) {
    cleanupAuth();
  }
});
```

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Context Not Found**
   ```bash
   # Clear and recreate contexts
   rm -rf storage/
   npx playwright test --project=setup
   ```

2. **Backend Not Ready**
   ```bash
   # Check backend status
   curl http://localhost:8000/system/v1/status
   
   # Wait for backend in setup
   PLAYWRIGHT_BACKEND_URL=http://localhost:8000 npx playwright test
   ```

3. **Token Generation Fails**
   ```bash
   # Check backend JWT configuration
   # Ensure JWT_SECRET_KEY is set
   # Verify test token endpoints are available
   ```

4. **Tests Fail in CI**
   ```bash
   # Use CI-specific settings
   PLAYWRIGHT_HEADLESS=true PLAYWRIGHT_CLEANUP_CONTEXTS=true npx playwright test
   ```

### Debug Mode

```bash
# Run with debug output
DEBUG=pw:api npx playwright test

# Run in debug mode with browser
npx playwright test --debug

# Generate trace
npx playwright test --trace on
```

## 📊 Performance Optimization

- **Token authentication** is ~10x faster than login authentication
- **Persistent contexts** reduce setup time by ~90%
- **Parallel execution** with role-specific projects maximizes throughput
- **Selective cleanup** preserves contexts between runs

This authentication system provides a robust, flexible foundation for testing the Assivy application with proper security and performance considerations.

## 📦 Package Configuration

Add to your `package.json`:

```json
{
  "scripts": {
    "test:e2e": "playwright test",
    "test:e2e:headed": "PLAYWRIGHT_HEADLESS=false playwright test",
    "test:e2e:login": "PLAYWRIGHT_AUTH_METHOD=login playwright test",
    "test:e2e:token": "PLAYWRIGHT_AUTH_METHOD=token playwright test",
    "test:e2e:setup": "playwright test --project=setup",
    "test:e2e:cleanup": "playwright test --project=cleanup",
    "test:e2e:report": "playwright show-report",
    "test:e2e:ui": "playwright test --ui"
  },
  "devDependencies": {
    "@playwright/test": "^1.40.0"
  }
}
```
