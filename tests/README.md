# Testing Guide for Assivy Backend

This directory contains a comprehensive test suite for the Assivy Backend FastAPI application, following Python testing best practices.

## Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Pytest configuration and global fixtures
├── pytest.ini                 # Pytest settings and markers
├── utils.py                    # Test utilities and helpers
├── fixtures/                   # Shared test fixtures
│   ├── __init__.py
│   ├── auth_fixtures.py        # Authentication-related fixtures
│   └── resource_fixtures.py    # Resource-related fixtures
├── unit/                       # Unit tests
│   ├── __init__.py
│   ├── test_auth_logic.py      # Auth utilities unit tests
│   └── test_resource_models.py # Model validation unit tests
└── integration/                # Integration tests
    ├── __init__.py
    ├── test_article_resources.py      # Article endpoint tests
    └── test_article_resource_legacy.py # Legacy test file (for reference)
```

## Test Categories

### Unit Tests (`tests/unit/`)
- Test individual components in isolation
- Fast execution (< 100ms per test)
- No external dependencies (database, network, file system)
- Mock all external dependencies
- Focus on business logic, validation, and edge cases

### Integration Tests (`tests/integration/`)
- Test API endpoints and service interactions
- Use real FastAPI test client
- May use test database or mocked services
- Test complete request/response cycles
- Verify API contracts and data flow

## Running Tests

### Prerequisites

Install test dependencies:
```bash
uv add --group test pytest pytest-asyncio pytest-cov pytest-mock httpx factory-boy faker freezegun
```

### Run All Tests
```bash
uv run pytest
```

### Run Specific Test Categories
```bash
# Run only unit tests
uv run pytest -m unit

# Run only integration tests
uv run pytest -m integration

# Run tests excluding slow ones
uv run pytest -m "not slow"

# Run tests with authentication
uv run pytest -m auth
```

### Run Specific Test Files
```bash
# Run article resource tests
uv run pytest tests/integration/test_article_resources.py

# Run model tests
uv run pytest tests/unit/test_resource_models.py
```

### Run with Coverage
```bash
# Run with coverage report
uv run pytest --cov=. --cov-report=html

# View coverage report
open htmlcov/index.html
```

### Run with Verbose Output
```bash
uv run pytest -v
```

## Test Markers

The test suite uses pytest markers to categorize tests:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests  
- `@pytest.mark.slow` - Slow-running tests
- `@pytest.mark.auth` - Tests requiring authentication
- `@pytest.mark.database` - Tests requiring database access
- `@pytest.mark.external` - Tests requiring external services

## Fixtures

### Global Fixtures (conftest.py)
- `test_client` - FastAPI test client
- `async_client` - Async HTTP client
- `auth_token` - Valid authentication token
- `auth_headers` - Authentication headers
- `api_base_url` - Base API URL

### Resource Fixtures (fixtures/resource_fixtures.py)
- `article_create_payload` - Valid article data
- `article_update_payload` - Article update data
- `invalid_article_payloads` - Invalid article data for error testing
- `pagination_params` - Pagination parameters
- `search_params` - Search parameters

### Auth Fixtures (fixtures/auth_fixtures.py)
- `mock_user_data` - Mock regular user
- `mock_superuser_data` - Mock superuser
- `invalid_tokens` - Invalid JWT tokens
- `unauthorized_headers` - Invalid auth headers

## Writing New Tests

### Unit Test Example
```python
import pytest
from mymodule import MyClass

@pytest.mark.unit
class TestMyClass:
    def test_method_with_valid_input(self):
        """Test method with valid input."""
        obj = MyClass()
        result = obj.my_method("valid_input")
        assert result == "expected_output"
        
    def test_method_with_invalid_input(self):
        """Test method with invalid input."""
        obj = MyClass()
        with pytest.raises(ValueError):
            obj.my_method("invalid_input")
```

### Integration Test Example
```python
import pytest
from fastapi.testclient import TestClient

@pytest.mark.integration
@pytest.mark.auth
class TestMyEndpoint:
    def test_create_resource_success(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: dict
    ):
        """Test successful resource creation."""
        payload = {"name": "test resource"}
        response = test_client.post(
            f"{api_base_url}/resources", 
            json=payload, 
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "test resource"
```

## Testing Best Practices

### General Guidelines
1. **Test Naming**: Use descriptive names that explain what is being tested
2. **Test Structure**: Follow Arrange-Act-Assert pattern
3. **One Assertion Per Test**: Each test should verify one specific behavior
4. **Independent Tests**: Tests should not depend on each other
5. **Fast Tests**: Keep unit tests fast (< 100ms each)

### Mocking Guidelines
1. **Mock External Dependencies**: Database, APIs, file system, etc.
2. **Use pytest-mock**: Prefer `mocker` fixture over `unittest.mock`
3. **Mock at the Right Level**: Mock at the boundary of your code
4. **Verify Mock Calls**: Assert that mocks are called with expected parameters

### Data Management
1. **Use Fixtures**: Create reusable test data with fixtures
2. **Factory Pattern**: Use factory-boy for complex test data generation
3. **Isolation**: Each test should use fresh test data
4. **Cleanup**: Clean up test data after each test

### Error Testing
1. **Test Error Cases**: Include tests for error conditions
2. **Validate Error Messages**: Check that error messages are helpful
3. **Test Error Codes**: Verify correct HTTP status codes
4. **Edge Cases**: Test boundary conditions and edge cases

## Continuous Integration

### Pre-commit Hooks
```bash
# Install pre-commit
uv add --group dev pre-commit

# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

### CI Pipeline
The test suite is designed to run in CI environments:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    uv run pytest --cov=. --cov-report=xml
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.xml
```

## Debugging Tests

### Run Single Test with Debug Output
```bash
uv run pytest tests/unit/test_resource_models.py::TestResourceStatus::test_resource_status_values -v -s
```

### Use Debugger
```python
import pytest

def test_something():
    # Add breakpoint
    pytest.set_trace()
    # Your test code here
```

### View Test Output
```bash
# Show print statements
uv run pytest -s

# Show debug logs
uv run pytest --log-cli-level=DEBUG
```

## Performance Testing

For performance-critical components, consider adding performance tests:

```python
import pytest
import time

@pytest.mark.slow
def test_operation_performance():
    """Test that operation completes within acceptable time."""
    start_time = time.time()
    
    # Perform operation
    result = expensive_operation()
    
    end_time = time.time()
    duration = end_time - start_time
    
    assert duration < 5.0  # Should complete within 5 seconds
    assert result is not None
```

## Documentation

- Keep test docstrings clear and concise
- Document complex test setups
- Explain why certain mocks or fixtures are needed
- Update this README when adding new test patterns

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the project root is in Python path
2. **Async Test Issues**: Use `@pytest.mark.asyncio` for async tests
3. **Database Connection**: Check test database configuration
4. **Authentication Failures**: Verify test token generation

### Getting Help

- Check pytest documentation: https://docs.pytest.org/
- Review FastAPI testing guide: https://fastapi.tiangolo.com/tutorial/testing/
- Search existing tests for similar patterns

# Testing Guide for AI Agents

This guide provides comprehensive information for AI agents (like Cursor) to fully automate testing and debugging of protected routes with tokens in the Assivy Backend.

## 🔐 Role-Based Authentication System

The system uses **role-based authentication** instead of superusers. All access is controlled by roles and permissions:

### Available Roles
- **SystemAdmin**: Full system access (all permissions)
- **TenantAdmin**: Tenant-level access (all non-system permissions)
- **Manager**: Management access (agents, resources, tasks, team)
- **Member**: Basic access (read operations)

### Key Changes
- ❌ No more superuser concept
- ✅ Everything controlled by roles and permissions
- ✅ Multi-tenant isolation
- ✅ Granular permission control

## 🧪 Testing Utilities

### 1. Role-Based Auth Test Helper

The `RoleBasedAuthTestHelper` class provides comprehensive testing utilities:

```python
from tests.utils.auth_test_helpers import RoleBasedAuthTestHelper

# Create helper instance
auth_helper = RoleBasedAuthTestHelper(test_client, "/api")

# Get tokens for different roles
system_admin_token = auth_helper.get_system_admin_token()
tenant_admin_token = auth_helper.get_tenant_admin_token()
manager_token = auth_helper.get_manager_token()
member_token = auth_helper.get_member_token()

# Get headers for testing
system_admin_headers = auth_helper.get_role_headers("system_admin")
member_headers = auth_helper.get_role_headers("member")
```

### 2. Pytest Fixtures

Use these fixtures in your tests:

```python
def test_protected_endpoint(system_admin_headers, member_headers):
    # Test with system admin
    response = test_client.get("/api/resources", headers=system_admin_headers)
    assert response.status_code == 200
    
    # Test with member
    response = test_client.get("/api/resources", headers=member_headers)
    assert response.status_code == 200
```

Available fixtures:
- `system_admin_token`, `system_admin_headers`
- `tenant_admin_token`, `tenant_admin_headers`
- `manager_token`, `manager_headers`
- `member_token`, `member_headers`
- `auth_token`, `auth_headers` (legacy, maps to system admin)

## 🚀 Best Practices for AI Agents

### 1. Test All Authentication Scenarios

```python
def test_comprehensive_auth(auth_helper):
    # Test all authentication scenarios
    result = auth_helper.test_authentication_scenarios("GET", "/resources")
    
    # Generate report
    from tests.utils.auth_test_helpers import generate_test_report
    report = generate_test_report(result)
    print(report)
    
    # Assert all scenarios passed
    assert result["summary"]["success_rate"] == "100.0%"
```

### 2. Test Role-Based Access Control

```python
def test_role_based_access(auth_helper):
    # Test with different roles
    result = auth_helper.test_role_based_access(
        "POST", "/resources",
        expected_results={
            "system_admin": 201,    # Can create
            "tenant_admin": 201,    # Can create
            "manager": 201,         # Can create
            "member": 403           # Cannot create
        }
    )
    
    assert result["summary"]["success_rate"] == "100.0%"
```

### 3. Test Multi-Tenant Isolation

```python
def test_tenant_isolation(auth_helper):
    # Test that tenants can't access each other's data
    result = auth_helper.test_multi_tenant_isolation("GET", "/resources")
    
    assert result["isolation_verified"] == True
```

### 4. Test Permission Boundaries

```python
def test_permission_boundaries(auth_helper):
    # Test system admin can access system endpoints
    system_result = auth_helper.test_protected_endpoint(
        "GET", "/system/db/collections",
        headers=auth_helper.get_role_headers("system_admin")
    )
    assert system_result["success"] == True
    
    # Test member cannot access system endpoints
    member_result = auth_helper.test_protected_endpoint(
        "GET", "/system/db/collections",
        headers=auth_helper.get_role_headers("member"),
        expected_status=403
    )
    assert member_result["success"] == True
```

## 🔧 Development Testing Endpoints

The system provides development endpoints for testing (only available in development mode):

### Token Generation
```bash
# Generate system admin token
curl -X POST http://localhost:8000/api/system/dev/token/system-admin

# Generate tenant admin token
curl -X POST http://localhost:8000/api/system/dev/token/tenant-admin

# Generate manager token
curl -X POST http://localhost:8000/api/system/dev/token/manager

# Generate member token
curl -X POST http://localhost:8000/api/system/dev/token/member
```

### Authentication Testing
```bash
# Test authentication
curl -X GET http://localhost:8000/api/system/dev/test-auth \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test bypass context
curl -X GET http://localhost:8000/api/system/dev/test-bypass
```

## 📋 Testing Checklist for AI Agents

### Authentication Tests
- [ ] Test without authentication (should return 401)
- [ ] Test with expired token (should return 401)
- [ ] Test with malformed token (should return 401)
- [ ] Test with wrong signature (should return 401)
- [ ] Test with empty token (should return 401)
- [ ] Test with wrong auth scheme (should return 401)

### Role-Based Tests
- [ ] Test system admin access (should work for everything)
- [ ] Test tenant admin access (should work for tenant resources)
- [ ] Test manager access (should work for management tasks)
- [ ] Test member access (should work for read operations)
- [ ] Test insufficient permissions (should return 403)

### Multi-Tenant Tests
- [ ] Test tenant isolation
- [ ] Test cross-tenant access prevention
- [ ] Test tenant-specific data access

### Permission Tests
- [ ] Test system-level permissions
- [ ] Test tenant-level permissions
- [ ] Test resource-specific permissions

## 🛠️ Debugging Tools

### 1. Validate Authentication Setup

```python
from tests.utils.auth_test_helpers import validate_authentication_setup

# Check if authentication is properly configured
validation = validate_authentication_setup(test_client)
if not validation["setup_valid"]:
    print("Authentication setup issues:")
    for rec in validation["recommendations"]:
        print(f"  - {rec}")
```

### 2. Generate Test Reports

```python
from tests.utils.auth_test_helpers import generate_test_report

# Generate human-readable test reports
report = generate_test_report(test_results)
print(report)
```

### 3. Custom Token Creation

```python
# Create custom token with specific permissions
custom_token = auth_helper.create_custom_role_token(
    permissions=["read_resource", "create_resource"],
    email="<EMAIL>"
)

# Create expired token for testing
expired_token = auth_helper.create_expired_token("system_admin")

# Create invalid tokens
invalid_tokens = auth_helper.create_invalid_token("malformed")
```

## 🎯 Example Test Patterns

### Pattern 1: Comprehensive Endpoint Testing

```python
@pytest.mark.integration
@pytest.mark.auth
class TestResourceEndpoints:
    def test_resource_crud_operations(self, auth_helper):
        """Test CRUD operations with different roles"""
        
        # Test creation
        create_result = auth_helper.test_role_based_access(
            "POST", "/resources",
            test_data={"name": "Test Resource"},
            expected_results={
                "system_admin": 201,
                "tenant_admin": 201,
                "manager": 201,
                "member": 403
            }
        )
        assert create_result["summary"]["success_rate"] == "100.0%"
        
        # Test reading
        read_result = auth_helper.test_role_based_access(
            "GET", "/resources",
            expected_results={
                "system_admin": 200,
                "tenant_admin": 200,
                "manager": 200,
                "member": 200
            }
        )
        assert read_result["summary"]["success_rate"] == "100.0%"
```

### Pattern 2: Permission Boundary Testing

```python
def test_system_endpoint_permissions(self, auth_helper):
    """Test that only system admin can access system endpoints"""
    
    # System admin should have access
    result = auth_helper.test_protected_endpoint(
        "GET", "/system/db/collections",
        headers=auth_helper.get_role_headers("system_admin")
    )
    assert result["success"] == True
    
    # Other roles should be denied
    for role in ["tenant_admin", "manager", "member"]:
        result = auth_helper.test_protected_endpoint(
            "GET", "/system/db/collections",
            headers=auth_helper.get_role_headers(role),
            expected_status=403
        )
        assert result["success"] == True
```

### Pattern 3: Multi-Tenant Testing

```python
def test_tenant_isolation(self, auth_helper):
    """Test that tenants cannot access each other's data"""
    
    tenant_a_id = str(uuid4())
    tenant_b_id = str(uuid4())
    
    # Create resource in tenant A
    tenant_a_headers = auth_helper.get_role_headers("tenant_admin", tenant_a_id)
    create_result = auth_helper.test_protected_endpoint(
        "POST", "/resources",
        headers=tenant_a_headers,
        json_data={"name": "Tenant A Resource"}
    )
    assert create_result["success"] == True
    
    # Try to access from tenant B (should fail or return empty)
    tenant_b_headers = auth_helper.get_role_headers("tenant_admin", tenant_b_id)
    list_result = auth_helper.test_protected_endpoint(
        "GET", "/resources",
        headers=tenant_b_headers
    )
    assert list_result["success"] == True
```

## 🚨 Common Issues and Solutions

### Issue 1: Token Generation Fails
**Symptoms**: 500 error when generating tokens
**Solution**: Check if development mode is enabled and dev routes are accessible

### Issue 2: Permission Denied Unexpectedly
**Symptoms**: 403 errors when expecting 200
**Solution**: Check if the endpoint requires specific permissions and the role has them

### Issue 3: Multi-Tenant Isolation Not Working
**Symptoms**: Tenants can see each other's data
**Solution**: Check if tenant filtering is properly implemented in the endpoint

### Issue 4: Tests Pass in Isolation but Fail Together
**Symptoms**: Tests work individually but fail when run together
**Solution**: Use function-scoped fixtures and ensure proper cleanup

## 📚 Additional Resources

- [Authentication Testing Guide](../docs/authentication_testing_guide.md)
- [Role and Permission Models](../models/role.py)
- [Middleware Permission System](../middleware/permission.py)
- [Running Context Management](../session/running_context.py)

## 🤖 AI Agent Optimization Tips

1. **Use the helper classes**: Don't manually create tokens, use `RoleBasedAuthTestHelper`
2. **Test all scenarios**: Always test authentication, authorization, and multi-tenant isolation
3. **Generate reports**: Use `generate_test_report()` for clear debugging information
4. **Validate setup**: Use `validate_authentication_setup()` to catch configuration issues early
5. **Follow patterns**: Use the provided test patterns for consistency
6. **Check permissions**: Always verify that the correct permissions are required for each endpoint

This testing system is designed to be comprehensive and AI-agent friendly, providing all the tools needed for automated testing and debugging of protected routes.
