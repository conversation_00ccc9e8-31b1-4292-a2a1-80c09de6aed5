import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4, UUID
from datetime import datetime, timezone

from services.shelf_service import ShelfService
from models.resource import Shelf, ResourceType
from models.library import Library
from fastapi import HTTPException


class TestShelfService:
    """Test cases for ShelfService"""

    @pytest.fixture
    def shelf_service(self):
        """Create a ShelfService instance for testing"""
        return ShelfService()

    @pytest.fixture
    def mock_repositories(self):
        """Mock repositories for testing"""
        return {
            'shelf_repo': AsyncMock(),
            'library_repo': AsyncMock(),
            'file_repo': AsyncMock(),
            'article_repo': AsyncMock(),
            'web_repo': AsyncMock()
        }

    @pytest.fixture
    def sample_library(self):
        """Sample library for testing"""
        return Library(
            id=uuid4(),
            name="Test Library",
            description="A test library",
            owner_id=uuid4(),
            tenant_id=uuid4(),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            created_by=uuid4()
        )

    @pytest.fixture
    def sample_shelf_data(self):
        """Sample shelf data for testing"""
        return {
            'library_id': str(uuid4()),
            'name': 'Test Shelf',
            'description': 'A test shelf',
            'resource_type': 'file',
            'tenant_id': str(uuid4())
        }

    @pytest.mark.asyncio
    async def test_create_shelf_success(self, shelf_service, mock_repositories, sample_library, sample_shelf_data):
        """Test successful shelf creation"""
        user_id = uuid4()
        
        # Mock repository initialization
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.shelf_repo = mock_repositories['shelf_repo']
            shelf_service.library_repo = mock_repositories['library_repo']
            
            # Mock library exists and user has access
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            sample_library.owner_id = user_id
            
            # Mock no existing shelf with same name
            mock_repositories['shelf_repo'].get_all.return_value = []
            
            # Mock shelf creation
            created_shelf = Shelf(
                id=uuid4(),
                library_id=UUID(sample_shelf_data['library_id']),
                name=sample_shelf_data['name'],
                description=sample_shelf_data['description'],
                resource_type=ResourceType.FILE,
                resource_count=0,
                tenant_id=UUID(sample_shelf_data['tenant_id']),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )
            mock_repositories['shelf_repo'].create.return_value = created_shelf
            
            # Test shelf creation
            result = await shelf_service.create_shelf(sample_shelf_data, user_id)
            
            # Assertions
            assert result.name == sample_shelf_data['name']
            assert result.resource_type == ResourceType.FILE
            assert result.resource_count == 0
            mock_repositories['library_repo'].get_by_id.assert_called_once()
            mock_repositories['shelf_repo'].create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_shelf_library_not_found(self, shelf_service, mock_repositories, sample_shelf_data):
        """Test shelf creation when library doesn't exist"""
        user_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.library_repo = mock_repositories['library_repo']
            
            # Mock library not found
            mock_repositories['library_repo'].get_by_id.return_value = None
            
            # Test shelf creation should raise HTTPException
            with pytest.raises(HTTPException) as exc_info:
                await shelf_service.create_shelf(sample_shelf_data, user_id)
            
            assert exc_info.value.status_code == 404
            assert "Library not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_create_shelf_access_denied(self, shelf_service, mock_repositories, sample_library, sample_shelf_data):
        """Test shelf creation when user doesn't have access to library"""
        user_id = uuid4()
        different_user_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.library_repo = mock_repositories['library_repo']
            
            # Mock library exists but owned by different user
            sample_library.owner_id = different_user_id
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            
            # Test shelf creation should raise HTTPException
            with pytest.raises(HTTPException) as exc_info:
                await shelf_service.create_shelf(sample_shelf_data, user_id)
            
            assert exc_info.value.status_code == 403
            assert "Access denied" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_create_shelf_duplicate_name(self, shelf_service, mock_repositories, sample_library, sample_shelf_data):
        """Test shelf creation when shelf with same name already exists"""
        user_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.shelf_repo = mock_repositories['shelf_repo']
            shelf_service.library_repo = mock_repositories['library_repo']
            
            # Mock library exists and user has access
            sample_library.owner_id = user_id
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            
            # Mock existing shelf with same name
            existing_shelf = Shelf(
                id=uuid4(),
                library_id=UUID(sample_shelf_data['library_id']),
                name=sample_shelf_data['name'],
                description="Existing shelf",
                resource_type=ResourceType.FILE,
                resource_count=0,
                tenant_id=UUID(sample_shelf_data['tenant_id']),
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id
            )
            mock_repositories['shelf_repo'].get_all.return_value = [existing_shelf]
            
            # Test shelf creation should raise HTTPException
            with pytest.raises(HTTPException) as exc_info:
                await shelf_service.create_shelf(sample_shelf_data, user_id)
            
            assert exc_info.value.status_code == 409
            assert "already exists" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_get_shelf_success(self, shelf_service, mock_repositories, sample_library):
        """Test successful shelf retrieval"""
        user_id = uuid4()
        shelf_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.shelf_repo = mock_repositories['shelf_repo']
            shelf_service.library_repo = mock_repositories['library_repo']
            
            # Mock shelf exists
            shelf = Shelf(
                id=shelf_id,
                library_id=sample_library.id,
                name="Test Shelf",
                description="A test shelf",
                resource_type=ResourceType.FILE,
                resource_count=0,
                tenant_id=sample_library.tenant_id,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id,
                is_deleted=False
            )
            mock_repositories['shelf_repo'].get_by_id.return_value = shelf
            
            # Mock library access
            sample_library.owner_id = user_id
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            
            # Test shelf retrieval
            result = await shelf_service.get_shelf(shelf_id, user_id)
            
            # Assertions
            assert result.id == shelf_id
            assert result.name == "Test Shelf"
            mock_repositories['shelf_repo'].get_by_id.assert_called_once_with(str(shelf_id))
            mock_repositories['library_repo'].get_by_id.assert_called_once_with(str(sample_library.id))

    @pytest.mark.asyncio
    async def test_list_library_shelves_success(self, shelf_service, mock_repositories, sample_library):
        """Test successful listing of library shelves"""
        user_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.shelf_repo = mock_repositories['shelf_repo']
            shelf_service.library_repo = mock_repositories['library_repo']
            shelf_service.file_repo = mock_repositories['file_repo']
            shelf_service.article_repo = mock_repositories['article_repo']
            shelf_service.web_repo = mock_repositories['web_repo']
            
            # Mock library access
            sample_library.owner_id = user_id
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            
            # Mock shelves
            shelves = [
                Shelf(
                    id=uuid4(),
                    library_id=sample_library.id,
                    name="Files Shelf",
                    resource_type=ResourceType.FILE,
                    resource_count=0,
                    tenant_id=sample_library.tenant_id,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    created_by=user_id,
                    is_deleted=False
                ),
                Shelf(
                    id=uuid4(),
                    library_id=sample_library.id,
                    name="Articles Shelf",
                    resource_type=ResourceType.ARTICLE,
                    resource_count=0,
                    tenant_id=sample_library.tenant_id,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    created_by=user_id,
                    is_deleted=False
                )
            ]
            mock_repositories['shelf_repo'].get_all.return_value = shelves
            
            # Mock resource counting
            for repo in [mock_repositories['file_repo'], mock_repositories['article_repo'], mock_repositories['web_repo']]:
                repo.get_all.return_value = []
            
            # Test shelf listing
            result = await shelf_service.list_library_shelves(sample_library.id, user_id)
            
            # Assertions
            assert len(result) == 2
            assert result[0].name == "Files Shelf"
            assert result[1].name == "Articles Shelf"
            mock_repositories['library_repo'].get_by_id.assert_called_once_with(str(sample_library.id))
            mock_repositories['shelf_repo'].get_all.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_shelf_with_resources_fails(self, shelf_service, mock_repositories, sample_library):
        """Test that deleting a shelf with resources fails"""
        user_id = uuid4()
        shelf_id = uuid4()
        
        with patch.object(shelf_service, '_ensure_initialized', return_value=None):
            shelf_service.shelf_repo = mock_repositories['shelf_repo']
            shelf_service.library_repo = mock_repositories['library_repo']
            shelf_service.file_repo = mock_repositories['file_repo']
            shelf_service.article_repo = mock_repositories['article_repo']
            shelf_service.web_repo = mock_repositories['web_repo']
            
            # Mock shelf exists
            shelf = Shelf(
                id=shelf_id,
                library_id=sample_library.id,
                name="Test Shelf",
                resource_type=ResourceType.FILE,
                resource_count=5,  # Has resources
                tenant_id=sample_library.tenant_id,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=user_id,
                is_deleted=False
            )
            mock_repositories['shelf_repo'].get_by_id.return_value = shelf
            
            # Mock library access
            sample_library.owner_id = user_id
            mock_repositories['library_repo'].get_by_id.return_value = sample_library
            
            # Mock resource count (has resources)
            mock_repositories['file_repo'].get_all.return_value = [MagicMock()]  # 1 resource
            mock_repositories['article_repo'].get_all.return_value = []
            mock_repositories['web_repo'].get_all.return_value = []
            
            # Test shelf deletion should fail
            with pytest.raises(HTTPException) as exc_info:
                await shelf_service.delete_shelf(shelf_id, user_id)
            
            assert exc_info.value.status_code == 409
            assert "Cannot delete shelf with" in str(exc_info.value.detail)
