#!/usr/bin/env python3
"""
Test script for article resource endpoints
"""
import requests
import json
import os
from config import settings

# API base URL
API_BASE = "http://localhost:8000/api"

def get_auth_token():
    """Get a valid auth token from the dev endpoint"""
    try:
        response = requests.post(f"{API_BASE}/dev/superuser-token")
        if response.status_code == 200:
            data = response.json()
            return data["access_token"]
        else:
            print(f"Failed to get auth token: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error getting auth token: {e}")
        return None

def get_auth_headers():
    """Get authentication headers with a valid token"""
    token = get_auth_token()
    if token:
        return {
            "Authorization": f"Bearer {token}"
        }
    else:
        return {}

def test_create_article():
    """Test creating an article resource"""
    print("Testing article resource creation...")
    
    url = f"{API_BASE}/resources/articles"
    
    # Prepare form data
    data = {
        "title": "Test Article",
        "description": "This is a test article for testing purposes",
        "content": "This is the main content of the test article. It contains some meaningful text that can be processed and indexed by the system. The article discusses various aspects of testing and quality assurance in software development.",
        "author": "Test Author",
        "tags": "testing,quality,software"
    }
    
    # Get auth headers
    headers = get_auth_headers()
    
    try:
        # Since the endpoint expects form data, we'll use form encoding
        response = requests.post(url, data=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ Article resource created successfully!")
            return response.json()
        else:
            print("❌ Failed to create article resource")
            return None
            
    except Exception as e:
        print(f"❌ Error testing article creation: {str(e)}")
        return None

def test_list_articles():
    """Test listing article resources"""
    print("\nTesting article resource listing...")
    
    url = f"{API_BASE}/resources/article"
    headers = get_auth_headers()
    
    try:
        # Test with pagination parameters
        params = {"skip": 0, "limit": 10}
        response = requests.get(url, params=params, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Article resources listed successfully!")
            return response.json()
        else:
            print("❌ Failed to list article resources")
            return None
            
    except Exception as e:
        print(f"❌ Error testing article listing: {str(e)}")
        return None

def test_get_article(resource_id: str):
    """Test getting a specific article resource"""
    print(f"\nTesting getting article resource {resource_id}...")
    
    url = f"{API_BASE}/resources/article/{resource_id}"
    headers = get_auth_headers()
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Article resource retrieved successfully!")
            return response.json()
        else:
            print("❌ Failed to get article resource")
            return None
            
    except Exception as e:
        print(f"❌ Error testing article retrieval: {str(e)}")
        return None

def main():
    """Run all tests"""
    print("🧪 Starting Article Resource Tests")
    print("=" * 50)
    
    # Test 1: Create article
    article_data = test_create_article()
    
    # Test 2: List articles
    test_list_articles()
    
    # Test 3: Get specific article (if creation was successful)
    if article_data and "resource" in article_data:
        resource_id = article_data["resource"]["id"]
        test_get_article(resource_id)
    
    print("\n🧪 Article Resource Tests Completed")
    print("=" * 50)

if __name__ == "__main__":
    main()
