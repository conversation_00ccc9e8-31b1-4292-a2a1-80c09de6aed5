import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
from uuid import uuid4

from main import app
from models.resource import ResourceType


class TestShelfAPI:
    """Integration tests for Shelf API endpoints"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)

    @pytest.fixture
    def mock_auth_context(self):
        """Mock authentication context"""
        return {
            'user': {
                'id': uuid4(),
                'tenant_id': uuid4(),
                'email': '<EMAIL>'
            }
        }

    @pytest.fixture
    def sample_library_id(self):
        """Sample library ID for testing"""
        return str(uuid4())

    @pytest.fixture
    def sample_shelf_data(self, sample_library_id):
        """Sample shelf creation data"""
        return {
            'library_id': sample_library_id,
            'name': 'Test Shelf',
            'description': 'A test shelf for integration testing',
            'resource_type': 'file'
        }

    def test_create_shelf_endpoint_structure(self, client):
        """Test that the shelf creation endpoint exists and has proper structure"""
        # This test validates the endpoint exists without authentication
        response = client.post('/api/v1/shelves', json={
            'library_id': str(uuid4()),
            'name': 'Test Shelf',
            'resource_type': 'file'
        })
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403, 422]  # Not 404, meaning endpoint exists

    def test_get_shelf_endpoint_structure(self, client):
        """Test that the get shelf endpoint exists"""
        shelf_id = str(uuid4())
        response = client.get(f'/api/v1/shelves/{shelf_id}')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_update_shelf_endpoint_structure(self, client):
        """Test that the update shelf endpoint exists"""
        shelf_id = str(uuid4())
        response = client.put(f'/api/v1/shelves/{shelf_id}', json={
            'name': 'Updated Shelf Name'
        })
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403, 422]  # Not 404, meaning endpoint exists

    def test_delete_shelf_endpoint_structure(self, client):
        """Test that the delete shelf endpoint exists"""
        shelf_id = str(uuid4())
        response = client.delete(f'/api/v1/shelves/{shelf_id}')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_add_resource_to_shelf_endpoint_structure(self, client):
        """Test that the add resource to shelf endpoint exists"""
        shelf_id = str(uuid4())
        response = client.post(f'/api/v1/shelves/{shelf_id}/resources', json={
            'resource_id': str(uuid4())
        })
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403, 422]  # Not 404, meaning endpoint exists

    def test_list_library_shelves_endpoint_structure(self, client):
        """Test that the list library shelves endpoint exists"""
        library_id = str(uuid4())
        response = client.get(f'/api/v1/shelves/library/{library_id}')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_library_shelves_endpoint_structure(self, client):
        """Test that the library shelves endpoint exists"""
        library_id = str(uuid4())
        response = client.get(f'/api/v1/libraries/{library_id}/shelves')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_library_resources_endpoint_structure(self, client):
        """Test that the library resources endpoint exists"""
        library_id = str(uuid4())
        response = client.get(f'/api/v1/libraries/{library_id}/resources')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_add_resource_to_library_endpoint_structure(self, client):
        """Test that the add resource to library endpoint exists"""
        library_id = str(uuid4())
        resource_id = str(uuid4())
        response = client.post(f'/api/v1/libraries/{library_id}/resources/{resource_id}')
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403]  # Not 404, meaning endpoint exists

    def test_bulk_add_resources_endpoint_structure(self, client):
        """Test that the bulk add resources endpoint exists"""
        library_id = str(uuid4())
        response = client.post(f'/api/v1/libraries/{library_id}/resources/bulk', json=[
            str(uuid4()), str(uuid4())
        ])
        
        # Should fail with authentication error, not 404
        assert response.status_code in [401, 403, 422]  # Not 404, meaning endpoint exists

    def test_shelf_data_validation(self, client):
        """Test shelf data validation"""
        # Test missing required fields
        response = client.post('/api/v1/shelves', json={})
        assert response.status_code == 422  # Validation error
        
        # Test invalid resource type
        response = client.post('/api/v1/shelves', json={
            'library_id': str(uuid4()),
            'name': 'Test Shelf',
            'resource_type': 'invalid_type'
        })
        assert response.status_code == 422  # Validation error

    def test_resource_type_enum_validation(self, client):
        """Test that resource type enum validation works"""
        valid_types = ['file', 'article', 'web']
        
        for resource_type in valid_types:
            response = client.post('/api/v1/shelves', json={
                'library_id': str(uuid4()),
                'name': f'Test {resource_type.title()} Shelf',
                'resource_type': resource_type
            })
            # Should fail with auth, not validation error
            assert response.status_code in [401, 403]

    @pytest.mark.asyncio
    async def test_shelf_service_integration(self):
        """Test that ShelfService can be imported and initialized"""
        from services.shelf_service import ShelfService
        
        service = ShelfService()
        assert service is not None
        assert hasattr(service, 'create_shelf')
        assert hasattr(service, 'get_shelf')
        assert hasattr(service, 'update_shelf')
        assert hasattr(service, 'delete_shelf')
        assert hasattr(service, 'list_library_shelves')

    def test_shelf_model_validation(self):
        """Test that Shelf model validation works correctly"""
        from models.resource import Shelf, ResourceType
        from uuid import uuid4
        from datetime import datetime, timezone
        
        # Test valid shelf creation
        shelf_data = {
            'id': uuid4(),
            'library_id': uuid4(),
            'name': 'Test Shelf',
            'description': 'A test shelf',
            'resource_type': ResourceType.FILE,
            'resource_count': 0,
            'tenant_id': uuid4(),
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'created_by': uuid4(),
            'is_deleted': False
        }
        
        shelf = Shelf(**shelf_data)
        assert shelf.name == 'Test Shelf'
        assert shelf.resource_type == ResourceType.FILE
        assert shelf.resource_count == 0

    def test_resource_type_enum(self):
        """Test ResourceType enum values"""
        from models.resource import ResourceType
        
        assert ResourceType.FILE.value == 'file'
        assert ResourceType.ARTICLE.value == 'article'
        assert ResourceType.WEB.value == 'web'
        
        # Test enum creation from string
        assert ResourceType('file') == ResourceType.FILE
        assert ResourceType('article') == ResourceType.ARTICLE
        assert ResourceType('web') == ResourceType.WEB

    def test_library_service_shelf_integration(self):
        """Test that LibraryService has shelf-related methods"""
        from services.library_service import LibraryService
        
        service = LibraryService()
        assert hasattr(service, 'add_resource_to_shelf')
        assert hasattr(service, '_create_default_shelves')
        assert hasattr(service, '_update_library_statistics')

    def test_data_library_service_shelf_methods(self):
        """Test that frontend service has shelf methods"""
        # This would test the frontend service if we were running in a full environment
        # For now, we'll just validate the structure exists
        import os
        frontend_service_path = 'src/services/dataLibraryService.js'
        
        if os.path.exists(frontend_service_path):
            with open(frontend_service_path, 'r') as f:
                content = f.read()
                
            # Check that shelf-related methods exist
            assert 'getLibraryShelves' in content
            assert 'createShelf' in content
            assert 'updateShelf' in content
            assert 'deleteShelf' in content
            assert 'addResourceToShelf' in content

    def test_frontend_components_exist(self):
        """Test that frontend components exist"""
        import os
        
        components = [
            'src/features/data-library/components/ShelfManager.jsx',
            'src/features/data-library/components/ShelfCard.jsx',
            'src/features/data-library/components/ShelfSelector.jsx'
        ]
        
        for component_path in components:
            assert os.path.exists(component_path), f"Component {component_path} should exist"

    def test_shelf_routes_registered(self):
        """Test that shelf routes are registered in the main app"""
        import os
        main_py_path = 'main.py'
        
        if os.path.exists(main_py_path):
            with open(main_py_path, 'r') as f:
                content = f.read()
                
            # Check that shelf routes are imported and registered
            assert 'shelves' in content
            assert 'shelves.router' in content
