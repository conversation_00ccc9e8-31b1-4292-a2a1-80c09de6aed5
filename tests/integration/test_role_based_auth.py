"""
Integration tests for role-based authentication system.

This module demonstrates the new role-based authentication testing system
that replaces the old superuser-based approach.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from typing import Dict, Any
from uuid import uuid4

from tests.utils.auth_test_helpers import (
    RoleBasedAuthTestHelper,
    generate_test_report,
    validate_authentication_setup
)


@pytest.mark.integration
@pytest.mark.auth
class TestRoleBasedAuthentication:
    """Test class for role-based authentication system."""
    
    def test_authentication_setup_validation(self, test_client: TestClient, api_base_url: str):
        """Test that authentication system is properly configured."""
        validation = validate_authentication_setup(test_client, api_base_url)
        
        # Print validation results for debugging
        if not validation["setup_valid"]:
            print("Authentication setup issues:")
            for rec in validation["recommendations"]:
                print(f"  - {rec}")
        
        assert validation["setup_valid"], "Authentication system not properly configured"
    
    def test_role_based_token_generation(self, test_client: TestClient, api_base_url: str):
        """Test that all role-based tokens can be generated."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Test system admin token
        system_admin_token = auth_helper.get_system_admin_token()
        assert system_admin_token is not None
        assert len(system_admin_token.split('.')) == 3  # Valid JWT format
        
        # Test tenant admin token
        tenant_admin_token = auth_helper.get_tenant_admin_token()
        assert tenant_admin_token is not None
        assert len(tenant_admin_token.split('.')) == 3
        
        # Test manager token
        manager_token = auth_helper.get_manager_token()
        assert manager_token is not None
        assert len(manager_token.split('.')) == 3
        
        # Test member token
        member_token = auth_helper.get_member_token()
        assert member_token is not None
        assert len(member_token.split('.')) == 3
    
    def test_comprehensive_authentication_scenarios(self, test_client: TestClient, api_base_url: str):
        """Test all authentication scenarios for a protected endpoint."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Test authentication scenarios
        result = auth_helper.test_authentication_scenarios("GET", "/resources/test/auth")
        
        # Generate and print report
        report = generate_test_report(result)
        print("\n" + report)
        
        # Assert all scenarios passed
        assert result["summary"]["success_rate"] == "100.0%", \
            f"Authentication scenarios failed: {result['summary']['issues']}"
    
    def test_role_based_access_control(self, test_client: TestClient, api_base_url: str):
        """Test role-based access control for different endpoints."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Test read access (all roles should have access)
        read_result = auth_helper.test_role_based_access(
            "GET", "/resources/test/auth",
            expected_results={
                "system_admin": 200,
                "tenant_admin": 200,
                "manager": 200,
                "member": 200
            }
        )
        
        print(f"\nRead access test results: {read_result['summary']['success_rate']}")
        assert read_result["summary"]["success_rate"] == "100.0%"
        
        # Test system endpoint access (only system admin should have access)
        system_result = auth_helper.test_role_based_access(
            "GET", "/system/db/collections",
            expected_results={
                "system_admin": 200,
                "tenant_admin": 403,  # Should be denied
                "manager": 403,       # Should be denied
                "member": 403         # Should be denied
            }
        )
        
        print(f"System endpoint test results: {system_result['summary']['success_rate']}")
        assert system_result["summary"]["success_rate"] == "100.0%"
    
    def test_multi_tenant_isolation(self, test_client: TestClient, api_base_url: str):
        """Test multi-tenant isolation."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Test multi-tenant isolation
        result = auth_helper.test_multi_tenant_isolation("GET", "/resources/test/auth")
        
        print(f"\nMulti-tenant isolation test: {result['isolation_verified']}")
        assert result["isolation_verified"] == True
    
    def test_custom_permission_tokens(self, test_client: TestClient, api_base_url: str):
        """Test custom tokens with specific permissions."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Create custom token with only read permissions
        read_only_token = auth_helper.create_custom_role_token(
            permissions=["read_resource", "read_agent"],
            email="<EMAIL>"
        )
        
        # Test that read-only token can access read endpoints
        read_result = auth_helper.test_protected_endpoint(
            "GET", "/resources/test/auth",
            headers=auth_helper.get_auth_headers(read_only_token)
        )
        assert read_result["success"] == True
        
        # Test that read-only token cannot access write endpoints
        write_result = auth_helper.test_protected_endpoint(
            "POST", "/resources",
            headers=auth_helper.get_auth_headers(read_only_token),
            json_data={"name": "Test Resource"},
            expected_status=403
        )
        assert write_result["success"] == True
    
    def test_expired_token_handling(self, test_client: TestClient, api_base_url: str):
        """Test handling of expired tokens."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Create expired token
        expired_token = auth_helper.create_expired_token("system_admin")
        
        # Test that expired token is rejected
        result = auth_helper.test_protected_endpoint(
            "GET", "/resources/test/auth",
            headers=auth_helper.get_auth_headers(expired_token),
            expected_status=401
        )
        assert result["success"] == True
    
    def test_invalid_token_handling(self, test_client: TestClient, api_base_url: str):
        """Test handling of invalid tokens."""
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        
        # Test malformed token
        malformed_token = auth_helper.create_invalid_token("malformed")
        result = auth_helper.test_protected_endpoint(
            "GET", "/resources/test/auth",
            headers=auth_helper.get_auth_headers(malformed_token),
            expected_status=401
        )
        assert result["success"] == True
        
        # Test wrong signature token
        wrong_sig_token = auth_helper.create_invalid_token("wrong_signature")
        result = auth_helper.test_protected_endpoint(
            "GET", "/resources/test/auth",
            headers=auth_helper.get_auth_headers(wrong_sig_token),
            expected_status=401
        )
        assert result["success"] == True

    def test_system_admin_access(self, test_client: TestClient, system_admin_headers: Dict[str, str]):
        """Test system admin access to protected endpoints."""
        # Test access to system endpoints
        response = test_client.get("/api/system/db/collections", headers=system_admin_headers)
        assert response.status_code == 200
        
        # Test access to regular endpoints
        response = test_client.get("/api/resources/test/auth", headers=system_admin_headers)
        assert response.status_code == 200
    
    def test_tenant_admin_access(self, test_client: TestClient, tenant_admin_headers: Dict[str, str]):
        """Test tenant admin access to protected endpoints."""
        # Test access to regular endpoints
        response = test_client.get("/api/resources/test/auth", headers=tenant_admin_headers)
        assert response.status_code == 200
        
        # Test access to system endpoints (should be denied)
        response = test_client.get("/api/system/db/collections", headers=tenant_admin_headers)
        assert response.status_code == 403
    
    def test_manager_access(self, test_client: TestClient, manager_headers: Dict[str, str]):
        """Test manager access to protected endpoints."""
        # Test access to regular endpoints
        response = test_client.get("/api/resources/test/auth", headers=manager_headers)
        assert response.status_code == 200
        
        # Test access to system endpoints (should be denied)
        response = test_client.get("/api/system/db/collections", headers=manager_headers)
        assert response.status_code == 403
    
    def test_member_access(self, test_client: TestClient, member_headers: Dict[str, str]):
        """Test member access to protected endpoints."""
        # Test access to regular endpoints
        response = test_client.get("/api/resources/test/auth", headers=member_headers)
        assert response.status_code == 200
        
        # Test access to system endpoints (should be denied)
        response = test_client.get("/api/system/db/collections", headers=member_headers)
        assert response.status_code == 403
    
    def test_legacy_fixture_compatibility(self, test_client: TestClient, auth_headers: Dict[str, str]):
        """Test that legacy fixtures still work for backward compatibility."""
        # Test that legacy auth_headers works (should be system admin)
        response = test_client.get("/api/system/db/collections", headers=auth_headers)
        assert response.status_code == 200


@pytest.mark.integration
@pytest.mark.auth
class TestDevelopmentEndpoints:
    """Test class for development endpoints."""
    
    def test_dev_token_endpoints(self, test_client: TestClient, api_base_url: str):
        """Test that development token endpoints work correctly."""
        # Test system admin token endpoint
        response = test_client.post(f"{api_base_url}/system/dev/token/system-admin")
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user_info"]["role"] == "SystemAdmin"
        
        # Test tenant admin token endpoint
        response = test_client.post(f"{api_base_url}/system/dev/token/tenant-admin")
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user_info"]["role"] == "TenantAdmin"
        
        # Test manager token endpoint
        response = test_client.post(f"{api_base_url}/system/dev/token/manager")
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user_info"]["role"] == "Manager"
        
        # Test member token endpoint
        response = test_client.post(f"{api_base_url}/system/dev/token/member")
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["user_info"]["role"] == "Member"
    
    def test_dev_status_endpoint(self, test_client: TestClient, api_base_url: str):
        """Test development status endpoint."""
        response = test_client.get(f"{api_base_url}/system/dev/status")
        assert response.status_code == 200
        data = response.json()
        assert "development_mode" in data
        assert "environment" in data
        assert data["development_mode"] == True  # Should be True in development
    
    def test_dev_test_auth_endpoint(self, test_client: TestClient, api_base_url: str):
        """Test development authentication test endpoint."""
        # Test without authentication
        response = test_client.get(f"{api_base_url}/system/dev/test-auth")
        assert response.status_code == 401
        
        # Test with authentication
        auth_helper = RoleBasedAuthTestHelper(test_client, api_base_url)
        headers = auth_helper.get_role_headers("system_admin")
        response = test_client.get(f"{api_base_url}/system/dev/test-auth", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["authenticated"] == True 