"""
Integration tests for article resource endpoints.

This module contains comprehensive integration tests for the article resource API,
including creation, retrieval, listing, updating, and deletion operations.
"""
import pytest
from fastapi.testclient import TestClient
from typing import Dict, Any, Optional
import json


@pytest.mark.integration
@pytest.mark.auth
class TestArticleResourceEndpoints:
    """Test class for article resource endpoints."""
    
    def test_create_article_success(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        article_create_payload: Dict[str, Any]
    ):
        """Test successful article creation."""
        url = f"{api_base_url}/resources/articles"
        
        response = test_client.post(
            url, 
            data=article_create_payload, 
            headers=auth_headers
        )
        
        assert response.status_code == 201
        response_data = response.json()
        
        # Verify response structure
        assert "resource" in response_data
        resource = response_data["resource"]
        
        # Verify created resource data
        assert resource["title"] == article_create_payload["title"]
        assert resource["description"] == article_create_payload["description"]
        assert resource["author"] == article_create_payload["author"]
        assert "id" in resource
        assert "created_at" in resource
        
    def test_create_article_without_auth(
        self, 
        test_client: TestClient, 
        api_base_url: str,
        article_create_payload: Dict[str, Any]
    ):
        """Test article creation without authentication should fail."""
        url = f"{api_base_url}/resources/articles"
        
        response = test_client.post(url, data=article_create_payload)
        
        assert response.status_code == 401
        
    def test_create_article_invalid_data(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        invalid_article_payloads: Dict[str, Dict[str, Any]]
    ):
        """Test article creation with invalid data."""
        url = f"{api_base_url}/resources/articles"
        
        for test_case, payload in invalid_article_payloads.items():
            response = test_client.post(
                url, 
                data=payload, 
                headers=auth_headers
            )
            
            # Should return validation error
            assert response.status_code in [400, 422], f"Failed test case: {test_case}"
            
    def test_list_articles_success(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        pagination_params: Dict[str, Dict[str, int]]
    ):
        """Test successful article listing with pagination."""
        url = f"{api_base_url}/resources/article"
        
        # Test default pagination
        params = pagination_params["default"]
        response = test_client.get(url, params=params, headers=auth_headers)
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify response structure
        assert isinstance(response_data, list) or "items" in response_data
        
    def test_list_articles_without_auth(
        self, 
        test_client: TestClient, 
        api_base_url: str,
        pagination_params: Dict[str, Dict[str, int]]
    ):
        """Test article listing without authentication should fail."""
        url = f"{api_base_url}/resources/article"
        params = pagination_params["default"]
        
        response = test_client.get(url, params=params)
        
        assert response.status_code == 401
        
    def test_list_articles_invalid_pagination(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        pagination_params: Dict[str, Dict[str, int]]
    ):
        """Test article listing with invalid pagination parameters."""
        url = f"{api_base_url}/resources/article"
        
        # Test with negative values
        for test_case in ["negative_skip", "negative_limit"]:
            params = pagination_params[test_case]
            response = test_client.get(url, params=params, headers=auth_headers)
            
            # Should either succeed with corrected values or return validation error
            assert response.status_code in [200, 400, 422], f"Failed test case: {test_case}"
            
    def test_get_article_success(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        article_create_payload: Dict[str, Any]
    ):
        """Test successful article retrieval."""
        # First create an article to retrieve
        create_url = f"{api_base_url}/resources/articles"
        create_response = test_client.post(
            create_url, 
            data=article_create_payload, 
            headers=auth_headers
        )
        
        assert create_response.status_code == 201
        created_resource = create_response.json()["resource"]
        resource_id = created_resource["id"]
        
        # Now retrieve the article
        get_url = f"{api_base_url}/resources/article/{resource_id}"
        response = test_client.get(get_url, headers=auth_headers)
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify retrieved data matches created data
        assert response_data["id"] == resource_id
        assert response_data["title"] == article_create_payload["title"]
        assert response_data["author"] == article_create_payload["author"]
        
    def test_get_article_not_found(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str]
    ):
        """Test retrieval of non-existent article."""
        non_existent_id = "non-existent-article-id-123"
        url = f"{api_base_url}/resources/article/{non_existent_id}"
        
        response = test_client.get(url, headers=auth_headers)
        
        assert response.status_code == 404
        
    def test_get_article_without_auth(
        self, 
        test_client: TestClient, 
        api_base_url: str
    ):
        """Test article retrieval without authentication should fail."""
        url = f"{api_base_url}/resources/article/some-id"
        
        response = test_client.get(url)
        
        assert response.status_code == 401


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.slow
class TestArticleResourceWorkflow:
    """Test complete workflows for article resources."""
    
    def test_complete_article_lifecycle(
        self, 
        test_client: TestClient, 
        api_base_url: str, 
        auth_headers: Dict[str, str],
        article_create_payload: Dict[str, Any],
        article_update_payload: Dict[str, Any]
    ):
        """Test complete CRUD workflow for articles."""
        
        # 1. Create article
        create_url = f"{api_base_url}/resources/articles"
        create_response = test_client.post(
            create_url, 
            data=article_create_payload, 
            headers=auth_headers
        )
        
        assert create_response.status_code == 201
        created_resource = create_response.json()["resource"]
        resource_id = created_resource["id"]
        
        # 2. Verify article appears in listing
        list_url = f"{api_base_url}/resources/article"
        list_response = test_client.get(
            list_url, 
            params={"skip": 0, "limit": 100}, 
            headers=auth_headers
        )
        
        assert list_response.status_code == 200
        # Note: Exact verification depends on your API response format
        
        # 3. Retrieve specific article
        get_url = f"{api_base_url}/resources/article/{resource_id}"
        get_response = test_client.get(get_url, headers=auth_headers)
        
        assert get_response.status_code == 200
        retrieved_resource = get_response.json()
        assert retrieved_resource["id"] == resource_id
        
        # 4. Update article (if update endpoint exists)
        # update_response = test_client.put(
        #     get_url, 
        #     data=article_update_payload, 
        #     headers=auth_headers
        # )
        # assert update_response.status_code == 200
        
        # 5. Delete article (if delete endpoint exists)
        # delete_response = test_client.delete(get_url, headers=auth_headers)
        # assert delete_response.status_code == 204
        
        # 6. Verify article is deleted
        # final_get_response = test_client.get(get_url, headers=auth_headers)
        # assert final_get_response.status_code == 404
