"""
Pytest configuration file for Assivy Backend tests.
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from httpx import AsyncClient
import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from config import settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_client() -> Generator[TestClient, None, None]:
    """
    Create a test client for the FastAPI application.
    This fixture is session-scoped to avoid recreating the client for each test.
    """
    with TestClient(app) as client:
        yield client


@pytest.fixture(scope="session")
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async test client for the FastAPI application.
    Useful for testing async endpoints and operations.
    """
    async with <PERSON>ync<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture(scope="session")
def api_base_url() -> str:
    """Return the base API URL for tests."""
    return "/api"


@pytest.fixture(scope="function")
async def system_admin_token(test_client: TestClient, api_base_url: str) -> str:
    """
    Get a system admin authentication token for testing protected endpoints.
    This fixture is function-scoped to ensure fresh tokens for each test.
    """
    response = test_client.post(f"{api_base_url}/system/dev/token/system-admin")
    assert response.status_code == 200
    data = response.json()
    return data["access_token"]


@pytest.fixture(scope="function")
async def tenant_admin_token(test_client: TestClient, api_base_url: str) -> str:
    """
    Get a tenant admin authentication token for testing protected endpoints.
    This fixture is function-scoped to ensure fresh tokens for each test.
    """
    response = test_client.post(f"{api_base_url}/system/dev/token/tenant-admin")
    assert response.status_code == 200
    data = response.json()
    return data["access_token"]


@pytest.fixture(scope="function")
async def manager_token(test_client: TestClient, api_base_url: str) -> str:
    """
    Get a manager authentication token for testing protected endpoints.
    This fixture is function-scoped to ensure fresh tokens for each test.
    """
    response = test_client.post(f"{api_base_url}/system/dev/token/manager")
    assert response.status_code == 200
    data = response.json()
    return data["access_token"]


@pytest.fixture(scope="function")
async def member_token(test_client: TestClient, api_base_url: str) -> str:
    """
    Get a member authentication token for testing protected endpoints.
    This fixture is function-scoped to ensure fresh tokens for each test.
    """
    response = test_client.post(f"{api_base_url}/system/dev/token/member")
    assert response.status_code == 200
    data = response.json()
    return data["access_token"]


@pytest.fixture(scope="function")
def system_admin_headers(system_admin_token: str) -> dict:
    """
    Get authentication headers with a system admin token.
    """
    return {
        "Authorization": f"Bearer {system_admin_token}"
    }


@pytest.fixture(scope="function")
def tenant_admin_headers(tenant_admin_token: str) -> dict:
    """
    Get authentication headers with a tenant admin token.
    """
    return {
        "Authorization": f"Bearer {tenant_admin_token}"
    }


@pytest.fixture(scope="function")
def manager_headers(manager_token: str) -> dict:
    """
    Get authentication headers with a manager token.
    """
    return {
        "Authorization": f"Bearer {manager_token}"
    }


@pytest.fixture(scope="function")
def member_headers(member_token: str) -> dict:
    """
    Get authentication headers with a member token.
    """
    return {
        "Authorization": f"Bearer {member_token}"
    }


# Legacy fixtures for backward compatibility
@pytest.fixture(scope="function")
async def auth_token(system_admin_token: str) -> str:
    """
    Legacy fixture - returns system admin token for backward compatibility.
    Use system_admin_token, tenant_admin_token, manager_token, or member_token instead.
    """
    return system_admin_token


@pytest.fixture(scope="function")
def auth_headers(system_admin_headers: dict) -> dict:
    """
    Legacy fixture - returns system admin headers for backward compatibility.
    Use system_admin_headers, tenant_admin_headers, manager_headers, or member_headers instead.
    """
    return system_admin_headers


@pytest.fixture(scope="function")
def sample_article_data() -> dict:
    """
    Provide sample article data for testing.
    """
    return {
        "title": "Test Article",
        "description": "This is a test article for testing purposes",
        "content": "This is the main content of the test article. It contains some meaningful text that can be processed and indexed by the system. The article discusses various aspects of testing and quality assurance in software development.",
        "author": "Test Author",
        "tags": "testing,quality,software"
    }


@pytest.fixture(scope="function")
def sample_invalid_article_data() -> dict:
    """
    Provide invalid article data for testing error cases.
    """
    return {
        "title": "",  # Invalid: empty title
        "description": "This is a test article for testing purposes",
        "content": "",  # Invalid: empty content
        "author": "Test Author",
        "tags": "testing,quality,software"
    }


# Test database configuration
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """
    Set up test environment variables and configurations.
    This fixture runs automatically for all tests.
    """
    # Set test environment variables
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = os.getenv("TEST_DATABASE_URL", settings.database_url)
    
    yield
    
    # Cleanup after all tests
    if "TESTING" in os.environ:
        del os.environ["TESTING"]


# Pytest configuration
def pytest_configure(config):
    """
    Configure pytest with custom markers and settings.
    """
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "auth: mark test as requiring authentication"
    )


# Custom pytest markers
pytestmark = [
    pytest.mark.asyncio,
]
