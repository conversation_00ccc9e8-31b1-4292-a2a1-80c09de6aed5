"""
Authentication fixtures for testing.
"""
import pytest
from typing import Dict, Any


@pytest.fixture
def mock_user_data() -> Dict[str, Any]:
    """Mock user data for testing."""
    return {
        "id": "test-user-123",
        "email": "<EMAIL>",
        "username": "testuser",
        "is_active": True,
        "tenant_id": "test-tenant-123",
        "role_id": "test-role-123"
    }


@pytest.fixture
def mock_superuser_data() -> Dict[str, Any]:
    """Mock superuser data for testing."""
    return {
        "id": "test-superuser-123",
        "email": "<EMAIL>",
        "username": "admin",
        "is_active": True,
        "tenant_id": "test-tenant-123",
        "role_id": "admin-role-123"
    }


@pytest.fixture
def invalid_tokens() -> Dict[str, str]:
    """Various invalid tokens for testing authentication errors."""
    return {
        "expired": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid",
        "malformed": "invalid.token.format",
        "empty": "",
        "wrong_signature": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.wrong_signature"
    }


@pytest.fixture
def unauthorized_headers() -> Dict[str, Dict[str, str]]:
    """Various unauthorized header configurations."""
    return {
        "no_auth": {},
        "invalid_bearer": {"Authorization": "InvalidBearer token123"},
        "wrong_scheme": {"Authorization": "Basic dGVzdDp0ZXN0"},
        "empty_token": {"Authorization": "Bearer "},
        "malformed_auth": {"Authorization": "Bearer invalid.token"}
    }
