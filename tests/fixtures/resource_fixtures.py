"""
Test fixtures for resources testing.
"""
import pytest
from typing import Dict, Any


@pytest.fixture
def article_create_payload() -> Dict[str, Any]:
    """Standard article creation payload for testing."""
    return {
        "title": "Integration Test Article",
        "description": "This article is created during integration testing",
        "content": "This is comprehensive content for the integration test article. It includes multiple paragraphs to test content processing capabilities. The content should be substantial enough to test various features like indexing, search, and retrieval.",
        "author": "Integration Test Author",
        "tags": "integration,testing,automated"
    }


@pytest.fixture
def article_update_payload() -> Dict[str, Any]:
    """Article update payload for testing."""
    return {
        "title": "Updated Integration Test Article",
        "description": "This article has been updated during integration testing",
        "content": "This is updated content for the integration test article. The content has been modified to test update functionality.",
        "author": "Updated Integration Test Author",
        "tags": "integration,testing,automated,updated"
    }


@pytest.fixture
def invalid_article_payloads() -> Dict[str, Dict[str, Any]]:
    """Various invalid article payloads for testing error cases."""
    return {
        "empty_title": {
            "title": "",
            "description": "Valid description",
            "content": "Valid content",
            "author": "Valid Author",
            "tags": "valid,tags"
        },
        "empty_content": {
            "title": "Valid Title",
            "description": "Valid description",
            "content": "",
            "author": "Valid Author",
            "tags": "valid,tags"
        },
        "missing_title": {
            "description": "Valid description",
            "content": "Valid content",
            "author": "Valid Author",
            "tags": "valid,tags"
        },
        "missing_content": {
            "title": "Valid Title",
            "description": "Valid description",
            "author": "Valid Author",
            "tags": "valid,tags"
        },
        "title_too_long": {
            "title": "x" * 1000,  # Assuming there's a title length limit
            "description": "Valid description",
            "content": "Valid content",
            "author": "Valid Author",
            "tags": "valid,tags"
        }
    }


@pytest.fixture
def pagination_params() -> Dict[str, Dict[str, int]]:
    """Various pagination parameters for testing."""
    return {
        "default": {"skip": 0, "limit": 10},
        "first_page": {"skip": 0, "limit": 5},
        "second_page": {"skip": 5, "limit": 5},
        "large_limit": {"skip": 0, "limit": 100},
        "zero_limit": {"skip": 0, "limit": 0},
        "negative_skip": {"skip": -1, "limit": 10},
        "negative_limit": {"skip": 0, "limit": -1}
    }


@pytest.fixture
def search_params() -> Dict[str, Dict[str, Any]]:
    """Various search parameters for testing."""
    return {
        "title_search": {"q": "Integration Test", "field": "title"},
        "content_search": {"q": "comprehensive content", "field": "content"},
        "author_search": {"q": "Integration Test Author", "field": "author"},
        "tag_search": {"q": "integration", "field": "tags"},
        "empty_search": {"q": "", "field": "title"},
        "special_chars": {"q": "test@#$%", "field": "title"}
    }
