from typing import Any, Dict, Optional, List
from datetime import datetime, timezone
from enum import Enum
from uuid import UUID
from pydantic import Field, field_serializer, computed_field
from .base import BaseModelWithTenant

class Library(BaseModelWithTenant):
    """Main library container for organizing resources"""
    name: str
    description: Optional[str] = None
    owner_id: UUID
    settings: Dict[str, Any] = Field(default_factory=dict)
    resource_count: int = Field(default=0, description="Total number of resources in this library")
    total_size: int = Field(default=0, description="Total size of all resources in bytes")
    
    @field_serializer('owner_id')
    def serialize_owner_id(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None

class PermissionLevel(str, Enum):
    VIEW = "view"
    EDIT = "edit"
    ADMIN = "admin"

    def __str__(self) -> str:
        return self.value

class ShareStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    REVOKED = "revoked"

    def __str__(self) -> str:
        return self.value

class Share(BaseModelWithTenant):
    """Resource sharing and permission management"""
    resource_id: UUID
    resource_type: str  # "file", "article", "web"
    shared_with: UUID
    permission_level: PermissionLevel = PermissionLevel.VIEW
    expires_at: Optional[datetime] = None
    
    @field_serializer('resource_id', 'shared_with')
    def serialize_uuids(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None
    
    @field_serializer('expires_at')
    def serialize_expires_at(self, value: datetime | None) -> str | None:
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat().replace('+00:00', 'Z')

class ChunkUsage(BaseModelWithTenant):
    """Track when specific chunks are used by agents"""
    chunk_id: UUID
    resource_id: UUID
    resource_type: str  # "file", "article", "web"
    agent_id: UUID
    query: str
    relevance_score: Optional[float] = None
    
    @field_serializer('chunk_id', 'resource_id', 'agent_id')
    def serialize_uuids(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None