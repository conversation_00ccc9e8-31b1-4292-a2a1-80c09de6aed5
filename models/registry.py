from typing import Dict, Type, TypeVar

from pydantic import BaseModel
from .action import BaseAction
from .resource import FileResource, ArticleResource, WebResource, Chunk, Shelf
from .entity import EntityDefinition, EntityInstance
from .task import Task
from .api_key import APIKey
from .user import User
from .role import Role
from .tenant import Tenant
from .agent import Agent, Usecase
from .ai_model import AIModel
from .library import Library
from .refresh_token import RefreshToken, TokenBlacklist

ModelType = TypeVar("ModelType", bound=BaseModel)

# Registry of model types and their collection names
model_registry: Dict[Type[ModelType], str] = {
    BaseAction: "Action",
    FileResource: "FileResource",
    ArticleResource: "ArticleResource",
    WebResource: "WebResource",
    Chunk: "Chunk",
    Shelf: "Shelf",
    Library: "Library",
    EntityDefinition: "EntityDefinition",
    EntityInstance: "EntityInstance",
    Task: "Task",
    APIKey: "APIKey",
    User: "User",
    Role: "Role",
    Tenant: "Tenant",
    Agent: "Agent",
    Usecase: "Usecase",
    AIModel: "AIModel",
    RefreshToken: "RefreshToken",
    TokenBlacklist: "TokenBlacklist"
}

def get_collection_name(model_type: Type[ModelType]) -> str:
    """Get the collection name for a model type"""
    collection_name = model_registry.get(model_type)
    if not collection_name:
        raise ValueError(f"No collection name registered for model type {model_type.__name__}")
    return collection_name

def register_model(model_type: Type[ModelType], collection_name: str) -> None:
    """Register a new model type with its collection name"""
    model_registry[model_type] = collection_name
