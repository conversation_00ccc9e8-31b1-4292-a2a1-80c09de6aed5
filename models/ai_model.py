from typing import Dict, Any, Optional
from pydantic import Field
from .base import BaseModelWithTenant

class AIModel(BaseModelWithTenant):
    """Reusable AI model configuration that can be referenced by agents"""
    name: str = Field(..., description="Name of the model configuration")
    provider: str = Field(..., description="AI model provider (openai, anthropic, google, ollama)")
    model_name: str = Field(..., description="AI model name (gpt-4, claude-3, gemini-pro, llama2)")
    temperature: float = Field(0.1, description="Model temperature setting")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens for the model")
    config: Dict[str, Any] = Field(default_factory=dict, description="Additional model configuration")
    is_default: bool = Field(False, description="Whether this is a default model configuration")
    
    @classmethod
    def create_default_openai(cls, tenant_id) -> "AIModel":
        """Factory method to create a default OpenAI model configuration"""
        return cls(
            name="Default GPT-4",
            provider="openai",
            model_name="gpt-4",
            temperature=0.1,
            is_default=True,
            tenant_id=tenant_id
        )
    
    @classmethod
    def create_default_anthropic(cls, tenant_id) -> "AIModel":
        """Factory method to create a default Anthropic model configuration"""
        return cls(
            name="Default Claude-3",
            provider="anthropic", 
            model_name="claude-3-sonnet-20240229",
            temperature=0.1,
            is_default=True,
            tenant_id=tenant_id
        )
