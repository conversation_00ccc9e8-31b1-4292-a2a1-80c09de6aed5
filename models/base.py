from typing import Any, Dict
from uuid import UUID
from pydantic import BaseModel as PydanticBaseModel, ConfigDict, field_serializer
from datetime import datetime, timezone

class BaseModelId(PydanticBaseModel):
    """Base model with Id only"""     
    id: UUID  | None = None
    
class BaseModel(BaseModelId):
    """Base model for all domain models"""
    created_at: datetime = datetime.now(timezone.utc)
    updated_at: datetime = datetime.now(timezone.utc)
    is_deleted: bool = False
    created_by: UUID | None = None
    updated_by: UUID | None = None
    
    @field_serializer('id', 'created_by', 'updated_by')
    def serialize_uuid(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None
    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, value: datetime | None) -> str | None:
        if value is None:
            return None
        # Ensure timezone-aware datetime and convert to RFC3339 format
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat().replace('+00:00', 'Z')

class BaseModelWithTenant(BaseModel):
    """Base model for domain models that require tenant identification"""
    tenant_id: UUID
    
    @field_serializer('tenant_id')
    def serialize_tenant_id(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None
    
class BaseModelWithTenantNoTracking(BaseModelId):
    """Base model for domain models that require tenant identification"""
    tenant_id: UUID
    
    @field_serializer('tenant_id')
    def serialize_tenant_id(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None

