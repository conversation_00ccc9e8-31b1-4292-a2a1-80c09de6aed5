from typing import Optional
from datetime import datetime
from uuid import UUID
from enum import Enum
from .base import BaseModelWithTenant

class TaskStatus(str, Enum):
    """Task status enum"""
    PENDING = "pending"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    
    def __str__(self) -> str:
        return self.value

class TaskType(str, Enum):
    RESOURCE_INDEXING = "resource_indexing"
    CUSTOM = "custom"
    
    def __str__(self) -> str:
        return self.value

class Task(BaseModelWithTenant):
    """Task model for background jobs"""
    type: TaskType
    status: TaskStatus
    resource_id: Optional[UUID] = None  # Reference to Resource
    agent_id: Optional[UUID] = None     # Reference to Agent
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    owner_id: UUID  # Reference to User