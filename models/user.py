from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import EmailStr
from .base import BaseModel

class User(BaseModel):
    """User model with tenant context"""
    email: EmailStr
    hashed_password: Optional[str] = None  # Optional for OAuth users
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True
    role_id: UUID  # Reference to Role instead of embedded Role object
    oauth_provider: Optional[str] = None  # e.g., 'google', 'github'
    oauth_id: Optional[str] = None  # Provider-specific user ID
    is_email_verified: bool = False  # Whether the email has been verified
    verification_token: Optional[str] = None  # Token for email verification
    verification_token_expires: Optional[datetime] = None  # Expiration time of the verification token
    tenant_id: Optional[UUID] = None  # All users should have a tenant (including system admins who belong to Assivy Platform)
    
    # Profile fields
    job_title: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    profile_image_url: Optional[str] = None
    timezone: Optional[str] = None

    def get_display_name(self) -> str:
        """Get user's display name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email.split('@')[0]
