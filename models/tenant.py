from pydantic import BaseModel
from typing import Optional, Dict, Any
from .base import BaseModel as CustomBaseModel

class Tenant(CustomBaseModel):
    name: str
    description: str = None
    subscription_type: Optional[str] = "basic"
    
    # Organization settings
    organization_name: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    website: Optional[str] = None
    organization_logo_url: Optional[str] = None
    
    # Contact information
    primary_contact_email: Optional[str] = None
    primary_contact_phone: Optional[str] = None
    address: Optional[Dict[str, Any]] = None  # street, city, state, country, postal_code
    
    # Tenant settings and metadata
    settings: Optional[Dict[str, Any]] = None
    
    # Security and compliance
    security_settings: Optional[Dict[str, Any]] = None
    compliance_settings: Optional[Dict[str, Any]] = None