from enum import Enum
from typing import List, Set, Optional, Union
from uuid import UUID, uuid5, NAMESPACE_DNS
from pydantic import field_serializer, field_validator
from .base import BaseModel

class Permission(str, Enum):
    # Agent permissions
    CREATE_AGENT = "create_agent"
    READ_AGENT = "read_agent"
    UPDATE_AGENT = "update_agent" 
    DELETE_AGENT = "delete_agent"
    EXECUTE_AGENT = "execute_agent"
    
    # Resource permissions
    CREATE_RESOURCE = "create_resource"
    READ_RESOURCE = "read_resource"
    UPDATE_RESOURCE = "update_resource"
    DELETE_RESOURCE = "delete_resource"
    SEARCH_RESOURCE = "search_resource"
    
    # Task permissions
    CREATE_TASK = "create_task"
    READ_TASK = "read_task"
    UPDATE_TASK = "update_task"
    DELETE_TASK = "delete_task"
    MANAGE_TASKS = "manage_tasks"
    READ_TASKS = "read_tasks"
    EXECUTE_TASK = "execute_task"
    
    # Team management permissions
    MANAGE_TEAM = "manage_team"
    INVITE_MEMBER = "invite_member"
    REMOVE_MEMBER = "remove_member"
    
    # Tenant-scoped management permissions
    MANAGE_TENANT_SETTINGS = "manage_tenant_settings"
    VIEW_TENANT_ANALYTICS = "view_tenant_analytics"
    MANAGE_TENANT_USERS = "manage_tenant_users"

    # Entity permissions
    ENTITY_READ = "entity:read"
    ENTITY_CREATE = "entity:create"
    ENTITY_UPDATE = "entity:update"
    ENTITY_DELETE = "entity:delete"
    
    TENANT_ADMIN = "admin"

    # System-wide permissions (for platform administrators)
    SYSTEM_ADMIN = "super_admin"                    # Full system access
    SYSTEM_CREATE_TENANT = "system:create_tenant"   # Create new tenants
    SYSTEM_DELETE_TENANT = "system:delete_tenant"   # Delete tenants
    SYSTEM_LIST_TENANTS = "system:list_tenants"     # List all tenants
    SYSTEM_MANAGE_USERS = "system:manage_users"     # Manage users across tenants
    SYSTEM_VIEW_ANALYTICS = "system:view_analytics" # View system-wide analytics

class Role(BaseModel):
    """Represents a role - predefined (tenant_id=None) or tenant-specific (tenant_id=UUID)"""
    name: str
    description: str
    permissions: Set[Permission]
    tenant_id: Optional[UUID] = None  # None = predefined role, UUID = tenant-specific role

    @field_validator('permissions', mode='before')
    @classmethod
    def validate_permissions(cls, value: Union[Set[Permission], List[str], List[Permission], str]) -> Set[Permission]:
        """Convert permissions from various formats to Set[Permission]"""
        import json
        
        if isinstance(value, set):
            return value
        elif isinstance(value, list):
            # Convert list of strings or Permission enums to set
            return {Permission(perm) if isinstance(perm, str) else perm for perm in value}
        elif isinstance(value, str):
            # Handle JSON string format from Weaviate
            try:
                parsed_list = json.loads(value)
                if isinstance(parsed_list, list):
                    return {Permission(perm) for perm in parsed_list}
                else:
                    raise ValueError(f"Expected list in JSON string, got {type(parsed_list)}")
            except json.JSONDecodeError:
                raise ValueError(f"Invalid JSON string for permissions: {value}")
        else:
            raise ValueError(f"Invalid permissions format: {type(value)}")

    @field_serializer('permissions')
    def serialize_permissions(self, value: Set[Permission]) -> List[str]:
        """Convert permissions set to list of strings for storage"""
        return [perm.value for perm in value] if value else []
    
    @field_serializer('tenant_id')
    def serialize_tenant_id(self, value: UUID | None) -> str | None:
        return str(value) if value is not None else None

    @staticmethod
    def _generate_role_id(role_name: str) -> UUID:
        """Generate a deterministic UUID for a role based on its name"""
        return uuid5(NAMESPACE_DNS, f"assivy-role-{role_name.lower()}")

    @classmethod
    def get_predefined_roles(cls) -> List["Role"]:
        """Get predefined roles that can be used by any tenant"""
        return [
            cls(
                id=cls._generate_role_id("Admin"),
                name="Admin",
                description="Full access to all features within tenant",
                permissions={
                    Permission.TENANT_ADMIN
                },
                tenant_id=None
            ),
            cls(
                id=cls._generate_role_id("Manager"),
                name="Manager",
                description="Can manage agents, resources, and team members",
                permissions={
                    Permission.CREATE_AGENT,
                    Permission.READ_AGENT,
                    Permission.UPDATE_AGENT,
                    Permission.DELETE_AGENT,
                    Permission.EXECUTE_AGENT,
                    Permission.CREATE_RESOURCE,
                    Permission.READ_RESOURCE,
                    Permission.UPDATE_RESOURCE,
                    Permission.DELETE_RESOURCE,
                    Permission.SEARCH_RESOURCE,
                    Permission.CREATE_TASK,
                    Permission.READ_TASK,
                    Permission.UPDATE_TASK,
                    Permission.DELETE_TASK,
                    Permission.MANAGE_TASKS,
                    Permission.READ_TASKS,
                    Permission.VIEW_TENANT_ANALYTICS,
                    Permission.MANAGE_TEAM,
                    Permission.INVITE_MEMBER,
                    Permission.REMOVE_MEMBER
                },
                tenant_id=None
            ),
            cls(
                id=cls._generate_role_id("Member"),
                name="Member",
                description="Can use agents and resources",
                permissions={
                    Permission.READ_AGENT,
                    Permission.EXECUTE_AGENT,
                    Permission.READ_RESOURCE,
                    Permission.SEARCH_RESOURCE,
                    Permission.READ_TASK,
                    Permission.READ_TASKS,
                    Permission.CREATE_RESOURCE,
                    Permission.READ_RESOURCE,
                    Permission.UPDATE_RESOURCE,
                    Permission.DELETE_RESOURCE
                },
                tenant_id=None
            ),
            cls(
                id=cls._generate_role_id("SystemAdmin"),
                name="SystemAdmin",
                description="Platform administrator with full system access",
                permissions=set(Permission),  # All permissions including system ones
                tenant_id=None
            ),
            cls(
                id=cls._generate_role_id("TenantManager"),
                name="TenantManager", 
                description="Can manage tenants but not full system access",
                permissions={
                    Permission.SYSTEM_CREATE_TENANT,
                    Permission.SYSTEM_LIST_TENANTS,
                    Permission.SYSTEM_VIEW_ANALYTICS,
                    # Can also have basic read permissions to understand the system
                    Permission.READ_AGENT,
                    Permission.READ_RESOURCE,
                    Permission.READ_TASK
                },
                tenant_id=None
            )
        ]

    def has_permission(self, permission: Permission) -> bool:
        """Check if this role has a specific permission"""
        return permission in self.permissions

    def is_predefined(self) -> bool:
        """Check if this is a predefined role (tenant_id is None)"""
        return self.tenant_id is None
    
    def is_tenant_specific(self) -> bool:
        """Check if this is a tenant-specific role (tenant_id is not None)"""
        return self.tenant_id is not None