from typing import Optional, Dict, Any, List
from pydantic import Field
from uuid import UUID
from .base import BaseModel, BaseModelWithTenant
from datetime import datetime

class AgentActionConfig(BaseModel):
    """Configuration for a specific action in an agent"""
    action_id: UUID = Field(..., description="ID of the action")
    is_enabled: bool = Field(True, description="Whether this action is enabled for the agent")
    is_visible: bool = Field(True, description="Whether this action is visible in the agent's interface")
    custom_parameters: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Custom parameters that override action defaults")
    display_name: Optional[str] = Field(None, description="Custom display name for this action")
    display_order: Optional[int] = Field(None, description="Order for displaying this action")

class AgentIntegrationConfig(BaseModel):
    """Configuration for an integration within an agent"""
    integration_id: UUID = Field(..., description="ID of the integration")
    user_connection_id: Optional[UUID] = Field(None, description="ID of the user's connection to this integration")
    is_enabled: bool = Field(True, description="Whether this integration is enabled for the agent")
    
    # Action configurations for this integration
    action_configs: List[AgentActionConfig] = Field(default_factory=list,
        description="Configuration for each action in this integration")
    
    # Integration-level settings
    custom_settings: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Custom settings for this integration")
    
    def get_action_config(self, action_id: UUID) -> Optional[AgentActionConfig]:
        """Get configuration for a specific action"""
        for config in self.action_configs:
            if config.action_id == action_id:
                return config
        return None
    
    def enable_action(self, action_id: UUID, visible: bool = True, **custom_params) -> None:
        """Enable an action for this integration"""
        config = self.get_action_config(action_id)
        if config:
            config.is_enabled = True
            config.is_visible = visible
            config.custom_parameters.update(custom_params)
        else:
            self.action_configs.append(AgentActionConfig(
                action_id=action_id,
                is_enabled=True,
                is_visible=visible,
                custom_parameters=custom_params
            ))
    
    def disable_action(self, action_id: UUID) -> None:
        """Disable an action for this integration"""
        config = self.get_action_config(action_id)
        if config:
            config.is_enabled = False
    
    def hide_action(self, action_id: UUID) -> None:
        """Hide an action from the interface but keep it enabled"""
        config = self.get_action_config(action_id)
        if config:
            config.is_visible = False

class Usecase(BaseModelWithTenant):
    """Represents a reusable usecase template that agents can configure"""
    name: str = Field(..., description="Name of the usecase (e.g., 'Email Assistant', 'Data Analysis')")
    description: Optional[str] = Field(None, description="Description of what this usecase does")
    action_ids: List[UUID] = Field(default_factory=list, 
        description="List of action IDs that this usecase can perform")
    default_parameters: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Default parameters for this usecase")
    is_system: bool = Field(False, description="Whether this is a system-provided usecase")
    category: Optional[str] = Field(None, description="Category for organization (e.g., 'productivity', 'customer_service')")
    
    @classmethod
    def create_general_usecase(cls, tenant_id: UUID, action_ids: List[UUID]) -> "Usecase":
        """Factory method to create a general usecase for simple action configurations"""
        return cls(
            name="General",
            description="General purpose usecase for configured actions",
            action_ids=action_ids,
            is_system=True,
            tenant_id=tenant_id
        )

class AgentUsecaseConfig(BaseModel):
    """Configuration of a usecase for a specific agent"""
    usecase_id: UUID = Field(..., description="The ID of the usecase to use")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Agent-specific parameters that override usecase defaults")
    action_overrides: Optional[Dict[UUID, Dict[str, Any]]] = Field(default_factory=dict,
        description="Action-specific parameter overrides (action_id -> parameters)")
    is_enabled: bool = Field(True, description="Whether this usecase is enabled for this agent")
    priority: int = Field(1, description="Priority order for usecase execution")

class Agent(BaseModelWithTenant):
    """Model for AI agents that can execute usecases and actions"""
    name: str = Field(..., description="Name of the agent")
    description: Optional[str] = Field(None, description="Description of the agent's capabilities")
    
    # Model configuration - reference to a predefined AI model
    model_id: UUID = Field(..., description="Reference to an AI model configuration")
    
    # Usecases configuration - agents use configured versions of usecases
    usecase_configs: List[AgentUsecaseConfig] = Field(default_factory=list, 
        description="List of usecase configurations for this agent")
    
    # Integration configurations - NEW
    integration_configs: List[AgentIntegrationConfig] = Field(default_factory=list,
        description="List of integration configurations for this agent")
    
    # Agent settings
    streaming: bool = Field(False, description="Whether to enable streaming responses")
    is_active: bool = Field(True, description="Whether the agent is active")
    last_active: Optional[datetime] = Field(None, description="Last time the agent was used")
    
    # Ownership
    owner_id: UUID = Field(..., description="ID of the user who owns this agent")
    
    def add_usecase(self, usecase_id: UUID, parameters: Optional[Dict[str, Any]] = None, 
                   action_overrides: Optional[Dict[UUID, Dict[str, Any]]] = None) -> None:
        """Add a usecase configuration to this agent"""
        config = AgentUsecaseConfig(
            usecase_id=usecase_id,
            parameters=parameters or {},
            action_overrides=action_overrides or {}
        )
        self.usecase_configs.append(config)
    
    def remove_usecase(self, usecase_id: UUID) -> None:
        """Remove a usecase configuration from this agent"""
        self.usecase_configs = [
            config for config in self.usecase_configs 
            if config.usecase_id != usecase_id
        ]
    
    def get_usecase_config(self, usecase_id: UUID) -> Optional[AgentUsecaseConfig]:
        """Get the configuration for a specific usecase"""
        for config in self.usecase_configs:
            if config.usecase_id == usecase_id:
                return config
        return None
    
    def update_usecase_parameters(self, usecase_id: UUID, parameters: Dict[str, Any]) -> bool:
        """Update parameters for a specific usecase"""
        config = self.get_usecase_config(usecase_id)
        if config:
            config.parameters.update(parameters)
            return True
        return False
    
    # Integration management methods
    def add_integration(self, integration_id: UUID, user_connection_id: Optional[UUID] = None,
                       custom_settings: Optional[Dict[str, Any]] = None) -> None:
        """Add an integration configuration to this agent"""
        config = AgentIntegrationConfig(
            integration_id=integration_id,
            user_connection_id=user_connection_id,
            custom_settings=custom_settings or {}
        )
        self.integration_configs.append(config)
    
    def remove_integration(self, integration_id: UUID) -> None:
        """Remove an integration configuration from this agent"""
        self.integration_configs = [
            config for config in self.integration_configs 
            if config.integration_id != integration_id
        ]
    
    def get_integration_config(self, integration_id: UUID) -> Optional[AgentIntegrationConfig]:
        """Get the configuration for a specific integration"""
        for config in self.integration_configs:
            if config.integration_id == integration_id:
                return config
        return None
    
    def enable_integration_action(self, integration_id: UUID, action_id: UUID, 
                                 visible: bool = True, **custom_params) -> bool:
        """Enable a specific action for an integration"""
        config = self.get_integration_config(integration_id)
        if config:
            config.enable_action(action_id, visible, **custom_params)
            return True
        return False
    
    def disable_integration_action(self, integration_id: UUID, action_id: UUID) -> bool:
        """Disable a specific action for an integration"""
        config = self.get_integration_config(integration_id)
        if config:
            config.disable_action(action_id)
            return True
        return False
    
    def hide_integration_action(self, integration_id: UUID, action_id: UUID) -> bool:
        """Hide a specific action for an integration"""
        config = self.get_integration_config(integration_id)
        if config:
            config.hide_action(action_id)
            return True
        return False
    
    def get_enabled_actions(self, integration_id: Optional[UUID] = None) -> List[UUID]:
        """Get list of enabled action IDs, optionally filtered by integration"""
        enabled_actions = []
        for integration_config in self.integration_configs:
            if integration_id is None or integration_config.integration_id == integration_id:
                if integration_config.is_enabled:
                    for action_config in integration_config.action_configs:
                        if action_config.is_enabled:
                            enabled_actions.append(action_config.action_id)
        return enabled_actions


