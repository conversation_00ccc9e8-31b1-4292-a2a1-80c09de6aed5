from typing import List, Dict, Any, Optional
from pydantic import Field, HttpUrl
from datetime import datetime
from enum import Enum
from .base import BaseModel, BaseModelWithTenant
from uuid import UUID

class IntegrationType(str, Enum):
    """Types of integrations available"""
    EMAIL = "email"
    CALENDAR = "calendar"
    CRM = "crm"
    PRODUCTIVITY = "productivity"
    COMMUNICATION = "communication"
    STORAGE = "storage"
    SOCIAL_MEDIA = "social_media"
    ANALYTICS = "analytics"
    PAYMENT = "payment"
    CUSTOM = "custom"

class AuthType(str, Enum):
    """Authentication types for integrations"""
    OAUTH2 = "oauth2"
    API_KEY = "api_key"
    BASIC_AUTH = "basic_auth"
    BEARER_TOKEN = "bearer_token"
    NO_AUTH = "no_auth"

class Integration(BaseModelWithTenant):
    """Model representing a third-party service integration"""
    name: str = Field(..., description="Name of the integration (e.g., 'Gmail', 'Google Calendar')")
    provider: str = Field(..., description="Provider name (e.g., 'Google', 'Microsoft', 'Salesforce')")
    description: Optional[str] = Field(None, description="Description of the integration")
    integration_type: IntegrationType = Field(..., description="Type of integration")
    
    # Visual properties
    icon_url: Optional[HttpUrl] = Field(None, description="URL to the integration's icon")
    color: Optional[str] = Field(None, description="Brand color for the integration (hex code)")
    
    # Technical configuration
    base_url: Optional[HttpUrl] = Field(None, description="Base URL for the integration's API")
    auth_type: AuthType = Field(..., description="Authentication method required")
    auth_config: Optional[Dict[str, Any]] = Field(default_factory=dict, 
        description="Authentication configuration (OAuth endpoints, scopes, etc.)")
    
    # Capabilities
    supported_actions: List[str] = Field(default_factory=list, 
        description="List of action names this integration supports")
    rate_limits: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Rate limiting configuration")
    
    # Status
    is_active: bool = Field(True, description="Whether this integration is available")
    is_system: bool = Field(False, description="Whether this is a system-provided integration")
    version: str = Field("1.0.0", description="Integration version")
    
    # Metadata
    documentation_url: Optional[HttpUrl] = Field(None, description="URL to integration documentation")
    support_url: Optional[HttpUrl] = Field(None, description="URL for support/help")
    
    class Config:
        use_enum_values = True

class UserIntegrationConnection(BaseModelWithTenant):
    """Represents a user's connection to an integration"""
    integration_id: UUID = Field(..., description="ID of the integration")
    user_id: UUID = Field(..., description="ID of the user")
    
    # Authentication data (encrypted)
    auth_data: Dict[str, Any] = Field(default_factory=dict, 
        description="Encrypted authentication credentials")
    
    # Connection status
    is_connected: bool = Field(False, description="Whether the connection is active")
    is_authorized: bool = Field(False, description="Whether the user has authorized access")
    last_sync: Optional[datetime] = Field(None, description="Last successful sync/connection test")
    
    # Configuration
    enabled_actions: List[str] = Field(default_factory=list,
        description="List of action names enabled for this connection")
    custom_settings: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="User-specific settings for this integration")
    
    # Error tracking
    last_error: Optional[str] = Field(None, description="Last error message if connection failed")
    error_count: int = Field(0, description="Number of consecutive errors")
    
    def is_healthy(self) -> bool:
        """Check if the connection is healthy"""
        return self.is_connected and self.is_authorized and self.error_count < 5
