from typing import List, Dict, Any, Optional, Union, Literal
from pydantic import Field, HttpUrl
from datetime import datetime
from enum import Enum
from uuid import UUID
from .base import BaseModel, BaseModelWithTenant

class ActionType(str, Enum):
    """
    Defines the types of actions an AI agent can perform.
    """
    API_CALL = "api_call"
    FUNCTION_CALL = "function_call"
    PROMPT_TEMPLATE = "prompt_template"
    KNOWLEDGE_BASE_ANSWER = "knowledge_base_answer"
    # Add other action types as needed

class BaseAction(BaseModelWithTenant):
    name: str = Field(..., description="A human-readable name for the action.")
    description: Optional[str] = Field(None, description="A detailed description of what the action does.")
    action_type: ActionType = Field(..., description="The type of the action.")
    
    # Integration association
    integration_id: Optional[UUID] = Field(None, description="ID of the integration this action belongs to")
    
    # Action metadata
    category: Optional[str] = Field(None, description="Category for grouping actions")
    tags: List[str] = Field(default_factory=list, description="Tags for action discovery")
    
    # Visibility and availability
    is_visible: bool = Field(True, description="Whether this action is visible to users")
    is_enabled: bool = Field(True, description="Whether this action can be executed")
    requires_auth: bool = Field(False, description="Whether this action requires authentication")
    
    # Execution properties
    timeout_seconds: Optional[int] = Field(30, description="Maximum execution time in seconds")
    retry_count: int = Field(0, description="Number of retry attempts on failure")
    
    # Input/Output schema
    input_schema: Optional[Dict[str, Any]] = Field(None, description="JSON schema for input validation")
    output_schema: Optional[Dict[str, Any]] = Field(None, description="JSON schema for output validation")

    class Config:
        # This allows Pydantic to work well with MongoDB's _id field if you choose to map action_id to _id
        # For example, you could add an alias: action_id: str = Field(default_factory=uuid.uuid4, alias='_id')
        # However, managing _id explicitly or letting MongoDB handle it is often simpler.
        use_enum_values = True # Ensures enum values are used in serialization

class APICallAction(BaseAction):
    """
    Action model for making an API call.
    """
    action_type: ActionType = ActionType.API_CALL
    url: HttpUrl = Field(..., description="The URL for the API endpoint.")
    method: str = Field(..., description="HTTP method (e.g., GET, POST, PUT).")
    headers_template: Optional[Dict[str, str]] = Field(None, description="Template for request headers. Can use placeholders.")
    payload_template: Optional[Dict[str, Any]] = Field(None, description="Template for the request payload/body. Can use placeholders.")

class FunctionCallAction(BaseAction):
    """
    Action model for executing a Python function.
    """
    action_type: ActionType = ActionType.FUNCTION_CALL
    function_name: str = Field(..., description="The fully qualified name of the function to call (e.g., 'my_module.my_function').")
    parameters_schema: Optional[Dict[str, Any]] = Field(
        None,
        description="A JSON schema defining the expected parameters for the function. Helps with validation and input generation."
    )
    # The actual function execution logic would be handled by the agent runtime.

class PromptTemplateAction(BaseAction):
    """
    Action model for using a prompt template.
    """
    action_type: ActionType = ActionType.PROMPT_TEMPLATE
    template_string: str = Field(..., description="The prompt template string with placeholders (e.g., 'Summarize this: {{text_input}}').")
    input_variables: List[str] = Field(..., description="A list of variable names expected by the template string.")
    output_parser: Optional[str] = Field(None, description="Optional name of a parser to process the LLM output (e.g., 'json_parser').")

class KnowledgeBaseAnswerAction(BaseAction):
    """
    Action model for querying a knowledge base (e.g., in MongoDB) and generating an answer.
    This is a specialized action that might involve multiple steps internally.
    """
    action_type: ActionType = ActionType.KNOWLEDGE_BASE_ANSWER
    max_retrieved_documents: int = Field(5, description="Maximum number of documents to retrieve from the knowledge base.")