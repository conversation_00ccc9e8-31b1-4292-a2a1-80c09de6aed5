from typing import List, Dict, Any, Optional
from pydantic import Field
from datetime import datetime
from uuid import UUID
from .base import BaseModel as BaseDBModel

class EntityField(BaseDBModel):
    name: str = Field(..., description="The name of the field")
    field_type: str = Field(..., description="The data type of the field (string, number, boolean, date, etc.)")
    required: bool = Field(default=False, description="Whether this field is required")
    description: Optional[str] = Field(None, description="A description of what this field represents")
    default_value: Optional[Any] = Field(None, description="Default value for this field if not provided")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Optional validation rules for the field")

class EntityDefinition(BaseDBModel):
    """Entity definition model for schema definition"""
    name: str = Field(..., description="The unique name of this entity type")
    description: Optional[str] = Field(None, description="A description of what this entity type represents")
    fields: List[EntityField] = Field(..., description="The list of fields that make up this entity type")
    is_active: bool = Field(default=True, description="Whether this entity type is currently active")

class EntityInstance(BaseDBModel):
    """Entity instance model for data records"""
    entity_type: str = Field(..., description="The type of entity this instance represents")
    data: Dict[str, Any] = Field(..., description="The actual data values for this entity instance")
    owner_id: UUID = Field(..., description="The ID of the user who owns this entity instance")
    is_active: bool = Field(default=True, description="Whether this entity instance is currently active")
