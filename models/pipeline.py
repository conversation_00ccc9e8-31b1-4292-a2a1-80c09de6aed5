from typing import List, Dict, Any, Optional, Union, Literal
from pydantic import Field, field_validator
from datetime import datetime
from enum import Enum
from uuid import UUID
from .base import BaseModel, BaseModelWithTenant

class NodeType(str, Enum):
    """Types of pipeline nodes"""
    DATA_SOURCE = "data_source"
    ENTITY_SCHEMA = "entity_schema"
    PROCESSOR = "processor"
    PARENT_CHILD_INDEXER = "parent_child_indexer"
    ENTITY_INSERTER = "entity_inserter"
    TRANSFORMER = "transformer"
    FILTER = "filter"
    CONDITIONAL = "conditional"

class DataSourceType(str, Enum):
    """Types of data sources"""
    GOOGLE_DRIVE = "google_drive"
    DROPBOX = "dropbox"
    LOCAL_FILES = "local_files"
    WEB_SCRAPING = "web_scraping"
    API_ENDPOINT = "api_endpoint"
    DATABASE = "database"
    ENTITY_COLLECTION = "entity_collection"

class TriggerType(str, Enum):
    """Pipeline trigger types"""
    MANUAL = "manual"
    SCHEDULED = "scheduled"
    WEBHOOK = "webhook"
    FILE_CHANGE = "file_change"
    ENTITY_CHANGE = "entity_change"

class NodeStatus(str, Enum):
    """Node execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class PipelineStatus(str, Enum):
    """Pipeline execution status"""
    DRAFT = "draft"
    ACTIVE = "active"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

# Base Node Configuration
class BaseNodeConfig(BaseModel):
    """Base configuration for all nodes"""
    node_type: NodeType = Field(..., description="Type of the node")
    name: str = Field(..., description="Human-readable name for the node")
    description: Optional[str] = Field(None, description="Description of what this node does")
    enabled: bool = Field(True, description="Whether this node is enabled")
    retry_count: int = Field(3, description="Number of retries on failure")
    timeout_seconds: int = Field(300, description="Timeout in seconds")

class DataSourceNodeConfig(BaseNodeConfig):
    """Configuration for data source nodes"""
    node_type: Literal[NodeType.DATA_SOURCE] = Field(NodeType.DATA_SOURCE)
    source_type: DataSourceType = Field(..., description="Type of data source")
    connection_config: Dict[str, Any] = Field(default_factory=dict, 
        description="Connection configuration (credentials, paths, etc.)")
    filter_config: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Filtering configuration (file types, date ranges, etc.)")
    
    # Schedule configuration
    schedule_cron: Optional[str] = Field(None, description="Cron expression for scheduled execution")
    incremental: bool = Field(True, description="Whether to process only new/changed items")
    last_sync_time: Optional[datetime] = Field(None, description="Last successful sync time")

class EntitySchemaNodeConfig(BaseNodeConfig):
    """Configuration for entity schema nodes"""
    node_type: Literal[NodeType.ENTITY_SCHEMA] = Field(NodeType.ENTITY_SCHEMA)
    entity_type: str = Field(..., description="Target collection name to insert documents")
    schema_config: Dict[str, Any] = Field(default_factory=dict,
        description="Schema configuration for the collection")

class ProcessorNodeConfig(BaseNodeConfig):
    """Configuration for processing nodes"""
    node_type: Literal[NodeType.PROCESSOR] = Field(NodeType.PROCESSOR)
    processor_type: str = Field(..., description="Type of processor (text_splitter, metadata_extractor, etc.)")
    processor_config: Dict[str, Any] = Field(default_factory=dict,
        description="Processor-specific configuration")

class ParentChildIndexerConfig(BaseNodeConfig):
    """Configuration for parent-child indexing nodes"""
    node_type: Literal[NodeType.PARENT_CHILD_INDEXER] = Field(NodeType.PARENT_CHILD_INDEXER)
    target_chunk_entity: str = Field(..., description="Target entity type for chunks")
    parent_id_field: str = Field("parent_id", description="Field name for parent reference")
    chunk_size: int = Field(1000, description="Size of chunks in characters")
    chunk_overlap: int = Field(200, description="Overlap between chunks")
    parent_metadata_fields: List[str] = Field(default_factory=list,
        description="Parent metadata fields to copy to chunks")
    
    # Index projection configuration (inspired by Azure Search)
    projection_config: Dict[str, Any] = Field(default_factory=dict,
        description="Configuration for index projection mappings")

class EntityInserterConfig(BaseNodeConfig):
    """Configuration for entity insertion nodes"""
    node_type: Literal[NodeType.ENTITY_INSERTER] = Field(NodeType.ENTITY_INSERTER)
    target_entity_type: str = Field(..., description="Target entity type")
    upsert_mode: bool = Field(True, description="Whether to update existing entities")
    conflict_resolution: str = Field("overwrite", description="How to handle conflicts")

# Pipeline Node Definition
class PipelineNode(BaseModel):
    """A node in the pipeline"""
    config: Union[
        DataSourceNodeConfig,
        EntitySchemaNodeConfig, 
        ProcessorNodeConfig,
        ParentChildIndexerConfig,
        EntityInserterConfig
    ] = Field(..., description="Node configuration", discriminator='node_type')
    
    # Graph connections
    input_nodes: List[str] = Field(default_factory=list, description="IDs of input nodes")
    output_nodes: List[str] = Field(default_factory=list, description="IDs of output nodes")
    
    # Runtime data
    last_execution: Optional[datetime] = Field(None, description="Last execution time")
    last_status: Optional[NodeStatus] = Field(None, description="Last execution status")
    last_error: Optional[str] = Field(None, description="Last error message")

# Pipeline Definition
class Pipeline(BaseModelWithTenant):
    """Pipeline definition for data processing workflows"""
    name: str = Field(..., description="Pipeline name")
    description: Optional[str] = Field(None, description="Pipeline description")
    version: str = Field("1.0.0", description="Pipeline version")
    
    # Nodes and connections
    nodes: List[PipelineNode] = Field(..., description="Pipeline nodes")
    
    # Trigger configuration
    trigger_type: TriggerType = Field(TriggerType.MANUAL, description="How the pipeline is triggered")
    trigger_config: Dict[str, Any] = Field(default_factory=dict,
        description="Trigger-specific configuration")
    
    # Status and metadata
    status: PipelineStatus = Field(PipelineStatus.DRAFT, description="Pipeline status")
    is_active: bool = Field(True, description="Whether pipeline is active")
    
    # Execution settings
    max_concurrent_nodes: int = Field(5, description="Maximum nodes to run concurrently")
    execution_timeout_minutes: int = Field(60, description="Maximum pipeline execution time")
    
    @field_validator('nodes')
    @classmethod
    def validate_nodes(cls, v):
        """Validate node connections"""
        node_ids = {node.id for node in v}
        
        for node in v:
            # Check input node references
            for input_id in node.input_nodes:
                if input_id not in node_ids:
                    raise ValueError(f"Node {node.id} references non-existent input node {input_id}")
            
            # Check output node references
            for output_id in node.output_nodes:
                if output_id not in node_ids:
                    raise ValueError(f"Node {node.id} references non-existent output node {output_id}")
        
        return v

# Pipeline Execution
class PipelineExecution(BaseModelWithTenant):
    """Record of a pipeline execution"""
    pipeline_id: UUID = Field(..., description="ID of the executed pipeline")
    pipeline_version: str = Field(..., description="Version of the pipeline")
    
    # Execution details
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Execution start time")
    completed_at: Optional[datetime] = Field(None, description="Execution completion time")
    status: PipelineStatus = Field(PipelineStatus.RUNNING, description="Execution status")
    
    # Results
    nodes_completed: int = Field(0, description="Number of nodes completed")
    nodes_failed: int = Field(0, description="Number of nodes failed")
    total_nodes: int = Field(0, description="Total number of nodes")
    
    # Execution metadata
    trigger_data: Optional[Dict[str, Any]] = Field(default_factory=dict,
        description="Data that triggered the execution")
    execution_logs: List[Dict[str, Any]] = Field(default_factory=list,
        description="Execution logs")
    error_message: Optional[str] = Field(None, description="Error message if failed")

class NodeExecution(BaseModel):
    """Record of a node execution within a pipeline run"""
    execution_id: UUID = Field(..., description="Pipeline execution ID")
    node_id: str = Field(..., description="Node ID")
    
    # Execution details
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = Field(None)
    status: NodeStatus = Field(NodeStatus.PENDING)
    
    # Results
    input_data: Optional[Dict[str, Any]] = Field(default_factory=dict)
    output_data: Optional[Dict[str, Any]] = Field(default_factory=dict)
    processing_stats: Optional[Dict[str, Any]] = Field(default_factory=dict)
    error_message: Optional[str] = Field(None)
    retry_count: int = Field(0)
