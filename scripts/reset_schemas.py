import asyncio
import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from data_access.weaviatedb import connect_to_weaviate, close_weaviate_connection, reset_schemas

logging.basicConfig(level=logging.INFO)

async def main():
    """Reset database schemas"""
    try:
        print("Connecting to database...")
        await connect_to_weaviate()
        
        print("Resetting schemas...")
        await reset_schemas()
        
        print("Schema reset completed successfully!")
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    finally:
        await close_weaviate_connection()

if __name__ == "__main__":
    asyncio.run(main())
