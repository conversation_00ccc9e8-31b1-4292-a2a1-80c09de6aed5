#!/usr/bin/env python3
"""
Library to Shelf Hierarchy Migration Script

This script migrates the existing Library → Resource relationships
to the new Library → Shelf → Resource hierarchy.

Migration Steps:
1. Create default shelves for each library
2. Assign existing resources to appropriate shelves based on their type
3. Update resource records to include shelf_id
4. Verify data integrity after migration

Usage:
    python scripts/migrate_to_shelf_hierarchy.py [--dry-run] [--tenant-id TENANT_ID]
"""

import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path
from datetime import datetime, timezone
from uuid import UUID, uuid4

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_access.factory import RepositoryFactory
from models.library import Library
from models.resource import Shelf, ResourceType, FileResource, ArticleResource, WebResource
from services.library_service import LibraryService
from services.shelf_service import ShelfService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ShelfMigration:
    """Handles the migration from Library-Resource to Library-Shelf-Resource hierarchy"""
    
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.library_repo = None
        self.shelf_repo = None
        self.file_repo = None
        self.article_repo = None
        self.web_repo = None
        self.migration_stats = {
            'libraries_processed': 0,
            'shelves_created': 0,
            'resources_migrated': 0,
            'errors': []
        }

    async def initialize(self):
        """Initialize repositories"""
        try:
            self.library_repo = await RepositoryFactory.create_library_repository()
            self.shelf_repo = await RepositoryFactory.create_shelf_repository()
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            logger.info("Migration repositories initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize repositories: {e}")
            raise

    async def migrate_tenant(self, tenant_id: str = None):
        """Migrate all libraries for a specific tenant or all tenants"""
        try:
            # Get all libraries
            query_conditions = {"is_deleted": False}
            if tenant_id:
                query_conditions["tenant_id"] = tenant_id
                
            libraries = await self.library_repo.get_all(query_conditions=query_conditions)
            
            logger.info(f"Found {len(libraries)} libraries to migrate")
            
            for library in libraries:
                await self.migrate_library(library)
                
            logger.info("Migration completed successfully")
            self.print_migration_summary()
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise

    async def migrate_library(self, library: Library):
        """Migrate a single library to shelf-based hierarchy"""
        try:
            logger.info(f"Migrating library: {library.name} (ID: {library.id})")
            
            # Step 1: Create default shelves for this library
            shelves = await self.create_default_shelves(library)
            
            # Step 2: Migrate resources to appropriate shelves
            await self.migrate_library_resources(library, shelves)
            
            self.migration_stats['libraries_processed'] += 1
            logger.info(f"Successfully migrated library: {library.name}")
            
        except Exception as e:
            error_msg = f"Failed to migrate library {library.name}: {e}"
            logger.error(error_msg)
            self.migration_stats['errors'].append(error_msg)

    async def create_default_shelves(self, library: Library) -> dict:
        """Create default shelves for each resource type"""
        shelves = {}
        
        default_shelf_configs = [
            {
                "name": "Documents",
                "description": "File-based resources like PDFs, Word documents, etc.",
                "resource_type": ResourceType.FILE
            },
            {
                "name": "Articles", 
                "description": "Text articles and written content",
                "resource_type": ResourceType.ARTICLE
            },
            {
                "name": "Web Pages",
                "description": "Web-crawled content and online resources", 
                "resource_type": ResourceType.WEB
            }
        ]

        for config in default_shelf_configs:
            try:
                # Check if shelf already exists
                existing_shelves = await self.shelf_repo.get_all(
                    query_conditions={
                        "library_id": str(library.id),
                        "resource_type": config["resource_type"].value,
                        "is_deleted": False
                    }
                )
                
                if existing_shelves:
                    logger.info(f"Shelf for {config['resource_type'].value} already exists in library {library.name}")
                    shelves[config["resource_type"]] = existing_shelves[0]
                    continue

                if not self.dry_run:
                    shelf = Shelf(
                        library_id=library.id,
                        name=config["name"],
                        description=config["description"],
                        resource_type=config["resource_type"],
                        resource_count=0,
                        tenant_id=library.tenant_id,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        created_by=library.created_by
                    )
                    
                    created_shelf = await self.shelf_repo.create(shelf, str(library.tenant_id))
                    shelves[config["resource_type"]] = created_shelf
                    self.migration_stats['shelves_created'] += 1
                    logger.info(f"Created shelf '{config['name']}' for library {library.name}")
                else:
                    logger.info(f"[DRY RUN] Would create shelf '{config['name']}' for library {library.name}")
                    # Create a mock shelf for dry run
                    shelf = Shelf(
                        id=uuid4(),
                        library_id=library.id,
                        name=config["name"],
                        description=config["description"],
                        resource_type=config["resource_type"],
                        resource_count=0,
                        tenant_id=library.tenant_id,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        created_by=library.created_by
                    )
                    shelves[config["resource_type"]] = shelf
                    
            except Exception as e:
                error_msg = f"Failed to create shelf {config['name']} for library {library.name}: {e}"
                logger.error(error_msg)
                self.migration_stats['errors'].append(error_msg)

        return shelves

    async def migrate_library_resources(self, library: Library, shelves: dict):
        """Migrate all resources in a library to appropriate shelves"""
        
        # Migrate file resources
        await self.migrate_resources_by_type(
            self.file_repo, 
            ResourceType.FILE, 
            shelves.get(ResourceType.FILE),
            str(library.tenant_id)
        )
        
        # Migrate article resources
        await self.migrate_resources_by_type(
            self.article_repo, 
            ResourceType.ARTICLE, 
            shelves.get(ResourceType.ARTICLE),
            str(library.tenant_id)
        )
        
        # Migrate web resources
        await self.migrate_resources_by_type(
            self.web_repo, 
            ResourceType.WEB, 
            shelves.get(ResourceType.WEB),
            str(library.tenant_id)
        )

    async def migrate_resources_by_type(self, repo, resource_type: ResourceType, shelf: Shelf, tenant_id: str):
        """Migrate resources of a specific type to their shelf"""
        if not shelf:
            logger.warning(f"No shelf found for resource type {resource_type.value}")
            return

        try:
            # Get all resources that don't have a shelf_id yet
            resources = await repo.get_all(
                query_conditions={
                    "shelf_id": None,  # Resources not yet assigned to a shelf
                    "is_deleted": False,
                    "tenant_id": tenant_id
                }
            )
            
            logger.info(f"Found {len(resources)} {resource_type.value} resources to migrate")
            
            for resource in resources:
                try:
                    if not self.dry_run:
                        # Assign resource to shelf
                        resource.shelf_id = shelf.id
                        resource.updated_at = datetime.now(timezone.utc)
                        
                        await repo.update(str(resource.id), resource)
                        self.migration_stats['resources_migrated'] += 1
                        logger.debug(f"Assigned {resource_type.value} resource {resource.id} to shelf {shelf.id}")
                    else:
                        logger.debug(f"[DRY RUN] Would assign {resource_type.value} resource {resource.id} to shelf {shelf.id}")
                        self.migration_stats['resources_migrated'] += 1
                        
                except Exception as e:
                    error_msg = f"Failed to migrate {resource_type.value} resource {resource.id}: {e}"
                    logger.error(error_msg)
                    self.migration_stats['errors'].append(error_msg)
                    
        except Exception as e:
            error_msg = f"Failed to get {resource_type.value} resources: {e}"
            logger.error(error_msg)
            self.migration_stats['errors'].append(error_msg)

    def print_migration_summary(self):
        """Print migration statistics"""
        print("\n" + "="*50)
        print("MIGRATION SUMMARY")
        print("="*50)
        print(f"Libraries processed: {self.migration_stats['libraries_processed']}")
        print(f"Shelves created: {self.migration_stats['shelves_created']}")
        print(f"Resources migrated: {self.migration_stats['resources_migrated']}")
        print(f"Errors encountered: {len(self.migration_stats['errors'])}")
        
        if self.migration_stats['errors']:
            print("\nErrors:")
            for error in self.migration_stats['errors']:
                print(f"  - {error}")
        
        if self.dry_run:
            print("\n⚠️  This was a DRY RUN - no actual changes were made")
        else:
            print("\n✅ Migration completed successfully")

async def main():
    """Main migration function"""
    parser = argparse.ArgumentParser(description='Migrate libraries to shelf-based hierarchy')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Run migration in dry-run mode (no actual changes)')
    parser.add_argument('--tenant-id', type=str, 
                       help='Migrate only libraries for specific tenant ID')
    
    args = parser.parse_args()
    
    if args.dry_run:
        print("🔍 Running in DRY RUN mode - no changes will be made")
    
    try:
        # Initialize database connections
        from data_access.weaviatedb import connect_to_weaviate
        await connect_to_weaviate()
        
        # Run migration
        migration = ShelfMigration(dry_run=args.dry_run)
        await migration.initialize()
        await migration.migrate_tenant(args.tenant_id)
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)
    finally:
        # Close database connections
        from data_access.weaviatedb import close_weaviate_connection
        await close_weaviate_connection()

if __name__ == "__main__":
    asyncio.run(main())
