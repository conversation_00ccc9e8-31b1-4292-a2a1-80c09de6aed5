#!/usr/bin/env python3
"""
Shelf Implementation Validation Script

This script validates that the Library → Shelf → Resource hierarchy
implementation is complete and functional.

Usage:
    python scripts/validate_shelf_implementation.py
"""

import os
import sys
import importlib.util
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ShelfImplementationValidator:
    """Validates the shelf implementation"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0

    def check(self, condition, success_msg, error_msg):
        """Helper method to check a condition and track results"""
        self.total_checks += 1
        if condition:
            print(f"✅ {success_msg}")
            self.success_count += 1
            return True
        else:
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False

    def warn(self, condition, success_msg, warning_msg):
        """Helper method to check a condition and issue warnings"""
        if condition:
            print(f"✅ {success_msg}")
            return True
        else:
            print(f"⚠️  {warning_msg}")
            self.warnings.append(warning_msg)
            return False

    def validate_backend_models(self):
        """Validate backend models are properly defined"""
        print("\n🔍 Validating Backend Models...")
        
        try:
            from models.resource import Shelf, ResourceType, ResourceBase
            self.check(True, "Shelf model imported successfully", "Failed to import Shelf model")
            
            # Check Shelf model has required fields
            shelf_fields = ['library_id', 'name', 'resource_type', 'resource_count']
            for field in shelf_fields:
                self.check(
                    hasattr(Shelf, '__annotations__') and field in Shelf.__annotations__,
                    f"Shelf model has {field} field",
                    f"Shelf model missing {field} field"
                )
            
            # Check ResourceType enum
            expected_types = ['FILE', 'ARTICLE', 'WEB']
            for resource_type in expected_types:
                self.check(
                    hasattr(ResourceType, resource_type),
                    f"ResourceType has {resource_type}",
                    f"ResourceType missing {resource_type}"
                )
            
            # Check ResourceBase has shelf_id
            self.check(
                hasattr(ResourceBase, '__annotations__') and 'shelf_id' in ResourceBase.__annotations__,
                "ResourceBase has shelf_id field",
                "ResourceBase missing shelf_id field"
            )
            
        except ImportError as e:
            self.check(False, "", f"Failed to import models: {e}")

    def validate_backend_services(self):
        """Validate backend services are properly implemented"""
        print("\n🔍 Validating Backend Services...")
        
        try:
            from services.shelf_service import ShelfService
            self.check(True, "ShelfService imported successfully", "Failed to import ShelfService")
            
            # Check ShelfService methods
            service_methods = ['create_shelf', 'get_shelf', 'update_shelf', 'delete_shelf', 'list_library_shelves']
            for method in service_methods:
                self.check(
                    hasattr(ShelfService, method),
                    f"ShelfService has {method} method",
                    f"ShelfService missing {method} method"
                )
            
        except ImportError as e:
            self.check(False, "", f"Failed to import ShelfService: {e}")
        
        try:
            from services.library_service import LibraryService
            
            # Check LibraryService has shelf-related methods
            shelf_methods = ['add_resource_to_shelf', '_create_default_shelves']
            for method in shelf_methods:
                self.check(
                    hasattr(LibraryService, method),
                    f"LibraryService has {method} method",
                    f"LibraryService missing {method} method"
                )
            
        except ImportError as e:
            self.check(False, "", f"Failed to import LibraryService: {e}")

    def validate_backend_routes(self):
        """Validate backend API routes are properly defined"""
        print("\n🔍 Validating Backend Routes...")
        
        # Check shelf routes file exists
        shelf_routes_path = project_root / "routes" / "shelves.py"
        self.check(
            shelf_routes_path.exists(),
            "Shelf routes file exists",
            "Shelf routes file missing"
        )
        
        if shelf_routes_path.exists():
            try:
                from routes.shelves import router
                self.check(True, "Shelf router imported successfully", "Failed to import shelf router")
            except ImportError as e:
                self.check(False, "", f"Failed to import shelf router: {e}")
        
        # Check main.py includes shelf routes
        main_py_path = project_root / "main.py"
        if main_py_path.exists():
            with open(main_py_path, 'r') as f:
                content = f.read()
            
            self.check(
                'shelves' in content and 'shelves.router' in content,
                "Shelf routes registered in main.py",
                "Shelf routes not registered in main.py"
            )

    def validate_repository_factory(self):
        """Validate repository factory has shelf methods"""
        print("\n🔍 Validating Repository Factory...")
        
        try:
            from data_access.factory import RepositoryFactory
            
            # Check factory has shelf repository method
            self.check(
                hasattr(RepositoryFactory, 'create_shelf_repository'),
                "RepositoryFactory has create_shelf_repository method",
                "RepositoryFactory missing create_shelf_repository method"
            )
            
        except ImportError as e:
            self.check(False, "", f"Failed to import RepositoryFactory: {e}")

    def validate_model_registry(self):
        """Validate model registry includes Shelf"""
        print("\n🔍 Validating Model Registry...")
        
        try:
            from models.registry import model_registry

            # Check Shelf is in registry
            from models.resource import Shelf
            self.check(
                Shelf in model_registry,
                "Shelf model registered in model_registry",
                "Shelf model not registered in model_registry"
            )

        except ImportError as e:
            self.check(False, "", f"Failed to import model_registry: {e}")

    def validate_frontend_services(self):
        """Validate frontend services have shelf methods"""
        print("\n🔍 Validating Frontend Services...")
        
        service_path = project_root / "src" / "services" / "dataLibraryService.js"
        if service_path.exists():
            with open(service_path, 'r') as f:
                content = f.read()
            
            shelf_methods = ['getLibraryShelves', 'createShelf', 'updateShelf', 'deleteShelf', 'addResourceToShelf']
            for method in shelf_methods:
                self.check(
                    method in content,
                    f"dataLibraryService has {method} method",
                    f"dataLibraryService missing {method} method"
                )
        else:
            self.check(False, "", "dataLibraryService.js file not found")

    def validate_frontend_components(self):
        """Validate frontend components exist"""
        print("\n🔍 Validating Frontend Components...")
        
        components = [
            ("ShelfManager", "src/features/data-library/components/ShelfManager.jsx"),
            ("ShelfCard", "src/features/data-library/components/ShelfCard.jsx"),
            ("ShelfSelector", "src/features/data-library/components/ShelfSelector.jsx")
        ]
        
        for component_name, component_path in components:
            full_path = project_root / component_path
            self.check(
                full_path.exists(),
                f"{component_name} component exists",
                f"{component_name} component missing at {component_path}"
            )

    def validate_library_details_page(self):
        """Validate LibraryDetailsPage has shelf integration"""
        print("\n🔍 Validating LibraryDetailsPage Integration...")
        
        page_path = project_root / "src" / "features" / "data-library" / "pages" / "LibraryDetailsPage.jsx"
        if page_path.exists():
            with open(page_path, 'r') as f:
                content = f.read()
            
            self.check(
                'ShelfManager' in content,
                "LibraryDetailsPage imports ShelfManager",
                "LibraryDetailsPage missing ShelfManager import"
            )
            
            self.check(
                'shelves' in content.lower(),
                "LibraryDetailsPage has shelves tab",
                "LibraryDetailsPage missing shelves tab"
            )
        else:
            self.check(False, "", "LibraryDetailsPage.jsx not found")

    def validate_tests(self):
        """Validate test files exist"""
        print("\n🔍 Validating Tests...")
        
        test_files = [
            "tests/services/test_shelf_service.py",
            "tests/integration/test_shelf_api.py"
        ]
        
        for test_file in test_files:
            test_path = project_root / test_file
            self.check(
                test_path.exists(),
                f"Test file {test_file} exists",
                f"Test file {test_file} missing"
            )

    def run_validation(self):
        """Run all validation checks"""
        print("🚀 Starting Shelf Implementation Validation...")
        print("=" * 60)
        
        # Run all validation methods
        self.validate_backend_models()
        self.validate_backend_services()
        self.validate_backend_routes()
        self.validate_repository_factory()
        self.validate_model_registry()
        self.validate_frontend_services()
        self.validate_frontend_components()
        self.validate_library_details_page()
        self.validate_tests()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        print(f"✅ Successful checks: {self.success_count}/{self.total_checks}")
        print(f"❌ Failed checks: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        success_rate = (self.success_count / self.total_checks) * 100 if self.total_checks > 0 else 0
        
        if success_rate >= 90:
            print(f"\n🎉 EXCELLENT! Implementation is {success_rate:.1f}% complete")
        elif success_rate >= 75:
            print(f"\n👍 GOOD! Implementation is {success_rate:.1f}% complete")
        elif success_rate >= 50:
            print(f"\n⚠️  PARTIAL! Implementation is {success_rate:.1f}% complete")
        else:
            print(f"\n❌ INCOMPLETE! Implementation is only {success_rate:.1f}% complete")
        
        return len(self.errors) == 0

def main():
    """Main validation function"""
    validator = ShelfImplementationValidator()
    success = validator.run_validation()
    
    if success:
        print("\n✅ All validations passed! The shelf implementation is ready.")
        sys.exit(0)
    else:
        print("\n❌ Some validations failed. Please address the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
