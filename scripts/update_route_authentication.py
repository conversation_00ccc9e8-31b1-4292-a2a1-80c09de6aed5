#!/usr/bin/env python3
"""
Route Authentication Update Script

This script updates all route files to use the new secure authentication
system instead of the old database-dependent authentication.
"""

import os
import sys
import re
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def update_route_file(file_path: Path) -> bool:
    """
    Update a single route file to use secure authentication
    
    Args:
        file_path: Path to the route file
        
    Returns:
        bool: True if file was updated, False otherwise
    """
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # 1. Update imports
        # Replace old middleware imports
        content = re.sub(
            r'from middleware\.permission import PermissionChecker.*\n',
            'from security.auth_middleware import requires_permissions, get_current_user, StatelessUser\n',
            content
        )
        
        # Replace RunningContext imports
        content = re.sub(
            r'from session\.running_context import RunningContext.*\n',
            '# RunningContext replaced with Stateless<PERSON>ser from secure authentication\n',
            content
        )
        
        # Add secure auth import if not present
        if 'from security.auth_middleware import' not in content:
            # Find the last import line and add after it
            import_lines = []
            other_lines = []
            in_imports = True
            
            for line in content.split('\n'):
                if in_imports and (line.startswith('from ') or line.startswith('import ') or line.strip() == '' or line.startswith('#')):
                    import_lines.append(line)
                else:
                    in_imports = False
                    other_lines.append(line)
            
            # Add the secure auth import
            import_lines.append('from security.auth_middleware import requires_permissions, get_current_user, StatelessUser')
            
            content = '\n'.join(import_lines + other_lines)
        
        # 2. Update permission checker definitions
        # Replace PermissionChecker instances with requires_permissions
        permission_patterns = [
            (r'(\w+)_permission = PermissionChecker\(\[([^\]]+)\]\)', r'# \1_permission now handled by requires_permissions'),
            (r'create_(\w+)_permission = PermissionChecker.*\n', ''),
            (r'read_(\w+)_permission = PermissionChecker.*\n', ''),
            (r'update_(\w+)_permission = PermissionChecker.*\n', ''),
            (r'delete_(\w+)_permission = PermissionChecker.*\n', ''),
            (r'manage_(\w+)_permission = PermissionChecker.*\n', ''),
            (r'execute_(\w+)_permission = PermissionChecker.*\n', ''),
        ]
        
        for pattern, replacement in permission_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 3. Update route dependencies
        # Replace context: RunningContext = Depends(...) with user: StatelessUser = Depends(...)
        
        # Pattern for old authentication dependencies
        old_dep_patterns = [
            (r'context: RunningContext = Depends\((\w+_permission)\)', r'user: StatelessUser = Depends(requires_permissions([Permission.\1.value]))'),
            (r'context: RunningContext = Depends\(requires_permissions\(\[([^\]]+)\]\)\)', r'user: StatelessUser = Depends(requires_permissions([\1]))'),
            (r'context: RunningContext = Depends\(PermissionChecker\(\[([^\]]+)\]\)\)', r'user: StatelessUser = Depends(requires_permissions([\1]))'),
        ]
        
        for pattern, replacement in old_dep_patterns:
            content = re.sub(pattern, replacement, content)
        
        # 4. Update function signatures and variable usage
        # Replace context parameter with user parameter
        content = re.sub(r'async def (\w+)\([^)]*context: RunningContext[^)]*\):', 
                        lambda m: m.group(0).replace('context: RunningContext', 'user: StatelessUser'), 
                        content)
        
        # Replace context usage with user usage
        content = re.sub(r'context\.user_id', 'user.id', content)
        content = re.sub(r'context\.tenant_id', 'user.tenant_id', content)
        content = re.sub(r'context\.user', 'user', content)
        
        # 5. Update permission checks
        # Replace context.has_permission with user.has_permission
        content = re.sub(r'await context\.has_permission\(([^)]+)\)', r'user.has_permission(\1)', content)
        content = re.sub(r'await context\.has_any_permission\(([^)]+)\)', r'user.has_any_permission(\1)', content)
        
        # 6. Clean up unused imports
        if 'RunningContext' not in content and 'from session.running_context import' in content:
            content = re.sub(r'from session\.running_context import.*\n', '', content)
        
        if 'PermissionChecker' not in content and 'from middleware.permission import' in content:
            content = re.sub(r'from middleware\.permission import.*\n', '', content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error updating {file_path}: {e}")
        return False

def main():
    """Main update script"""
    print("🔄 Updating Route Authentication")
    print("=" * 50)
    
    routes_dir = project_root / "routes"
    if not routes_dir.exists():
        print("❌ Routes directory not found")
        return
    
    updated_files = []
    skipped_files = []
    
    # Process all Python files in routes directory
    for py_file in routes_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue
            
        print(f"📝 Processing {py_file.name}...")
        
        if update_route_file(py_file):
            updated_files.append(py_file.name)
            print(f"  ✅ Updated {py_file.name}")
        else:
            skipped_files.append(py_file.name)
            print(f"  ⏭️  No changes needed for {py_file.name}")
    
    print("\n" + "=" * 50)
    print("📊 Update Summary:")
    print(f"✅ Updated files: {len(updated_files)}")
    for file_name in updated_files:
        print(f"   - {file_name}")
    
    print(f"⏭️  Skipped files: {len(skipped_files)}")
    for file_name in skipped_files:
        print(f"   - {file_name}")
    
    print("\n⚠️  Manual Review Required:")
    print("1. Check that all permission values are correctly mapped")
    print("2. Verify that user.has_permission() calls use correct permission strings")
    print("3. Test all endpoints to ensure authentication works")
    print("4. Update any custom authentication logic")
    
    print("\n🔧 Next Steps:")
    print("1. Run the backend server and test authentication")
    print("2. Check for any import errors or missing dependencies")
    print("3. Test protected endpoints with valid JWT tokens")
    print("4. Update any remaining manual authentication checks")

if __name__ == "__main__":
    main()
