#!/usr/bin/env python3
"""
Migration Script for Unified Authentication Service

This script helps migrate from the old unified_unified_auth_service.py and secure_unified_unified_auth_service.py
to the new unified_unified_unified_auth_service.py by:

1. Analyzing current usage across the codebase
2. Updating import statements
3. Replacing deprecated method calls
4. Creating database indexes for refresh tokens
5. Validating the migration

Usage:
    python scripts/migrate_to_unified_auth.py --analyze    # Analyze current usage
    python scripts/migrate_to_unified_auth.py --migrate    # Perform migration
    python scripts/migrate_to_unified_auth.py --validate   # Validate migration
"""

import os
import re
import sys
import argparse
import asyncio
from pathlib import Path
from typing import List, Dict, Set, Tuple
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuthMigrationAnalyzer:
    """Analyzes current authentication service usage"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.python_files = []
        self.usage_patterns = {
            'auth_service_imports': [],
            'secure_auth_service_imports': [],
            'method_calls': [],
            'dependency_injections': []
        }
    
    def find_python_files(self) -> List[Path]:
        """Find all Python files in the project"""
        python_files = []
        for root, dirs, files in os.walk(self.project_root):
            # Skip common non-source directories
            dirs[:] = [d for d in dirs if d not in {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv', 'env'}]

            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)

        return python_files
    
    def analyze_file(self, file_path: Path) -> Dict[str, List[str]]:
        """Analyze a single file for auth service usage"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            results = {
                'auth_service_imports': [],
                'secure_auth_service_imports': [],
                'method_calls': [],
                'dependency_injections': []
            }
            
            # Check for import statements
            auth_import_patterns = [
                r'from\s+services\.auth_service\s+import\s+(.+)',
                r'import\s+services\.auth_service',
                r'from\s+services\s+import\s+auth_service'
            ]
            
            secure_auth_import_patterns = [
                r'from\s+services\.secure_auth_service\s+import\s+(.+)',
                r'import\s+services\.secure_auth_service',
                r'from\s+services\s+import\s+secure_auth_service'
            ]
            
            for pattern in auth_import_patterns:
                matches = re.findall(pattern, content, re.MULTILINE)
                if matches:
                    results['auth_service_imports'].extend(matches)
            
            for pattern in secure_auth_import_patterns:
                matches = re.findall(pattern, content, re.MULTILINE)
                if matches:
                    results['secure_auth_service_imports'].extend(matches)
            
            # Check for method calls
            method_patterns = [
                r'auth_service\.(\w+)',
                r'secure_auth_service\.(\w+)',
                r'AuthService\(\)\.(\w+)',
                r'SecureAuthService\(\)\.(\w+)'
            ]
            
            for pattern in method_patterns:
                matches = re.findall(pattern, content)
                results['method_calls'].extend(matches)
            
            # Check for dependency injection
            dependency_patterns = [
                r'Depends\(lambda: unified_auth_service\)',
                r'Depends\(lambda: unified_auth_service\)',
                r'auth_service\s*=\s*Depends\(',
                r'secure_auth_service\s*=\s*Depends\('
            ]
            
            for pattern in dependency_patterns:
                if re.search(pattern, content):
                    results['dependency_injections'].append(pattern)
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
            return {}
    
    def analyze_project(self) -> Dict[str, Dict]:
        """Analyze entire project for auth service usage"""
        python_files = self.find_python_files()
        logger.info(f"Analyzing {len(python_files)} Python files...")
        
        project_analysis = {}
        
        for file_path in python_files:
            relative_path = file_path.relative_to(self.project_root)
            analysis = self.analyze_file(file_path)
            
            # Only include files with auth service usage
            if any(analysis.values()):
                project_analysis[str(relative_path)] = analysis
        
        return project_analysis
    
    def print_analysis_report(self, analysis: Dict[str, Dict]):
        """Print analysis report"""
        print("\n" + "="*80)
        print("AUTHENTICATION SERVICE USAGE ANALYSIS")
        print("="*80)
        
        total_files = len(analysis)
        print(f"Files with auth service usage: {total_files}")
        
        if total_files == 0:
            print("No authentication service usage found!")
            return
        
        print("\nFiles requiring migration:")
        for file_path, file_analysis in analysis.items():
            print(f"\n📁 {file_path}")
            
            if file_analysis.get('auth_service_imports'):
                print(f"  🔸 auth_service imports: {file_analysis['auth_service_imports']}")
            
            if file_analysis.get('secure_auth_service_imports'):
                print(f"  🔸 secure_auth_service imports: {file_analysis['secure_auth_service_imports']}")
            
            if file_analysis.get('method_calls'):
                unique_methods = set(file_analysis['method_calls'])
                print(f"  🔸 Method calls: {list(unique_methods)}")
            
            if file_analysis.get('dependency_injections'):
                print(f"  🔸 Dependency injections: {file_analysis['dependency_injections']}")
        
        # Summary statistics
        all_methods = []
        for file_analysis in analysis.values():
            all_methods.extend(file_analysis.get('method_calls', []))
        
        unique_methods = set(all_methods)
        print(f"\n📊 Summary:")
        print(f"  • Total files to migrate: {total_files}")
        print(f"  • Unique methods used: {len(unique_methods)}")
        print(f"  • Most common methods: {list(unique_methods)[:10]}")


class AuthMigrationExecutor:
    """Executes the migration to unified auth service"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.migration_map = {
            # Import replacements
            'from services.unified_auth_service import unified_auth_service': 'from services.unified_auth_service import unified_auth_service',
            'from services.unified_auth_service import unified_auth_service': 'from services.unified_auth_service import unified_auth_service',
            'from services.unified_auth_service import unified_auth_service': 'from services.unified_auth_service import unified_auth_service',
            'from services.unified_auth_service import unified_auth_service': 'from services.unified_auth_service import unified_auth_service',
            
            # Method call replacements
            'unified_unified_auth_service.': 'unified_unified_unified_auth_service.',
            'secure_unified_unified_auth_service.': 'unified_unified_unified_auth_service.',
            'unified_unified_auth_service.': 'unified_unified_unified_auth_service.',
            'Secureunified_unified_auth_service.': 'unified_unified_unified_auth_service.',
            
            # Dependency injection replacements
            'lambda: unified_auth_service': 'lambda: unified_auth_service',
            'lambda: unified_auth_service': 'lambda: unified_auth_service',
        }
    
    def migrate_file(self, file_path: Path) -> bool:
        """Migrate a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply migration replacements
            for old_pattern, new_pattern in self.migration_map.items():
                content = content.replace(old_pattern, new_pattern)
            
            # Write back if changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"Migrated: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error migrating {file_path}: {e}")
            return False
    
    def migrate_project(self, analysis: Dict[str, Dict]) -> Dict[str, bool]:
        """Migrate entire project"""
        results = {}
        
        for file_path in analysis.keys():
            full_path = self.project_root / file_path
            success = self.migrate_file(full_path)
            results[file_path] = success
        
        return results
    
    async def create_database_indexes(self):
        """Create database indexes for refresh tokens"""
        try:
            from data_access.factory import RepositoryFactory
            from models.refresh_token import RefreshToken, TokenBlacklist
            
            logger.info("Creating database indexes for refresh tokens...")
            
            # Create repositories to ensure indexes
            refresh_repo = await RepositoryFactory.create_repository(RefreshToken)
            blacklist_repo = await RepositoryFactory.create_repository(TokenBlacklist)
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating database indexes: {e}")


async def main():
    parser = argparse.ArgumentParser(description="Migrate to unified authentication service")
    parser.add_argument("--analyze", action="store_true", help="Analyze current usage")
    parser.add_argument("--migrate", action="store_true", help="Perform migration")
    parser.add_argument("--validate", action="store_true", help="Validate migration")
    parser.add_argument("--create-indexes", action="store_true", help="Create database indexes")
    
    args = parser.parse_args()
    
    if not any([args.analyze, args.migrate, args.validate, args.create_indexes]):
        parser.print_help()
        return
    
    analyzer = AuthMigrationAnalyzer(project_root)
    executor = AuthMigrationExecutor(project_root)
    
    if args.analyze:
        logger.info("Analyzing authentication service usage...")
        analysis = analyzer.analyze_project()
        analyzer.print_analysis_report(analysis)
    
    if args.migrate:
        logger.info("Performing migration...")
        analysis = analyzer.analyze_project()
        results = executor.migrate_project(analysis)
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\nMigration completed: {successful}/{total} files migrated successfully")
    
    if args.create_indexes:
        logger.info("Creating database indexes...")
        await executor.create_database_indexes()
    
    if args.validate:
        logger.info("Validating migration...")
        # Re-analyze to check for remaining old usage
        analysis = analyzer.analyze_project()
        
        if not analysis:
            print("✅ Migration validation successful - no old auth service usage found")
        else:
            print("⚠️  Migration validation found remaining issues:")
            analyzer.print_analysis_report(analysis)


if __name__ == "__main__":
    asyncio.run(main())


# ============================================================================
# TESTING UTILITIES
# ============================================================================

class UnifiedAuthTester:
    """Test suite for unified authentication service"""

    async def test_token_creation(self):
        """Test token pair creation"""
        from services.unified_auth_service import unified_auth_service
        from models.user import User
        from uuid import uuid4

        # Create test user
        test_user = User(
            id=uuid4(),
            email="<EMAIL>",
            tenant_id=uuid4(),
            role_id=uuid4(),
            is_active=True
        )

        # Test token creation
        token_data = await unified_unified_unified_auth_service.create_token_pair(test_user)

        assert "access_token" in token_data
        assert "refresh_token" in token_data
        assert token_data["token_type"] == "bearer"

        print("✅ Token creation test passed")

    async def test_token_refresh(self):
        """Test token refresh flow"""
        from services.unified_auth_service import unified_auth_service

        # This would require a valid refresh token
        # Implementation depends on test setup
        print("✅ Token refresh test setup complete")

    async def test_blacklist_functionality(self):
        """Test token blacklisting"""
        from services.unified_auth_service import unified_auth_service

        # Test blacklist check
        is_blacklisted = await unified_unified_unified_auth_service.is_token_blacklisted("test-jti")
        assert is_blacklisted == False

        print("✅ Blacklist functionality test passed")

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Running unified auth service tests...")

        try:
            await self.test_token_creation()
            await self.test_token_refresh()
            await self.test_blacklist_functionality()

            print("✅ All tests passed!")

        except Exception as e:
            print(f"❌ Test failed: {e}")


async def run_tests():
    """Run test suite"""
    tester = UnifiedAuthTester()
    await tester.run_all_tests()
