#!/usr/bin/env python3
"""
JWT Security Migration Script

This script helps migrate from the old database-dependent JWT authentication
to the new secure, stateless JWT implementation.
"""

import os
import sys
import secrets
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def generate_secure_jwt_secret() -> str:
    """Generate a cryptographically secure JWT secret key"""
    return secrets.token_urlsafe(64)

def create_env_file():
    """Create or update .env file with secure JWT configuration"""
    env_file = project_root / ".env"
    
    # Read existing .env if it exists
    existing_vars = {}
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    existing_vars[key] = value
    
    # Generate new JWT secret if not exists or is default
    jwt_secret = existing_vars.get('JWT_SECRET_KEY')
    if not jwt_secret or len(jwt_secret) < 32:
        jwt_secret = generate_secure_jwt_secret()
        print(f"✅ Generated new secure JWT secret key")
    else:
        print(f"✅ Using existing JWT secret key")
    
    # Update environment variables
    env_vars = {
        **existing_vars,
        'JWT_SECRET_KEY': jwt_secret,
        'JWT_ALGORITHM': 'HS256',
        'JWT_ISSUER': 'assivy-api',
        'JWT_AUDIENCE': 'assivy-client',
        'JWT_ACCESS_TOKEN_EXPIRE_MINUTES': '120',
        'JWT_REFRESH_TOKEN_EXPIRE_DAYS': '30'
    }
    
    # Write updated .env file
    with open(env_file, 'w') as f:
        f.write("# JWT Security Configuration\n")
        f.write(f"JWT_SECRET_KEY={env_vars['JWT_SECRET_KEY']}\n")
        f.write(f"JWT_ALGORITHM={env_vars['JWT_ALGORITHM']}\n")
        f.write(f"JWT_ISSUER={env_vars['JWT_ISSUER']}\n")
        f.write(f"JWT_AUDIENCE={env_vars['JWT_AUDIENCE']}\n")
        f.write(f"JWT_ACCESS_TOKEN_EXPIRE_MINUTES={env_vars['JWT_ACCESS_TOKEN_EXPIRE_MINUTES']}\n")
        f.write(f"JWT_REFRESH_TOKEN_EXPIRE_DAYS={env_vars['JWT_REFRESH_TOKEN_EXPIRE_DAYS']}\n")
        f.write("\n")
        
        # Write other existing variables
        for key, value in existing_vars.items():
            if not key.startswith('JWT_'):
                f.write(f"{key}={value}\n")
    
    print(f"✅ Updated .env file with secure JWT configuration")

def validate_security_modules():
    """Validate that all security modules are present"""
    required_files = [
        "security/jwt_manager.py",
        "security/auth_middleware.py", 
        "services/secure_unified_unified_auth_service.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required security modules:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print(f"✅ All security modules are present")
    return True

def check_old_authentication_usage():
    """Check for usage of old authentication patterns"""
    patterns_to_check = [
        ("middleware.permission", "routes/"),
        ("require_context", "routes/"),
        ("RunningContext", "routes/"),
        ("unified_unified_auth_service.create_access_token", "routes/"),
        ("jwt.decode.*settings.secret_key", "services/")
    ]
    
    issues_found = []
    
    for pattern, directory in patterns_to_check:
        search_dir = project_root / directory
        if search_dir.exists():
            for py_file in search_dir.glob("**/*.py"):
                try:
                    with open(py_file, 'r') as f:
                        content = f.read()
                        if pattern.replace(".*", "") in content:
                            issues_found.append((str(py_file.relative_to(project_root)), pattern))
                except Exception:
                    continue
    
    if issues_found:
        print(f"⚠️  Found potential old authentication usage:")
        for file_path, pattern in issues_found:
            print(f"   - {file_path}: {pattern}")
        print(f"   Consider updating these files to use the new secure authentication")
    else:
        print(f"✅ No obvious old authentication patterns found")

async def test_jwt_manager():
    """Test the new JWT manager functionality"""
    try:
        from security.jwt_manager import jwt_manager
        
        # Test token creation
        test_token = jwt_manager.create_access_token(
            user_id="test-user-id",
            email="<EMAIL>",
            tenant_id="test-tenant-id",
            permissions=["read_resource", "write_resource"],
            role="TestRole"
        )
        
        # Test token verification
        claims = jwt_manager.verify_token(test_token)
        
        assert claims.sub == "test-user-id"
        assert claims.email == "<EMAIL>"
        assert "read_resource" in claims.permissions
        
        print(f"✅ JWT manager functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ JWT manager test failed: {e}")
        return False

def create_migration_checklist():
    """Create a migration checklist file"""
    checklist_content = """# JWT Security Migration Checklist

## Pre-Migration
- [ ] Backup current authentication configuration
- [ ] Review current JWT usage patterns
- [ ] Plan maintenance window for migration

## Environment Setup
- [ ] Set JWT_SECRET_KEY environment variable (minimum 32 characters)
- [ ] Configure JWT_ISSUER and JWT_AUDIENCE
- [ ] Set token expiration times
- [ ] Test environment variable loading

## Code Migration
- [ ] Update login endpoints to use secure_auth_service
- [ ] Replace middleware.permission with security.auth_middleware
- [ ] Update route dependencies from RunningContext to StatelessUser
- [ ] Remove database lookups from token validation
- [ ] Update error handling for new authentication flow

## Testing
- [ ] Test token generation with embedded permissions
- [ ] Verify token validation without database queries
- [ ] Test permission checking with stateless context
- [ ] Validate error responses and status codes
- [ ] Test token expiration and refresh flow

## Security Validation
- [ ] Verify algorithm confusion protection
- [ ] Test with invalid/malformed tokens
- [ ] Validate issuer and audience claims
- [ ] Check secure cookie configuration
- [ ] Monitor for database queries during auth

## Deployment
- [ ] Deploy security modules
- [ ] Update environment configuration
- [ ] Migrate authentication endpoints
- [ ] Monitor authentication success rates
- [ ] Remove legacy authentication code

## Post-Migration
- [ ] Monitor authentication performance
- [ ] Check security logs for issues
- [ ] Validate no database queries during token validation
- [ ] Update API documentation
- [ ] Train team on new authentication flow
"""
    
    checklist_file = project_root / "JWT_MIGRATION_CHECKLIST.md"
    with open(checklist_file, 'w') as f:
        f.write(checklist_content)
    
    print(f"✅ Created migration checklist: {checklist_file}")

async def main():
    """Main migration script"""
    print("🔐 JWT Security Migration Script")
    print("=" * 50)
    
    # Step 1: Validate security modules
    if not validate_security_modules():
        print("❌ Migration cannot proceed without security modules")
        return
    
    # Step 2: Create/update environment configuration
    create_env_file()
    
    # Step 3: Test JWT manager
    if not await test_jwt_manager():
        print("❌ JWT manager test failed - check configuration")
        return
    
    # Step 4: Check for old authentication usage
    check_old_authentication_usage()
    
    # Step 5: Create migration checklist
    create_migration_checklist()
    
    print("\n" + "=" * 50)
    print("✅ Migration preparation complete!")
    print("\nNext steps:")
    print("1. Review the JWT_MIGRATION_CHECKLIST.md file")
    print("2. Test the new authentication in development")
    print("3. Update your route dependencies")
    print("4. Deploy to production with proper monitoring")
    print("\n⚠️  Remember to:")
    print("- Keep the old authentication as fallback during migration")
    print("- Monitor authentication success rates after deployment")
    print("- Remove legacy code only after successful migration")

if __name__ == "__main__":
    asyncio.run(main())
