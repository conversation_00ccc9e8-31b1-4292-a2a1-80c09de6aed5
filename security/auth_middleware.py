"""
Secure Authentication Middleware

This module provides stateless JWT authentication middleware that validates
tokens using only cryptographic verification without database lookups.
"""

from typing import Optional, Set
from uuid import UUID
import logging

from fastapi import Request, HTTPException, status, Depends, Cookie
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from security.jwt_manager import jwt_manager, JWTClaims
from session.running_context import RunningContext, set_context
from models.user import User
from models.role import Permission

logger = logging.getLogger(__name__)

# Security scheme for OpenAPI documentation
security_scheme = HTTPBearer(auto_error=False)

# Cookie configuration
COOKIE_NAME = "assivy_auth_token"

class StatelessUser(User):
    """
    Stateless user model that contains all necessary information from JWT token
    
    This eliminates the need for database lookups during authentication.
    All user data is derived from the validated JWT token claims.
    """
    
    def __init__(self, claims: JWTClaims):
        # Generate a deterministic role_id from role name for validation
        from uuid import uuid5, NAMESPACE_DNS
        role_id = uuid5(NAMESPACE_DNS, f"role:{claims.role}")

        # Create user object from JWT claims without database lookup
        super().__init__(
            id=UUID(claims.sub),
            email=claims.email,
            username=claims.email.split("@")[0],  # Derive username from email
            tenant_id=UUID(claims.tid),
            is_active=True,  # Token existence implies active user
            role_id=role_id,  # Generate deterministic role_id for validation
            created_at=claims.iat,
            updated_at=claims.iat
        )
        
        # Store additional claims data
        self._permissions = set(claims.permissions)
        self._role_name = claims.role
        self._token_jti = claims.jti
        self._token_iat = claims.iat
        self._token_exp = claims.exp
    
    @property
    def permissions(self) -> Set[str]:
        """Get user permissions from token claims"""
        return self._permissions

    @property
    def role(self) -> str:
        """Get user role name from token claims"""
        return self._role_name

    @property
    def jti(self) -> str:
        """Get JWT ID from token claims"""
        return self._token_jti

    @property
    def iat(self):
        """Get issued at timestamp from token claims"""
        return self._token_iat

    @property
    def exp(self):
        """Get expiration timestamp from token claims"""
        return self._token_exp
    
    @property
    def role_name(self) -> str:
        """Get user role name from token claims"""
        return self._role_name
    
    @property
    def token_id(self) -> Optional[str]:
        """Get JWT token ID for tracking"""
        return self._token_jti
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
        return permission in self._permissions
    
    def has_any_permission(self, permissions: Set[str]) -> bool:
        """Check if user has any of the specified permissions"""
        return bool(permissions.intersection(self._permissions))
    
    def has_all_permissions(self, permissions: Set[str]) -> bool:
        """Check if user has all of the specified permissions"""
        return permissions.issubset(self._permissions)
    
    def is_system_admin(self) -> bool:
        """Check if user has system admin privileges"""
        return Permission.SYSTEM_ADMIN.value in self._permissions
    
    def is_tenant_admin(self) -> bool:
        """Check if user has tenant admin privileges"""
        return Permission.TENANT_ADMIN.value in self._permissions

class StatelessRunningContext(RunningContext):
    """
    Stateless running context that doesn't require database lookups
    
    All user permissions and role information are contained within
    the context, eliminating the need for database queries.
    """
    
    def __init__(self, user: StatelessUser, request_id: str, **metadata):
        super().__init__(user=user, request_id=request_id, metadata=metadata)
        self._user_permissions = user.permissions
    
    async def get_user_permissions(self) -> Set[str]:
        """Get user permissions (already cached from token)"""
        return self._user_permissions
    
    async def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission (stateless)"""
        return permission in self._user_permissions
    
    async def has_any_permission(self, permissions: list) -> bool:
        """Check if user has any of the specified permissions (stateless)"""
        return bool(set(permissions).intersection(self._user_permissions))
    
    async def has_all_permissions(self, permissions: list) -> bool:
        """Check if user has all of the specified permissions (stateless)"""
        return set(permissions).issubset(self._user_permissions)

async def get_token_from_request(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
    cookie_token: Optional[str] = Cookie(None, alias=COOKIE_NAME)
) -> Optional[str]:
    """
    Extract JWT token from request (header or cookie)
    
    Args:
        request: FastAPI request object
        credentials: HTTP Bearer credentials from header
        cookie_token: JWT token from cookie
        
    Returns:
        str: JWT token if found, None otherwise
    """
    # Try Authorization header first
    if credentials and credentials.credentials:
        return credentials.credentials
    
    # Try cookie as fallback
    if cookie_token:
        return cookie_token
    
    # Try manual header extraction as final fallback
    auth_header = request.headers.get("Authorization")
    if auth_header:
        return jwt_manager.extract_token_from_header(auth_header)
    
    return None

async def authenticate_request(
    request: Request,
    token: Optional[str] = Depends(get_token_from_request)
) -> Optional[StatelessUser]:
    """
    Authenticate request using stateless JWT validation
    
    This function performs ONLY cryptographic token validation.
    No database lookups are performed during authentication.
    
    Args:
        request: FastAPI request object
        token: JWT token from request
        
    Returns:
        StatelessUser: Authenticated user with embedded permissions
        None: If no token provided (for optional authentication)
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    if not token:
        return None
    
    try:
        # Verify token using only cryptographic validation
        claims = jwt_manager.verify_token(token, token_type="access")
        
        # Create stateless user from token claims
        user = StatelessUser(claims)
        
        logger.debug(f"Authenticated user {user.id} via stateless JWT")
        return user
        
    except HTTPException:
        # Re-raise HTTP exceptions from JWT verification
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )

async def require_authentication(
    request: Request,
    user: Optional[StatelessUser] = Depends(authenticate_request)
) -> StatelessUser:
    """
    Require authentication for protected endpoints
    
    Args:
        request: FastAPI request object
        user: Authenticated user from token
        
    Returns:
        StatelessUser: Authenticated user
        
    Raises:
        HTTPException: If user is not authenticated
    """
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return user

async def create_running_context(
    request: Request,
    user: StatelessUser = Depends(require_authentication)
) -> StatelessRunningContext:
    """
    Create and set running context for authenticated requests
    
    Args:
        request: FastAPI request object
        user: Authenticated user
        
    Returns:
        StatelessRunningContext: Request context with user information
    """
    import uuid
    
    # Create stateless running context
    context = StatelessRunningContext(
        user=user,
        request_id=str(uuid.uuid4()),
        request_path=str(request.url.path),
        request_method=request.method,
        user_agent=request.headers.get("user-agent", ""),
        ip_address=request.client.host if request.client else "unknown"
    )
    
    # Set context for the request
    set_context(context)
    
    return context

# Convenience dependency for endpoints that need authentication
async def get_current_user(
    user: StatelessUser = Depends(require_authentication)
) -> StatelessUser:
    """Get the current authenticated user"""
    return user

# Convenience dependency for endpoints that need running context
async def get_running_context(
    context: StatelessRunningContext = Depends(create_running_context)
) -> StatelessRunningContext:
    """Get the current running context"""
    return context

# Optional authentication dependency
async def get_optional_user(
    user: Optional[StatelessUser] = Depends(authenticate_request)
) -> Optional[StatelessUser]:
    """Get the current user if authenticated, None otherwise"""
    return user

class StatelessPermissionChecker:
    """
    Stateless permission checker that uses token-embedded permissions
    
    This eliminates database lookups during permission validation.
    All permission information is contained within the JWT token.
    """
    
    def __init__(self, required_permissions: Optional[list] = None):
        self.required_permissions = set(required_permissions) if required_permissions else set()
    
    async def __call__(self, user: StatelessUser = Depends(get_current_user)) -> StatelessUser:
        """
        Check user permissions using stateless validation
        
        Args:
            user: Authenticated user with embedded permissions
            
        Returns:
            StatelessUser: User if permissions are satisfied
            
        Raises:
            HTTPException: If user lacks required permissions
        """
        if not self.required_permissions:
            return user
        
        # Check permissions using token-embedded data (no database lookup)
        has_required = user.has_any_permission(self.required_permissions)
        has_admin = user.is_system_admin() or user.is_tenant_admin()
        
        if not (has_required or has_admin):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {', '.join(self.required_permissions)}"
            )
        
        return user

def requires_permissions(permissions: Optional[list] = None):
    """Create a stateless permission checker dependency"""
    return StatelessPermissionChecker(permissions)

def requires_system_admin():
    """Require system admin permission (stateless)"""
    return StatelessPermissionChecker([Permission.SYSTEM_ADMIN.value])

def requires_tenant_admin():
    """Require tenant admin permission (stateless)"""
    return StatelessPermissionChecker([Permission.TENANT_ADMIN.value])
