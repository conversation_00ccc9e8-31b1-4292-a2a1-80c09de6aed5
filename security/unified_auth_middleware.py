"""
Unified Authentication Middleware

This middleware integrates with the UnifiedAuthService to provide:
- Stateless JWT access token validation
- Token blacklist checking
- API key authentication support
- Automatic token refresh handling
- Security logging and monitoring
"""

from typing import Optional, Set, Union
from uuid import UUID
import logging

from fastapi import Request, HTTPException, status, Depends, Cookie, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials, APIKeyHeader

from security.jwt_manager import jwt_manager, JWTClaims
from security.auth_middleware import Stateless<PERSON>ser, StatelessRunningContext
from session.running_context import set_context
from services.unified_auth_service import unified_auth_service, COOKIE_NAME, REFRESH_COOKIE_NAME
from models.role import Permission

logger = logging.getLogger(__name__)

# Security schemes
security_scheme = HTTPBearer(auto_error=False)
api_key_header_auth = APIKeyHeader(name="X-API-Key", auto_error=False)


async def get_token_from_request(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Security(security_scheme),
    cookie_token: Optional[str] = <PERSON><PERSON>(None, alias=COOKIE_NAME),
    api_key: Optional[str] = Security(api_key_header_auth)
) -> Optional[str]:
    """
    Extract authentication token from request (Bearer, Cookie, or API Key)
    
    Priority order:
    1. Authorization header (Bearer token)
    2. API Key header
    3. Auth cookie
    
    Args:
        request: FastAPI request object
        credentials: Bearer token credentials
        cookie_token: Token from cookie
        api_key: API key from header
        
    Returns:
        str: Authentication token or None
    """
    # 1. Check Authorization header first
    if credentials and credentials.credentials:
        return credentials.credentials
    
    # 2. Check API key header
    if api_key:
        # For API keys, we'll handle authentication differently
        # Return a special marker to indicate API key auth
        return f"api_key:{api_key}"
    
    # 3. Check auth cookie
    if cookie_token:
        return cookie_token
    
    return None


async def authenticate_request(
    request: Request,
    token: Optional[str] = Depends(get_token_from_request)
) -> Optional[StatelessUser]:
    """
    Authenticate request using unified authentication service
    
    This function handles:
    - JWT token validation with blacklist checking
    - API key authentication
    - Security logging
    
    Args:
        request: FastAPI request object
        token: Authentication token from request
        
    Returns:
        StatelessUser: Authenticated user with embedded permissions
        None: If no token provided (for optional authentication)
        
    Raises:
        HTTPException: If token is invalid, expired, or blacklisted
    """
    if not token:
        return None
    
    try:
        # Handle API key authentication
        if token.startswith("api_key:"):
            api_key_value = token[8:]  # Remove "api_key:" prefix
            user = await unified_auth_service.authenticate_api_key(api_key_value)
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid API key",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # For API key auth, we need to get user permissions
            from services.role_service import RoleService
            role_service = RoleService()
            role = await role_service.get_role_with_default(user.role_id)
            permissions = [perm.value for perm in role.permissions] if role else []
            
            # Create JWT claims-like object for StatelessUser
            claims = JWTClaims(
                sub=str(user.id),
                email=user.email,
                tid=str(user.tenant_id) if user.tenant_id else "",
                permissions=permissions,
                role=role.name if role else "Unknown",
                iss="assivy-api",
                aud="assivy-client",
                iat=None,
                exp=None,
                jti="api_key_auth"
            )
            
            stateless_user = StatelessUser(claims)
            logger.debug(f"Authenticated user {user.id} via API key")
            return stateless_user
        
        # Handle JWT token authentication
        else:
            # Verify token cryptographically
            claims = jwt_manager.verify_token(token, token_type="access")
            
            # Check if token is blacklisted
            is_blacklisted = await unified_auth_service.is_token_blacklisted(claims.jti)
            if is_blacklisted:
                logger.warning(f"Blacklisted token used: {claims.jti}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # Create stateless user from token claims
            user = StatelessUser(claims)
            logger.debug(f"Authenticated user {user.id} via JWT token")
            return user
        
    except HTTPException:
        # Re-raise HTTP exceptions from authentication
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def require_authentication(
    request: Request,
    user: Optional[StatelessUser] = Depends(authenticate_request)
) -> StatelessUser:
    """
    Require authentication for protected endpoints
    
    Args:
        request: FastAPI request object
        user: Authenticated user from token
        
    Returns:
        StatelessUser: Authenticated user
        
    Raises:
        HTTPException: If user is not authenticated
    """
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return user


async def create_running_context(
    request: Request,
    user: StatelessUser = Depends(require_authentication)
) -> StatelessRunningContext:
    """
    Create and set running context for authenticated requests
    
    Args:
        request: FastAPI request object
        user: Authenticated user
        
    Returns:
        StatelessRunningContext: Request context with user information
    """
    import uuid
    
    # Create stateless running context
    context = StatelessRunningContext(
        user=user,
        request_id=str(uuid.uuid4()),
        request_path=str(request.url.path),
        request_method=request.method,
        user_agent=request.headers.get("user-agent", ""),
        ip_address=request.client.host if request.client else "unknown"
    )
    
    # Set context for the request
    set_context(context)
    
    return context


# Convenience function to get current authenticated user
async def get_current_user(
    request: Request,
    user: StatelessUser = Depends(require_authentication)
) -> StatelessUser:
    """Get current authenticated user"""
    return user


class UnifiedPermissionChecker:
    """
    Permission checker using stateless JWT claims
    
    This class validates permissions using token-embedded data,
    eliminating the need for database lookups during authorization.
    """
    
    def __init__(self, required_permissions: Optional[Set[Union[str, Permission]]] = None):
        if required_permissions:
            # Convert Permission enums to strings
            self.required_permissions = {
                perm.value if isinstance(perm, Permission) else perm
                for perm in required_permissions
            }
        else:
            self.required_permissions = set()
    
    async def __call__(self, user: StatelessUser = Depends(get_current_user)) -> StatelessUser:
        """
        Check user permissions using stateless validation
        
        Args:
            user: Authenticated user with embedded permissions
            
        Returns:
            StatelessUser: User if permissions are satisfied
            
        Raises:
            HTTPException: If user lacks required permissions
        """
        if not self.required_permissions:
            return user
        
        # Check permissions using token-embedded data (no database lookup)
        has_required = user.has_any_permission(self.required_permissions)
        has_admin = user.is_system_admin() or user.is_tenant_admin()
        
        if not (has_required or has_admin):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {', '.join(self.required_permissions)}"
            )
        
        return user


# Convenience functions for common permission patterns
def requires_permissions(permissions: Optional[Set[Union[str, Permission]]] = None):
    """Create permission checker dependency"""
    return UnifiedPermissionChecker(permissions)

def requires_system_admin():
    """Require system admin permission"""
    return UnifiedPermissionChecker({Permission.SYSTEM_ADMIN})

def requires_tenant_admin():
    """Require tenant admin permission"""
    return UnifiedPermissionChecker({Permission.TENANT_ADMIN})

def requires_any_admin():
    """Require any admin permission"""
    return UnifiedPermissionChecker({Permission.SYSTEM_ADMIN, Permission.TENANT_ADMIN})
