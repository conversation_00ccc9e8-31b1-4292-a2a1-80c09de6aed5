#!/usr/bin/env python3
"""
Test runner script for <PERSON><PERSON><PERSON>end.

This script provides convenient commands for running different types of tests
and generating reports.
"""
import subprocess
import sys
import argparse
from pathlib import Path


def run_command(cmd: list[str], description: str = None) -> int:
    """Run a shell command and return the exit code."""
    if description:
        print(f"\n🧪 {description}")
        print("=" * 50)
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    return result.returncode


def run_all_tests():
    """Run all tests with coverage."""
    return run_command([
        "uv", "run", "pytest", 
        "--cov=.", 
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov",
        "-v"
    ], "Running all tests with coverage")


def run_unit_tests():
    """Run only unit tests."""
    return run_command([
        "uv", "run", "pytest", 
        "-m", "unit",
        "-v"
    ], "Running unit tests")


def run_integration_tests():
    """Run only integration tests."""
    return run_command([
        "uv", "run", "pytest", 
        "-m", "integration",
        "-v"
    ], "Running integration tests")


def run_fast_tests():
    """Run tests excluding slow ones."""
    return run_command([
        "uv", "run", "pytest", 
        "-m", "not slow",
        "-v"
    ], "Running fast tests")


def run_with_watch():
    """Run tests in watch mode (requires pytest-watch)."""
    return run_command([
        "uv", "run", "ptw", 
        "--", 
        "-v"
    ], "Running tests in watch mode")


def lint_code():
    """Run code linting."""
    commands = [
        (["uv", "run", "black", "--check", "."], "Checking code formatting with Black"),
        (["uv", "run", "isort", "--check-only", "."], "Checking import sorting with isort"),
        (["uv", "run", "flake8", "."], "Running flake8 linting"),
    ]
    
    for cmd, desc in commands:
        exit_code = run_command(cmd, desc)
        if exit_code != 0:
            return exit_code
    
    return 0


def format_code():
    """Format code with black and isort."""
    commands = [
        (["uv", "run", "black", "."], "Formatting code with Black"),
        (["uv", "run", "isort", "."], "Sorting imports with isort"),
    ]
    
    for cmd, desc in commands:
        exit_code = run_command(cmd, desc)
        if exit_code != 0:
            return exit_code
    
    return 0


def install_test_deps():
    """Install test dependencies."""
    return run_command([
        "uv", "add", "--group", "test",
        "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock", 
        "httpx", "factory-boy", "faker", "freezegun"
    ], "Installing test dependencies")


def install_dev_deps():
    """Install development dependencies."""
    return run_command([
        "uv", "add", "--group", "dev",
        "black", "isort", "flake8", "mypy", "pre-commit"
    ], "Installing development dependencies")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Assivy Backend Test Runner")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Test commands
    subparsers.add_parser("all", help="Run all tests with coverage")
    subparsers.add_parser("unit", help="Run unit tests only")
    subparsers.add_parser("integration", help="Run integration tests only")
    subparsers.add_parser("fast", help="Run fast tests (exclude slow tests)")
    subparsers.add_parser("watch", help="Run tests in watch mode")
    
    # Code quality commands
    subparsers.add_parser("lint", help="Run code linting")
    subparsers.add_parser("format", help="Format code")
    
    # Setup commands
    subparsers.add_parser("install-test", help="Install test dependencies")
    subparsers.add_parser("install-dev", help="Install development dependencies")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Map commands to functions
    commands = {
        "all": run_all_tests,
        "unit": run_unit_tests,
        "integration": run_integration_tests,
        "fast": run_fast_tests,
        "watch": run_with_watch,
        "lint": lint_code,
        "format": format_code,
        "install-test": install_test_deps,
        "install-dev": install_dev_deps,
    }
    
    if args.command in commands:
        exit_code = commands[args.command]()
        sys.exit(exit_code)
    else:
        print(f"Unknown command: {args.command}")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
