"""
Utility functions for data type conversions.
"""
from uuid import UUID
from typing import Any, Dict, List, Union


def convert_uuids_to_strings(obj: Any) -> Any:
    """
    Recursively convert UUID objects to strings in a data structure.
    
    Args:
        obj: The object to convert (dict, list, tuple, set, or any other type)
        
    Returns:
        The object with all UUID instances converted to strings
    """
    if isinstance(obj, UUID):
        return str(obj)
    elif isinstance(obj, dict):
        return {k: convert_uuids_to_strings(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_uuids_to_strings(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_uuids_to_strings(item) for item in obj)
    elif isinstance(obj, set):
        return {convert_uuids_to_strings(item) for item in obj}
    else:
        return obj


def convert_strings_to_uuids(obj: Any, uuid_fields: List[str] = None) -> Any:
    """
    Convert string representations back to UUID objects for specified fields.
    
    Args:
        obj: The object to convert
        uuid_fields: List of field names that should be converted to UUIDs
        
    Returns:
        The object with specified string fields converted to UUIDs
    """
    if uuid_fields is None:
        uuid_fields = []
        
    if isinstance(obj, dict):
        result = {}
        for k, v in obj.items():
            if k in uuid_fields and isinstance(v, str):
                try:
                    result[k] = UUID(v)
                except ValueError:
                    result[k] = v  # Keep as string if not a valid UUID
            else:
                result[k] = convert_strings_to_uuids(v, uuid_fields)
        return result
    elif isinstance(obj, list):
        return [convert_strings_to_uuids(item, uuid_fields) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_strings_to_uuids(item, uuid_fields) for item in obj)
    elif isinstance(obj, set):
        return {convert_strings_to_uuids(item, uuid_fields) for item in obj}
    else:
        return obj
