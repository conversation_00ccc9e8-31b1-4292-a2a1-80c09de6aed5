"""
Test Authentication Utilities

This module provides utilities for creating test users and bypass contexts
for development and testing purposes.
"""

import os
from uuid import UUID, uuid4
from datetime import datetime, timezone
from typing import Optional

from models.user import User
from models.tenant import Tenant
from session.running_context import RunningContext, set_context


def is_development_mode() -> bool:
    """Check if the application is running in development mode"""
    env = os.getenv("ENVIRONMENT", "production").lower()
    return env in ["development", "dev", "local", "test"]


def create_test_superuser(tenant_id: Optional[UUID] = None, user_id: Optional[UUID] = None) -> User:
    """
    Create a test superuser for development/testing purposes with predictable UUIDs

    Args:
        tenant_id: Optional tenant ID. If not provided, uses default test customer tenant.
        user_id: Optional user ID. If not provided, generates a predictable one.

    Returns:
        User: A test user with superuser privileges and predictable ID
    """
    if tenant_id is None:
        # Default to the standard test customer tenant ID
        tenant_id = UUID("00000000-0000-0000-0000-000000000002")

    if user_id is None:
        # Generate a predictable user ID based on tenant for consistency
        user_id = uuid4()

    return User(
        id=user_id,
        email=f"test.user.{str(user_id)[:8]}@test.assivy.com",
        username=f"testuser_{str(user_id)[:8]}",
        tenant_id=tenant_id,
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        role_id=uuid4()  # Generate a temporary role_id, will be set by the calling code
    )


def create_bypass_context(tenant_id: Optional[UUID] = None) -> RunningContext:
    """
    Create a bypass context for development/testing that skips permission checks
    
    Args:
        tenant_id: Optional tenant ID. If not provided, generates a new one.
        
    Returns:
        RunningContext: A context with bypass permissions
    """
    if not is_development_mode():
        raise RuntimeError("Bypass context can only be created in development mode")
    
    if tenant_id is None:
        tenant_id = uuid4()
    
    # Create a test user
    test_user = create_test_superuser(tenant_id)
    
    # Create a bypass context
    context = RunningContext(
        user=test_user,
        request_id=str(uuid4()),
        metadata={"bypass": True, "test_mode": True}
    )
    
    # Set the context in the current thread/task
    set_context(context)
    
    return context


def create_test_tenant(name: str = "Test Tenant", tenant_id: Optional[UUID] = None) -> Tenant:
    """
    Create a test tenant for development/testing purposes with predictable UUID

    Args:
        name: Name of the test tenant
        tenant_id: Optional fixed tenant ID. If not provided, uses a predictable default.

    Returns:
        Tenant: A test tenant with predictable ID for consistent testing
    """
    # Use predictable UUID if none provided - follows the pattern from system API
    if tenant_id is None:
        # Default to the standard test customer tenant ID
        tenant_id = UUID("00000000-0000-0000-0000-000000000002")

    return Tenant(
        id=tenant_id,
        name=name,
        description=f"Test tenant: {name}",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


def get_standard_test_uuids() -> dict:
    """
    Get standard predictable UUIDs for test entities

    These UUIDs match those used in the system API endpoints for consistent testing.

    Returns:
        dict: Dictionary containing standard test UUIDs for users and tenants
    """
    return {
        "users": {
            "system_admin": "00000000-0000-0000-0000-000000000001",
            "tenant_admin": "00000000-0000-0000-0000-000000000002",
            "manager": "00000000-0000-0000-0000-000000000003",
            "member": "00000000-0000-0000-0000-000000000004"
        },
        "tenants": {
            "system_platform": "00000000-0000-0000-0000-000000000001",
            "test_customer": "00000000-0000-0000-0000-000000000002",
            "demo_tenant": "00000000-0000-0000-0000-000000000003"
        }
    }


def get_test_credentials() -> dict:
    """
    Get standard test credentials for development/testing with predictable UUIDs

    Returns:
        dict: Dictionary containing test user credentials with fixed UUIDs
    """
    uuids = get_standard_test_uuids()

    return {
        "system_admin": {
            "id": uuids["users"]["system_admin"],
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "role": "SystemAdmin",
            "tenant_id": uuids["tenants"]["system_platform"]
        },
        "tenant_admin": {
            "id": uuids["users"]["tenant_admin"],
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "role": "TenantAdmin",
            "tenant_id": uuids["tenants"]["test_customer"]
        },
        "manager": {
            "id": uuids["users"]["manager"],
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "role": "Manager",
            "tenant_id": uuids["tenants"]["test_customer"]
        },
        "member": {
            "id": uuids["users"]["member"],
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "role": "Member",
            "tenant_id": uuids["tenants"]["test_customer"]
        }
    }


def create_standard_test_user(user_type: str = "member") -> User:
    """
    Create a test user with standard predictable UUID

    Args:
        user_type: Type of user to create (system_admin, tenant_admin, manager, member)

    Returns:
        User: A test user with predictable ID matching system API standards

    Raises:
        ValueError: If user_type is not recognized
    """
    credentials = get_test_credentials()

    if user_type not in credentials:
        raise ValueError(f"Unknown user type: {user_type}. Must be one of: {list(credentials.keys())}")

    user_data = credentials[user_type]

    return User(
        id=UUID(user_data["id"]),
        email=user_data["email"],
        username=user_data["email"].split("@")[0],
        tenant_id=UUID(user_data["tenant_id"]),
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        role_id=uuid4()  # Generate a temporary role_id, will be set by the calling code
    )


def create_standard_test_tenant(tenant_type: str = "test_customer") -> Tenant:
    """
    Create a test tenant with standard predictable UUID

    Args:
        tenant_type: Type of tenant to create (system_platform, test_customer, demo_tenant)

    Returns:
        Tenant: A test tenant with predictable ID matching system API standards

    Raises:
        ValueError: If tenant_type is not recognized
    """
    uuids = get_standard_test_uuids()

    if tenant_type not in uuids["tenants"]:
        raise ValueError(f"Unknown tenant type: {tenant_type}. Must be one of: {list(uuids['tenants'].keys())}")

    tenant_names = {
        "system_platform": "Assivy System Platform",
        "test_customer": "Test Customer Organization",
        "demo_tenant": "Demo Organization"
    }

    tenant_descriptions = {
        "system_platform": "System administration tenant",
        "test_customer": "Standard test customer tenant",
        "demo_tenant": "Demonstration tenant for showcasing features"
    }

    return Tenant(
        id=UUID(uuids["tenants"][tenant_type]),
        name=tenant_names[tenant_type],
        description=tenant_descriptions[tenant_type],
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
