"""
Factory classes to simplify agent creation for end users.
These provide convenient methods to create agents with reusable usecases and actions.
"""

from uuid import UUID, uuid4
from typing import List, Dict, Any, Optional
from models.ai_model import AIModel
from models.agent import Agent, Usecase, AgentUsecaseConfig
from models.action import BaseAction

class AgentFactory:
    """Factory class to simplify agent creation"""
    
    @staticmethod
    def create_simple_agent(
        name: str,
        tenant_id: UUID,
        owner_id: UUID,
        model_id: UUID,
        usecase_configs: List[Dict[str, Any]],
        description: Optional[str] = None,
        streaming: bool = False
    ) -> Agent:
        """
        Create an agent with configured usecases.
        
        Args:
            name: Agent name
            tenant_id: Tenant ID
            owner_id: Owner ID
            model_id: AI model ID to use
            usecase_configs: List of dicts with 'usecase_id', optional 'parameters', 'action_overrides'
            description: Optional description
            streaming: Enable streaming
            
        Example usecase_configs:
        [
            {
                'usecase_id': weather_usecase_id,
                'parameters': {'default_location': 'New York'},
                'action_overrides': {weather_action_id: {'api_key': 'your-key'}}
            },
            {
                'usecase_id': email_usecase_id,
                'parameters': {'default_tone': 'professional'}
            }
        ]
        """
        
        # Create usecase configurations
        agent_usecase_configs = []
        for config in usecase_configs:
            agent_config = AgentUsecaseConfig(
                usecase_id=config['usecase_id'],
                parameters=config.get('parameters', {}),
                action_overrides=config.get('action_overrides', {}),
                is_enabled=config.get('is_enabled', True),
                priority=config.get('priority', 1)
            )
            agent_usecase_configs.append(agent_config)
        
        # Create agent
        agent = Agent(
            id=uuid4(),
            name=name,
            description=description or f"Agent with {len(usecase_configs)} configured usecases",
            model_id=model_id,
            usecase_configs=agent_usecase_configs,
            streaming=streaming,
            owner_id=owner_id,
            tenant_id=tenant_id
        )
        
        return agent
    
    @staticmethod
    def create_agent_from_usecases(
        name: str,
        tenant_id: UUID,
        owner_id: UUID,
        model_id: UUID,
        usecases: List[Usecase],
        usecase_parameters: Optional[Dict[UUID, Dict[str, Any]]] = None,
        action_overrides: Optional[Dict[UUID, Dict[UUID, Dict[str, Any]]]] = None,
        description: Optional[str] = None,
        streaming: bool = False
    ) -> Agent:
        """
        Create an agent from existing usecase objects.
        
        Args:
            name: Agent name
            tenant_id: Tenant ID
            owner_id: Owner ID  
            model_id: AI model ID
            usecases: List of Usecase objects to configure
            usecase_parameters: Optional dict mapping usecase_id to parameters
            action_overrides: Optional dict mapping usecase_id -> action_id -> parameters
            description: Optional description
            streaming: Enable streaming
        """
        
        usecase_configs = []
        for usecase in usecases:
            config = AgentUsecaseConfig(
                usecase_id=usecase.id,
                parameters=usecase_parameters.get(usecase.id, {}) if usecase_parameters else {},
                action_overrides=action_overrides.get(usecase.id, {}) if action_overrides else {}
            )
            usecase_configs.append(config)
        
        agent = Agent(
            id=uuid4(),
            name=name,
            description=description or f"Agent with {len(usecases)} usecases",
            model_id=model_id,
            usecase_configs=usecase_configs,
            streaming=streaming,
            owner_id=owner_id,
            tenant_id=tenant_id
        )
        
        return agent

class UsecaseFactory:
    """Factory class to create reusable usecases"""
    
    @staticmethod
    def create_usecase(
        name: str,
        tenant_id: UUID,
        action_ids: List[UUID],
        description: Optional[str] = None,
        default_parameters: Optional[Dict[str, Any]] = None,
        category: Optional[str] = None,
        is_system: bool = False
    ) -> Usecase:
        """
        Create a reusable usecase.
        
        Args:
            name: Usecase name
            tenant_id: Tenant ID
            action_ids: List of action IDs this usecase can perform
            description: Optional description
            default_parameters: Default parameters for this usecase
            category: Category for organization
            is_system: Whether this is a system usecase
        """
        
        usecase = Usecase(
            id=uuid4(),
            name=name,
            description=description or f"Usecase with {len(action_ids)} actions",
            action_ids=action_ids,
            default_parameters=default_parameters or {},
            is_system=is_system,
            category=category,
            tenant_id=tenant_id
        )
        
        return usecase

# Convenience functions for common patterns
def quick_agent_with_usecases(
    agent_name: str,
    tenant_id: UUID,
    owner_id: UUID,
    usecase_ids: List[UUID],
    usecase_parameters: Optional[Dict[UUID, Dict[str, Any]]] = None,
    model_id: Optional[UUID] = None
) -> tuple[Agent, Optional[AIModel]]:
    """
    Quick setup for an agent using existing usecases.
    
    Args:
        agent_name: Name of the agent
        tenant_id: Tenant ID
        owner_id: Owner ID
        usecase_ids: List of existing usecase IDs to use
        usecase_parameters: Optional parameters for each usecase
        model_id: Optional model ID (creates default if not provided)
    
    Returns:
        (agent, model) - model is None if model_id was provided
    """
    
    # Create default model if not provided
    model = None
    if model_id is None:
        model = AIModel.create_default_openai(tenant_id)
        model.id = uuid4()
        model_id = model.id
    
    # Create usecase configs
    usecase_configs = []
    for usecase_id in usecase_ids:
        config = {
            'usecase_id': usecase_id,
            'parameters': usecase_parameters.get(usecase_id, {}) if usecase_parameters else {}
        }
        usecase_configs.append(config)
    
    # Create agent
    agent = AgentFactory.create_simple_agent(
        name=agent_name,
        tenant_id=tenant_id,
        owner_id=owner_id,
        model_id=model_id,
        usecase_configs=usecase_configs
    )
    
    return agent, model
