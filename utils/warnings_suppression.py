"""
Warnings Suppression Module

This module configures environment variables and suppresses common warnings
that occur during application startup and runtime.
"""

import warnings
import os
import logging

# Configure logging to reduce noise
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

# Suppress specific warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Suppress specific library warnings
warnings.filterwarnings("ignore", message=".*unclosed.*", category=ResourceWarning)
warnings.filterwarnings("ignore", message=".*SSL.*", category=UserWarning)

# Set environment variables to reduce warnings
os.environ.setdefault("PYTHONWARNINGS", "ignore")

# Suppress Pydantic warnings if needed
try:
    import pydantic
    warnings.filterwarnings("ignore", category=pydantic.warnings.PydanticDeprecatedSince20)
except (ImportError, AttributeError):
    pass

# Suppress FastAPI warnings
warnings.filterwarnings("ignore", message=".*FastAPI.*", category=UserWarning)

# Suppress async warnings
warnings.filterwarnings("ignore", message=".*coroutine.*", category=RuntimeWarning)
