from typing import List, Optional
from uuid import UUID, uuid4
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from datetime import datetime
import logging

from models.library import Library
from models.role import Permission
from services.library_service import LibraryService
from session.running_context import RunningContext
from security.unified_auth_middleware import get_current_user, requires_permissions, create_running_context
from security.auth_middleware import Stateless<PERSON>ser, StatelessRunningContext

logger = logging.getLogger(__name__)

# Define permission checkers for different operations (reuse resource permissions)
create_library_permission = requires_permissions({Permission.CREATE_RESOURCE})
read_library_permission = requires_permissions({Permission.READ_RESOURCE})
view_library_permission = requires_permissions({Permission.READ_RESOURCE})
update_library_permission = requires_permissions({Permission.UPDATE_RESOURCE})
delete_library_permission = requires_permissions({Permission.DELETE_RESOURCE})

router = APIRouter(prefix="/libraries", tags=["libraries"])

# Pydantic models for request/response
class LibraryCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    settings: dict = Field(default_factory=dict)

class LibraryUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    settings: Optional[dict] = None

class LibraryResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    owner_id: str
    resource_count: int
    total_size: int
    created_at: str
    updated_at: str
    owner_name: Optional[str] = None

# Shelf models
class ShelfCreate(BaseModel):
    library_id: str = Field(..., description="ID of the library this shelf belongs to")
    name: str = Field(..., min_length=1, max_length=255, description="Name of the shelf")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the shelf")
    # Removed resource_type - shelves now support mixed resource types

class ShelfCreateInLibrary(BaseModel):
    """RESTful model for creating shelf in a library (library_id comes from URL)"""
    name: str = Field(..., min_length=1, max_length=255, description="Name of the shelf")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the shelf")
    # Removed resource_type - shelves now support mixed resource types

class ShelfUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="New name for the shelf")
    description: Optional[str] = Field(None, max_length=1000, description="New description for the shelf")

class ResourceAssignment(BaseModel):
    resource_id: str = Field(..., description="ID of the resource to assign to the shelf")

# Dependency to get library service
async def get_library_service() -> LibraryService:
    service = LibraryService()
    await service.initialize()
    return service

@router.post("", response_model=LibraryResponse, status_code=status.HTTP_201_CREATED)
async def create_library(
    library_data: LibraryCreate,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(create_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Create a new library"""
    try:
        # Prepare library data
        create_data = {
            'name': library_data.name,
            'description': library_data.description,
            'settings': library_data.settings,
            'tenant_id': str(context.user.tenant_id)
        }

        # Create library
        library = await library_service.create_library(create_data, context.user.id)

        # Convert to response format
        response_data = {
            'id': str(library.id),
            'name': library.name,
            'description': library.description,
            'owner_id': str(library.owner_id),
            'resource_count': library.resource_count,
            'total_size': library.total_size,
            'created_at': library.created_at.isoformat(),
            'updated_at': library.updated_at.isoformat(),
            'owner_name': context.user.get_display_name()  # Add owner name from context
        }
        
        return JSONResponse(content=response_data, status_code=201)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create library: {str(e)}"
        )

@router.get("")
async def list_libraries(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(read_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """List all libraries for the current user"""
    try:
        # Get libraries
        libraries = await library_service.list_user_libraries(context.user.id, context.user.tenant_id, skip, limit)

        # For now, use the length of returned libraries as total (will implement proper count later)
        total_count = len(libraries)

        # Convert to response format
        libraries_data = []
        for library in libraries:
            libraries_data.append({
                'id': str(library.id),
                'name': library.name,
                'description': library.description,
                'owner_id': str(library.owner_id),
                'resource_count': library.resource_count,
                'total_size': library.total_size,
                'created_at': library.created_at.isoformat(),
                'updated_at': library.updated_at.isoformat(),
                'owner_name': context.user.get_display_name()
            })

        # Return paginated response
        response_data = {
            'libraries': libraries_data,
            'total': total_count,
            'skip': skip,
            'limit': limit
        }

        return JSONResponse(content=response_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list libraries: {str(e)}"
        )

@router.get("/{library_id}", response_model=LibraryResponse)
async def get_library(
    library_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(read_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Get a specific library by ID"""
    try:
        library = await library_service.get_library(UUID(library_id), context.user.id)
        
        response_data = {
            'id': str(library.id),
            'name': library.name,
            'description': library.description,
            'owner_id': str(library.owner_id),
            'resource_count': library.resource_count,
            'total_size': library.total_size,
            'created_at': library.created_at.isoformat(),
            'updated_at': library.updated_at.isoformat(),
            'owner_name': context.user.get_display_name()
        }
        
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get library: {str(e)}"
        )

@router.put("/{library_id}", response_model=LibraryResponse)
async def update_library(
    library_id: str,
    library_data: LibraryUpdate,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(update_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Update a library"""
    try:
        # Prepare update data (exclude None values)
        update_data = {k: v for k, v in library_data.dict().items() if v is not None}
        
        # Update library
        library = await library_service.update_library(UUID(library_id), update_data, context.user.id)
        
        response_data = {
            'id': str(library.id),
            'name': library.name,
            'description': library.description,
            'owner_id': str(library.owner_id),
            'resource_count': library.resource_count,
            'total_size': library.total_size,
            'created_at': library.created_at.isoformat(),
            'updated_at': library.updated_at.isoformat(),
            'owner_name': context.user.get_display_name()
        }
        
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update library: {str(e)}"
        )

@router.delete("/{library_id}")
async def delete_library(
    library_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(delete_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Delete a library"""
    try:
        success = await library_service.delete_library(UUID(library_id), context.user.id)
        
        if success:
            return JSONResponse(content={"message": "Library deleted successfully"})
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete library"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete library: {str(e)}"
        )

@router.get("/{library_id}/shelves")
async def get_library_shelves(
    library_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(view_library_permission)
):
    """Get all shelves in a library - supports mixed resource types"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert library_id to UUID
        from uuid import UUID
        library_uuid = UUID(library_id)

        # Get shelves using the service
        shelves = await shelf_service.list_library_shelves(
            library_uuid,
            context.user.id
        )

        # Convert shelves to dict format for JSON response
        shelves_data = []
        for shelf in shelves:
            shelf_dict = {
                'id': str(shelf.id),
                'library_id': str(shelf.library_id),
                'name': shelf.name,
                'description': shelf.description,
                'resource_count': shelf.resource_count or 0,
                'created_at': shelf.created_at.isoformat() if shelf.created_at else None,
                'updated_at': shelf.updated_at.isoformat() if shelf.updated_at else None
            }
            shelves_data.append(shelf_dict)

        return JSONResponse(content={
            'shelves': shelves_data,
            'total': len(shelves_data)
        })

    except Exception as e:
        logger.error(f"Failed to get library shelves: {e}")
        # Fallback to empty list to prevent frontend errors
        return JSONResponse(content={
            'shelves': [],
            'total': 0
        })

@router.post("/{library_id}/shelves", status_code=201)
async def create_library_shelf(
    library_id: str,
    shelf_data: ShelfCreateInLibrary,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(create_library_permission)
):
    """Create a new shelf in a library (RESTful endpoint)"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Prepare shelf data for service (add library_id from URL)
        create_data = {
            'library_id': library_id,
            'name': shelf_data.name,
            'description': shelf_data.description,
            'tenant_id': str(context.user.tenant_id)
        }

        # Create shelf using the service
        created_shelf = await shelf_service.create_shelf(create_data, context.user.id)

        # Convert to response format
        response_data = {
            'id': str(created_shelf.id),
            'library_id': str(created_shelf.library_id),
            'name': created_shelf.name,
            'description': created_shelf.description,
            'resource_count': created_shelf.resource_count or 0,
            'created_at': created_shelf.created_at.isoformat() if created_shelf.created_at else None,
            'updated_at': created_shelf.updated_at.isoformat() if created_shelf.updated_at else None
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create shelf in library {library_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create shelf: {str(e)}"
        )

@router.get("/{library_id}/resources")
async def get_library_resources(
    library_id: str,
    skip: int = 0,
    limit: int = 20,
    resource_type: Optional[str] = None,
    search: Optional[str] = None,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(read_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Get resources in a library with pagination and filtering"""
    try:
        resources_data = await library_service.get_library_resources(
            UUID(library_id),
            context.user.id,
            skip=skip,
            limit=limit,
            resource_type=resource_type,
            search=search
        )

        return JSONResponse(content=resources_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get library resources: {str(e)}"
        )

@router.post("/{library_id}/resources/{resource_id}")
async def add_resource_to_library(
    library_id: str,
    resource_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(update_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Add a resource to a library (will be assigned to appropriate shelf)"""
    try:
        success = await library_service.add_resource_to_library(
            UUID(resource_id),
            UUID(library_id),
            context.user.id
        )

        if success:
            return JSONResponse(
                content={"message": "Resource added to library successfully"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add resource to library"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add resource to library: {str(e)}"
        )

@router.post("/{library_id}/resources/bulk")
async def bulk_add_resources_to_library(
    library_id: str,
    resource_ids: List[str],
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(update_library_permission),
    library_service: LibraryService = Depends(get_library_service)
):
    """Add multiple resources to a library"""
    try:
        resource_uuids = [UUID(rid) for rid in resource_ids]
        results = await library_service.bulk_add_resources_to_library(
            resource_uuids,
            UUID(library_id),
            context.user.id
        )

        return JSONResponse(content=results)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk add resources to library: {str(e)}"
        )

# Create a separate router for shelf endpoints
shelf_router = APIRouter(prefix="/shelves", tags=["shelves"])

@shelf_router.post("", status_code=201)
async def create_shelf(
    shelf_data: ShelfCreate,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(create_library_permission)
):
    """Create a new shelf in a library"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Prepare shelf data for service
        create_data = {
            'library_id': shelf_data.library_id,
            'name': shelf_data.name,
            'description': shelf_data.description,
            'tenant_id': str(context.user.tenant_id)
        }

        # Create shelf using the service
        created_shelf = await shelf_service.create_shelf(create_data, context.user.id)

        # Convert to response format
        response_data = {
            'id': str(created_shelf.id),
            'library_id': str(created_shelf.library_id),
            'name': created_shelf.name,
            'description': created_shelf.description,
            'resource_count': created_shelf.resource_count or 0,
            'created_at': created_shelf.created_at.isoformat() if created_shelf.created_at else None,
            'updated_at': created_shelf.updated_at.isoformat() if created_shelf.updated_at else None
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create shelf: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create shelf: {str(e)}"
        )

@shelf_router.get("/{shelf_id}")
async def get_shelf(
    shelf_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(view_library_permission)
):
    """Get a specific shelf by ID"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert shelf_id to UUID
        from uuid import UUID
        shelf_uuid = UUID(shelf_id)

        # Get shelf using the service
        shelf = await shelf_service.get_shelf(shelf_uuid, context.user.id)

        # Convert to response format
        response_data = {
            'id': str(shelf.id),
            'library_id': str(shelf.library_id),
            'name': shelf.name,
            'description': shelf.description,
            'resource_count': shelf.resource_count or 0,
            'created_at': shelf.created_at.isoformat() if shelf.created_at else None,
            'updated_at': shelf.updated_at.isoformat() if shelf.updated_at else None
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get shelf {shelf_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shelf: {str(e)}"
        )

@shelf_router.put("/{shelf_id}")
async def update_shelf(
    shelf_id: str,
    shelf_data: ShelfUpdate,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(update_library_permission)
):
    """Update a shelf"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert shelf_id to UUID
        from uuid import UUID
        shelf_uuid = UUID(shelf_id)

        # Prepare update data (exclude None values)
        update_data = {k: v for k, v in shelf_data.dict().items() if v is not None}

        # Update shelf using the service
        updated_shelf = await shelf_service.update_shelf(shelf_uuid, update_data, context.user.id)

        # Convert to response format
        response_data = {
            'id': str(updated_shelf.id),
            'library_id': str(updated_shelf.library_id),
            'name': updated_shelf.name,
            'description': updated_shelf.description,
            'resource_count': updated_shelf.resource_count or 0,
            'created_at': updated_shelf.created_at.isoformat() if updated_shelf.created_at else None,
            'updated_at': updated_shelf.updated_at.isoformat() if updated_shelf.updated_at else None
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update shelf {shelf_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update shelf: {str(e)}"
        )

@shelf_router.delete("/{shelf_id}")
async def delete_shelf(
    shelf_id: str,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(delete_library_permission)
):
    """Delete a shelf (only if empty)"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert shelf_id to UUID
        from uuid import UUID
        shelf_uuid = UUID(shelf_id)

        # Delete shelf using the service
        success = await shelf_service.delete_shelf(shelf_uuid, context.user.id)

        if success:
            return JSONResponse(
                content={"message": "Shelf deleted successfully"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Shelf not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete shelf {shelf_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete shelf: {str(e)}"
        )

@shelf_router.get("/{shelf_id}/resources")
async def get_shelf_resources(
    shelf_id: str,
    search: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=100),
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(view_library_permission)
):
    """Get all resources in a shelf"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert shelf_id to UUID
        from uuid import UUID
        shelf_uuid = UUID(shelf_id)

        # Get shelf resources using the service
        resources = await shelf_service.get_shelf_resources(
            shelf_uuid,
            context.user.id,
            search=search,
            limit=limit
        )

        return JSONResponse(
            content={
                "resources": resources,
                "total": len(resources)
            },
            status_code=200
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get resources for shelf {shelf_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shelf resources: {str(e)}"
        )

@shelf_router.post("/{shelf_id}/resources")
async def add_resource_to_shelf(
    shelf_id: str,
    resource_data: ResourceAssignment,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(update_library_permission)
):
    """Add a resource to a shelf"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert IDs to UUID
        from uuid import UUID
        shelf_uuid = UUID(shelf_id)
        resource_uuid = UUID(resource_data.resource_id)

        # Add resource to shelf using the service
        success = await shelf_service.add_resource_to_shelf(
            shelf_uuid,
            resource_uuid,
            context.user.id
        )

        if success:
            return JSONResponse(
                content={"message": "Resource added to shelf successfully"},
                status_code=200
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add resource to shelf"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add resource {resource_data.resource_id} to shelf {shelf_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add resource to shelf: {str(e)}"
        )

@shelf_router.get("/library/{library_id}")
async def list_library_shelves(
    library_id: str,
    resource_type: Optional[str] = None,
    context: StatelessRunningContext = Depends(create_running_context),
    permission_check: StatelessUser = Depends(view_library_permission)
):
    """List all shelves in a library"""
    try:
        from services.shelf_service import ShelfService
        shelf_service = ShelfService()
        await shelf_service.initialize()

        # Convert library_id to UUID
        from uuid import UUID
        library_uuid = UUID(library_id)

        # Get shelves using the service
        shelves = await shelf_service.list_library_shelves(
            library_uuid,
            context.user.id,
            resource_type
        )

        # Convert shelves to dict format for JSON response
        shelves_data = []
        for shelf in shelves:
            shelf_dict = {
                'id': str(shelf.id),
                'library_id': str(shelf.library_id),
                'name': shelf.name,
                'description': shelf.description,
                'resource_count': shelf.resource_count or 0,
                'created_at': shelf.created_at.isoformat() if shelf.created_at else None,
                'updated_at': shelf.updated_at.isoformat() if shelf.updated_at else None
            }
            shelves_data.append(shelf_dict)

        return JSONResponse(content={
            'shelves': shelves_data,
            'total': len(shelves_data)
        })

    except Exception as e:
        logger.error(f"Failed to list library shelves: {e}")
        # Fallback to empty list to prevent frontend errors
        return JSONResponse(content={
            'shelves': [],
            'total': 0
        })
