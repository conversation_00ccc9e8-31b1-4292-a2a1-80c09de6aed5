"""
File serving routes for MinIO storage integration.
Handles serving files from MinIO storage backend.
"""

from fastapi import APIRouter, Depends, HTTPException, Response, Request
from fastapi.responses import StreamingResponse
from typing import Optional
import logging
from urllib.parse import unquote
import os

from services.storage import storage_manager
from security.unified_auth_middleware import get_current_user, requires_permissions
from security.auth_middleware import StatelessUser

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/files", tags=["files"])

# Get allowed origins from environment or use defaults (same as main.py)
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:5173,http://localhost:3000,http://localhost:4028").split(",")
allowed_origins = [origin.strip() for origin in allowed_origins if origin.strip()]


@router.get("/{file_path:path}")
async def serve_file(
    file_path: str,
    request: Request,
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Serve files from MinIO storage.
    
    Args:
        file_path: The storage key/path of the file to serve
        context: Authentication context (optional for public files)
    
    Returns:
        StreamingResponse: The file content with appropriate headers
    """
    try:
        logger.info(f"Serving file request for path: {file_path}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")
        
        # Decode URL-encoded path
        file_path = unquote(file_path)
        logger.info(f"Decoded file path: {file_path}")
        
        # Check if file exists
        logger.info(f"Checking if file exists: {file_path}")
        if not await storage_manager.file_exists(file_path):
            logger.warning(f"File not found: {file_path}")
            raise HTTPException(status_code=404, detail="File not found")
        
        logger.info(f"File exists, getting metadata for: {file_path}")
        # Get file metadata
        metadata = await storage_manager.get_file_metadata(file_path)
        logger.info(f"File metadata: {metadata}")
        
        # Basic access control - users can only access files from their tenant
        # Public files or files that don't require authentication can be served directly
        if context and current_user:
            logger.info(f"User authenticated: {current_user.id}, tenant: {current_user.tenant_id}")
            # Extract tenant_id from file path (format: tenant_id/type/date/filename)
            path_parts = file_path.split('/')
            logger.info(f"File path parts: {path_parts}")
            if len(path_parts) >= 1:
                file_tenant_id = path_parts[0]
                logger.info(f"File tenant ID: {file_tenant_id}, User tenant ID: {current_user.tenant_id}")
                # Check if user has access to this tenant's files
                if str(current_user.tenant_id) != file_tenant_id:
                    logger.info("Checking system permissions for cross-tenant access")
                    # Check if user has system-level permissions
                    has_system_permission = await context.has_any_permission([
                        "super_admin", "system:create_tenant", "system:delete_tenant", 
                        "system:list_tenants", "system:manage_users", "system:view_analytics"
                    ])
                    logger.info(f"Has system permission: {has_system_permission}")
                    if not has_system_permission:
                        # Allow access to profile images for now (can be made more restrictive)
                        if not (len(path_parts) >= 2 and path_parts[1] == 'profile_images'):
                            logger.warning(f"Access denied for user {current_user.id} to file {file_path}")
                            raise HTTPException(status_code=403, detail="Access denied")
        else:
            logger.info("No authentication context provided")
        
        logger.info(f"Getting file stream for: {file_path}")
        # Get file stream
        file_stream = await storage_manager.get_file_stream(file_path)
        
        # Determine content type
        content_type = metadata.content_type or 'application/octet-stream'
        logger.info(f"Content type: {content_type}")
        
        # Create streaming response
        def iterfile():
            try:
                while True:
                    chunk = file_stream.read(8192)  # 8KB chunks
                    if not chunk:
                        break
                    yield chunk
            finally:
                if hasattr(file_stream, 'close'):
                    file_stream.close()
                if hasattr(file_stream, 'release_conn'):
                    file_stream.release_conn()
        
        # Set appropriate headers
        headers = {
            "Content-Type": content_type,
            "Cache-Control": "public, max-age=3600",  # Cache for 1 hour
        }
        
        # Add CORS headers
        origin = request.headers.get("origin")
        if origin and origin in allowed_origins:
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
            headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
            headers["Access-Control-Allow-Headers"] = "*"

        # Add content disposition - default to inline for browser viewing
        # Extract filename from path
        filename = file_path.split('/')[-1]
        
        # Check if download is specifically requested via query parameter
        download_requested = request.query_params.get('download', '').lower() in ['true', '1', 'yes']
        
        if download_requested:
            headers["Content-Disposition"] = f"attachment; filename=\"{filename}\""
        else:
            # Default to inline for browser viewing (including PDFs, images, text files, etc.)
            headers["Content-Disposition"] = f"inline; filename=\"{filename}\""
        
        logger.info(f"Response headers: {headers}")
        logger.info(f"Successfully serving file: {file_path}")
        return StreamingResponse(iterfile(), headers=headers, media_type=content_type)

    except HTTPException as e:
        logger.error(f"HTTP Exception in serve_file: {e.status_code} - {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in serve_file for path {file_path}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.head("/{file_path:path}")
async def check_file_exists(
    file_path: str,
    request: Request,
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Check if a file exists (HEAD request).
    
    Args:
        file_path: The storage key/path of the file to check
        context: Authentication context (optional for public files)
    
    Returns:
        Response: 200 if file exists, 404 if not found
    """
    try:
        logger.info(f"HEAD request for file path: {file_path}")
        logger.info(f"Request headers: {dict(request.headers)}")
        
        # Decode URL-encoded path
        file_path = unquote(file_path)
        logger.info(f"Decoded file path: {file_path}")
        
        # Check if file exists
        logger.info(f"Checking if file exists: {file_path}")
        if not await storage_manager.file_exists(file_path):
            logger.warning(f"File not found for HEAD request: {file_path}")
            raise HTTPException(status_code=404, detail="File not found")
        
        # Basic access control (same as GET)
        if context and current_user:
            logger.info(f"User authenticated for HEAD request: {current_user.id}, tenant: {current_user.tenant_id}")
            path_parts = file_path.split('/')
            logger.info(f"File path parts: {path_parts}")
            if len(path_parts) >= 1:
                file_tenant_id = path_parts[0]
                logger.info(f"File tenant ID: {file_tenant_id}, User tenant ID: {current_user.tenant_id}")
                if str(current_user.tenant_id) != file_tenant_id:
                    logger.info("Checking system permissions for cross-tenant access in HEAD request")
                    # Check if user has system-level permissions
                    has_system_permission = await context.has_any_permission([
                        "super_admin", "system:create_tenant", "system:delete_tenant", 
                        "system:list_tenants", "system:manage_users", "system:view_analytics"
                    ])
                    logger.info(f"Has system permission: {has_system_permission}")
                    if not has_system_permission:
                        if not (len(path_parts) >= 2 and path_parts[1] == 'profile_images'):
                            logger.warning(f"Access denied for user {current_user.id} to file {file_path} in HEAD request")
                            raise HTTPException(status_code=403, detail="Access denied")
        else:
            logger.info("No authentication context provided for HEAD request")
        
        logger.info(f"Getting file metadata for HEAD request: {file_path}")
        # Get file metadata for headers
        metadata = await storage_manager.get_file_metadata(file_path)
        logger.info(f"File metadata: {metadata}")
        content_type = metadata.content_type or 'application/octet-stream'
        
        headers = {
            "Content-Type": content_type,
            "Cache-Control": "public, max-age=3600",
        }
        # Add CORS headers
        origin = request.headers.get("origin")
        if origin and origin in allowed_origins:
            headers["Access-Control-Allow-Origin"] = origin
            headers["Access-Control-Allow-Credentials"] = "true"
            headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
            headers["Access-Control-Allow-Headers"] = "*"
        
        logger.info(f"HEAD response headers: {headers}")
        logger.info(f"Successfully processed HEAD request for file: {file_path}")
        return Response(headers=headers)
        
    except HTTPException as e:
        logger.error(f"HTTP Exception in check_file_exists: {e.status_code} - {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error in check_file_exists for path {file_path}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to check file: {str(e)}")


@router.options("/{file_path:path}")
async def handle_file_options(
    file_path: str,
    request: Request
):
    """
    Handle OPTIONS (preflight) requests for CORS.
    """
    origin = request.headers.get("origin")
    headers = {}
    
    if origin and origin in allowed_origins:
        headers["Access-Control-Allow-Origin"] = origin
        headers["Access-Control-Allow-Credentials"] = "true"
        headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
        headers["Access-Control-Allow-Headers"] = "*"
    
    return Response(status_code=200, headers=headers)


@router.options("/{tenant_id}/files/{date_path}/{filename}")
async def options_legacy_file(tenant_id: str, date_path: str, filename: str, request: Request):
    """
    Handle OPTIONS preflight requests for legacy file paths.
    """
    origin = request.headers.get("origin")
    headers = {
        "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
        "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
        "Access-Control-Allow-Headers": "*",
        "Access-Control-Allow-Credentials": "true",
        "Access-Control-Max-Age": "86400",  # 24 hours
    }
    return Response(status_code=200, headers=headers)

# Legacy route to handle old file paths with duplicate /files/ segment
@router.get("/{tenant_id}/files/{date_path}/{filename}")
async def serve_legacy_file(
    tenant_id: str,
    date_path: str,
    filename: str,
    request: Request,
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Legacy route to handle old file paths with duplicate /files/ segment.
    Redirects to the correct file path without the duplicate segment.
    """
    try:
        logger.info(f"Legacy file request - tenant: {tenant_id}, date: {date_path}, filename: {filename}")
        
        # Reconstruct the correct file path without the duplicate /files/
        correct_file_path = f"{tenant_id}/files/{date_path}/{filename}"
        logger.info(f"Redirecting to correct path: {correct_file_path}")
        
        # Call the main serve_file function with the correct path
        return await serve_file(correct_file_path, request, context)
        
    except Exception as e:
        logger.error(f"Error serving legacy file {tenant_id}/files/{date_path}/{filename}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.head("/{tenant_id}/files/{date_path}/{filename}")
async def check_legacy_file_exists(
    tenant_id: str,
    date_path: str,
    filename: str,
    request: Request,
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Legacy HEAD route to handle old file paths with duplicate /files/ segment.
    """
    try:
        logger.info(f"Legacy HEAD request - tenant: {tenant_id}, date: {date_path}, filename: {filename}")
        
        # Reconstruct the correct file path
        correct_file_path = f"{tenant_id}/files/{date_path}/{filename}"
        logger.info(f"HEAD request for correct path: {correct_file_path}")
        
        # Call the main check_file_exists function with the correct path
        return await check_file_exists(correct_file_path, request, context)
        
    except Exception as e:
        logger.error(f"Error checking legacy file {tenant_id}/files/{date_path}/{filename}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to check file: {str(e)}")
