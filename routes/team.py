from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from uuid import UUID
from middleware.permission import Permission<PERSON>he<PERSON>
from models.role import Permission
from session.running_context import RunningContext
from models.user import User
from services.team_service import TeamService, team_service

router = APIRouter(prefix="/api/team", tags=["team"])

# Define permission checkers for different operations
manage_team_permission = PermissionChecker([Permission.MANAGE_TEAM])
invite_member_permission = PermissionChecker([Permission.INVITE_MEMBER])
remove_member_permission = PermissionChecker([Permission.REMOVE_MEMBER])

@router.post("/members/invite")
async def invite_team_member(
    email: str,
    role_name: str,
    context: RunningContext = Depends(invite_member_permission)
):
    """Invite a new member to the team"""
    try:
        if not context.tenant_id:
            raise HTTPException(status_code=400, detail="Missing tenant context")
        await team_service.invite_member(
            email=email,
            role_name=role_name,
            tenant_id=context.tenant_id
        )
        return {"message": "Team member invited successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/members/{user_id}")
async def remove_team_member(
    user_id: UUID,
    context: RunningContext = Depends(remove_member_permission)
):
    """Remove a member from the team"""
    try:
        if not context.tenant_id:
            raise HTTPException(status_code=400, detail="Missing tenant context")
        await team_service.remove_member(
            user_id=user_id,
            tenant_id=context.tenant_id
        )
        return {"message": "Team member removed successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/members")
async def list_team_members(
    context: RunningContext = Depends(manage_team_permission)
):
    """List all team members"""
    try:
        if not context.tenant_id:
            raise HTTPException(status_code=400, detail="Missing tenant context")
        members = await team_service.list_team_members(
            tenant_id=context.tenant_id
        )
        return members
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/members/{user_id}/role")
async def update_member_role(
    user_id: str,
    role_name: str,
    context: RunningContext = Depends(manage_team_permission)
):
    """Update a team member's role"""
    try:
        await team_service.update_member_role(
            tenant_id=context.current_user.tenant_id,
            user_id=user_id,
            role_name=role_name,
            updater=context.current_user
        )
        return {"message": "Team member role updated successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))