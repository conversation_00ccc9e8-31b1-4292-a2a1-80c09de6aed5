from fastapi import APIRouter, Depends, HTTPException, Query, Form
from typing import List, Optional, Dict
from services.task_scheduler import TaskScheduler
from services.task_service import task_service, TaskService
from middleware.permission import Permission<PERSON>hecker
from models.role import Permission
from session.running_context import RunningContext
from models.task import Task, TaskStatus, TaskType
from fastapi.responses import JSONResponse
from uuid import UUID

router = APIRouter(prefix="/tasks", tags=["tasks"])
scheduler = TaskScheduler()

# Define permission checkers for different operations
create_task_permission = PermissionChecker([Permission.CREATE_TASK])
read_task_permission = PermissionChecker([Permission.READ_TASK])
update_task_permission = PermissionChecker([Permission.UPDATE_TASK])
delete_task_permission = PermissionChecker([Permission.DELETE_TASK])
manage_tasks_permission = PermissionChecker([Permission.MANAGE_TASKS])
read_tasks_permission = PermissionChecker([Permission.READ_TASKS])

# Initialize services
task_service = TaskService()

@router.post("", response_model=Task)
async def create_task_route(
    name: str = Form(...),
    task_type: TaskType = Form(...),
    schedule: Optional[str] = Form(None),
    resource_id: Optional[str] = Form(None),
    metadata: Optional[dict] = Form({}),
    context: RunningContext = Depends(manage_tasks_permission)
):
    """Create a new task"""
    tenant_id = context.tenant_id
    if not tenant_id:
        raise HTTPException(status_code=400, detail="Tenant id missing in user context")
    
    created_task = await task_service.create_task(
        name=name,
        task_type=task_type,
        tenant_id=tenant_id,
        user=context.user,
        schedule=schedule,
        resource_id=resource_id,
        metadata=metadata
    )
    
    if schedule:
        await scheduler.schedule_task(str(created_task.id), schedule)
    
    return JSONResponse({
        "message": "Task created successfully",
        "task": created_task.dict()
    }, status_code=201)

@router.get("", response_model=List[Task])
async def list_tasks_route(
    status: Optional[TaskStatus] = None,
    context: RunningContext = Depends(read_tasks_permission)
):
    """List all tasks"""
    tasks = await task_service.list_tasks(context.user, status)
    return tasks

@router.get("/{task_id}", response_model=Task)
async def get_task_route(
    task_id: str,
    context: RunningContext = Depends(read_task_permission)
):
    """Get a specific task"""
    task = await task_service.get_task(task_id, context.user)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.put("/{task_id}", response_model=Task)
async def update_task_route(
    task_id: str,
    update_data: Dict,
    context: RunningContext = Depends(update_task_permission)
):
    """Update a task"""
    updated_task = await task_service.update_task(task_id, update_data, context.user)
    if not updated_task:
        raise HTTPException(status_code=404, detail="Task not found")
    return updated_task

@router.delete("/{task_id}")
async def delete_task_route(
    task_id: str,
    context: RunningContext = Depends(delete_task_permission)
):
    """Delete a task"""
    # Cancel any scheduled job first
    await scheduler.cancel_task(task_id)
    
    success = await task_service.delete_task(task_id, context.user)
    if not success:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return JSONResponse({"message": "Task deleted successfully"}, status_code=200)

@router.post("/{task_id}/pause")
async def pause_task(
    task_id: str,
    context: RunningContext = Depends(update_task_permission)
):
    """Pause a scheduled task"""
    try:
        success = await scheduler.pause_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        
        await task_service.update_task_status(task_id, TaskStatus.PAUSED, context.user)
        
        return {"message": "Task paused successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{task_id}/resume")
async def resume_task(
    task_id: str,
    context: RunningContext = Depends(update_task_permission)
):
    """Resume a paused task"""
    try:
        success = await scheduler.resume_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
            
        await task_service.update_task_status(task_id, TaskStatus.PENDING, context.user)
        
        return {"message": "Task resumed successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{task_id}/execute")
async def execute_task_now(
    task_id: str,
    context: RunningContext = Depends(update_task_permission)
):
    """Execute a task immediately"""
    try:
        task = await task_service.get_task(task_id, context.user)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        await scheduler._execute_task(task_id)
        return {"message": "Task execution started"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))