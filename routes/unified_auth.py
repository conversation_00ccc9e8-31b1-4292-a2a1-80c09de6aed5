"""
Unified Authentication Routes

This module provides authentication endpoints using the UnifiedAuthService
with secure JWT + refresh token pattern implementation.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, Form, Cookie, Body
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional
from pydantic import BaseModel, EmailStr
import logging

from services.unified_auth_service import unified_auth_service, COOKIE_NAME, REFRESH_COOKIE_NAME
from security.unified_auth_middleware import get_current_user, create_running_context
from security.auth_middleware import StatelessUser, StatelessRunningContext
from models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])

# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class LoginRequest(BaseModel):
    """Schema for login request"""
    email: EmailStr
    password: str

class LoginResponse(BaseModel):
    """Schema for login response"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    refresh_expires_in: int
    user: dict
    message: str

class RefreshRequest(BaseModel):
    """Schema for token refresh request"""
    refresh_token: str

class RefreshResponse(BaseModel):
    """Schema for token refresh response"""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    refresh_expires_in: int
    user: dict
    message: str

class LogoutResponse(BaseModel):
    """Schema for logout response"""
    message: str
    success: bool

class ErrorResponse(BaseModel):
    """Schema for error response"""
    message: str
    error: Optional[str] = None

# ============================================================================
# AUTHENTICATION ENDPOINTS
# ============================================================================

@router.post("/login", response_model=LoginResponse)
async def login(
    request: Request,
    response: Response,
    login_data: LoginRequest
):
    """
    Authenticate user and return JWT token pair
    
    This endpoint:
    1. Validates user credentials
    2. Creates access token with embedded permissions (stateless)
    3. Creates refresh token stored in database with rotation support
    4. Sets secure HTTP-only cookies
    5. Returns token pair with user information
    """
    try:
        # Authenticate user
        user = await unified_auth_service.authenticate_user(
            login_data.email,
            login_data.password
        )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Create secure token pair with embedded permissions
        token_data = await unified_auth_service.create_token_pair(user, request)

        # Set secure auth cookies
        unified_auth_service.set_auth_cookies(
            response,
            token_data["access_token"],
            token_data["refresh_token"]
        )
        
        logger.info(f"User {user.id} logged in successfully")
        
        return LoginResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=token_data["expires_in"],
            refresh_expires_in=token_data["refresh_expires_in"],
            user=token_data["user"],
            message="Login successful"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )

@router.post("/refresh", response_model=RefreshResponse)
async def refresh_token(
    request: Request,
    response: Response,
    refresh_token_cookie: Optional[str] = Cookie(None, alias=REFRESH_COOKIE_NAME)
):
    """
    Refresh access token using refresh token with rotation
    
    This endpoint:
    1. Validates refresh token against database
    2. Checks user is still active
    3. Revokes old refresh token (rotation)
    4. Creates new token pair
    5. Updates cookies with new tokens
    
    Accepts refresh token from:
    - Request body (JSON)
    - Form data
    - HTTP-only cookie
    """
    try:
        # Get refresh token from various sources
        refresh_token = None

        # Try to get from cookie first
        if refresh_token_cookie:
            refresh_token = refresh_token_cookie
        else:
            # Try to parse JSON body manually
            try:
                body = await request.body()
                if body:
                    import json
                    data = json.loads(body.decode())
                    if isinstance(data, dict) and "refresh_token" in data:
                        refresh_token = data["refresh_token"]
            except Exception:
                # Silently ignore JSON parsing errors
                pass

        if not refresh_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Refresh token required"
            )

        # Refresh token with rotation
        token_data = await unified_auth_service.refresh_access_token(refresh_token, request)

        # Update cookies with new tokens
        unified_auth_service.set_auth_cookies(
            response,
            token_data["access_token"],
            token_data["refresh_token"]
        )
        
        logger.info(f"Token refreshed successfully for user {token_data['user']['id']}")

        return RefreshResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=token_data["expires_in"],
            refresh_expires_in=token_data["refresh_expires_in"],
            user=token_data["user"],
            message="Token refreshed successfully"
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post("/logout", response_model=LogoutResponse)
async def logout(
    request: Request,
    response: Response,
    current_user: StatelessUser = Depends(get_current_user),
    access_token_cookie: Optional[str] = Cookie(None, alias=COOKIE_NAME),
    refresh_token_cookie: Optional[str] = Cookie(None, alias=REFRESH_COOKIE_NAME)
):
    """
    Logout user by blacklisting tokens and revoking refresh tokens
    
    This endpoint:
    1. Blacklists current access token
    2. Revokes refresh token from database
    3. Clears authentication cookies
    4. Logs security event
    """
    try:
        # Get tokens from request
        access_token = None
        refresh_token = refresh_token_cookie

        # Try to get access token from cookie first
        if access_token_cookie:
            access_token = access_token_cookie
        else:
            # Try to get access token from Authorization header
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                access_token = auth_header[7:]  # Remove "Bearer " prefix
        
        # Perform logout
        success = await unified_auth_service.logout(
            access_token=access_token or "",  # Empty string if no access token
            refresh_token=refresh_token,
            request=request
        )

        # Clear authentication cookies
        unified_auth_service.clear_auth_cookies(response)
        
        logger.info(f"User {current_user.id} logged out successfully")
        
        return LogoutResponse(
            message="Logout successful",
            success=success
        )
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        # Even if logout fails, clear cookies for security
        unified_auth_service.clear_auth_cookies(response)
        
        return LogoutResponse(
            message="Logout completed (with errors)",
            success=False
        )

@router.post("/logout-all")
async def logout_all_devices(
    request: Request,
    response: Response,
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Logout user from all devices by revoking all refresh tokens
    
    This endpoint:
    1. Revokes all refresh tokens for the user
    2. Clears current device cookies
    3. Forces re-authentication on all devices
    """
    try:
        # Revoke all user tokens
        revoked_count = await unified_auth_service.revoke_all_user_tokens(
            user_id=current_user.id,
            reason="Logout from all devices"
        )

        # Clear cookies on current device
        unified_auth_service.clear_auth_cookies(response)
        
        logger.info(f"User {current_user.id} logged out from all devices ({revoked_count} tokens revoked)")
        
        return {
            "message": f"Logged out from all devices ({revoked_count} sessions terminated)",
            "success": True,
            "tokens_revoked": revoked_count
        }
        
    except Exception as e:
        logger.error(f"Logout all error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout from all devices"
        )

# ============================================================================
# UTILITY ENDPOINTS
# ============================================================================

@router.get("/me")
async def get_current_user_info(
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Get current authenticated user information
    
    Returns user data embedded in the JWT token (no database lookup)
    """
    return {
        "user": {
            "id": str(current_user.id),
            "email": current_user.email,
            "tenant_id": str(current_user.tenant_id) if current_user.tenant_id else None,
            "role": current_user.role,
            "permissions": list(current_user.permissions),
            "is_system_admin": current_user.is_system_admin(),
            "is_tenant_admin": current_user.is_tenant_admin()
        },
        "token_info": {
            "jti": current_user.jti,
            "issued_at": current_user.iat,
            "expires_at": current_user.exp
        }
    }

@router.get("/validate")
async def validate_token(
    current_user: StatelessUser = Depends(get_current_user)
):
    """
    Validate current token and return basic info
    
    This endpoint can be used by clients to check if their token is still valid
    """
    return {
        "valid": True,
        "user_id": str(current_user.id),
        "expires_at": current_user.exp,
        "message": "Token is valid"
    }
