from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Optional
from uuid import UUID
from models.tenant import Tenant
from services.tenant_service import TenantService
from middleware.permission import PermissionChecker, requires_system_admin
from session.running_context import RunningContext
from models.role import Permission

router = APIRouter(prefix="/tenants", tags=["tenants"])

# Initialize services
tenant_service = TenantService()

@router.post("", response_model=Tenant)
async def create_tenant(
    name: str,
    description: Optional[str] = None,
    settings: Optional[Dict] = None,
    context: RunningContext = Depends(PermissionChecker([Permission.SYSTEM_CREATE_TENANT]))
):
    """Create a new tenant (requires system:create_tenant permission)"""
    try:
        tenant = await tenant_service.create_tenant(
            name=name,
            description=description,
            settings=settings,
            created_by=context.user.id
        )
        return tenant
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("", response_model=List[Tenant])
async def list_tenants(
    context: RunningContext = Depends(PermissionChecker([Permission.SYSTEM_LIST_TENANTS]))
):
    """List all tenants (requires system:list_tenants permission)"""
    try:
        tenants = await tenant_service.list_tenants()
        return tenants
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{tenant_id}", response_model=Tenant)
async def get_tenant(
    tenant_id: UUID,
    context: RunningContext = Depends(PermissionChecker([Permission.MANAGE_TENANT_SETTINGS]))
):
    """Get a specific tenant (requires manage_tenant_settings permission or system access)"""
    # Check if user can access this tenant
    if not await context.has_permission(Permission.SYSTEM_LIST_TENANTS.value):
        # Non-system users can only view their own tenant
        if context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Can only view own tenant unless you have system permissions"
            )
    
    try:
        tenant = await tenant_service.get_tenant(tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")
        return tenant
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{tenant_id}", response_model=Tenant)
async def update_tenant(
    tenant_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    settings: Optional[Dict] = None,
    context: RunningContext = Depends(PermissionChecker([Permission.MANAGE_TENANT_SETTINGS]))
):
    """Update a tenant (requires manage_tenant_settings permission or system access)"""
    # Check if user can access this tenant
    if not await context.has_permission(Permission.SYSTEM_LIST_TENANTS.value):
        # Non-system users can only update their own tenant
        if context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Can only update own tenant unless you have system permissions"
            )
    
    try:
        updated_tenant = await tenant_service.update_tenant(
            tenant_id=tenant_id,
            name=name,
            description=description,
            settings=settings
        )
        return updated_tenant
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{tenant_id}")
async def delete_tenant(
    tenant_id: UUID,
    context: RunningContext = Depends(PermissionChecker([Permission.SYSTEM_DELETE_TENANT]))
):
    """Delete a tenant (requires system:delete_tenant permission)"""
    try:
        success = await tenant_service.delete_tenant(tenant_id)
        if not success:
            raise HTTPException(status_code=404, detail="Tenant not found")
        return {"message": "Tenant deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{tenant_id}/analytics")
async def get_tenant_analytics(
    tenant_id: UUID,
    context: RunningContext = Depends(PermissionChecker([Permission.VIEW_TENANT_ANALYTICS]))
):
    """Get analytics for a tenant (requires view_tenant_analytics permission or system access)"""
    # Check if user can access this tenant's analytics
    if not await context.has_permission(Permission.SYSTEM_VIEW_ANALYTICS.value):
        # Non-system users can only view their own tenant analytics
        if context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Can only view own tenant analytics unless you have system permissions"
            )
    
    try:
        analytics = await tenant_service.get_tenant_analytics(tenant_id)
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
