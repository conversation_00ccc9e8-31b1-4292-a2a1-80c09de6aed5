from fastapi import APIRouter, Depends, HTTPException, Form, Query
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
from uuid import UUID
import logging

from models.pipeline import (
    Pipeline, PipelineExecution, NodeExecution,
    PipelineStatus, NodeType, DataSourceType, TriggerType,
    DataSourceNodeConfig, EntitySchemaNodeConfig, ProcessorNodeConfig,
    ParentChildIndexerConfig, EntityInserterConfig, PipelineNode
)
from services.pipeline_service import PipelineService
from session.running_context import RunningContext, require_context
from security.unified_auth_middleware import get_current_user, create_running_context, requires_permissions
from security.auth_middleware import StatelessUser, StatelessRunningContext

from models.role import Permission

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/pipelines", tags=["pipelines"])

# Permission checkers
create_pipeline_permission = requires_permissions({Permission.CREATE_RESOURCE})
read_pipeline_permission = requires_permissions({Permission.READ_RESOURCE})
update_pipeline_permission = requires_permissions({Permission.UPDATE_RESOURCE})
delete_pipeline_permission = requires_permissions({Permission.DELETE_RESOURCE})
execute_pipeline_permission = requires_permissions({Permission.UPDATE_RESOURCE})

# Dependency to get pipeline service
async def get_pipeline_service() -> PipelineService:
    service = PipelineService()
    await service.initialize()
    return service

# Pipeline CRUD Operations
@router.post("/", response_model=Pipeline)
async def create_pipeline(
    pipeline: Pipeline,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(create_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Create a new data processing pipeline"""
    try:
        # Ensure tenant context
        pipeline.tenant_id = current_user.tenant_id
        
        created_pipeline = await pipeline_service.create_pipeline(
            pipeline=pipeline,
            user_id=current_user.id
        )
        
        return JSONResponse({
            "message": "Pipeline created successfully",
            "pipeline": created_pipeline.dict()
        }, status_code=201)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating pipeline: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create pipeline: {str(e)}")

@router.get("/", response_model=List[Pipeline])
async def list_pipelines(
    status: Optional[PipelineStatus] = Query(None, description="Filter by pipeline status"),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """List all pipelines for the current tenant"""
    try:
        pipelines = await pipeline_service.list_pipelines(
            tenant_id=current_user.tenant_id,
            status=status
        )
        
        return JSONResponse({
            "pipelines": [p.dict() for p in pipelines],
            "total": len(pipelines)
        })
        
    except Exception as e:
        logger.error(f"Error listing pipelines: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list pipelines: {str(e)}")

@router.get("/{pipeline_id}", response_model=Pipeline)
async def get_pipeline(
    pipeline_id: UUID,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Get a specific pipeline by ID"""
    try:
        pipeline = await pipeline_service.get_pipeline(
            pipeline_id=pipeline_id,
            tenant_id=current_user.tenant_id
        )
        
        if not pipeline:
            raise HTTPException(status_code=404, detail="Pipeline not found")
        
        return JSONResponse({
            "pipeline": pipeline.dict()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting pipeline {pipeline_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get pipeline: {str(e)}")

@router.put("/{pipeline_id}", response_model=Pipeline)
async def update_pipeline(
    pipeline_id: UUID,
    updates: Dict[str, Any],
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(update_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Update a pipeline"""
    try:
        updated_pipeline = await pipeline_service.update_pipeline(
            pipeline_id=pipeline_id,
            updates=updates,
            user_id=current_user.id
        )
        
        return JSONResponse({
            "message": "Pipeline updated successfully",
            "pipeline": updated_pipeline.dict()
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating pipeline {pipeline_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update pipeline: {str(e)}")

# Pipeline Execution
@router.post("/{pipeline_id}/execute", response_model=PipelineExecution)
async def execute_pipeline(
    pipeline_id: UUID,
    trigger_data: Optional[Dict[str, Any]] = None,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(execute_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Execute a pipeline"""
    try:
        execution = await pipeline_service.execute_pipeline(
            pipeline_id=pipeline_id,
            tenant_id=current_user.tenant_id,
            trigger_data=trigger_data,
            user_id=current_user.id
        )
        
        return JSONResponse({
            "message": "Pipeline execution started",
            "execution": execution.dict()
        }, status_code=202)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error executing pipeline {pipeline_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to execute pipeline: {str(e)}")

@router.get("/{pipeline_id}/executions", response_model=List[PipelineExecution])
async def get_pipeline_executions(
    pipeline_id: UUID,
    limit: int = Query(10, ge=1, le=100, description="Number of executions to return"),
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Get execution history for a pipeline"""
    try:
        # Get executions from repository
        execution_repo = await pipeline_service.execution_repo
        executions = await execution_repo.find_by_filters({
            "pipeline_id": str(pipeline_id),
            "tenant_id": str(current_user.tenant_id)
        }, limit=limit)
        
        return JSONResponse({
            "executions": [e.dict() for e in executions],
            "total": len(executions)
        })
        
    except Exception as e:
        logger.error(f"Error getting executions for pipeline {pipeline_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get executions: {str(e)}")

@router.get("/executions/{execution_id}", response_model=PipelineExecution)
async def get_execution(
    execution_id: UUID,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(read_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Get details of a specific execution"""
    try:
        execution_repo = await pipeline_service.execution_repo
        execution = await execution_repo.get_by_id(str(execution_id))
        
        if not execution:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        # Check tenant access
        if str(execution.tenant_id) != str(current_user.tenant_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        return JSONResponse({
            "execution": execution.dict()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting execution {execution_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get execution: {str(e)}")

# Pipeline Templates and Examples
@router.get("/templates", response_model=List[Dict[str, Any]])
async def get_pipeline_templates():
    """Get predefined pipeline templates"""
    templates = [
        {
            "name": "Google Drive to Entity Indexer",
            "description": "Load files from Google Drive, split into chunks, and index to entities",
            "nodes": [
                {
                    "id": "google_drive_source",
                    "config": {
                        "node_type": NodeType.DATA_SOURCE,
                        "name": "Google Drive Source",
                        "source_type": DataSourceType.GOOGLE_DRIVE,
                        "connection_config": {
                            "folder_id": "your_folder_id",
                            "file_types": ["pdf", "docx", "txt"]
                        }
                    },
                    "output_nodes": ["text_processor"]
                },
                {
                    "id": "text_processor",
                    "config": {
                        "node_type": NodeType.PROCESSOR,
                        "name": "Text Processor",
                        "processor_type": "text_splitter",
                        "processor_config": {
                            "chunk_size": 1000,
                            "chunk_overlap": 200
                        }
                    },
                    "input_nodes": ["google_drive_source"],
                    "output_nodes": ["parent_child_indexer"]
                },
                {
                    "id": "parent_child_indexer",
                    "config": {
                        "node_type": NodeType.PARENT_CHILD_INDEXER,
                        "name": "Parent-Child Indexer",
                        "target_chunk_entity": "document_chunk",
                        "chunk_size": 1000,
                        "chunk_overlap": 200,
                        "parent_metadata_fields": ["filename", "source", "created_date"]
                    },
                    "input_nodes": ["text_processor"],
                    "output_nodes": ["entity_inserter"]
                },
                {
                    "id": "entity_inserter",
                    "config": {
                        "node_type": NodeType.ENTITY_INSERTER,
                        "name": "Entity Inserter",
                        "target_entity_type": "document_chunk",
                        "upsert_mode": True
                    },
                    "input_nodes": ["parent_child_indexer"],
                    "output_nodes": []
                }
            ]
        },
        {
            "name": "Entity Collection Processor",
            "description": "Process existing entities and create enriched versions",
            "nodes": [
                {
                    "id": "entity_source",
                    "config": {
                        "node_type": NodeType.ENTITY_SCHEMA,
                        "name": "Entity Source",
                        "entity_type": "document",
                        "query_config": {
                            "is_active": True
                        }
                    },
                    "output_nodes": ["metadata_extractor"]
                },
                {
                    "id": "metadata_extractor",
                    "config": {
                        "node_type": NodeType.PROCESSOR,
                        "name": "Metadata Extractor",
                        "processor_type": "metadata_extractor",
                        "processor_config": {
                            "add_timestamp": True,
                            "add_word_count": True
                        }
                    },
                    "input_nodes": ["entity_source"],
                    "output_nodes": ["enriched_inserter"]
                },
                {
                    "id": "enriched_inserter",
                    "config": {
                        "node_type": NodeType.ENTITY_INSERTER,
                        "name": "Enriched Entity Inserter",
                        "target_entity_type": "enriched_document",
                        "upsert_mode": True
                    },
                    "input_nodes": ["metadata_extractor"],
                    "output_nodes": []
                }
            ]
        }
    ]
    
    return JSONResponse({
        "templates": templates
    })

@router.post("/templates/{template_name}/create")
async def create_pipeline_from_template(
    template_name: str,
    pipeline_name: str = Form(...),
    pipeline_description: Optional[str] = Form(None),
    template_config: Optional[Dict[str, Any]] = None,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(create_pipeline_permission),
    pipeline_service: PipelineService = Depends(get_pipeline_service)
):
    """Create a pipeline from a template"""
    try:
        # Get templates
        templates_response = await get_pipeline_templates()
        templates = templates_response.body.decode()
        import json
        templates_data = json.loads(templates)["templates"]
        
        # Find template
        template = next((t for t in templates_data if t["name"].lower().replace(" ", "_") == template_name), None)
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Create pipeline from template
        pipeline_nodes = []
        for node_data in template["nodes"]:
            node = PipelineNode(
                id=node_data["id"],
                config=node_data["config"],
                input_nodes=node_data.get("input_nodes", []),
                output_nodes=node_data.get("output_nodes", [])
            )
            pipeline_nodes.append(node)
        
        pipeline = Pipeline(
            name=pipeline_name,
            description=pipeline_description or template["description"],
            tenant_id=current_user.tenant_id,
            nodes=pipeline_nodes,
            trigger_type=TriggerType.MANUAL
        )
        
        # Apply template configuration if provided
        if template_config:
            # Apply configuration overrides
            for node in pipeline.nodes:
                if node.id in template_config:
                    node_config = template_config[node.id]
                    for key, value in node_config.items():
                        if hasattr(node.config, key):
                            setattr(node.config, key, value)
        
        created_pipeline = await pipeline_service.create_pipeline(
            pipeline=pipeline,
            user_id=current_user.id
        )
        
        return JSONResponse({
            "message": "Pipeline created from template successfully",
            "pipeline": created_pipeline.dict()
        }, status_code=201)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating pipeline from template {template_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create pipeline from template: {str(e)}")

# Node Configuration Helpers
@router.get("/node-types", response_model=List[Dict[str, Any]])
async def get_node_types():
    """Get available node types and their configurations"""
    node_types = [
        {
            "type": NodeType.DATA_SOURCE,
            "name": "Data Source",
            "description": "Load data from external sources",
            "config_schema": {
                "source_type": {
                    "type": "enum",
                    "values": [e.value for e in DataSourceType],
                    "description": "Type of data source"
                },
                "connection_config": {
                    "type": "object",
                    "description": "Connection configuration"
                },
                "filter_config": {
                    "type": "object", 
                    "description": "Filtering configuration"
                }
            }
        },
        {
            "type": NodeType.ENTITY_SCHEMA,
            "name": "Entity Schema",
            "description": "Load entities from entity collections",
            "config_schema": {
                "entity_type": {
                    "type": "string",
                    "description": "Target entity type name"
                },
                "query_config": {
                    "type": "object",
                    "description": "Query configuration"
                }
            }
        },
        {
            "type": NodeType.PROCESSOR,
            "name": "Processor",
            "description": "Process and transform data",
            "config_schema": {
                "processor_type": {
                    "type": "enum",
                    "values": ["text_splitter", "metadata_extractor", "content_filter"],
                    "description": "Type of processor"
                },
                "processor_config": {
                    "type": "object",
                    "description": "Processor-specific configuration"
                }
            }
        },
        {
            "type": NodeType.PARENT_CHILD_INDEXER,
            "name": "Parent-Child Indexer",
            "description": "Create parent-child relationships between documents and chunks",
            "config_schema": {
                "target_chunk_entity": {
                    "type": "string",
                    "description": "Target entity type for chunks"
                },
                "chunk_size": {
                    "type": "integer",
                    "description": "Size of chunks in characters"
                },
                "chunk_overlap": {
                    "type": "integer",
                    "description": "Overlap between chunks"
                },
                "parent_metadata_fields": {
                    "type": "array",
                    "description": "Parent metadata fields to copy to chunks"
                }
            }
        },
        {
            "type": NodeType.ENTITY_INSERTER,
            "name": "Entity Inserter",
            "description": "Insert processed data into entity collections",
            "config_schema": {
                "target_entity_type": {
                    "type": "string",
                    "description": "Target entity type"
                },
                "upsert_mode": {
                    "type": "boolean",
                    "description": "Whether to update existing entities"
                }
            }
        }
    ]
    
    return JSONResponse({
        "node_types": node_types
    })
