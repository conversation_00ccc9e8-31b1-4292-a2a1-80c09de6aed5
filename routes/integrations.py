from fastapi import APIRouter, Depends, HTTPException, Query, Body
from typing import List, Dict, Optional, Any
from uuid import UUID
from services.integration_service import IntegrationService
from models.integration import Integration, UserIntegrationConnection, IntegrationType, AuthType
from models.action import BaseAction
from middleware.permission import Per<PERSON><PERSON>hecker
from models.role import Permission
from session.running_context import RunningContext
from pydantic import BaseModel

router = APIRouter(prefix="/integrations", tags=["integrations"])

# Define permission checkers
read_integration_permission = PermissionChecker([Permission.READ_AGENT])
manage_integration_permission = PermissionChecker([Permission.UPDATE_AGENT])
admin_integration_permission = PermissionChecker([Permission.CREATE_AGENT])

# Initialize service
integration_service = IntegrationService()

# Request/Response models
class CreateIntegrationRequest(BaseModel):
    name: str
    provider: str
    integration_type: IntegrationType
    auth_type: AuthType
    description: Optional[str] = None
    base_url: Optional[str] = None
    auth_config: Optional[Dict[str, Any]] = None
    icon_url: Optional[str] = None
    color: Optional[str] = None
    documentation_url: Optional[str] = None
    support_url: Optional[str] = None

class CreateConnectionRequest(BaseModel):
    integration_id: UUID
    auth_data: Dict[str, Any]
    enabled_actions: Optional[List[str]] = None

class UpdateConnectionRequest(BaseModel):
    enabled_actions: Optional[List[str]] = None
    custom_settings: Optional[Dict[str, Any]] = None

# Integration Management Routes
@router.post("", response_model=Integration)
async def create_integration(
    request: CreateIntegrationRequest,
    context: RunningContext = Depends(admin_integration_permission)
):
    """Create a new integration (admin only)"""
    try:
        integration = await integration_service.create_integration(
            name=request.name,
            provider=request.provider,
            integration_type=request.integration_type,
            auth_type=request.auth_type,
            tenant_id=context.tenant_id,
            description=request.description,
            base_url=request.base_url,
            auth_config=request.auth_config,
            icon_url=request.icon_url,
            color=request.color,
            documentation_url=request.documentation_url,
            support_url=request.support_url
        )
        return integration
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("", response_model=List[Integration])
async def list_integrations(
    integration_type: Optional[IntegrationType] = Query(None),
    is_active: Optional[bool] = Query(None),
    context: RunningContext = Depends(read_integration_permission)
):
    """List available integrations"""
    integrations = await integration_service.list_integrations(
        tenant_id=context.tenant_id,
        integration_type=integration_type,
        is_active=is_active
    )
    return integrations

@router.get("/{integration_id}", response_model=Integration)
async def get_integration(
    integration_id: UUID,
    context: RunningContext = Depends(read_integration_permission)
):
    """Get a specific integration"""
    integration = await integration_service.get_integration(integration_id)
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    return integration

@router.put("/{integration_id}", response_model=Integration)
async def update_integration(
    integration_id: UUID,
    updates: Dict[str, Any] = Body(...),
    context: RunningContext = Depends(admin_integration_permission)
):
    """Update an integration (admin only)"""
    integration = await integration_service.update_integration(integration_id, **updates)
    if not integration:
        raise HTTPException(status_code=404, detail="Integration not found")
    return integration

@router.delete("/{integration_id}")
async def delete_integration(
    integration_id: UUID,
    context: RunningContext = Depends(admin_integration_permission)
):
    """Delete an integration (admin only)"""
    success = await integration_service.delete_integration(integration_id)
    if not success:
        raise HTTPException(status_code=404, detail="Integration not found")
    return {"message": "Integration deleted successfully"}

# Integration Actions Routes
@router.get("/{integration_id}/actions", response_model=List[BaseAction])
async def get_integration_actions(
    integration_id: UUID,
    is_enabled: Optional[bool] = Query(None),
    context: RunningContext = Depends(read_integration_permission)
):
    """Get all actions for an integration"""
    actions = await integration_service.get_integration_actions(
        integration_id=integration_id,
        is_enabled=is_enabled
    )
    return actions

@router.post("/{integration_id}/actions/{action_id}")
async def add_action_to_integration(
    integration_id: UUID,
    action_id: UUID,
    context: RunningContext = Depends(admin_integration_permission)
):
    """Add an action to an integration (admin only)"""
    success = await integration_service.add_action_to_integration(
        integration_id=integration_id,
        action_id=action_id
    )
    if not success:
        raise HTTPException(status_code=400, detail="Failed to add action to integration")
    return {"message": "Action added to integration successfully"}

# User Connection Routes
@router.post("/connections", response_model=UserIntegrationConnection)
async def create_user_connection(
    request: CreateConnectionRequest,
    context: RunningContext = Depends(manage_integration_permission)
):
    """Create a user connection to an integration"""
    try:
        connection = await integration_service.create_user_connection(
            integration_id=request.integration_id,
            user_id=context.user_id,
            tenant_id=context.tenant_id,
            auth_data=request.auth_data,
            enabled_actions=request.enabled_actions
        )
        return connection
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to create connection")

@router.get("/connections", response_model=List[UserIntegrationConnection])
async def list_user_connections(
    is_connected: Optional[bool] = Query(None),
    context: RunningContext = Depends(read_integration_permission)
):
    """List user's integration connections"""
    connections = await integration_service.list_user_connections(
        user_id=context.user_id,
        tenant_id=context.tenant_id,
        is_connected=is_connected
    )
    return connections

@router.get("/connections/{integration_id}", response_model=UserIntegrationConnection)
async def get_user_connection(
    integration_id: UUID,
    context: RunningContext = Depends(read_integration_permission)
):
    """Get user's connection for a specific integration"""
    connection = await integration_service.get_user_connection(
        integration_id=integration_id,
        user_id=context.user_id
    )
    if not connection:
        raise HTTPException(status_code=404, detail="Connection not found")
    return connection

@router.put("/connections/{connection_id}", response_model=UserIntegrationConnection)
async def update_user_connection(
    connection_id: UUID,
    request: UpdateConnectionRequest,
    context: RunningContext = Depends(manage_integration_permission)
):
    """Update a user's integration connection"""
    # Get the connection to verify ownership
    connection = await integration_service.connection_repository.find_by_id(connection_id)
    if not connection or connection.user_id != context.user_id:
        raise HTTPException(status_code=404, detail="Connection not found")
    
    # Update the connection
    updates = {}
    if request.enabled_actions is not None:
        updates["enabled_actions"] = request.enabled_actions
    if request.custom_settings is not None:
        updates["custom_settings"] = request.custom_settings
    
    for key, value in updates.items():
        setattr(connection, key, value)
    
    await integration_service.connection_repository.save(connection)
    return connection

@router.post("/connections/{connection_id}/actions/{action_name}/enable")
async def enable_connection_action(
    connection_id: UUID,
    action_name: str,
    context: RunningContext = Depends(manage_integration_permission)
):
    """Enable an action for a user connection"""
    success = await integration_service.enable_action_for_connection(
        connection_id=connection_id,
        action_name=action_name
    )
    if not success:
        raise HTTPException(status_code=404, detail="Connection not found")
    return {"message": f"Action '{action_name}' enabled successfully"}

@router.post("/connections/{connection_id}/actions/{action_name}/disable")
async def disable_connection_action(
    connection_id: UUID,
    action_name: str,
    context: RunningContext = Depends(manage_integration_permission)
):
    """Disable an action for a user connection"""
    success = await integration_service.disable_action_for_connection(
        connection_id=connection_id,
        action_name=action_name
    )
    if not success:
        raise HTTPException(status_code=404, detail="Connection not found")
    return {"message": f"Action '{action_name}' disabled successfully"}

# System Setup Route
@router.post("/setup-system")
async def setup_system_integrations(
    context: RunningContext = Depends(admin_integration_permission)
):
    """Set up default system integrations (admin only)"""
    await integration_service.setup_system_integrations(context.tenant_id)
    return {"message": "System integrations set up successfully"}
