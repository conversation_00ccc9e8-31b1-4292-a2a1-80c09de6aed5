from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Optional
from services.action_service import ActionService
from models.action import ActionType, BaseAction
from middleware.permission import Permission<PERSON>hecker
from models.role import Permission
from session.running_context import RunningContext

router = APIRouter(prefix="/actions", tags=["actions"])

# Define permission checkers for different operations
create_action_permission = PermissionChecker([Permission.CREATE_AGENT])
read_action_permission = PermissionChecker([Permission.READ_AGENT])
update_action_permission = PermissionChecker([Permission.UPDATE_AGENT])
delete_action_permission = PermissionChecker([Permission.DELETE_AGENT])
execute_action_permission = PermissionChecker([Permission.EXECUTE_AGENT])

# Initialize service
action_service = ActionService()

@router.post("")
async def create_action(
    name: str,
    action_type: ActionType,
    parameters: List[Dict],
    output: Dict,
    description: Optional[str] = None,
    api_config: Optional[Dict] = None,
    action_config: Optional[Dict] = None,
    graph_config: Optional[Dict] = None,
    tags: List[str] = None,
    category: Optional[str] = None,
    context: RunningContext = Depends(create_action_permission)
):
    try:
        action = await action_service.create_action(
            name=name,
            action_type=action_type,
            parameters=parameters,
            output=output,
            description=description,
            api_config=api_config,
            action_config=action_config,
            graph_config=graph_config,
            tags=tags,
            category=category,
            created_by=context.user_id
        )
        return action
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("")
async def list_actions(
    action_type: Optional[ActionType] = None,
    category: Optional[str] = None,
    tags: List[str] = Query(None),
    context: RunningContext = Depends(read_action_permission)
):
    filter_dict = {}
    if action_type:
        filter_dict["type"] = action_type
    if category:
        filter_dict["category"] = category
    if tags:
        filter_dict["tags"] = {"$in": tags}
    return await action_service.list_actions(filter_dict)

@router.get("/{name}")
async def get_action(
    name: str,
    context: RunningContext = Depends(read_action_permission)
):
    action = await action_service.get_action(name)
    if not action:
        raise HTTPException(status_code=404, detail="Action not found")
    return action

@router.put("/{name}")
async def update_action(
    name: str,
    update_data: Dict,
    context: RunningContext = Depends(update_action_permission)
):
    action = await action_service.update_action(name, update_data)
    if not action:
        raise HTTPException(status_code=404, detail="Action not found")
    return action

@router.delete("/{name}")
async def delete_action(
    name: str,
    context: RunningContext = Depends(delete_action_permission)
):
    if not await action_service.delete_action(name):
        raise HTTPException(status_code=404, detail="Action not found")
    return {"status": "success"}
