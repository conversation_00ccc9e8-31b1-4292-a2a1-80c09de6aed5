from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
from services.entity_service import EntityService
from session.running_context import RunningContext
from middleware.permission import Permission<PERSON>he<PERSON>
from middleware.rate_limiter import rate_limit
from models.role import Permission
from models.entity import EntityDefinition, EntityInstance, EntityField
from uuid import UUID

router = APIRouter(prefix="/entities", tags=["entities"])

# Define permission requirements for routes
entity_read_permission = PermissionChecker([Permission.ENTITY_READ])
entity_write_permission = PermissionChecker([Permission.ENTITY_CREATE, Permission.ENTITY_UPDATE])
entity_admin_permission = PermissionChecker([Permission.ENTITY_CREATE, Permission.ENTITY_UPDATE, Permission.ENTITY_DELETE])

# Initialize service
entity_service = EntityService()

class EntityDefinitionUpdate(BaseModel):
    """Schema for entity definition updates"""
    description: Optional[str] = None
    fields: Optional[List[EntityField]] = None
    is_active: Optional[bool] = None

class EntityInstanceUpdate(BaseModel):
    """Schema for entity instance updates"""
    data: Dict[str, Any]
    is_active: Optional[bool] = None

@router.post("/definitions", status_code=status.HTTP_201_CREATED)
@rate_limit(rate_limit=20, window=60)  # 20 creates per minute
async def create_entity_definition(
    entity: EntityDefinition,
    context: RunningContext = Depends(entity_admin_permission)
):
    """Create a new entity definition
    
    Args:
        entity: The entity definition to create
        context: User access context with permissions
        
    Returns:
        The created entity definition
        
    Raises:
        HTTPException: If validation fails or entity already exists"""
    try:
        result = await entity_service.create_entity_definition(
            entity=entity,
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/definitions", response_model=List[EntityDefinition])
@rate_limit(rate_limit=100, window=60)  # 100 reads per minute
async def list_entity_definitions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    context: RunningContext = Depends(entity_read_permission)
):
    """List all entity definitions for tenant
    
    Args:
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        context: User access context with permissions
        
    Returns:
        List of entity definitions"""
    return await entity_service.list_entity_definitions(
        current_user=context.current_user,
        tenant_id=context.current_user.tenant_id
    )

@router.get("/definitions/{name}", response_model=EntityDefinition)
@rate_limit(rate_limit=100, window=60)  # 100 reads per minute
async def get_entity_definition(
    name: str,
    context: RunningContext = Depends(entity_read_permission)
):
    """Get a specific entity definition by name
    
    Args:
        name: The name of the entity definition to retrieve
        context: User access context with permissions
        
    Returns:
        The entity definition if found
        
    Raises:
        HTTPException: If entity definition not found"""
    entity = await entity_service.get_entity_definition(
        name=name,
        current_user=context.current_user,
        tenant_id=context.current_user.tenant_id
    )
    if not entity:
        raise HTTPException(status_code=404, detail="Entity definition not found")
    return entity

@router.put("/definitions/{name}", response_model=EntityDefinition)
@rate_limit(rate_limit=50, window=60)  # 50 updates per minute
async def update_entity_definition(
    name: str,
    update_data: EntityDefinitionUpdate,
    context: RunningContext = Depends(entity_write_permission)
):
    """Update an entity definition"""
    try:
        entity = await entity_service.update_entity_definition(
            name=name,
            update_data=update_data.dict(exclude_unset=True),
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        if not entity:
            raise HTTPException(status_code=404, detail="Entity definition not found")
        return entity
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/definitions/{name}", status_code=status.HTTP_204_NO_CONTENT)
@rate_limit(rate_limit=20, window=60)  # 20 deletes per minute
async def delete_entity_definition(
    name: str,
    context: RunningContext = Depends(entity_admin_permission)
):
    """Delete an entity definition
    
    Args:
        name: The name of the entity definition to delete
        context: User access context with permissions
        
    Returns:
        204 No Content on success
        
    Raises:
        HTTPException: If entity definition not found or cannot be deleted"""
    try:
        success = await entity_service.delete_entity_definition(
            name=name,
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        if not success:
            raise HTTPException(status_code=404, detail="Entity definition not found")
        return {"message": "Entity definition deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/instances/{entity_type}", response_model=EntityInstance, status_code=status.HTTP_201_CREATED)
@rate_limit(rate_limit=50, window=60)  # 50 creates per minute
async def create_entity_instance(
    entity_type: str,
    data: Dict[str, Any],
    context: RunningContext = Depends(entity_write_permission)
):
    """Create a new entity instance
    
    Args:
        entity_type: The type of entity to create
        data: The entity instance data
        context: User access context with permissions
        
    Returns:
        The created entity instance
        
    Raises:
        HTTPException: If validation fails or entity type not found"""
    try:
        instance = await entity_service.create_entity_instance(
            entity_type=entity_type,
            data=data,
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        return instance
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/instances/{entity_type}", response_model=List[EntityInstance])
@rate_limit(rate_limit=100, window=60)  # 100 reads per minute
async def list_entity_instances(
    entity_type: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    context: RunningContext = Depends(entity_read_permission)
):
    """List entity instances of a specific type
    
    Args:
        entity_type: The type of entities to list
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        context: User access context with permissions
        
    Returns:
        List of entity instances"""
    return await entity_service.list_entity_instances(
        entity_type=entity_type,
        current_user=context.current_user,
        tenant_id=context.current_user.tenant_id
    )

@router.get("/instances/{entity_type}/{instance_id}", response_model=EntityInstance)
@rate_limit(rate_limit=100, window=60)  # 100 reads per minute
async def get_entity_instance(
    entity_type: str,
    instance_id: UUID,
    context: RunningContext = Depends(entity_read_permission)
):
    """Get a specific entity instance
    
    Args:
        entity_type: The type of entity
        instance_id: The UUID of the instance to retrieve
        context: User access context with permissions
        
    Returns:
        The entity instance if found
        
    Raises:
        HTTPException: If instance not found"""
    instance = await entity_service.get_entity_instance(
        instance_id=instance_id,
        current_user=context.current_user,
        tenant_id=context.current_user.tenant_id
    )
    if not instance:
        raise HTTPException(status_code=404, detail="Entity instance not found")
    return instance

@router.put("/instances/{entity_type}/{instance_id}")
async def update_entity_instance(
    entity_type: str,
    instance_id: UUID,
    data: EntityInstanceUpdate,
    context: RunningContext = Depends(entity_write_permission)
):
    """Update an entity instance"""
    try:
        instance = await entity_service.update_entity_instance(
            instance_id=instance_id,
            update_data=data.dict(exclude_unset=True),
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        if not instance:
            raise HTTPException(status_code=404, detail="Entity instance not found")
        return instance
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/instances/{entity_type}/{instance_id}", status_code=status.HTTP_204_NO_CONTENT)
@rate_limit(rate_limit=20, window=60)  # 20 deletes per minute
async def delete_entity_instance(
    entity_type: str,
    instance_id: UUID,
    context: RunningContext = Depends(entity_admin_permission)
):
    """Delete an entity instance"""
    try:
        success = await entity_service.delete_entity_instance(
            instance_id=instance_id,
            current_user=context.current_user,
            tenant_id=context.current_user.tenant_id
        )
        if not success:
            raise HTTPException(status_code=404, detail="Entity instance not found")
        return {"message": "Entity instance deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))