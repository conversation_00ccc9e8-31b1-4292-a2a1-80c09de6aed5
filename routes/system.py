from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
from datetime import datetime, timedelta, timezone
from pydantic import BaseModel, Field
import logging
import os
import inspect
# JWT functionality now handled by security.jwt_manager
from passlib.context import CryptContext

# Core imports
from config import settings
from security.jwt_manager import jwt_manager
from data_access.weaviatedb import (
    upsert_schemas,
    get_all_collections,
    reset_schemas,
    reset_collection_schema,
    WeaviateRepository,
    get_client,
    ensure_client_connected
)
from data_access.factory import RepositoryFactory
from models.registry import model_registry
from middleware.permission import PermissionChecker, requires_system_admin
from models.role import Permission, Role
from models.user import User
from models.tenant import Tenant
from session.running_context import RunningContext
from utils.test_auth import create_bypass_context, is_development_mode

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# ===============================
# PYDANTIC MODELS
# ===============================

class CollectionInfo(BaseModel):
    """Collection information model"""
    name: str = Field(..., description="Collection name")
    description: str = Field(..., description="Collection description")
    document_count: int = Field(..., description="Number of documents in collection")
    indexes: List[str] = Field(default_factory=list, description="Available indexes")
    properties: List[Dict[str, Any]] = Field(default_factory=list, description="Collection properties")
    created_at: Optional[datetime] = Field(None, description="Collection creation time")
    updated_at: Optional[datetime] = Field(None, description="Last update time")

class DocumentResponse(BaseModel):
    """Document response model"""
    id: str = Field(..., description="Document ID")
    properties: Dict[str, Any] = Field(..., description="Document properties")
    created_at: Optional[datetime] = Field(None, description="Document creation time")
    updated_at: Optional[datetime] = Field(None, description="Last update time")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class SchemaInfo(BaseModel):
    """Schema information model"""
    model_name: str = Field(..., description="Pydantic model name")
    collection_name: str = Field(..., description="Database collection name")
    fields: Dict[str, Any] = Field(..., description="Model fields and types")
    description: Optional[str] = Field(None, description="Model description")
    version: str = Field(default="1.0", description="Schema version")

class TestUserRequest(BaseModel):
    """Test user creation request"""
    email: Optional[str] = Field(None, description="User email (auto-generated if not provided)")
    tenant_id: Optional[str] = Field(None, description="Tenant ID (auto-generated if not provided)")
    role: str = Field(default="member", description="User role (system_admin, tenant_admin, manager, member)")
    fixed_uuid: bool = Field(default=True, description="Use predictable UUID for testing")

class TestTokenRequest(BaseModel):
    """Test token generation request"""
    user_id: Optional[str] = Field(None, description="User ID (auto-generated if not provided)")
    tenant_id: Optional[str] = Field(None, description="Tenant ID (auto-generated if not provided)")
    role: str = Field(default="member", description="User role")
    permissions: Optional[List[str]] = Field(None, description="Custom permissions (overrides role)")
    expires_in_hours: int = Field(default=24, ge=1, le=8760, description="Token expiration in hours")
    permanent: bool = Field(default=False, description="Create non-expiring token")

class TestFixturesResponse(BaseModel):
    """Test fixtures response model"""
    users: Dict[str, Dict[str, Any]] = Field(..., description="Test users with credentials")
    tenants: Dict[str, Dict[str, Any]] = Field(..., description="Test tenants")
    tokens: Dict[str, str] = Field(..., description="Pre-generated tokens")
    endpoints: Dict[str, str] = Field(..., description="Useful API endpoints")

class SystemStatusResponse(BaseModel):
    """System status response model"""
    environment: str = Field(..., description="Current environment")
    development_mode: bool = Field(..., description="Development mode status")
    database_connected: bool = Field(..., description="Database connection status")
    collections_count: int = Field(..., description="Number of collections")
    version: str = Field(default="1.0", description="API version")
    uptime: Optional[str] = Field(None, description="System uptime")

# ===============================
# ROUTER CONFIGURATION
# ===============================

router = APIRouter(
    prefix="/system/v1",
    tags=["System Administration"],
    responses={
        404: {"description": "Resource not found"},
        403: {"description": "Insufficient permissions"},
        500: {"description": "Internal server error"}
    }
)

# ===============================
# SECURITY AND ENVIRONMENT CHECKS
# ===============================

def is_development_environment() -> bool:
    """Check if running in development environment"""
    env = os.getenv("ENVIRONMENT", "production").lower()
    return env in ["development", "dev", "local", "test"]

def require_development_mode():
    """Dependency to ensure endpoint is only available in development"""
    if not is_development_environment():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="System administration endpoints are only available in development mode"
        )

# System-level permission checkers
system_admin_permission = PermissionChecker([Permission.SYSTEM_ADMIN])
system_view_permission = PermissionChecker([Permission.SYSTEM_VIEW_ANALYTICS])

# Development bypass context helper
async def get_bypass_context(tenant_id: Optional[str] = None) -> RunningContext:
    """Create bypass context for development endpoints"""
    require_development_mode()
    try:
        return create_bypass_context(UUID(tenant_id) if tenant_id else None)
    except Exception as e:
        logger.error(f"Failed to create bypass context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create development context"
        )

# ===============================
# CORE DATABASE MANAGEMENT
# ===============================

@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """
    Get comprehensive system status information

    Returns system health, environment info, and basic statistics.
    Available in all environments for monitoring purposes.
    """
    try:
        # Check database connection
        db_connected = True
        collections_count = 0
        try:
            collections = await get_all_collections()
            collections_count = len(collections)
        except Exception:
            db_connected = False

        return SystemStatusResponse(
            environment=os.getenv("ENVIRONMENT", "unknown"),
            development_mode=is_development_environment(),
            database_connected=db_connected,
            collections_count=collections_count,
            version="1.0",
            uptime=None  # Could be implemented with startup time tracking
        )
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system status"
        )

@router.get("/collections", response_model=List[CollectionInfo])
async def list_collections(
    context: RunningContext = Depends(system_view_permission)
):
    """
    List all database collections with comprehensive metadata

    Returns detailed information about each collection including:
    - Document count
    - Available indexes
    - Schema properties
    - Creation/update timestamps
    """
    try:
        collections = await get_all_collections()
        result = []

        for collection_data in collections:
            # Get additional metadata for each collection
            collection_name = collection_data.get("name", "unknown")

            try:
                # Get document count
                client = get_client()
                await ensure_client_connected()
                collection_obj = client.collections.get(collection_name)
                count_result = await collection_obj.aggregate.over_all(total_count=True)
                doc_count = count_result.total_count if count_result else 0

                # Get collection properties
                properties = []
                if hasattr(collection_obj, 'config') and hasattr(collection_obj.config, 'properties'):
                    properties = [
                        {
                            "name": prop_name,
                            "type": str(prop_config.data_type) if hasattr(prop_config, 'data_type') else "unknown",
                            "description": getattr(prop_config, 'description', '')
                        }
                        for prop_name, prop_config in collection_obj.config.properties.items()
                    ]

                result.append(CollectionInfo(
                    name=collection_name,
                    description=collection_data.get("description", f"Collection for {collection_name} data"),
                    document_count=doc_count,
                    indexes=[],  # Could be enhanced to show actual indexes
                    properties=properties,
                    created_at=None,  # Could be tracked in metadata
                    updated_at=None
                ))

            except Exception as e:
                logger.warning(f"Failed to get metadata for collection {collection_name}: {e}")
                # Return basic info even if metadata retrieval fails
                result.append(CollectionInfo(
                    name=collection_name,
                    description=collection_data.get("description", f"Collection for {collection_name} data"),
                    document_count=0,
                    indexes=[],
                    properties=[]
                ))

        return result

    except Exception as e:
        logger.error(f"Failed to list collections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve collections: {str(e)}"
        )

@router.get("/collections/{collection_name}/data", response_model=List[DocumentResponse])
async def get_collection_data(
    collection_name: str,
    skip: int = Query(0, ge=0, description="Number of documents to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of documents to return"),
    search: Optional[str] = Query(None, description="Search query to filter documents"),
    context: RunningContext = Depends(system_view_permission)
):
    """
    Retrieve documents from a specific collection with pagination and filtering

    Supports:
    - Pagination via skip/limit parameters
    - Text search across document properties
    - Structured data return with metadata
    """
    try:
        # Find the model type for this collection
        model_type = None
        for mt, cn in model_registry.items():
            if cn == collection_name:
                model_type = mt
                break

        if not model_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found in model registry"
            )

        # Create repository for the model type
        repo = await RepositoryFactory.create_repository(model_type)

        # Build query conditions for search
        query_conditions = {}
        if search:
            # This would need to be enhanced based on your search implementation
            query_conditions["search"] = search

        # Get data from collection
        objects = await repo.get_all(skip=skip, limit=limit, query_conditions=query_conditions)

        # Convert to response format
        result = []
        for obj in objects:
            doc_data = obj.model_dump()
            result.append(DocumentResponse(
                id=str(doc_data.get("id", "unknown")),
                properties=doc_data,
                created_at=doc_data.get("created_at"),
                updated_at=doc_data.get("updated_at"),
                metadata={
                    "collection": collection_name,
                    "model_type": model_type.__name__
                }
            ))

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get collection data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving collection data: {str(e)}"
        )

@router.get("/collections/{collection_name}/data/{document_id}", response_model=DocumentResponse)
async def get_document_by_id(
    collection_name: str,
    document_id: str,
    context: RunningContext = Depends(system_view_permission)
):
    """
    Get a specific document by ID from a collection

    Returns the complete document with all properties and metadata.
    """
    try:
        # Find the model type for this collection
        model_type = None
        for mt, cn in model_registry.items():
            if cn == collection_name:
                model_type = mt
                break

        if not model_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Collection '{collection_name}' not found in model registry"
            )

        # Create repository for the model type
        repo = await RepositoryFactory.create_repository(model_type)

        # Get document by ID
        try:
            document_uuid = UUID(document_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid document ID format: {document_id}"
            )

        obj = await repo.get_by_id(document_uuid)

        if not obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Document {document_id} not found in collection {collection_name}"
            )

        doc_data = obj.model_dump()
        return DocumentResponse(
            id=str(doc_data.get("id", document_id)),
            properties=doc_data,
            created_at=doc_data.get("created_at"),
            updated_at=doc_data.get("updated_at"),
            metadata={
                "collection": collection_name,
                "model_type": model_type.__name__
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get document: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving document: {str(e)}"
        )

# ===============================
# SCHEMA MANAGEMENT
# ===============================

@router.get("/schemas", response_model=List[SchemaInfo])
async def list_schemas(
    context: RunningContext = Depends(system_view_permission)
):
    """
    List all available Pydantic model schemas

    Returns comprehensive information about each registered model including:
    - Model name and collection mapping
    - Field definitions with types
    - Schema descriptions and versions
    """
    try:
        schemas = []

        for model_type, collection_name in model_registry.items():
            # Get model fields information
            fields = {}
            if hasattr(model_type, 'model_fields'):
                for field_name, field_info in model_type.model_fields.items():
                    field_type = "unknown"
                    if hasattr(field_info, 'annotation'):
                        field_type = str(field_info.annotation)
                    elif hasattr(field_info, 'type_'):
                        field_type = str(field_info.type_)

                    fields[field_name] = {
                        "type": field_type,
                        "required": getattr(field_info, 'is_required', lambda: False)(),
                        "description": getattr(field_info, 'description', ''),
                        "default": getattr(field_info, 'default', None)
                    }

            schemas.append(SchemaInfo(
                model_name=model_type.__name__,
                collection_name=collection_name,
                fields=fields,
                description=getattr(model_type, '__doc__', ''),
                version="1.0"
            ))

        return schemas

    except Exception as e:
        logger.error(f"Failed to list schemas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve schemas: {str(e)}"
        )

@router.get("/schemas/{model_name}", response_model=SchemaInfo)
async def get_schema_details(
    model_name: str,
    context: RunningContext = Depends(system_view_permission)
):
    """
    Get detailed schema definition for a specific model

    Returns comprehensive field information, validation rules,
    and relationship mappings for the specified model.
    """
    try:
        # Find the model by name
        target_model = None
        target_collection = None

        for model_type, collection_name in model_registry.items():
            if model_type.__name__ == model_name:
                target_model = model_type
                target_collection = collection_name
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model '{model_name}' not found in registry"
            )

        # Get detailed field information
        fields = {}
        if hasattr(target_model, 'model_fields'):
            for field_name, field_info in target_model.model_fields.items():
                field_details = {
                    "type": str(getattr(field_info, 'annotation', 'unknown')),
                    "required": getattr(field_info, 'is_required', lambda: False)(),
                    "description": getattr(field_info, 'description', ''),
                    "default": getattr(field_info, 'default', None),
                    "constraints": {}
                }

                # Add validation constraints if available
                if hasattr(field_info, 'constraints'):
                    field_details["constraints"] = field_info.constraints

                fields[field_name] = field_details

        # Get model schema as JSON schema for additional details
        json_schema = {}
        try:
            if hasattr(target_model, 'model_json_schema'):
                json_schema = target_model.model_json_schema()
        except Exception as e:
            logger.warning(f"Failed to get JSON schema for {model_name}: {e}")

        return SchemaInfo(
            model_name=model_name,
            collection_name=target_collection,
            fields=fields,
            description=getattr(target_model, '__doc__', ''),
            version="1.0"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get schema details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving schema details: {str(e)}"
        )

@router.post("/schemas/{model_name}/init", response_model=Dict[str, str])
async def initialize_schema(
    model_name: str,
    context: RunningContext = Depends(system_admin_permission)
):
    """
    Initialize/create database collection from Pydantic model

    Creates the database collection with proper schema definition
    based on the specified Pydantic model. Safe to run multiple times.
    """
    try:
        # Find the model by name
        target_model = None
        target_collection = None

        for model_type, collection_name in model_registry.items():
            if model_type.__name__ == model_name:
                target_model = model_type
                target_collection = collection_name
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model '{model_name}' not found in registry"
            )

        # Initialize the specific schema
        await upsert_schemas()  # This will create/update all schemas including the target

        return {
            "status": f"Schema for model '{model_name}' initialized successfully",
            "collection": target_collection,
            "model": model_name
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to initialize schema: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize schema: {str(e)}"
        )

@router.post("/schemas/{model_name}/reset", response_model=Dict[str, str])
async def reset_schema(
    model_name: str,
    context: RunningContext = Depends(system_admin_permission)
):
    """
    Reset collection schema (drop and recreate from updated model)

    WARNING: This will delete all data in the collection and recreate
    the schema from the current model definition. Use with caution!
    """
    try:
        # Find the model by name
        target_model = None
        target_collection = None

        for model_type, collection_name in model_registry.items():
            if model_type.__name__ == model_name:
                target_model = model_type
                target_collection = collection_name
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model '{model_name}' not found in registry"
            )

        # Reset the specific collection schema
        await reset_collection_schema(target_collection)

        return {
            "status": f"Schema for model '{model_name}' reset successfully",
            "collection": target_collection,
            "model": model_name,
            "warning": "All data in the collection has been deleted"
        }

    except HTTPException:
        raise
    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Failed to reset schema: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset schema: {str(e)}"
        )

# ===============================
# TESTING INFRASTRUCTURE
# ===============================

@router.post("/test/init-users", response_model=Dict[str, Any])
async def create_test_users(
    _: None = Depends(require_development_mode)
):
    """
    Create fixed test users with predictable UUIDs for consistent testing

    Creates a set of test users with known IDs and credentials:
    - System Administrator (full system access)
    - Tenant Administrator (tenant-level access)
    - Manager (team management access)
    - Member (basic user access)

    All users have predictable UUIDs for reliable testing.
    """
    try:
        context = await get_bypass_context()

        # Define test users with fixed UUIDs
        test_users = {
            "system_admin": {
                "id": "********-0000-0000-0000-********0001",
                "email": "<EMAIL>",
                "role": "SystemAdmin",
                "tenant_id": "********-0000-0000-0000-********0001"
            },
            "tenant_admin": {
                "id": "********-0000-0000-0000-********0002",
                "email": "<EMAIL>",
                "role": "TenantAdmin",
                "tenant_id": "********-0000-0000-0000-********0002"
            },
            "manager": {
                "id": "********-0000-0000-0000-********0003",
                "email": "<EMAIL>",
                "role": "Manager",
                "tenant_id": "********-0000-0000-0000-********0002"
            },
            "member": {
                "id": "********-0000-0000-0000-********0004",
                "email": "<EMAIL>",
                "role": "Member",
                "tenant_id": "********-0000-0000-0000-********0002"
            }
        }

        created_users = {}
        user_repo = await RepositoryFactory.create_user_repository()
        role_repo = await RepositoryFactory.create_role_repository()

        for user_key, user_data in test_users.items():
            try:
                # Check if user already exists
                existing_user = await user_repo.get_by_id(UUID(user_data["id"]))
                if existing_user:
                    created_users[user_key] = {
                        "id": user_data["id"],
                        "email": user_data["email"],
                        "status": "already_exists"
                    }
                    continue

                # Get or create appropriate role
                role_name = user_data["role"]
                roles = await role_repo.get_all(0, 1, {"name": role_name})

                if not roles:
                    # Create role if it doesn't exist
                    role_permissions = _get_role_permissions(role_name)
                    role = Role(
                        id=uuid4(),
                        name=role_name,
                        description=f"Test {role_name} role",
                        permissions=set(role_permissions),
                        tenant_id=UUID(user_data["tenant_id"]) if role_name != "SystemAdmin" else None
                    )
                    role = await role_repo.create(role)
                else:
                    role = roles[0]

                # Create user with hashed password
                hashed_password = pwd_context.hash("TestPassword123!")
                user = User(
                    id=UUID(user_data["id"]),
                    email=user_data["email"],
                    username=user_data["email"].split("@")[0],
                    hashed_password=hashed_password,
                    role_id=role.id,
                    tenant_id=UUID(user_data["tenant_id"]),
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                created_user = await user_repo.create(user)
                created_users[user_key] = {
                    "id": str(created_user.id),
                    "email": created_user.email,
                    "role": role_name,
                    "tenant_id": str(created_user.tenant_id),
                    "status": "created"
                }

            except Exception as e:
                logger.error(f"Failed to create test user {user_key}: {e}")
                created_users[user_key] = {
                    "id": user_data["id"],
                    "email": user_data["email"],
                    "status": "failed",
                    "error": str(e)
                }

        return {
            "message": "Test users initialization completed",
            "users": created_users,
            "note": "Use these fixed IDs for consistent testing"
        }

    except Exception as e:
        logger.error(f"Failed to create test users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create test users: {str(e)}"
        )

def _get_role_permissions(role_name: str) -> List[str]:
    """Get permissions for a given role"""
    if role_name == "SystemAdmin":
        return [perm.value for perm in Permission]
    elif role_name == "TenantAdmin":
        return [perm.value for perm in Permission if not perm.value.startswith("system:")]
    elif role_name == "Manager":
        return [
            "create_agent", "read_agent", "update_agent", "delete_agent", "execute_agent",
            "create_resource", "read_resource", "update_resource", "delete_resource", "search_resource",
            "create_task", "read_task", "update_task", "delete_task", "manage_tasks", "read_tasks",
            "view_tenant_analytics", "manage_team", "invite_member", "remove_member"
        ]
    else:  # Member
        return ["read_agent", "execute_agent", "read_resource", "search_resource", "read_task", "read_tasks"]

@router.post("/test/init-tenants", response_model=Dict[str, Any])
async def create_test_tenants(
    _: None = Depends(require_development_mode)
):
    """
    Create fixed test tenants with predictable UUIDs

    Creates standard test tenants:
    - System Platform Tenant (for system admin)
    - Test Customer Tenant (for regular testing)
    - Demo Tenant (for demonstrations)
    """
    try:
        context = await get_bypass_context()

        # Define test tenants with fixed UUIDs
        test_tenants = {
            "system_platform": {
                "id": "********-0000-0000-0000-********0001",
                "name": "Assivy System Platform",
                "description": "System administration tenant",
                "type": "system"
            },
            "test_customer": {
                "id": "********-0000-0000-0000-********0002",
                "name": "Test Customer Organization",
                "description": "Standard test customer tenant",
                "type": "customer"
            },
            "demo_tenant": {
                "id": "********-0000-0000-0000-********0003",
                "name": "Demo Organization",
                "description": "Demonstration tenant for showcasing features",
                "type": "demo"
            }
        }

        created_tenants = {}
        tenant_repo = await RepositoryFactory.create_tenant_repository()

        for tenant_key, tenant_data in test_tenants.items():
            try:
                # Check if tenant already exists
                existing_tenant = await tenant_repo.get_by_id(UUID(tenant_data["id"]))
                if existing_tenant:
                    created_tenants[tenant_key] = {
                        "id": tenant_data["id"],
                        "name": tenant_data["name"],
                        "status": "already_exists"
                    }
                    continue

                # Create tenant
                tenant = Tenant(
                    id=UUID(tenant_data["id"]),
                    name=tenant_data["name"],
                    description=tenant_data["description"],
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                created_tenant = await tenant_repo.create(tenant)
                created_tenants[tenant_key] = {
                    "id": str(created_tenant.id),
                    "name": created_tenant.name,
                    "description": created_tenant.description,
                    "status": "created"
                }

            except Exception as e:
                logger.error(f"Failed to create test tenant {tenant_key}: {e}")
                created_tenants[tenant_key] = {
                    "id": tenant_data["id"],
                    "name": tenant_data["name"],
                    "status": "failed",
                    "error": str(e)
                }

        return {
            "message": "Test tenants initialization completed",
            "tenants": created_tenants,
            "note": "Use these fixed IDs for consistent testing"
        }

    except Exception as e:
        logger.error(f"Failed to create test tenants: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create test tenants: {str(e)}"
        )

@router.get("/test/fixtures", response_model=TestFixturesResponse)
async def get_test_fixtures(
    _: None = Depends(require_development_mode)
):
    """
    Return all test fixture IDs and credentials for documentation/AI tools

    Provides a comprehensive reference of all test data including:
    - User accounts with credentials
    - Tenant information
    - Pre-generated tokens
    - Useful API endpoints for testing
    """
    try:
        # Standard test users
        test_users = {
            "system_admin": {
                "id": "********-0000-0000-0000-********0001",
                "email": "<EMAIL>",
                "role": "SystemAdmin",
                "tenant_id": "********-0000-0000-0000-********0001",
                "password": "TestPassword123!",
                "description": "Full system administrator access"
            },
            "tenant_admin": {
                "id": "********-0000-0000-0000-********0002",
                "email": "<EMAIL>",
                "role": "TenantAdmin",
                "tenant_id": "********-0000-0000-0000-********0002",
                "password": "TestPassword123!",
                "description": "Tenant-level administrator"
            },
            "manager": {
                "id": "********-0000-0000-0000-********0003",
                "email": "<EMAIL>",
                "role": "Manager",
                "tenant_id": "********-0000-0000-0000-********0002",
                "password": "TestPassword123!",
                "description": "Team manager with resource access"
            },
            "member": {
                "id": "********-0000-0000-0000-********0004",
                "email": "<EMAIL>",
                "role": "Member",
                "tenant_id": "********-0000-0000-0000-********0002",
                "password": "TestPassword123!",
                "description": "Basic user with limited access"
            }
        }

        # Standard test tenants
        test_tenants = {
            "system_platform": {
                "id": "********-0000-0000-0000-********0001",
                "name": "Assivy System Platform",
                "description": "System administration tenant"
            },
            "test_customer": {
                "id": "********-0000-0000-0000-********0002",
                "name": "Test Customer Organization",
                "description": "Standard test customer tenant"
            },
            "demo_tenant": {
                "id": "********-0000-0000-0000-********0003",
                "name": "Demo Organization",
                "description": "Demonstration tenant"
            }
        }

        # Pre-generated tokens (these would be generated dynamically in real implementation)
        test_tokens = {
            "system_admin_token": "Use POST /system/v1/test/tokens/permanent with system_admin user_id",
            "tenant_admin_token": "Use POST /system/v1/test/tokens/permanent with tenant_admin user_id",
            "manager_token": "Use POST /system/v1/test/tokens/permanent with manager user_id",
            "member_token": "Use POST /system/v1/test/tokens/permanent with member user_id"
        }

        # Useful API endpoints for testing
        useful_endpoints = {
            "auth_login": "POST /auth/login",
            "create_test_users": "POST /system/v1/test/init-users",
            "create_test_tenants": "POST /system/v1/test/init-tenants",
            "generate_token": "POST /system/v1/test/tokens/permanent",
            "list_collections": "GET /system/v1/collections",
            "list_schemas": "GET /system/v1/schemas",
            "system_status": "GET /system/v1/status"
        }

        return TestFixturesResponse(
            users=test_users,
            tenants=test_tenants,
            tokens=test_tokens,
            endpoints=useful_endpoints
        )

    except Exception as e:
        logger.error(f"Failed to get test fixtures: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve test fixtures: {str(e)}"
        )

# ===============================
# AUTHENTICATION TESTING
# ===============================

@router.post("/test/tokens/permanent", response_model=Dict[str, Any])
async def generate_permanent_token(
    request: TestTokenRequest,
    _: None = Depends(require_development_mode)
):
    """
    Generate non-expiring JWT tokens for testing

    Creates JWT tokens with extended or permanent expiration for testing purposes.
    Supports custom user IDs, tenant IDs, roles, and permissions.

    WARNING: Only available in development mode. Tokens should not be used in production.
    """
    try:
        # Use provided IDs or generate new ones
        user_id = request.user_id or str(uuid4())
        tenant_id = request.tenant_id or str(uuid4())

        # Determine permissions based on role or custom permissions
        if request.permissions:
            permissions = request.permissions
        else:
            permissions = _get_role_permissions(request.role)

        # Calculate expiration
        if request.permanent:
            # Set expiration far in the future (100 years)
            exp_time = datetime.now(timezone.utc) + timedelta(days=36500)
        else:
            exp_time = datetime.now(timezone.utc) + timedelta(hours=request.expires_in_hours)

        # Generate secure token using JWT manager
        token = jwt_manager.create_access_token(
            user_id=user_id,
            email=f"test.user.{user_id[:8]}@test.assivy.com",
            tenant_id=tenant_id,
            permissions=permissions,
            role=request.role,
            expires_delta=exp_time - datetime.now(timezone.utc),
            additional_claims={"test_token": True}  # Mark as test token
        )

        return {
            "token": token,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "role": request.role,
            "permissions": permissions,
            "expires_at": exp_time.isoformat() if not request.permanent else "never",
            "permanent": request.permanent,
            "warning": "This is a test token - do not use in production"
        }

    except Exception as e:
        logger.error(f"Failed to generate test token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate test token: {str(e)}"
        )

@router.post("/test/bypass-permissions", response_model=Dict[str, str])
async def bypass_permissions_for_testing(
    duration_minutes: int = Query(60, ge=1, le=1440, description="Duration in minutes"),
    _: None = Depends(require_development_mode)
):
    """
    Temporarily disable permission checks for testing

    Creates a temporary bypass context that disables permission validation
    for the specified duration. Useful for automated testing scenarios.

    WARNING: Only available in development mode. Use with extreme caution.
    """
    try:
        # This would need to be implemented in your permission system
        # For now, return information about how to use bypass context

        return {
            "status": "Permission bypass information provided",
            "message": f"Use create_bypass_context() in your test code for {duration_minutes} minutes",
            "warning": "Permission bypass is only available in development mode",
            "note": "Implement actual bypass logic in your permission middleware"
        }

    except Exception as e:
        logger.error(f"Failed to setup permission bypass: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to setup permission bypass: {str(e)}"
        )

# ===============================
# DATABASE SYNCHRONIZATION
# ===============================

@router.post("/db/sync", response_model=Dict[str, str])
async def sync_database_schemas(
    context: RunningContext = Depends(system_admin_permission)
):
    """
    Synchronize database schemas with model registry

    Updates all database collections to match the current Pydantic model definitions.
    Safe to run multiple times - will only update schemas that have changed.
    """
    try:
        await upsert_schemas()
        return {
            "status": "Database schemas synchronized successfully",
            "message": "All collections updated to match current model definitions"
        }
    except Exception as e:
        logger.error(f"Failed to sync database schemas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync database schemas: {str(e)}"
        )

@router.post("/db/reset", response_model=Dict[str, str])
async def reset_all_database_schemas(
    context: RunningContext = Depends(system_admin_permission)
):
    """
    Reset all database schemas (WARNING: This will delete all collections and recreate them)

    Completely drops and recreates all database collections from scratch.
    This will permanently delete ALL data in the database.

    Use with extreme caution - only for development/testing purposes.
    """
    try:
        await reset_schemas()
        return {
            "status": "Database schemas reset successfully",
            "warning": "All data has been permanently deleted",
            "message": "All collections recreated from current model definitions"
        }
    except Exception as e:
        logger.error(f"Failed to reset database schemas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset database schemas: {str(e)}"
        )

@router.get("/db/collections/{collection_name}/data/{object_id}", response_model=Dict[str, Any])
async def get_collection_object(
    collection_name: str,
    object_id: UUID,
    context: RunningContext = Depends(system_view_permission)
):
    """Get a specific object from a collection by ID
    
    Args:
        collection_name: Name of the collection to query
        object_id: UUID of the object to retrieve
        context: User access context with permissions
        
    Returns:
        The requested object if found
        
    Raises:
        HTTPException: If collection/object not found or error occurs
    """
    try:
        # Find the model type for this collection
        model_type = None
        for mt, cn in model_registry.items():
            if cn == collection_name:
                model_type = mt
                break
                
        if not model_type:
            raise HTTPException(
                status_code=404,
                detail=f"Collection {collection_name} not found"
            )
            
        # Create repository for the model type
        repo = await RepositoryFactory.create_repository(model_type)
        
        # Get object by ID
        obj = await repo.get_by_id(object_id)
        
        if not obj:
            raise HTTPException(
                status_code=404,
                detail=f"Object {object_id} not found in collection {collection_name}"
            )
            
        return obj.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting object: {str(e)}"
        )

@router.patch("/db/collections/{collection_name}/data/{object_id}", response_model=Dict[str, Any])
async def update_collection_object(
    collection_name: str,
    object_id: UUID,
    property_name: str = Query(..., description="Name of the property to update"),
    property_value: str = Query(..., description="New value for the property")   
):
    """Update a specific property of an object in a collection by ID
    
    Args:
        collection_name: Name of the collection to query
        object_id: UUID of the object to update
        property_name: Name of the property to update (query parameter)
        property_value: New value for the property (query parameter)
        context: User access context with permissions
        
    Returns:
        The updated object
        
    Raises:
        HTTPException: If collection/object not found or update fails
    """
    try:
        # Find the model type for this collection
        model_type = None
        for mt, cn in model_registry.items():
            if cn == collection_name:
                model_type = mt
                break
                
        if not model_type:
            raise HTTPException(
                status_code=404,
                detail=f"Collection {collection_name} not found"
            )
            
        # Create repository for the model type
        repo = await RepositoryFactory.create_repository(model_type)
        
        # Get object by ID
        obj = await repo.get_by_id(object_id)
        
        if not obj:
            raise HTTPException(
                status_code=404,
                detail=f"Object {object_id} not found in collection {collection_name}"
            )
        
        # Check if the property exists on the object
        if not hasattr(obj, property_name):
            raise HTTPException(
                status_code=400,
                detail=f"Property '{property_name}' does not exist on object type {model_type.__name__}"
            )
        
        # Try to convert property_value to the appropriate type based on the property
        try:
            # Get the property type from the model
            import inspect
            field_info = model_type.model_fields.get(property_name)
            if field_info:
                # Try to convert the value to the expected type
                if field_info.annotation == bool:
                    property_value = property_value.lower() in ('true', '1', 'yes', 'on')
                elif field_info.annotation == int:
                    property_value = int(property_value)
                elif field_info.annotation == float:
                    property_value = float(property_value)
                elif field_info.annotation == UUID:
                    property_value = UUID(property_value)
                # For strings and other types, keep as is
        except (ValueError, TypeError) as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid value '{property_value}' for property '{property_name}': {str(e)}"
            )
        
        # Save the updated object
        updated_obj = await repo.update(object_id, {property_name: property_value})
        
        return updated_obj.model_dump()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error updating object: {str(e)}"
        )

@router.post("/db/collections/{collection_name}/reset", response_model=Dict[str, str])
async def reset_collection(
    collection_name: str,
    context: RunningContext = Depends(system_admin_permission)
):
    """Reset a specific collection schema
    
    WARNING: This will delete all data in the collection and recreate its schema.
    
    Args:
        collection_name: Name of the collection to reset
        
    Returns:
        Status message
        
    Raises:
        HTTPException: If collection not found or error occurs
    """
    try:
        await reset_collection_schema(collection_name)
        return {"status": f"Collection '{collection_name}' schema reset successfully"}
    except ValueError as ve:
        raise HTTPException(
            status_code=404,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset collection schema: {str(e)}"
        )

# ===============================
# DEVELOPMENT TESTING ENDPOINTS
# ===============================
# These endpoints are only available in development mode and provide
# role-based tokens for testing instead of superuser tokens

@router.get("/dev/status")
async def dev_status():
    """Get development mode status"""
    return {
        "development_mode": is_development_mode(),
        "environment": os.getenv("ENVIRONMENT", "unknown")
    }

@router.post("/dev/token/system-admin")
async def generate_system_admin_token(tenant_id: Optional[str] = None):
    """Generate a system admin JWT token for testing (development only)"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        from utils.test_auth import create_test_superuser
        from models.role import Permission
        from data_access.factory import RepositoryFactory
        
        # Create test user
        user = create_test_superuser(UUID(tenant_id) if tenant_id else uuid4())
        user.email = "<EMAIL>"
        
        # Ensure user exists in database
        user_repo = await RepositoryFactory.create_user_repository()
        role_repo = await RepositoryFactory.create_role_repository()
        
        # Get or create a system admin role
        from models.role import Role
        system_admin_role_id = Role._generate_role_id("SystemAdmin")
        
        # Try to get the predefined SystemAdmin role
        try:
            system_role = await role_repo.get_by_id(system_admin_role_id)
        except:
            system_role = None
            
        if not system_role:
            # Create the predefined SystemAdmin role
            system_role = Role(
                id=system_admin_role_id,
                name="SystemAdmin",
                description="Platform administrator with full system access",
                permissions=set(Permission),  # All permissions including system ones
                tenant_id=None
            )
            system_role = await role_repo.create(system_role)
        
        user.role_id = system_role.id
        
        # Check if user already exists
        existing_user = await user_repo.get_all(0, 1, {"email": user.email})
        if existing_user:
            user = existing_user[0]
            # Update user's role_id if it's different
            if user.role_id != system_role.id:
                user.role_id = system_role.id
                user = await user_repo.update(user.id, user)
        else:
            # Create the user in database
            user = await user_repo.create(user)
        
        # Create token with system admin permissions using secure JWT manager
        token = jwt_manager.create_access_token(
            user_id=str(user.id),
            email=user.email,
            tenant_id=str(user.tenant_id),
            permissions=[perm.value for perm in Permission],  # All permissions
            role="SystemAdmin",
            expires_delta=timedelta(hours=1),
            additional_claims={"username": "SystemAdmin", "test_token": True}
        )
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": str(user.id),
                "email": user.email,
                "tenant_id": str(user.tenant_id),
                "role": "SystemAdmin",
                "permissions": [perm.value for perm in Permission]
            },
            "usage": {
                "curl_example": f'curl -H "Authorization: Bearer {token}" http://localhost:8000/api/resources/file',
                "header": f"Authorization: Bearer {token}"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate token: {str(e)}")

@router.post("/dev/token/tenant-admin")
async def generate_tenant_admin_token(tenant_id: Optional[str] = None):
    """Generate a tenant admin JWT token for testing (development only)"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        from utils.test_auth import create_test_superuser
        from models.role import Permission
        from data_access.factory import RepositoryFactory
        
        # Create test user
        user = create_test_superuser(UUID(tenant_id) if tenant_id else uuid4())
        user.email = "<EMAIL>"
        
        # Ensure user exists in database
        user_repo = await RepositoryFactory.create_user_repository()
        role_repo = await RepositoryFactory.create_role_repository()
        
        # Get or create a tenant admin role
        tenant_roles = await role_repo.get_all(0, 1, {"name": "TenantAdmin"})
        if tenant_roles:
            user.role_id = tenant_roles[0].id
        else:
            # Create a tenant admin role if none exists
            tenant_permissions = [perm for perm in Permission if not perm.value.startswith("system:")]
            tenant_role = Role(
                id=uuid4(),
                name="TenantAdmin",
                description="Tenant Administrator",
                permissions=set(tenant_permissions)
            )
            tenant_role = await role_repo.create(tenant_role)
            user.role_id = tenant_role.id
        
        # Check if user already exists
        existing_user = await user_repo.get_all(0, 1, {"email": user.email})
        if existing_user:
            user = existing_user[0]
        else:
            # Create the user in database
            user = await user_repo.create(user)
        
        # Create token with tenant admin permissions (all except system permissions)
        tenant_permissions = [perm.value for perm in Permission if not perm.value.startswith("system:")]
        token = jwt_manager.create_access_token(
            user_id=str(user.id),
            email=user.email,
            tenant_id=str(user.tenant_id),
            permissions=tenant_permissions,
            role="TenantAdmin",
            expires_delta=timedelta(hours=1),
            additional_claims={"username": "TenantAdmin", "test_token": True}
        )
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": str(user.id),
                "email": user.email,
                "tenant_id": str(user.tenant_id),
                "role": "TenantAdmin",
                "permissions": tenant_permissions
            },
            "usage": {
                "curl_example": f'curl -H "Authorization: Bearer {token}" http://localhost:8000/api/resources/file',
                "header": f"Authorization: Bearer {token}"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate token: {str(e)}")

@router.post("/dev/token/manager")
async def generate_manager_token(tenant_id: Optional[str] = None):
    """Generate a manager JWT token for testing (development only)"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        from utils.test_auth import create_test_superuser
        from data_access.factory import RepositoryFactory
        
        # Create test user
        user = create_test_superuser(UUID(tenant_id) if tenant_id else uuid4())
        user.email = "<EMAIL>"
        
        # Ensure user exists in database
        user_repo = await RepositoryFactory.create_user_repository()
        role_repo = await RepositoryFactory.create_role_repository()
        
        # Get or create a manager role
        manager_roles = await role_repo.get_all(0, 1, {"name": "Manager"})
        if manager_roles:
            user.role_id = manager_roles[0].id
        else:
            # Create a manager role if none exists
            manager_permissions = [
                "create_agent", "read_agent", "update_agent", "delete_agent", "execute_agent",
                "create_resource", "read_resource", "update_resource", "delete_resource", "search_resource",
                "create_task", "read_task", "update_task", "delete_task", "manage_tasks", "read_tasks",
                "view_tenant_analytics", "manage_team", "invite_member", "remove_member"
            ]
            manager_role = Role(
                id=uuid4(),
                name="Manager",
                description="Team Manager",
                permissions=set(manager_permissions)
            )
            manager_role = await role_repo.create(manager_role)
            user.role_id = manager_role.id
        
        # Check if user already exists
        existing_user = await user_repo.get_all(0, 1, {"email": user.email})
        if existing_user:
            user = existing_user[0]
        else:
            # Create the user in database
            user = await user_repo.create(user)
        
        # Manager permissions from Role.get_predefined_roles()
        manager_permissions = [
            "create_agent", "read_agent", "update_agent", "delete_agent", "execute_agent",
            "create_resource", "read_resource", "update_resource", "delete_resource", "search_resource",
            "create_task", "read_task", "update_task", "delete_task", "manage_tasks", "read_tasks",
            "view_tenant_analytics", "manage_team", "invite_member", "remove_member"
        ]
        
        token = jwt_manager.create_access_token(
            user_id=str(user.id),
            email=user.email,
            tenant_id=str(user.tenant_id),
            permissions=manager_permissions,
            role="Manager",
            expires_delta=timedelta(hours=1),
            additional_claims={"username": "Manager", "test_token": True}
        )
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": str(user.id),
                "email": user.email,
                "tenant_id": str(user.tenant_id),
                "role": "Manager",
                "permissions": manager_permissions
            },
            "usage": {
                "curl_example": f'curl -H "Authorization: Bearer {token}" http://localhost:8000/api/resources/file',
                "header": f"Authorization: Bearer {token}"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate token: {str(e)}")

@router.post("/dev/token/member")
async def generate_member_token(tenant_id: Optional[str] = None):
    """Generate a member JWT token for testing (development only)"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        from utils.test_auth import create_test_superuser
        from data_access.factory import RepositoryFactory
        
        # Create test user
        user = create_test_superuser(UUID(tenant_id) if tenant_id else uuid4())
        user.email = "<EMAIL>"
        
        # Ensure user exists in database
        user_repo = await RepositoryFactory.create_user_repository()
        role_repo = await RepositoryFactory.create_role_repository()
        
        # Get or create a member role
        member_roles = await role_repo.get_all(0, 1, {"name": "Member"})
        if member_roles:
            user.role_id = member_roles[0].id
        else:
            # Create a member role if none exists
            member_permissions = [
                "read_agent", "execute_agent", "read_resource", "search_resource", "read_task", "read_tasks"
            ]
            member_role = Role(
                id=uuid4(),
                name="Member",
                description="Team Member",
                permissions=set(member_permissions)
            )
            member_role = await role_repo.create(member_role)
            user.role_id = member_role.id
        
        # Check if user already exists
        existing_user = await user_repo.get_all(0, 1, {"email": user.email})
        if existing_user:
            user = existing_user[0]
        else:
            # Create the user in database
            user = await user_repo.create(user)
        
        # Member permissions from Role.get_predefined_roles()
        member_permissions = [
            "read_agent", "execute_agent", "read_resource", "search_resource", "read_task", "read_tasks"
        ]
        
        token = jwt_manager.create_access_token(
            user_id=str(user.id),
            email=user.email,
            tenant_id=str(user.tenant_id),
            permissions=member_permissions,
            role="Member",
            expires_delta=timedelta(hours=1),
            additional_claims={"username": "Member", "test_token": True}
        )
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": str(user.id),
                "email": user.email,
                "tenant_id": str(user.tenant_id),
                "role": "Member",
                "permissions": member_permissions
            },
            "usage": {
                "curl_example": f'curl -H "Authorization: Bearer {token}" http://localhost:8000/api/resources/file',
                "header": f"Authorization: Bearer {token}"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate token: {str(e)}")

@router.get("/dev/test-auth")  
async def test_authentication():
    """Test endpoint to verify authentication is working"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404, 
            detail="Development endpoints not available in production"
        )
    
    from session.running_context import get_context
    context = get_context()
    
    if not context:
        return {"authenticated": False, "message": "No context found"}
    
    return {
        "authenticated": True,
        "user_id": context.user_id,
        "tenant_id": str(context.tenant_id),
        "bypass_mode": context.get_metadata("bypass", False)
    }

@router.get("/dev/bypass-example")
async def bypass_example(tenant_id: Optional[str] = None):
    """Example endpoint showing how to use bypass context in specific controllers"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create a bypass context for this specific endpoint
        tenant_uuid = UUID(tenant_id) if tenant_id else None
        context = create_bypass_context(tenant_uuid)
        
        # Now you can use require_context() or require_user() normally
        from session.running_context import require_context
        current_context = require_context()
        
        return {
            "message": "Bypass context created successfully",
            "user_id": current_context.user_id,
            "tenant_id": str(current_context.tenant_id),
            "bypass_mode": current_context.get_metadata("bypass", False)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create bypass context: {str(e)}")

# Development-only endpoints with bypass context
@router.post("/dev/db/sync", response_model=Dict[str, str])
async def dev_sync_database_schemas():
    """Development endpoint: Synchronize database schemas with bypass context"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context for development
        context = create_bypass_context()
        
        # Perform the sync operation
        await upsert_schemas()
        
        return {
            "status": "Database schemas synchronized successfully",
            "bypass_mode": True,
            "user_id": context.user_id
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync database schemas: {str(e)}"
        )

@router.post("/dev/db/reset", response_model=Dict[str, str])
async def dev_reset_database_schemas():
    """Development endpoint: Reset database schemas with bypass context"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context for development
        context = create_bypass_context()
        
        # Perform the reset operation
        await reset_schemas()
        
        return {
            "status": "Database schemas reset successfully",
            "bypass_mode": "true",
            "user_id": str(context.user_id)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset database schemas: {str(e)}"
        )

@router.get("/dev/db/collections", response_model=Dict[str, Any])
async def dev_list_collections(
    collection_name: Optional[str] = Query(None, description="Optional collection name. If empty, shows all collections")
):
    """Development endpoint: List all collections or get specific collection info with bypass context
    
    Args:
        collection_name: Optional collection name. If provided, returns details for that specific collection.
                        If empty or not provided, returns all collections.
    
    Returns:
        Dict containing either all collections or specific collection details
    """
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context for development
        context = create_bypass_context()
        
        if collection_name:
            # Get specific collection details
            client = get_client()
            
            # Ensure client is connected
            await ensure_client_connected()
            
            # Check if collection exists
            try:
                collections = await client.collections.list_all(simple=False)
                
                if collection_name not in collections:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Collection '{collection_name}' not found"
                    )
                
                collection_obj = collections[collection_name]
                
                # Get collection count
                collection_count = await client.collections.get(collection_name).aggregate.over_all(total_count=True)
                count = collection_count.total_count if collection_count else 0
                
                return {
                    "collection": {
                        "name": collection_name,
                        "description": getattr(collection_obj, 'description', '') or f"Collection for {collection_name} data",
                        "properties": collection_obj.properties if hasattr(collection_obj, 'properties') else [],
                        "count": count
                    },
                    "bypass_mode": True,
                    "user_id": context.user_id
                }
                
            except HTTPException:
                # Re-raise HTTP exceptions
                raise
            except Exception as collection_error:
                raise HTTPException(
                    status_code=404,
                    detail=f"Collection '{collection_name}' not found: {str(collection_error)}"
                )
        else:
            # Get all collections
            collections = await get_all_collections()
            
            return {
                "collections": collections,
                "bypass_mode": True,
                "user_id": context.user_id
            }
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get collection(s): {str(e)}"
        )

@router.get("/dev/db/collections/{collection_name}/data", response_model=Dict[str, Any])
async def dev_get_collection_data(
    collection_name: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """Development endpoint: Get collection data with bypass context"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context for development
        context = create_bypass_context()
        
        # Get Weaviate client directly
        client = get_client()
        
        # Get the collection using the v4 API
        collection = client.collections.get(collection_name)
        
        # Query data using the new v4 API
        result = await collection.query.fetch_objects(
            limit=limit,
            offset=skip
        )
        
        # Process the results
        data = []
        if result and hasattr(result, 'objects'):
            for obj in result.objects:
                # Create a clean object with properties and metadata
                clean_obj = {
                    "id": str(obj.uuid),
                    "properties": obj.properties,
                    "created_at": obj.metadata.creation_time if obj.metadata else None,
                    "updated_at": obj.metadata.last_update_time if obj.metadata else None,
                }
                data.append(clean_obj)
        
        return {
            "data": data,
            "collection": collection_name,
            "skip": skip,
            "limit": limit,
            "total_returned": len(data),
            "bypass_mode": True,
            "user_id": context.user_id,
            "database_type": "weaviate"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting collection data: {str(e)}"
        )

@router.post("/dev/db/collections/{collection_name}/reset", response_model=Dict[str, str])
async def dev_reset_collection(collection_name: str):
    """Development endpoint: Reset collection schema with bypass context"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context for development
        context = create_bypass_context()
        
        # Perform the reset operation
        await reset_collection_schema(collection_name)
        
        return {
            "status": f"Collection '{collection_name}' schema reset successfully",
            "bypass_mode": "true",
            "user_id": str(context.user_id)
        }
    except ValueError as ve:
        raise HTTPException(
            status_code=404,
            detail=str(ve)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset collection schema: {str(e)}"
        )

@router.get("/dev/test-bypass")
async def test_bypass_context():
    """Test endpoint to verify bypass context is working correctly"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        # Create bypass context
        context = create_bypass_context()
        
        # Test that we can access the context
        from session.running_context import require_context, require_user
        current_context = require_context()
        user = require_user()
        
        return {
            "message": "Bypass context working correctly",
            "context_user_id": context.user_id,
            "current_user_id": str(user.id),
            "user_email": user.email,
            "tenant_id": str(user.tenant_id),
            "bypass_mode": context.get_metadata("bypass", False),
            "context_match": context.user_id == str(user.id)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Bypass context test failed: {str(e)}"
        )

@router.get("/dev/test-search")
async def test_search_endpoint():
    """Test search functionality without permission checking (development only)"""
    if not is_development_mode():
        raise HTTPException(
            status_code=404,
            detail="Development endpoints not available in production"
        )
    
    try:
        from services.resource_service import get_resource_service
        from models.resource import ResourceType
        from session.running_context import get_context
        
        context = get_context()
        if not context or not context.user:
            return {"error": "No authenticated user found"}
        
        resource_service = await get_resource_service()
        
        # Test search
        results = await resource_service.search_resources(
            query="test",
            tenant_id=str(context.tenant_id),
            resource_type=ResourceType.ARTICLE,
            current_user=context.user,
            k=5
        )
        
        return {
            "success": True,
            "results_count": len(results),
            "results": [
                {
                    "content": doc.page_content[:100] + "..." if len(doc.page_content) > 100 else doc.page_content,
                    "metadata": doc.metadata
                }
                for doc in results
            ]
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "user_id": context.user.id if context and context.user else None,
            "tenant_id": str(context.tenant_id) if context else None
        }

# ===============================
# SYSTEM INITIALIZATION
# ===============================

@router.post("/init", status_code=status.HTTP_201_CREATED)
async def initialize_system():
    """
    Initialize the system with required roles and platform tenant

    This endpoint should be called once during system setup to create:
    - System roles (SystemAdmin, TenantManager, etc.)
    - Assivy Platform tenant (for system administrators)

    Note: Test tenant and user creation is now handled by separate development endpoints:
    - POST /api/system/v1/test/init-tenants (for test tenants)
    - POST /api/system/v1/test/init-users (for test users)

    Returns:
        Initialization results and created entities
    """
    try:
        from services.system_service import system_service
        
        result = await system_service.initialize_system()
        
        if result["errors"]:
            return JSONResponse(
                status_code=status.HTTP_207_MULTI_STATUS,
                content={
                    "message": "System initialization completed with some errors",
                    "result": result
                }
            )
        
        return {
            "message": "System initialization completed successfully",
            "result": result,
            "next_steps": {
                "test_tenants": "POST /api/system/v1/test/init-tenants",
                "test_users": "POST /api/system/v1/test/init-users",
                "note": "Use the test endpoints above to create development users and tenants"
            }
        }
        
    except Exception as e:
        logger.error(f"System initialization failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"System initialization failed: {str(e)}"
        )