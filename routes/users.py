from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import JSO<PERSON>esponse, RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime, timedelta, timezone
from pydantic import BaseModel, EmailStr, field_validator
import os
import shutil
from pathlib import Path
import re
import logging
import uuid

from models.user import User
from models.role import Permission
from services.user_service import UserService
from services.unified_auth_service import unified_auth_service
from security.unified_auth_middleware import get_current_user, create_running_context, requires_permissions
from security.auth_middleware import StatelessUser, StatelessRunningContext
from services.tenant_service import TenantService
from services.storage import storage_manager  # Add storage manager import
from session.running_context import RunningContext

from data_access.factory import RepositoryFactory
from config import settings
from authlib.integrations.starlette_client import OAuth
from starlette.requests import Request
from starlette.config import Config
from jose import jwt, JWTError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["users"])

# Pydantic models for request/response
class UserRegistration(BaseModel):
    """Schema for user registration"""
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    company_name: Optional[str] = None  # For tenant naming
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class UserProfileUpdate(BaseModel):
    """Schema for user profile updates"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    timezone: Optional[str] = None

class UserProfileResponse(BaseModel):
    """Schema for user profile response"""
    id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    bio: Optional[str] = None
    profile_image_url: Optional[str] = None
    timezone: Optional[str] = None
    tenant_id: str
    role_name: Optional[str] = None
    is_email_verified: bool
    profile_updated_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class LoginRequest(BaseModel):
    """Schema for login request"""
    email: EmailStr
    password: str

# Initialize services
async def get_user_service():
    user_repo = await RepositoryFactory.create_user_repository()
    role_repo = await RepositoryFactory.create_role_repository()
    return UserService(user_repo, role_repo)

async def get_tenant_service():
    return TenantService()

# OAuth configuration for Google
oauth = OAuth(Config(environ={
    "GOOGLE_CLIENT_ID": settings.google_client_id,
    "GOOGLE_CLIENT_SECRET": settings.google_client_secret,
}))

oauth.register(
    name="google",
    client_id=settings.google_client_id,
    client_secret=settings.google_client_secret,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={"scope": "openid email profile"},
)

# ===============================
# AUTHENTICATION ENDPOINTS
# ===============================

@router.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    user_service: UserService = Depends(get_user_service),
    set_cookie: bool = Query(False, description="Set authentication cookie in addition to returning token")
):
    """
    Login endpoint that returns JWT access token
    
    This endpoint handles authentication for all user types:
    - System administrators (no tenant required)
    - Tenant administrators and users (tenant-specific)
    
    Args:
        form_data: OAuth2 form with username (email) and password
        set_cookie: If True, sets authentication cookie in addition to returning token
        
    Returns:
        JWT access token and user information
        
    Raises:
        HTTPException: For authentication failures
    """
    try:
        # Authenticate user credentials
        user = await unified_auth_service.authenticate_user(form_data.username, form_data.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if account is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is inactive. Please contact support."
            )
        
        # Check email verification (skip for development and system users)
        if (settings.environment != "development" and 
            not user.is_email_verified and 
            user.tenant_id is not None):  # System users don't need email verification
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Please verify your email before logging in. Check your inbox for the verification link."
            )
        
        # Get user role information for the response
        role_repo = await RepositoryFactory.create_role_repository()
        user_role = await role_repo.get_by_id(user.role_id)
        
        # Create token pair using unified auth service
        token_data = await unified_auth_service.create_token_pair(user)
        
        # Return token data from unified auth service
        return JSONResponse(content=token_data)
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return generic message
        logger.error(f"Login error for {form_data.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during login. Please try again."
        )

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register_user(
    registration: UserRegistration,
    user_service: UserService = Depends(get_user_service),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Register a new user and create their tenant
    
    This creates a new tenant and makes the registering user the tenant admin.
    For multi-tenant SaaS, each registration creates a new isolated tenant.
    
    Args:
        registration: User registration data including optional company info
        
    Returns:
        Success message with tenant information
        
    Raises:
        HTTPException: If email already exists or validation fails
    """
    # Check if user already exists globally (across all tenants)
    existing_user = await user_service.get_user_by_email(registration.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered. Please use a different email or try logging in."
        )
    
    try:
        # Determine tenant name - use company name if provided, otherwise derive from email
        tenant_name = registration.company_name or registration.email.split('@')[0].title()
        
        # Create tenant for the new user
        tenant = await tenant_service.create_tenant(
            name=tenant_name,
            description=f"Tenant for {registration.email}",
            settings={
                "created_via": "registration",
                "primary_contact": registration.email
            },
            created_by=None  # Will be set after user creation
        )
        
        # Initialize default roles for the new tenant
        from services.role_service import role_service
        await role_service.initialize_default_roles(tenant.id)

        # Register user as the first user (tenant admin) in their new tenant
        user = await user_service.register_first_user(
            email=registration.email,
            password=registration.password,
            tenant_id=tenant.id,
            first_name=registration.first_name,
            last_name=registration.last_name
        )

        # Update tenant to set the created_by field
        await tenant_service.update_tenant(
            tenant_id=tenant.id,
            settings={
                **tenant.settings,
                "created_by": str(user.id)
            }
        )

        # Generate verification token and set expiry (24 hours)
        verification_token = unified_auth_service.create_verification_token()
        user.verification_token = verification_token
        user.verification_token_expires = datetime.now(timezone.utc) + timedelta(hours=24)

        # In development, auto-verify emails
        if settings.environment == "development":
            user.is_email_verified = True
            user.verification_token = None
            user.verification_token_expires = None
        else:
            user.is_email_verified = False

        await user_service.update_user(user)

        # Send verification email only in production
        if settings.environment != "development":
            unified_auth_service.send_verification_email(user.email, verification_token)
        
        response_data = {
            "message": "Registration successful!",
            "email": user.email,
            "tenant_id": str(tenant.id),
            "tenant_name": tenant.name,
            "user_role": "Admin",
            "email_verified": user.is_email_verified
        }
        
        if settings.environment != "development":
            response_data["message"] += " Please check your email to verify your account before logging in."
        else:
            response_data["message"] += " You can now log in immediately (development mode)."
        
        return response_data
        
    except ValueError as ve:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        # Cleanup: If user creation failed, try to clean up the tenant
        try:
            if 'tenant' in locals():
                await tenant_service.delete_tenant(tenant.id)
        except:
            pass  # Best effort cleanup
            
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@router.post("/login/cookie")
async def login_with_cookie(
    login_data: LoginRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    Login endpoint that sets authentication cookie
    
    This endpoint is designed for frontend applications that want to use
    cookie-based authentication instead of token-based authentication.
    
    Args:
        login_data: Email and password for authentication
        
    Returns:
        User information with authentication cookie set
        
    Raises:
        HTTPException: For authentication failures
    """
    try:
        # Authenticate user credentials
        unified_auth_service = unified_auth_service
        user = await unified_auth_service.authenticate_user(login_data.email, login_data.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if account is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is inactive. Please contact support."
            )
        
        # Check email verification (skip for development and system users)
        if (settings.environment != "development" and 
            not user.is_email_verified and 
            user.tenant_id is not None):  # System users don't need email verification
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Please verify your email before logging in. Check your inbox for the verification link."
            )
        
        # Get user role information for the response
        role_repo = await RepositoryFactory.create_role_repository()
        user_role = await role_repo.get_by_id(user.role_id)
        
        # Create access token
        access_token_expires = timedelta(minutes=unified_auth_service.access_token_expire_minutes)
        
        # Include user info in token payload
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "tenant_id": str(user.tenant_id) if user.tenant_id else None,
            "role_name": user_role.name if user_role else None
        }
        
        access_token = unified_auth_service.create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )
        
        # Prepare response with user information
        user_info = user_service.serialize_user(user)
        user_info.update({
            "role_name": user_role.name if user_role else None,
            "permissions": list(user_role.permissions) if user_role else []
        })
        
        # Create response with cookie
        cookie_params = unified_auth_service.create_auth_cookie(access_token, access_token_expires)
        response = JSONResponse({
            "message": "Login successful",
            "user": user_info,
            "expires_in": unified_auth_service.access_token_expire_minutes * 60
        })
        response.set_cookie(
            key=cookie_params["key"],
            value=cookie_params["value"],
            expires=cookie_params["expires"],
            secure=cookie_params["secure"],
            httponly=cookie_params["httponly"],
            samesite=cookie_params["samesite"],
            path=cookie_params["path"]
        )
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return generic message
        logger.error(f"Login error for {login_data.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during login. Please try again."
        )

@router.post("/logout")
async def logout(
    token: str = Form(...),
    user_service: UserService = Depends(get_user_service)
):
    """
    Logout endpoint that clears authentication
    
    Args:
        token: The authentication token to invalidate
        
    Returns:
        Success message with cleared cookie
    """
    # Create auth service instance to get cookie parameters
    unified_auth_service = unified_auth_service
    
    # Create response with cookie clearing
    cookie_params = unified_auth_service.clear_auth_cookie()
    response = JSONResponse({"message": "Logout successful"}, status_code=200)
    response.set_cookie(
        key=cookie_params["key"],
        value=cookie_params["value"],
        expires=cookie_params["expires"],
        secure=cookie_params["secure"],
        httponly=cookie_params["httponly"],
        samesite=cookie_params["samesite"],
        path=cookie_params["path"]
    )
    return response

@router.post("/logout/cookie")
async def logout_with_cookie(
    user_service: UserService = Depends(get_user_service)
):
    """
    Logout endpoint that clears authentication cookie
    
    This endpoint is designed for frontend applications using cookie-based authentication.
    
    Returns:
        Success message with cleared cookie
    """
    # Create auth service instance to get cookie parameters
    unified_auth_service = unified_auth_service
    
    # Create response with cookie clearing
    cookie_params = unified_auth_service.clear_auth_cookie()
    response = JSONResponse({"message": "Logout successful"}, status_code=200)
    response.set_cookie(
        key=cookie_params["key"],
        value=cookie_params["value"],
        expires=cookie_params["expires"],
        secure=cookie_params["secure"],
        httponly=cookie_params["httponly"],
        samesite=cookie_params["samesite"],
        path=cookie_params["path"]
    )
    return response

@router.post("/refresh")
async def refresh_token(
    refresh_token: str = Form(...),
    user_service: UserService = Depends(get_user_service)
):
    """
    Refresh access token using refresh token
    
    Args:
        refresh_token: The refresh token
        
    Returns:
        New access token and user information
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        # Use secure authentication service for refresh token handling
        token_data = await unified_auth_service.refresh_access_token(refresh_token)

        return token_data
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during token refresh"
        )

@router.get("/verify-email")
async def verify_email(
    token: str,
    user_service: UserService = Depends(get_user_service)
):
    """Verify user's email using the verification token
    
    Args:
        token: The verification token sent to user's email
        
    Returns:
        RedirectResponse to dashboard with access token after successful verification
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    # Verify email and get user
    verified_user = await unified_auth_service.verify_email(token)
    if verified_user:
        # Redirect to login page with success message
        frontend_login_url = f"{settings.frontend_url}/login?email={verified_user.email}"
        return RedirectResponse(url=frontend_login_url)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token"
        )

@router.post("/resend-verification")
async def resend_verification_email(
    email: EmailStr,
    user_service: UserService = Depends(get_user_service)
):
    """Resend verification email for unverified users
    
    Args:
        email: The email address to resend verification to
        
    Returns:
        Success message if email sent
        
    Raises:
        HTTPException: If user not found or already verified
    """
    user = await user_service.get_user_by_email(email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if user.is_email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already verified"
        )
    
    # Generate new verification token
    verification_token = unified_auth_service.create_verification_token()
    user.verification_token = verification_token
    user.verification_token_expires = datetime.now(timezone.utc) + timedelta(hours=24)
    await user_service.update_user(user.id, user.tenant_id, is_active=False)

    # Send verification email
    unified_auth_service.send_verification_email(user.email, verification_token)
    
    return JSONResponse({
        "message": "Verification email sent successfully"
    })

@router.get("/google/login")
async def google_login(request: Request):
    redirect_uri = request.url_for("google_auth_callback")
    return await oauth.google.authorize_redirect(request, redirect_uri)

@router.get("/google/callback")
async def google_auth_callback(
    request: Request,
    user_service: UserService = Depends(get_user_service)
):
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get("userinfo")
        if not user_info:
            raise HTTPException(status_code=400, detail="Failed to fetch user info")

        # Try to authenticate or create user
        user = await user_service.get_or_create_oauth_user(
            email=user_info["email"],
            oauth_data={
                "provider": "google",
                "profile": user_info
            }
        )

        # Generate access token for the user
        access_token = unified_auth_service.create_access_token(data={"sub": user.id})
        return JSONResponse({
            "access_token": access_token,
            "token_type": "bearer",
            "user": user_service.serialize_user(user)
        })

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api-key")
async def generate_api_key_endpoint(
    request: Request,
    current_user: StatelessUser = Depends(get_current_user)
):
    api_key = await unified_auth_service.generate_api_key(current_user.email)
    return JSONResponse({"api_key": api_key}, status_code=200)

# ===============================
# CURRENT USER PROFILE ENDPOINTS
# ===============================

@router.get("/me")
async def read_users_me(
    request: Request,
    current_user: StatelessUser = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    return user_service.serialize_user(current_user)

@router.put("/me")
async def update_current_user(
    profile_update: UserProfileUpdate,
    current_user: StatelessUser = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Update current user's profile"""
    try:
        user = await user_service.get_user_by_id(current_user.id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Convert Pydantic model to dict, excluding None values
        update_data = profile_update.model_dump(exclude_none=True)
        
        # Update user profile
        updated_user = await user_service.update_user_profile(
            current_user.id, 
            update_data
        )
        
        if not updated_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return user_service.serialize_user(updated_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update profile")

@router.post("/me/image")
async def upload_current_user_image(
    request: Request,
    file: UploadFile = File(...),
    current_user: StatelessUser = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Upload profile image for current user"""
    try:
        # Basic file validation
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Validate file type
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(status_code=400, detail="Invalid file extension")
        
        # Validate file size (5MB max)
        file.file.seek(0, 2)  # Seek to end
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning
        
        if file_size > 5 * 1024 * 1024:  # 5MB
            raise HTTPException(status_code=400, detail="File size too large. Maximum 5MB allowed.")
        
        # Get user
        user = await user_service.get_user_by_id(current_user.id)
        if not user or not user.tenant_id:
            raise HTTPException(status_code=400, detail="User tenant not found")
        
        # Upload to storage using storage manager - let it generate the key
        key = await storage_manager.upload_file(
            file_content=file.file,
            tenant_id=str(user.tenant_id),
            filename=file.filename,
            file_type="profile_images",
            content_type=file.content_type,
            metadata={
                "user_id": str(user.id),
                "uploaded_at": datetime.now().isoformat()
            }
        )
        
        # Generate the URL for accessing the image
        image_url = f"/files/{key}"
        
        # Update user profile with image URL
        updated_user = await user_service.update_user_profile(
            user.id, 
            {"profile_image_url": image_url}
        )
        
        return JSONResponse({
            "message": "Profile image uploaded successfully",
            "profile_image_url": image_url
        }, status_code=200)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading profile image: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upload profile image")

@router.delete("/me/image")
async def delete_current_user_image(
    request: Request,
    current_user: StatelessUser = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Delete current user's profile image"""
    try:
        user = await user_service.get_user_by_id(current_user.id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if user.profile_image_url:
            # Extract the storage key from the URL
            # URL format: /files/{key} or /api/files/{key} or /uploads/profile_images/{tenant_id}/{filename}
            try:
                if user.profile_image_url.startswith("/files/"):
                    # MinIO storage key (new format)
                    key = user.profile_image_url.replace("/files/", "")
                    await storage_manager.delete_file(key)
                    logger.info(f"Deleted profile image from MinIO: {key}")
                elif user.profile_image_url.startswith("/api/files/"):
                    # MinIO storage key (old format)
                    key = user.profile_image_url.replace("/api/files/", "")
                    await storage_manager.delete_file(key)
                    logger.info(f"Deleted profile image from MinIO: {key}")
                elif user.profile_image_url.startswith("/uploads/"):
                    # Legacy local file - also try to delete from storage if it exists
                    file_path = Path("uploads") / user.profile_image_url.lstrip("/uploads/")
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Deleted legacy profile image: {file_path}")
                    
                    # Also try to find it in MinIO storage (migration case)
                    try:
                        # Try to construct the likely MinIO key
                        tenant_id = str(user.tenant_id)
                        filename = Path(user.profile_image_url).name
                        # Look for files in profile_images directory
                        potential_keys = await storage_manager.list_files(
                            prefix=f"{tenant_id}/profile_images/",
                            limit=100
                        )
                        for key in potential_keys:
                            if filename in key:
                                await storage_manager.delete_file(key)
                                logger.info(f"Deleted migrated profile image: {key}")
                                break
                    except Exception as e:
                        logger.warning(f"Could not find/delete migrated image: {e}")
                        
            except Exception as e:
                logger.warning(f"Failed to delete image file: {str(e)}")
        
        # Clear profile image URL
        await user_service.update_user_profile(
            user.id, 
            {"profile_image_url": None}
        )
        
        return JSONResponse({
            "message": "Profile image deleted successfully"
        }, status_code=200)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting profile image: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete profile image")

@router.get("/me/preferences")
async def get_current_user_preferences(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Get current user's preferences"""
    return {"preferences": {}}

@router.put("/me/preferences")
async def update_current_user_preferences(
    preferences: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Update current user's preferences"""
    return {"preferences": preferences, "message": "Preferences updated successfully"}

@router.get("/me/notification-preferences")
async def get_current_user_notification_preferences(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Get current user's notification preferences"""
    return {"preferences": {}}

@router.put("/me/notification-preferences")
async def update_current_user_notification_preferences(
    preferences: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Update current user's notification preferences"""
    return {"preferences": preferences, "message": "Notification preferences updated successfully"}

@router.get("/me/security-settings")
async def get_current_user_security_settings(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Get current user's security settings"""
    return {"settings": {}}

@router.put("/me/security-settings")
async def update_current_user_security_settings(
    settings: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Update current user's security settings"""
    return {"settings": settings, "message": "Security settings updated successfully"}

@router.post("/me/2fa/enable")
async def enable_current_user_2fa(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Enable two-factor authentication for current user"""
    return {"message": "2FA setup initiated", "qr_code": "", "secret": ""}

@router.post("/me/2fa/verify")
async def verify_current_user_2fa(
    token_data: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Verify and confirm two-factor authentication for current user"""
    return {"message": "2FA enabled successfully", "recovery_codes": []}

@router.post("/me/2fa/disable")
async def disable_current_user_2fa(
    password_data: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Disable two-factor authentication for current user"""
    return {"message": "2FA disabled successfully"}

@router.get("/me/api-tokens")
async def get_current_user_api_tokens(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Get current user's API tokens"""
    return {"tokens": []}

@router.post("/me/api-tokens")
async def create_current_user_api_token(
    token_data: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Create new API token for current user"""
    return {"token": "new_token_here", "message": "API token created successfully"}

@router.delete("/me/api-tokens/{token_id}")
async def revoke_current_user_api_token(
    token_id: str,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Revoke API token for current user"""
    return {"message": "API token revoked successfully"}

@router.get("/me/activity-logs")
async def get_current_user_activity_logs(
    current_user: StatelessUser = Depends(get_current_user)
):
    """Get current user's activity logs"""
    return {"activities": [], "pagination": {}}

@router.post("/me/delete-account")
async def delete_current_user_account(
    delete_data: dict,
    current_user: StatelessUser = Depends(get_current_user)
):
    """Delete current user's account"""
    return {"message": "Account deletion initiated"}

# ===============================
# USER MANAGEMENT ENDPOINTS
# ===============================

@router.get("/{user_id}", response_model=UserProfileResponse)
async def get_user_by_id(
    user_id: UUID,
    current_user: StatelessUser = Depends(get_current_user),
    permission_check: StatelessUser = Depends(requires_permissions({Permission.MANAGE_TENANT_USERS})),
    user_service: UserService = Depends(get_user_service)
):
    """Get user by ID (requires manage_tenant_users permission)"""
    try:
        # Check if user is in same tenant (unless system admin)
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # System admins can view any user, others can only view users in their tenant
        if not current_user.has_permission(Permission.SYSTEM_MANAGE_USERS.value):
            if current_user.tenant_id != user.tenant_id:
                raise HTTPException(
                    status_code=403,
                    detail="Can only view users in your own tenant"
                )
        
        # Get role information
        from services.role_service import role_service
        role = await role_service.get_role_by_id(user.role_id)
        
        # Construct profile response
        profile_data = {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "job_title": user.job_title,
            "department": user.department,
            "phone": user.phone,
            "location": user.location,
            "bio": user.bio,
            "profile_image_url": user.profile_image_url,
            "timezone": user.timezone,
            "tenant_id": str(user.tenant_id) if user.tenant_id else None,
            "role_name": role.name if role else None,
            "is_email_verified": user.is_email_verified,
            "profile_updated_at": user.profile_updated_at,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        }
        
        return profile_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve profile")

# Add missing RESTful endpoints for user management
