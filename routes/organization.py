from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from typing import Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, EmailStr
import os
import shutil
from pathlib import Path

from models.tenant import Tenant
from models.role import Permission
from services.tenant_service import TenantService
from security.unified_auth_middleware import get_current_user, requires_permissions
from security.auth_middleware import StatelessUser
from config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/organization", tags=["organization"])

# Pydantic models for request/response
class OrganizationUpdate(BaseModel):
    """Schema for organization updates"""
    organization_name: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    website: Optional[str] = None
    primary_contact_email: Optional[EmailStr] = None
    primary_contact_phone: Optional[str] = None
    address: Optional[Dict[str, Any]] = None
    description: Optional[str] = None

class OrganizationResponse(BaseModel):
    """Schema for organization response"""
    id: str
    name: str
    description: Optional[str] = None
    organization_name: Optional[str] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    website: Optional[str] = None
    organization_logo_url: Optional[str] = None
    primary_contact_email: Optional[str] = None
    primary_contact_phone: Optional[str] = None
    address: Optional[Dict[str, Any]] = None
    subscription_type: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Initialize services
tenant_service = TenantService()

@router.get("/settings", response_model=OrganizationResponse)
async def get_organization_settings(
    current_user: StatelessUser = Depends(get_current_user), permission_check: StatelessUser = Depends(requires_permissions({Permission.MANAGE_TENANT_SETTINGS}))
):
    """Get organization settings for current tenant"""
    try:
        tenant = await tenant_service.get_tenant(current_user.tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Construct organization response
        org_data = {
            "id": str(tenant.id),
            "name": tenant.name,
            "description": tenant.description,
            "organization_name": tenant.organization_name,
            "industry": tenant.industry,
            "company_size": tenant.company_size,
            "website": tenant.website,
            "organization_logo_url": tenant.organization_logo_url,
            "primary_contact_email": tenant.primary_contact_email,
            "primary_contact_phone": tenant.primary_contact_phone,
            "address": tenant.address,
            "subscription_type": tenant.subscription_type,
            "created_at": tenant.created_at,
            "updated_at": tenant.updated_at
        }
        
        return org_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving organization settings: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve organization settings")

@router.put("/settings", response_model=OrganizationResponse)
async def update_organization_settings(
    org_update: OrganizationUpdate,
    current_user: StatelessUser = Depends(get_current_user), permission_check: StatelessUser = Depends(requires_permissions({Permission.MANAGE_TENANT_SETTINGS}))
):
    """Update organization settings for current tenant"""
    try:
        tenant = await tenant_service.get_tenant(current_user.tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Update only provided fields
        update_data = org_update.model_dump(exclude_unset=True)
        if update_data:
            # Update tenant
            updated_tenant = await tenant_service.update_tenant(
                tenant_id=current_user.tenant_id,
                **update_data
            )
            
            # Construct response
            org_data = {
                "id": str(updated_tenant.id),
                "name": updated_tenant.name,
                "description": updated_tenant.description,
                "organization_name": updated_tenant.organization_name,
                "industry": updated_tenant.industry,
                "company_size": updated_tenant.company_size,
                "website": updated_tenant.website,
                "organization_logo_url": updated_tenant.organization_logo_url,
                "primary_contact_email": updated_tenant.primary_contact_email,
                "primary_contact_phone": updated_tenant.primary_contact_phone,
                "address": updated_tenant.address,
                "subscription_type": updated_tenant.subscription_type,
                "created_at": updated_tenant.created_at,
                "updated_at": updated_tenant.updated_at
            }
            
            return org_data
        else:
            raise HTTPException(status_code=400, detail="No fields to update")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating organization settings: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update organization settings")

def get_upload_path(tenant_id: str, file_type: str = "organization_logos") -> Path:
    """Get secure upload path based on tenant"""
    base_upload_dir = Path(settings.upload_dir if hasattr(settings, 'upload_dir') else "uploads")
    tenant_upload_dir = base_upload_dir / file_type / tenant_id
    tenant_upload_dir.mkdir(parents=True, exist_ok=True)
    return tenant_upload_dir

def validate_image_file(file: UploadFile) -> bool:
    """Validate uploaded image file"""
    # Check file size (max 10MB for organization logos)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File size too large. Maximum 10MB allowed.")
    
    # Check file type
    allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml"]
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail="Invalid file type. Only JPEG, PNG, GIF, WebP, and SVG are allowed.")
    
    # Check file extension
    allowed_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"]
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in allowed_extensions:
        raise HTTPException(status_code=400, detail="Invalid file extension.")
    
    return True

@router.post("/logo")
async def upload_organization_logo(
    file: UploadFile = File(...),
    current_user: StatelessUser = Depends(get_current_user), permission_check: StatelessUser = Depends(requires_permissions({Permission.MANAGE_TENANT_SETTINGS}))
):
    """Upload organization logo for current tenant"""
    try:
        # Validate file
        validate_image_file(file)
        
        # Get tenant info
        tenant = await tenant_service.get_tenant(current_user.tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        # Create secure upload path based on tenant
        upload_dir = get_upload_path(str(tenant.id), "organization_logos")
        
        # Generate unique filename
        file_extension = Path(file.filename).suffix.lower()
        filename = f"logo_{int(datetime.utcnow().timestamp())}{file_extension}"
        file_path = upload_dir / filename
        
        # Remove old logo if exists
        if tenant.organization_logo_url:
            try:
                old_file_path = Path(tenant.organization_logo_url.replace("/uploads/", ""))
                if old_file_path.exists():
                    old_file_path.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete old organization logo: {e}")
        
        # Save new file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Update tenant with new logo URL
        relative_path = f"/uploads/organization_logos/{tenant.id}/{filename}"
        await tenant_service.update_tenant(
            tenant_id=tenant.id,
            organization_logo_url=relative_path
        )
        
        return {
            "success": True,
            "message": "Organization logo uploaded successfully",
            "organization_logo_url": relative_path
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading organization logo: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upload organization logo")

@router.delete("/logo")
async def delete_organization_logo(
    current_user: StatelessUser = Depends(get_current_user), permission_check: StatelessUser = Depends(requires_permissions({Permission.MANAGE_TENANT_SETTINGS}))
):
    """Delete organization logo for current tenant"""
    try:
        tenant = await tenant_service.get_tenant(current_user.tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        if tenant.organization_logo_url:
            # Remove file from storage
            try:
                file_path = Path(tenant.organization_logo_url.replace("/uploads/", ""))
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete organization logo file: {e}")
            
            # Update tenant
            await tenant_service.update_tenant(
                tenant_id=tenant.id,
                organization_logo_url=None
            )
        
        return {
            "success": True,
            "message": "Organization logo deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting organization logo: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete organization logo")
