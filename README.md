# Assivy Backend

A comprehensive backend service for the Assivy AI platform, built with FastAPI and modern Python tooling.

## 🚀 Quick Start

The Assivy Backend uses a unified CLI tool for all operations. This consolidates all development, testing, and deployment tasks into a single entry point.

### Prerequisites

- Python 3.12+
- [UV](https://docs.astral.sh/uv/) package manager
- Docker (for MinIO and Weaviate services)

### Installation

```bash
# Install dependencies
python assivy.py install

# Install development dependencies
python assivy.py install --dev

# Install test dependencies
python assivy.py install --test
```

### Development

```bash
# Start development server with auto-reload
python assivy.py dev

# Start production server
python assivy.py start

# Start Docker services (MinIO, Weaviate)
python assivy.py services start --wait
```

### Testing

```bash
# Run all tests with coverage
python assivy.py test

# Run unit tests only
python assivy.py test --unit

# Run integration tests only
python assivy.py test --integration

# Run fast tests (exclude slow tests)
python assivy.py test --fast

# Run tests in watch mode
python assivy.py test --watch
```

### Code Quality

```bash
# Run code linting
python assivy.py lint

# Format code
python assivy.py format

# Run type checking
python assivy.py type-check

# Run security checks
python assivy.py security
```

### Utilities

```bash
# Clean generated files
python assivy.py clean

# Check environment setup
python assivy.py env-check

# Get help
python assivy.py --help
python assivy.py <command> --help
```

## 🔧 Alternative: Using Make

For convenience, you can also use the simplified Makefile which delegates to the CLI tool:

```bash
# Start development server
make dev

# Run tests
make test

# Install dependencies
make install

# See all available commands
make help
```

## 📋 Available Commands

### Server Commands
- `dev` - Start development server with auto-reload
- `start` - Start production server

### Testing Commands
- `test` - Run all tests with coverage
- `test --unit` - Run unit tests only
- `test --integration` - Run integration tests only
- `test --fast` - Run fast tests (exclude slow tests)
- `test --watch` - Run tests in watch mode
- `test --coverage` - Generate coverage report

### Development Commands
- `install` - Install dependencies
- `install --dev` - Install development dependencies
- `install --test` - Install test dependencies
- `lint` - Run code linting
- `lint --fix` - Auto-fix linting issues
- `format` - Format code
- `format --check` - Check formatting without changes
- `type-check` - Run type checking
- `security` - Run security checks

### Services Commands
- `services start` - Start Docker services (MinIO, Weaviate)
- `services stop` - Stop Docker services
- `services restart` - Restart Docker services
- `services status` - Check service status

### Utility Commands
- `clean` - Clean generated files
- `clean --all` - Clean all files including virtual environment
- `env-check` - Check environment setup
- `help` - Show detailed help

## 🏗️ Architecture

The backend is built with a modular architecture:

- **FastAPI** - Web framework
- **UV** - Package management and virtual environments
- **MongoDB** - Primary database
- **Weaviate** - Vector database for embeddings
- **MinIO** - Object storage
- **Docker Compose** - Service orchestration

## 🔐 Authentication

The system supports multiple authentication methods:

- JWT-based authentication
- API key authentication
- Role-based access control
- Multi-tenant architecture

## 📚 API Documentation

Once the server is running, you can access:

- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc
- **OpenAPI schema**: http://localhost:8000/openapi.json

## 🧪 Testing

The project includes comprehensive testing:

- **Unit tests** - Test individual components
- **Integration tests** - Test component interactions
- **Coverage reporting** - Track test coverage
- **CI/CD ready** - Automated testing pipeline

## 🚀 Deployment

### Development
```bash
# Start all services
python assivy.py services start --wait
python assivy.py dev
```

### Production
```bash
# Start production server
python assivy.py start
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python assivy.py test`
5. Run linting: `python assivy.py lint`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
