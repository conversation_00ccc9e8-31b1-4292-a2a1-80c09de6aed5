{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": true,
    "esModuleInterop": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "components/*": ["./src/components/*"],
      "pages/*": ["./src/pages/*"],
      "features/*": ["./src/features/*"],
      "services/*": ["./src/services/*"],
      "utils/*": ["./src/utils/*"],
      "contexts/*": ["./src/contexts/*"],
      "hooks/*": ["./src/hooks/*"],
      "adapters/*": ["./src/adapters/*"]
    }
  },
  "include": ["src/**/*.js", "src/**/*.jsx"],
  "exclude": ["node_modules", "build", "dist"]
}