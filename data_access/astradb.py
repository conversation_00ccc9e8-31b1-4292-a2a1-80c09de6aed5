from astrapy import DataAPIClient
from astrapy.collection import AsyncCollection
from astrapy.data.database import AsyncDatabase
from typing import Optional, Dict, Any, List, TypeVar, Generic
import logging
from config import settings
from uuid import UUID, uuid4
from datetime import datetime, timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from data_access.base import BaseRepository, ModelType
from models.registry import get_collection_name, model_registry
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

class AstraDBDatabase:
    """AstraDB connection manager"""
    client: DataAPIClient = None
    database: AsyncDatabase = None
    collections: Dict[str, AsyncCollection] = {}

async def connect_to_astradb():
    """Connect to AstraDB"""
    print("Connecting to AstraDB...")
    try:
        # First create the client
        client = DataAPIClient()
        
        # Then get a database instance using the api_endpoint and token
        database = client.get_async_database(
            api_endpoint=settings.astra_api_endpoint,
            token=settings.astra_token
        )
        
        # Store both client and database instances
        AstraDBDatabase.client = client
        AstraDBDatabase.database = database
        
        print("Successfully connected to AstraDB!")
        
        # Set up collections
        #await setup_collections()
    except Exception as e:
        logger.error(f"Error connecting to AstraDB: {str(e)}")
        raise

async def setup_collections():
    """Set up collections with appropriate schemas"""
    try:
        # Get all known collection names from model registry
        collections = set(model_registry.values())
        
        # Get existing collections once
        existing_collections = await AstraDBDatabase.database.list_collections()
        
        # Create and store collections
        for collection_name in collections:
            try:
                if collection_name not in existing_collections:
                    # Create collection without any index configuration
                    await AstraDBDatabase.database.create_collection(collection_name)
                    logger.info(f"Created collection {collection_name}")
                
                # Store collection reference
                collection = AstraDBDatabase.database.get_collection(collection_name)
                AstraDBDatabase.collections[collection_name] = collection
                
            except Exception as e:
                logger.error(f"Error setting up collection {collection_name}: {str(e)}")
                continue
            
    except Exception as e:
        logger.error(f"Error setting up AstraDB collections: {str(e)}")
        raise

async def close_astradb_connection():
    """Close AstraDB connection"""
    print("Closing AstraDB connection...")
    if AstraDBDatabase.client:
        # No explicit close needed for astrapy client
        pass
    print("AstraDB connection closed")

def get_database():
    """Get database instance for dependency injection"""
    if AstraDBDatabase.database is None:
        raise RuntimeError("Database not initialized")
    return AstraDBDatabase.database

class AstraDBRepository(BaseRepository[ModelType]):
    """AstraDB implementation of the BaseRepository interface"""

    @classmethod
    async def from_model_type(cls, model_type: type, database, collection_name: str = None) -> 'AstraDBRepository[ModelType]':
        """Factory method to create a new repository instance with database connection"""
        name = collection_name or get_collection_name(model_type)
        collection = database.get_collection(name)
        return cls(collection, model_type)
    
    def __init__(self, collection: AsyncCollection, model_type: type):
        self.model_type = model_type
        self.collection: Optional[AsyncCollection] = collection

    @property
    def is_initialized(self) -> bool:
        return self.collection is not None

    def _check_initialized(self):
        if not self.is_initialized:
            raise RuntimeError("AstraDBRepository not properly initialized. Use AstraDBRepository.create() factory method.")

    def _should_apply_tenant_filter(self) -> bool:
        context = self.context
        if not context or not context.tenant_id:
            return False
        
        # Check if user has system-level permissions instead of is_superuser
        # Note: This is a synchronous method, so we can't await async permission checks
        # For safety, we'll apply tenant filtering unless we can determine system access
        # through other means (like checking user role directly)
        try:
            # Check if user has system admin role directly (synchronous check)
            if hasattr(context.user, 'role_name') and context.user.role_name == 'SystemAdmin':
                return False
        except Exception:
            # If permission check fails, apply tenant filter for safety
            pass
            
        return self.model.__name__ not in ['User', 'Tenant', 'Role', 'APIKey']

    def _get_tenant_query_filter(self) -> Dict[str, Any]:
        if not self._should_apply_tenant_filter():
            return {}
        return {"tenant_id": self.required_context.tenant_id}

    async def create(self, data: ModelType, tenant_id_for_creation: Optional[str] = None) -> ModelType:
        self._check_initialized()
        doc_data = data.model_dump()
        doc_id = uuid4()
        doc_data["_id"] = str(doc_id)  # AstraDB uses _id instead of id
        doc_data["id"] = str(doc_id)

        # Add audit fields
        self._add_audit_fields(doc_data)
        
        # Handle tenant ID
        if tenant_id_for_creation and self.context:
            # Check if user has system-level permissions to set tenant_id
            try:
                system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                    "system:list_tenants", "system:manage_users", "system:view_analytics"]
                has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                if has_system_access:
                    doc_data["tenant_id"] = tenant_id_for_creation
            except Exception:
                # If permission check fails, don't allow tenant_id override
                pass
        elif self._should_apply_tenant_filter():
            doc_data["tenant_id"] = self.required_context.tenant_id
            
        # Add metadata
        self._add_context_metadata(doc_data)
        
        # Insert document - AstraPy expects a dictionary
        await self.collection.insert_one(doc_data)
        return await self.get_by_id(doc_id)

    async def get_by_id(self, doc_id: UUID) -> Optional[ModelType]:
        self._check_initialized()
        filter_dict = {"id": str(doc_id)}
        if self._should_apply_tenant_filter():
            filter_dict["tenant_id"] = self.required_context.tenant_id
            
        cursor = self.collection.find(
            filter=filter_dict,
            limit=1
        )
        documents = await cursor.to_list()
        if not documents:
            return None
            
        return self.model_type(**documents[0])

    async def get_all(self, skip: int = 0, limit: int = 100, 
                 query_conditions: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        self._check_initialized()
        filter_dict = {}
        
        if self._should_apply_tenant_filter():
            filter_dict["tenant_id"] = self.required_context.tenant_id
        
        if query_conditions:
            filter_dict.update(query_conditions)
        
        # AstraPy's find() requires sort when using skip
        cursor = self.collection.find(
            filter=filter_dict,
            sort={"id": 1},  # Sort by ID ascending
            skip=skip,
            limit=limit
        )
        
        documents = await cursor.to_list()
        return [self.model_type(**doc) for doc in documents]

    async def update(self, doc_id: UUID, data: ModelType) -> Optional[ModelType]:
        self._check_initialized()
        # First check if document exists and is accessible
        existing_doc = await self.get_by_id(doc_id)
        if not existing_doc:
            return None

        # Prepare update data
        update_data = data.model_dump(exclude_unset=True)
        
        # Add audit fields
        self._add_audit_fields(update_data, is_update=True)
        
        # Protect certain fields
        for protected_field in ["created_at", "created_by", "id", "_id"]:
            update_data.pop(protected_field, None)
            
        # Add metadata
        self._add_context_metadata(update_data)
        
        # Handle tenant ID
        if "tenant_id" in update_data:
            if self.context:
                # Check if user has system-level permissions to modify tenant_id
                try:
                    system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                        "system:list_tenants", "system:manage_users", "system:view_analytics"]
                    has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                    if not has_system_access:
                        del update_data["tenant_id"]
                except Exception:
                    # If permission check fails, remove tenant_id for safety
                    del update_data["tenant_id"]
            else:
                # No context, remove tenant_id for safety
                del update_data["tenant_id"]

        filter_dict = {"id": str(doc_id)}
        if self._should_apply_tenant_filter():
            filter_dict["tenant_id"] = self.required_context.tenant_id

        # AstraPy update syntax
        await self.collection.update(
            filter=filter_dict,
            update={"$set": update_data}
        )
            
        return await self.get_by_id(doc_id)

    async def delete(self, doc_id: UUID) -> bool:
        self._check_initialized()
        filter_dict = {"id": str(doc_id)}
        if self._should_apply_tenant_filter():
            filter_dict["tenant_id"] = self.required_context.tenant_id
            
        result = await self.collection.delete(filter=filter_dict)
        return result.deleted_count > 0

    async def find_by_id_system(self, id: UUID) -> Optional[ModelType]:
        self._check_initialized()
        cursor = self.collection.find(
            filter={"id": str(id)},
            limit=1
        )
        documents = await cursor.to_list()
        return self.model_type(**documents[0]) if documents else None

    async def find_all_system(self, skip: int = 0, limit: int = 100, 
                    filters: Dict[str, Any] = None) -> List[ModelType]:
        self._check_initialized()
        filter_dict = filters or {}
        cursor = self.collection.find(
            filter=filter_dict,
            sort={"id": 1},  # Sort by ID ascending
            skip=skip,
            limit=limit
        )
        documents = await cursor.to_list()
        return [self.model_type(**doc) for doc in documents]

async def cleanup_collections():
    """Delete all collections to allow clean recreation"""
    try:
        existing_collections = await AstraDBDatabase.database.list_collections()
        for collection_name in existing_collections:
            try:
                await AstraDBDatabase.database.delete_collection(collection_name)
                logger.info(f"Deleted collection {collection_name}")
            except Exception as e:
                logger.error(f"Error deleting collection {collection_name}: {str(e)}")
                continue
    except Exception as e:
        logger.error(f"Error cleaning up collections: {str(e)}")
        raise