from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from typing import Optional, Dict, Any, List, TypeVar, Generic
import logging
from config import settings
from uuid import UUID, uuid4
from datetime import datetime, timezone
from fastapi import HTT<PERSON>Ex<PERSON>, status
from data_access.base import BaseRepository, ModelType
from models.user import User
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class MongoDBDatabase:
    """MongoDB connection manager"""
    client: AsyncIOMotorClient = None
    db: AsyncIOMotorDatabase = None

async def connect_to_mongo():
    """Connect to MongoDB and set up indexes"""
    print("Connecting to MongoDB...")
    MongoDBDatabase.client = AsyncIOMotorClient(
        settings.mongo_url,
        uuidRepresentation='standard'
    )
    MongoDBDatabase.db = MongoDBDatabase.client["assivy"]
    print("Successfully connected to MongoDB!")
    
    # Set up indexes
    try:
        if MongoDBDatabase.db:
            # Common indexes for all collections
            collections = [
                "file_resources", "article_resources", "web_resources",
                "entity_definitions", "entity_instances", "actions",
                "tasks", "agents", "api_keys"
            ]
            
            for collection in collections:
                await MongoDBDatabase.db[collection].create_index([("tenant_id", 1)])
                await MongoDBDatabase.db[collection].create_index([("created_by", 1)])
                await MongoDBDatabase.db[collection].create_index([("updated_by", 1)])
    except Exception as e:
        logger.error(f"Error creating MongoDB indexes: {str(e)}")

async def close_mongo_connection():
    """Close MongoDB connection"""
    print("Closing MongoDB connection...")
    if MongoDBDatabase.client:
        MongoDBDatabase.client.close()
    print("MongoDB connection closed")

def get_database() -> AsyncIOMotorDatabase:
    """Get database connection for dependency injection"""
    if MongoDBDatabase.db is None:
        raise RuntimeError("Database not initialized")
    return MongoDBDatabase.db

@dataclass
class LookupConfig:
    """Configuration for MongoDB $lookup aggregation"""
    local_field: str  # Field in current collection (e.g., "role_id")
    foreign_collection: str  # Collection to join with (e.g., "roles")
    foreign_field: str = "_id"  # Field in foreign collection, defaults to _id
    as_field: str = None  # Output field name, defaults to local_field without _id suffix

class MongoDBRepository(BaseRepository[ModelType]):
    """MongoDB implementation of the BaseRepository interface"""

    @classmethod
    async def initialize(cls, collection_name: str, model_type: type, db: AsyncIOMotorDatabase) -> 'MongoDBRepository[ModelType]':
        """Factory method to create a new repository instance with database connection"""
        repo = cls(collection_name, model_type)
        repo.collection = db[collection_name]
        return repo
    
    def __init__(self, collection_name: str, model_type: type):
        self.collection_name = collection_name
        self.model_type = model_type
        self.collection: Optional[AsyncIOMotorCollection] = None

    @property
    def is_initialized(self) -> bool:
        return self.collection is not None

    def _check_initialized(self):
        if not self.is_initialized:
            raise RuntimeError("MongoDBRepository not properly initialized. Use MongoDBRepository.create() factory method.")

    def _should_apply_tenant_filter(self) -> bool:
        context = self.context
        if not context or not context.tenant_id:
            return False
        
        # Check if user has system-level permissions instead of is_superuser
        # Note: This is a synchronous method, so we can't await async permission checks
        # For safety, we'll apply tenant filtering unless we can determine system access
        # through other means (like checking user role directly)
        try:
            # Check if user has system admin role directly (synchronous check)
            if hasattr(context.user, 'role_name') and context.user.role_name == 'SystemAdmin':
                return False
        except Exception:
            # If permission check fails, apply tenant filter for safety
            pass
            
        return self.model.__name__ not in ['User', 'Tenant', 'Role', 'APIKey']

    def _get_tenant_query_filter(self) -> Dict[str, Any]:
        if not self._should_apply_tenant_filter():
            return {}
        return {"tenant_id": self.required_context.tenant_id}

    async def create(self, data: ModelType, tenant_id_for_creation: Optional[str] = None) -> ModelType:
        self._check_initialized()
        doc_data = data.model_dump()
        doc_id = uuid4()
        doc_data["_id"] = doc_id

        # Add audit fields
        self._add_audit_fields(doc_data)
        
        # Handle tenant ID
        if tenant_id_for_creation and self.context:
            # Check if user has system-level permissions to set tenant_id
            try:
                system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                    "system:list_tenants", "system:manage_users", "system:view_analytics"]
                has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                if has_system_access:
                    doc_data["tenant_id"] = tenant_id_for_creation
            except Exception:
                # If permission check fails, don't allow tenant_id override
                pass
        elif self._should_apply_tenant_filter():
            doc_data["tenant_id"] = self.required_context.tenant_id
            
        # Add metadata
        self._add_context_metadata(doc_data)

        await self.collection.insert_one(doc_data)
        created_doc_dict = await self.collection.find_one({"_id": doc_id})
        if created_doc_dict is None:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
                              detail="Failed to retrieve document after creation.")
        
        return self.model_type(**created_doc_dict)

    async def get_by_id(self, doc_id: UUID) -> Optional[ModelType]:
        self._check_initialized()
        query_filter = {"_id": doc_id}
        tenant_filter = self._get_tenant_query_filter()
        query_filter.update(tenant_filter)
        
        document = await self.collection.find_one(query_filter)
        if not document:
            return None
                
        return self.model_type(**document)

    async def get_all(self, skip: int = 0, limit: int = 100, 
                     query_conditions: Optional[Dict[str, Any]] = None) -> List[ModelType]:
        self._check_initialized()
        # Start with tenant filter
        query_filter = self._get_tenant_query_filter()
        
        # Add any additional conditions
        if query_conditions:
            query_filter.update(query_conditions)

        cursor = self.collection.find(query_filter).skip(skip).limit(limit)
        documents = await cursor.to_list(length=limit)
        return [self.model_type(**doc) for doc in documents]

    async def update(self, doc_id: UUID, data: ModelType) -> Optional[ModelType]:
        self._check_initialized()
        # Build query with tenant filter
        query_filter = {"_id": doc_id}
        tenant_filter = self._get_tenant_query_filter()
        query_filter.update(tenant_filter)
        
        # Check if document exists and is accessible
        existing_doc = await self.collection.find_one(query_filter)
        if not existing_doc:
            return None

        # Prepare update data
        update_data = data.model_dump(exclude_unset=True)
        
        # Add audit fields
        self._add_audit_fields(update_data, is_update=True)
        
        # Protect certain fields
        for protected_field in ["created_at", "created_by", "id", "_id"]:
            update_data.pop(protected_field, None)
            
        # Add metadata
        self._add_context_metadata(update_data)
        
        # Handle tenant ID
        if "tenant_id" in update_data:
            if self.context:
                # Check if user has system-level permissions to modify tenant_id
                try:
                    system_permissions = ["super_admin", "system:create_tenant", "system:delete_tenant", 
                                        "system:list_tenants", "system:manage_users", "system:view_analytics"]
                    has_system_access = any(self.context.has_permission(perm) for perm in system_permissions)
                    if not has_system_access:
                        del update_data["tenant_id"]
                except Exception:
                    # If permission check fails, remove tenant_id for safety
                    del update_data["tenant_id"]
            else:
                # No context, remove tenant_id for safety
                del update_data["tenant_id"]

        # Perform update
        updated_doc = await self.collection.find_one_and_update(
            query_filter,
            {"$set": update_data},
            return_document=True
        )
        return self.model_type(**updated_doc) if updated_doc else None

    async def delete(self, doc_id: UUID) -> bool:
        self._check_initialized()
        query_filter = {"_id": doc_id}
        tenant_filter = self._get_tenant_query_filter()
        query_filter.update(tenant_filter)
        
        result = await self.collection.delete_one(query_filter)
        return result.deleted_count > 0
    
    async def find_by_id_system(self, id: UUID) -> Optional[ModelType]:
        self._check_initialized()
        document = await self.collection.find_one({"_id": id})
        return self.model_type(**document) if document else None

    async def find_all_system(self, skip: int = 0, limit: int = 100, filters: Dict[str, Any] = None) -> List[ModelType]:
        self._check_initialized()
        query = filters or {}
        cursor = self.collection.find(query).skip(skip).limit(limit)
        documents = await cursor.to_list(length=limit)
        return [self.model_type(**doc) for doc in documents]