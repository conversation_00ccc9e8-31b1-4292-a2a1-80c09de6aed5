import logging
from typing import TypeVar, Type, Dict
from datetime import datetime, timezone
from uuid import uuid4
import asyncio

from fastapi import Depends
from pydantic import EmailStr

# Models
from models.registry import get_collection_name
from models.user import User
from models.action import BaseAction
from models.resource import FileResource, ArticleResource, WebResource, Chunk, Shelf
from models.library import Library
from models.entity import EntityDefinition, EntityInstance
from models.task import Task
from models.api_key import APIKey
from models.role import Role
from models.tenant import Tenant
from .base import ModelType

# Data access
from .base import BaseRepository
from .weaviatedb import WeaviateRepository, get_client_async
from .mocked import MockedDBRepository

# Config
from config import settings

# Set up logger
logger = logging.getLogger(__name__)
T = TypeVar('T', bound=ModelType)

class RepositoryFactory:
    """Factory for creating repositories with the current implementation"""

    @classmethod
    async def create_repository(
        cls,
        model_type: Type[T]
    ) -> BaseRepository[T]:
        """
        Create a repository instance for the given model type
        
        Args:
            model_type: The model class for the repository
            
        Returns:
            An instance of BaseRepository for the specified model
        """
        collection_name = get_collection_name(model_type)
            
        if settings.testing_mode:
            return MockedDBRepository(collection_name, model_type)
        else:
            client = await get_client_async()
            return await WeaviateRepository.from_model_type(model_type, client, collection_name)

    @staticmethod
    def register_model(model_type: Type[ModelType], collection_name: str) -> None:
        """
        Register a new model type with its collection name
        
        Args:
            model_type: The model class to register
            collection_name: The collection name to use in MongoDB
        """
        RepositoryFactory._model_registry[model_type] = collection_name

    # Convenience methods using the generic create_repository
    @classmethod
    async def create_action_repository(cls) -> BaseRepository[BaseAction]:
        """Create a repository for Action models"""
        return await cls.create_repository(BaseAction)

    @classmethod
    async def create_file_resource_repository(cls) -> BaseRepository[FileResource]:
        """Create a repository for FileResource models"""
        return await cls.create_repository(FileResource)

    @classmethod
    async def create_article_resource_repository(cls) -> BaseRepository[ArticleResource]:
        """Create a repository for ArticleResource models"""
        return await cls.create_repository(ArticleResource)

    @classmethod
    async def create_web_resource_repository(cls) -> BaseRepository[WebResource]:
        """Create a repository for WebResource models"""
        return await cls.create_repository(WebResource)

    @classmethod
    async def create_shelf_repository(cls) -> BaseRepository[Shelf]:
        """Create a repository for Shelf models"""
        return await cls.create_repository(Shelf)

    @classmethod
    async def create_library_repository(cls) -> BaseRepository[Library]:
        """Create a repository for Library models"""
        return await cls.create_repository(Library)

    @classmethod
    async def create_entity_definition_repository(cls) -> BaseRepository[EntityDefinition]:
        """Create a repository for EntityDefinition models"""
        return await cls.create_repository(EntityDefinition)

    @classmethod
    async def create_entity_instance_repository(cls) -> BaseRepository[EntityInstance]:
        """Create a repository for EntityInstance models"""
        return await cls.create_repository(EntityInstance)

    @classmethod
    async def create_task_repository(cls) -> BaseRepository[Task]:
        """Create a repository for Task models"""
        return await cls.create_repository(Task)

    @classmethod
    async def create_api_key_repository(cls) -> BaseRepository[APIKey]:
        """Create a repository for APIKey models"""
        return await cls.create_repository(APIKey)

    @classmethod
    async def create_user_repository(cls) -> BaseRepository[User]:
        """Create a repository for User models"""
        return await cls.create_repository(User)

    @classmethod
    async def create_role_repository(cls) -> BaseRepository[Role]:
        """Create a repository for Role models"""
        return await cls.create_repository(Role)

    @classmethod
    async def create_tenant_repository(cls) -> BaseRepository[Tenant]:
        """Create a repository for Tenant models"""
        return await cls.create_repository(Tenant)

    @classmethod
    async def create_chunk_repository(cls) -> BaseRepository[Chunk]:
        """Create a repository for Chunk models"""
        return await cls.create_repository(Chunk)