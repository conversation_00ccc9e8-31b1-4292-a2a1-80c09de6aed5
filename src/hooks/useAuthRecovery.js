import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

/**
 * Custom hook for handling authentication recovery
 * Useful for components that might face intermittent auth issues
 */
export const useAuthRecovery = () => {
  const { refreshAuthState, isAuthenticated, isLoading } = useAuth();
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryAttempts, setRecoveryAttempts] = useState(0);

  const attemptRecovery = useCallback(async () => {
    if (isRecovering || recoveryAttempts >= 3) {
      return false;
    }

    setIsRecovering(true);
    setRecoveryAttempts(prev => prev + 1);

    try {
      const success = await refreshAuthState();
      if (success) {
        setRecoveryAttempts(0);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Auth recovery failed:', error);
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [refreshAuthState, isRecovering, recoveryAttempts]);

  const resetRecovery = useCallback(() => {
    setRecoveryAttempts(0);
    setIsRecovering(false);
  }, []);

  return {
    attemptRecovery,
    resetRecovery,
    isRecovering,
    recoveryAttempts,
    canAttemptRecovery: !isRecovering && recoveryAttempts < 3,
    isAuthenticated: isAuthenticated && !isLoading
  };
};

export default useAuthRecovery;
