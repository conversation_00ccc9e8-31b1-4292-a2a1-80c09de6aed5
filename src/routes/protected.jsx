import { DashboardOverview } from "../features/dashboard";
import { AgentListing, AgentCreation, AgentPlaygroundPage } from "../features/agents";
import { UserProfileSettings } from "../features/user-management";
import { APIServiceLayer } from "../features/api-service";
import { ResourcePage, ViewResource, FileManager, DuplicateFileCleanup } from "../features/resources";
import { IntegrationManagement } from "../features/integrations";
import { DataLibraryPage, LibraryDetailsPage, ShelfDetailsPage, AddResourcesPage } from "../features/data-library";

export const protectedRoutes = [
  // Dashboard Routes
  {
    path: "/dashboard",
    element: DashboardOverview,
    title: "Dashboard Overview",
  },
  
  // Agent Routes
  {
    path: "/agents",
    element: AgentListing,
    title: "AI Agents",
  },
  {
    path: "/agents/new",
    element: AgentCreation,
    title: "Create Agent",
  },
  {
    path: "/agent-playground",
    element: AgentPlaygroundPage,
    title: "Agent Playground",
  },
  {
    path: "/agent-playground/:id",
    element: AgentPlaygroundPage,
    title: "Agent Playground",
  },
  {
    path: "/resources",
    element: ResourcePage,
    title: "Resources",
  },
  {
    path: "/resources/file",
    element: FileManager,
    title: "File Manager",
  },
  {
    path: "/resources/cleanup",
    element: DuplicateFileCleanup,
    title: "Duplicate File Cleanup",
  },
  {
    path: "/resources/:type/:id/view",
    element: ViewResource,
    title: "View Resource",
  },
  
  // Integration Management
  {
    path: "/integrations",
    element: IntegrationManagement,
    title: "Integrations",
  },

  // Data Library Routes
  {
    path: "/data-library",
    element: DataLibraryPage,
    title: "Data Library",
  },
  {
    path: "/data-library/library/:libraryId",
    element: LibraryDetailsPage,
    title: "Library Details",
  },
  {
    path: "/data-library/library/:libraryId/shelf/:shelfId",
    element: ShelfDetailsPage,
    title: "Shelf Details",
  },
  {
    path: "/data-library/library/:libraryId/add-resources",
    element: AddResourcesPage,
    title: "Add Resources",
  },
  
  // API Service Layer
  {
    path: "/api-service",
    element: APIServiceLayer,
    title: "API Service Layer",
  },
  
  // User Settings
  {
    path: "/settings",
    element: UserProfileSettings,
    title: "User Settings",
  },
  
  // Settings Routes
  {
    path: "/settings/profile",
    element: UserProfileSettings,
    title: "Profile Settings",
  },
  
  // API & Integration Routes
  {
    path: "/api-service-layer",
    element: APIServiceLayer,
    title: "API Service Layer",
  },
];
