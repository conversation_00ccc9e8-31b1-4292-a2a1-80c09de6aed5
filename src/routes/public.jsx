import { LandingPage } from "../features/landing";
import { LoginScreen, RegisterScreen } from "../features/auth";
import { Unauthorized } from "../features/errors";
import DashboardTestPage from "../app/dashboard-test/page";

export const publicRoutes = [
  {
    path: "/",
    element: LandingPage,
    title: "Home",
  },
  {
    path: "/auth/login",
    element: LoginScreen,
    title: "Login",
  },
  {
    path: "/login", // Legacy route redirect
    element: LoginScreen,
    title: "Login",
  },
  {
    path: "/auth/register",
    element: RegisterScreen,
    title: "Register",
  },
  {
    path: "/register", // Legacy route redirect
    element: RegisterScreen,
    title: "Register",
  },
  {
    path: "/unauthorized",
    element: Unauthorized,
    title: "Unauthorized",
  },
  {
    path: "/dashboard-test",
    element: DashboardTestPage,
    title: "Dashboard Test",
  },
];
