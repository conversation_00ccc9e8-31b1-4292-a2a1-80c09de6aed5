import React from "react";
import { Routes as RouterRoutes, Route } from "react-router-dom";
import { publicRoutes } from './public';
import { protectedRoutes } from './protected';
import { NotFound } from "features/errors";
import { ProtectedRoute } from "components/ui";

const Routes = () => {
  return (
    <RouterRoutes>
      {/* Public Routes */}
      {publicRoutes.map(({ path, element: Element }) => (
        <Route key={path} path={path} element={<Element />} />
      ))}
      
      {/* Protected Routes */}
      {protectedRoutes.map(({ path, element: Element }) => (
        <Route
          key={path}
          path={path}
          element={
            <ProtectedRoute>
              <Element />
            </ProtectedRoute>
          }
        />
      ))}
      
      {/* 404 Not Found */}
      <Route path="*" element={<NotFound />} />
    </RouterRoutes>
  );
};

export default Routes;
