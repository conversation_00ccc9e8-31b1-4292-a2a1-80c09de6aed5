import React from "react";
import { BrowserRouter } from "react-router-dom";
import Routes from "./Routes";
import ScrollToTop from "components/ScrollToTop";
import { ErrorBoundary } from "features/errors";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
        <ScrollToTop />
        <Routes />
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default AppRouter;
