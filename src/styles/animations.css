@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes stagger-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

.stagger-animate-1 {
  animation: stagger-fade-in 0.3s ease-out forwards;
}
.stagger-animate-2 {
  animation: stagger-fade-in 0.3s ease-out 0.1s forwards;
}
.stagger-animate-3 {
  animation: stagger-fade-in 0.3s ease-out 0.2s forwards;
}
.stagger-animate-4 {
  animation: stagger-fade-in 0.3s ease-out 0.3s forwards;
}

.feature-card {
  opacity: 0;
}
