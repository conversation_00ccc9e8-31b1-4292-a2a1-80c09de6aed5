@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 206 100% 97%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 200 50% 95%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 200 50% 92%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 200 50% 95%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 200 50% 88%;
    --input: 200 50% 88%;
    --ring: 199 89% 48%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gradient-to-br from-sky-100 via-blue-50 to-sky-200 text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

/* React PDF Styles - TextLayer and AnnotationLayer */
.react-pdf__Page__textContent {
  border: 1px solid darkgrey;
  box-sizing: border-box;
}

.react-pdf__Page__textContent--disabled {
  border: none;
}

.react-pdf__Page__annotations {
  border: 1px solid darkgrey;
  box-sizing: border-box;
}

.react-pdf__Page__annotations--disabled {
  border: none;
}

.react-pdf__Page__canvas {
  margin: 0 auto;
}

/* TextLayer styles for text selection */
.react-pdf__Page__textContent {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.react-pdf__Page__textContent--disabled {
  opacity: 0;
}

.react-pdf__Page__textContent > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

/* AnnotationLayer styles for PDF annotations */
.react-pdf__Page__annotations {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.react-pdf__Page__annotations--disabled {
  opacity: 0;
}

.react-pdf__Page__annotations > section {
  position: absolute;
}

.react-pdf__Page__annotations > section > a {
  position: absolute;
  font-size: 1em;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border: 1px solid rgba(255, 255, 0, 0.3);
}

.react-pdf__Page__annotations > section > a:hover,
.react-pdf__Page__annotations > section > a:focus {
  background: rgba(255, 255, 0, 0.2);
  box-shadow: 0 2px 10px rgba(255, 255, 0, 0.3);
}

/* Additional PDF viewer styles for better integration */
.react-pdf__Page {
  margin: 1em auto;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
}

.react-pdf__Page__canvas {
  display: block;
}

.react-pdf__Page__textContent,
.react-pdf__Page__annotations {
  pointer-events: auto;
}

.react-pdf__Page__textContent--disabled,
.react-pdf__Page__annotations--disabled {
  pointer-events: none;
}

/* Smooth scrolling for file content areas */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling for better UX */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}