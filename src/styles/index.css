@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    
    /* New consistent background colors */
    --surface: 0 0% 100%;
    --surface-secondary: 210 40% 98%;
    --surface-muted: 210 40% 96%;
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 16.3% 46.9%;
    --text-muted: 215.4 16.3% 46.9%;
    --text-tertiary: 215.4 16.3% 46.9%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --spacing: .25rem
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    
    /* Dark mode surface colors */
    --surface: 222.2 84% 4.9%;
    --surface-secondary: 217.2 32.6% 17.5%;
    --surface-muted: 217.2 32.6% 17.5%;
    --text-primary: 210 40% 98%;
    --text-secondary: 215 20.2% 65.1%;
    --text-muted: 215 20.2% 65.1%;
    --text-tertiary: 215 20.2% 65.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 32.2%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --spacing: .25rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gradient-to-br from-sky-100 via-blue-50 to-sky-200 text-foreground;
    font-family: Inter, sans-serif;
  }
}

body {
  margin: 0;
  padding: 0;
}

/* Utility classes for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* Animation utilities */
@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation-duration: 200ms;
  animation-fill-mode: forwards;
}

.slide-in-from-top-2 {
  animation-name: slide-in-from-top;
}

/* React PDF Styles - TextLayer and AnnotationLayer */
.react-pdf__Page__textContent {
  border: 1px solid darkgrey;
  box-sizing: border-box;
}

.react-pdf__Page__textContent--disabled {
  border: none;
}

.react-pdf__Page__annotations {
  border: 1px solid darkgrey;
  box-sizing: border-box;
}

.react-pdf__Page__annotations--disabled {
  border: none;
}

.react-pdf__Page__canvas {
  margin: 0 auto;
}

/* TextLayer styles for text selection */
.react-pdf__Page__textContent {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.react-pdf__Page__textContent--disabled {
  opacity: 0;
}

.react-pdf__Page__textContent > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

/* AnnotationLayer styles for PDF annotations */
.react-pdf__Page__annotations {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.react-pdf__Page__annotations--disabled {
  opacity: 0;
}

.react-pdf__Page__annotations > section {
  position: absolute;
}

.react-pdf__Page__annotations > section > a {
  position: absolute;
  font-size: 1em;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border: 1px solid rgba(255, 255, 0, 0.3);
}

.react-pdf__Page__annotations > section > a:hover,
.react-pdf__Page__annotations > section > a:focus {
  background: rgba(255, 255, 0, 0.2);
  box-shadow: 0 2px 10px rgba(255, 255, 0, 0.3);
}

/* Additional PDF viewer styles for better integration */
.react-pdf__Page {
  margin: 1em auto;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
}

.react-pdf__Page__canvas {
  display: block;
}

.react-pdf__Page__textContent,
.react-pdf__Page__annotations {
  pointer-events: auto;
}

.react-pdf__Page__textContent--disabled,
.react-pdf__Page__annotations--disabled {
  pointer-events: none;
}

/* Smooth scrolling for file content areas */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling for better UX */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
