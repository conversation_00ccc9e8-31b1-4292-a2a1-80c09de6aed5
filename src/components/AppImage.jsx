import React from 'react';

function Image({
  src,
  alt = "Image Name",
  className = "",
  ...props
}) {
  // Convert relative uploads URLs to full URLs
  const getImageSrc = (src) => {
    if (!src) return null;
    
    // If it's already a full URL, return as-is
    if (src.startsWith('http://') || src.startsWith('https://')) {
      return src;
    }
    
    // If it's a relative uploads path, prepend the base URL (without /api prefix)
    if (src.startsWith('/uploads/')) {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      // Remove /api suffix if present, since uploads are served directly
      const baseUrl = apiBaseUrl.replace(/\/api$/, '');
      return `${baseUrl}${src}`;
    }
    
    // If it's a /files/ path (new format), prepend the base URL
    if (src.startsWith('/files/')) {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      // Ensure we have the /api prefix
      const baseUrl = apiBaseUrl.endsWith('/api') ? apiBaseUrl : `${apiBaseUrl}/api`;
      return `${baseUrl}${src}`;
    }
    
    // If it's a /api/files/ path (old format), prepend the base URL
    if (src.startsWith('/api/files/')) {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      // Ensure we have the /api prefix
      const baseUrl = apiBaseUrl.endsWith('/api') ? apiBaseUrl : `${apiBaseUrl}/api`;
      return `${baseUrl}${src}`;
    }
    
    // Return the src as-is for other paths
    return src;
  };

  const imageSrc = getImageSrc(src);

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onError={(e) => {
        e.target.src = "/assets/images/no_image.png"
      }}
      {...props}
    />
  );
}

export default Image;
