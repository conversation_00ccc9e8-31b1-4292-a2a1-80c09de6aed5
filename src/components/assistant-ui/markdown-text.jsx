"use client";

import { useState } from "react";
import { CheckIcon, CopyIcon } from "lucide-react";
import { TooltipIconButton } from "./tooltip-icon-button";
import { cn } from "../../lib/utils";
import { MarkdownTextPrimitive } from "@assistant-ui/react-markdown";
import { SyntaxHighlighter } from "./syntax-highlighter";

const useCopyToClipboard = ({ copiedDuration = 3000 } = {}) => {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = (value) => {
    if (!value) return;

    navigator.clipboard.writeText(value).then(() => {
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), copiedDuration);
    });
  };

  return { isCopied, copyToClipboard };
};

export const CodeHeader = ({ language, code }) => {
  const { isCopied, copyToClipboard } = useCopyToClipboard();
  const onCopy = () => {
    if (!code || isCopied) return;
    copyToClipboard(code);
  };

  return (
    <div className="flex items-center justify-between gap-4 rounded-t-lg bg-zinc-900 px-4 py-2 text-sm font-semibold text-white">
      <span className="lowercase">{language}</span>
      <TooltipIconButton tooltip="Copy" onClick={onCopy}>
        {!isCopied && <CopyIcon />}
        {isCopied && <CheckIcon />}
      </TooltipIconButton>
    </div>
  );
};

// Custom Code component with syntax highlighting
const Code = ({ className, children, ...props }) => {
  // Extract language from className (format: "language-javascript")
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "";
  
  const code = String(children).replace(/\n$/, "");

  if (language) {
    return (
      <div className="my-4 overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800">
        <CodeHeader language={language} code={code} />
        <div className="overflow-x-auto">
          <SyntaxHighlighter
            language={language}
            code={code}
            className="[&>pre]:m-0 [&>pre]:rounded-none [&>pre]:border-0 [&>pre]:p-4 [&>pre]:!bg-zinc-50 [&>pre]:dark:!bg-zinc-900 [&_code]:!text-zinc-900 [&_code]:dark:!text-zinc-100 [&_code]:!bg-transparent"
          />
        </div>
      </div>
    );
  }

  // Inline code
  return (
    <code 
      className="rounded bg-zinc-100 px-1.5 py-0.5 text-sm font-mono text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100" 
      {...props}
    >
      {children}
    </code>
  );
};

// Enhanced markdown text component using assistant-ui/react-markdown
export const MarkdownText = ({ children, ...props }) => {
  return (
    <MarkdownTextPrimitive 
      className="prose prose-slate dark:prose-invert max-w-none" 
      components={{
        code: Code,
      }}
      {...props}
    >
      {children}
    </MarkdownTextPrimitive>
  );
};
