"use client";

import { useCallback, useLayoutEffect, useState } from "react";
import { bundledLanguages, createHighlighter, createOnigurumaEngine } from "shiki";

const CODE_BLOCK_THEME_LIGHT = "github-light";
const CODE_BLOCK_THEME_DARK = "github-dark";

const themes = [CODE_BLOCK_THEME_LIGHT, CODE_BLOCK_THEME_DARK];
const languages = Object.keys(bundledLanguages);

let highlighterPromise = null;

const getHighlighter = () => {
  if (!highlighterPromise) {
    highlighterPromise = createHighlighter({
      engine: createOnigurumaEngine(),
      themes,
      langs: languages,
    });
  }
  return highlighterPromise;
};

// Hook to detect current theme
const useTheme = () => {
  const [isDark, setIsDark] = useState(false);

  useLayoutEffect(() => {
    // Check initial theme
    const checkTheme = () => {
      const isDarkMode = 
        document.documentElement.classList.contains('dark') ||
        window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(isDarkMode);
    };

    checkTheme();

    // Watch for theme changes
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', checkTheme);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', checkTheme);
    };
  }, []);

  return isDark ? 'dark' : 'light';
};

export const useSyntaxHighlighter = () => {
  const [highlighter, setHighlighter] = useState(null);
  const theme = useTheme();

  useLayoutEffect(() => {
    getHighlighter().then(setHighlighter);
  }, []);

  return useCallback(
    ({ language, code }) => {
      if (!highlighter) return { html: "", language };

      const themeToUse = theme === "dark" ? CODE_BLOCK_THEME_DARK : CODE_BLOCK_THEME_LIGHT;
      
      try {
        const html = highlighter.codeToHtml(code, {
          lang: language,
          theme: themeToUse,
        });
        return { html, language };
      } catch (error) {
        console.warn(`Failed to highlight code for language "${language}":`, error);
        return { html: `<pre><code>${code}</code></pre>`, language };
      }
    },
    [highlighter, theme]
  );
};

export const SyntaxHighlighter = ({ language, code, className }) => {
  const highlight = useSyntaxHighlighter();
  const { html } = highlight({ language, code });

  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
};
