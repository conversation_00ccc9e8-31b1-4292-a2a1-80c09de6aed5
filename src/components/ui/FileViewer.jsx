import React, { useState, useEffect, useMemo } from 'react';
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import { File, ExternalLink, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Add custom styles for DocViewer containment
const docViewerStyles = `
  .react-doc-viewer {
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
  }
  
  .react-doc-viewer > div {
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
  }
  
  .react-doc-viewer iframe {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
  }
  
  .react-doc-viewer canvas {
    max-width: 100% !important;
    height: auto !important;
  }
  
  .react-doc-viewer img {
    max-width: 100% !important;
    height: auto !important;
  }
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('docviewer-containment-styles')) {
  const style = document.createElement('style');
  style.id = 'docviewer-containment-styles';
  style.textContent = docViewerStyles;
  document.head.appendChild(style);
}

const FileViewer = ({ file, fileUrl, onError }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewerError, setViewerError] = useState(null);
  const [blobUrl, setBlobUrl] = useState(null);

  // Fetch file with credentials and create blob URL
  useEffect(() => {
    if (!fileUrl) {
      setError('No file URL provided');
      setLoading(false);
      return;
    }

    const fetchFileAsBlob = async () => {
      try {
        setLoading(true);
        setError(null);
        setViewerError(null);
        
        console.log('Fetching file with credentials:', fileUrl);
        
        const response = await fetch(fileUrl, {
          credentials: 'include',
          headers: {
            'Accept': '*/*',
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
        }
        
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        setBlobUrl(blobUrl);
        setLoading(false);
        
        console.log('File fetched successfully, blob URL created:', blobUrl);
      } catch (error) {
        console.error('Error fetching file:', error);
        setError(error.message);
        setLoading(false);
        if (onError) onError(error);
      }
    };

    fetchFileAsBlob();

    // Cleanup blob URL on unmount or URL change
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [fileUrl]);

  // Prepare document for the viewer using blob URL
  const documents = useMemo(() => {
    if (!blobUrl) return [];
    
    const filename = file?.filename || file?.name || fileUrl?.split('/').pop();
    return [
      {
        uri: blobUrl,
        fileName: filename,
        fileType: file?.file_type || filename?.split('.').pop()?.toLowerCase()
      }
    ];
  }, [file, fileUrl, blobUrl]);

  const handleOpenInNewTab = () => {
    window.open(fileUrl, '_blank');
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-600 p-4">
        <AlertCircle className="h-12 w-12 mb-2" />
        <p className="text-lg font-semibold">Error loading file</p>
        <p className="text-sm text-center">{error}</p>
        <div className="flex gap-2 mt-4">
          <Button onClick={handleOpenInNewTab} variant="outline" size="sm">
            <ExternalLink className="h-4 w-4 mr-2" />
            Open in New Tab
          </Button>
        </div>
      </div>
    );
  }

  if (!documents.length || loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-600 p-4">
        {loading ? (
          <>
            <Loader2 className="h-8 w-8 animate-spin mb-2" />
            <span>Loading file...</span>
          </>
        ) : (
          <>
            <File className="h-12 w-12 mb-2" />
            <p className="text-lg">No file to display</p>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="w-full h-full bg-white rounded-lg border overflow-hidden">
      {/* Simple Header */}
      <div className="flex justify-between items-center p-3 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <File className="h-4 w-4" />
          <span className="text-sm font-medium truncate">{documents[0].fileName}</span>
        </div>
        <Button onClick={handleOpenInNewTab} variant="outline" size="sm">
          <ExternalLink className="h-4 w-4 mr-2" />
          Open in New Tab
        </Button>
      </div>

      {/* Single Content Container */}
      <div className="w-full h-full">
        {viewerError ? (
          <div className="flex flex-col items-center justify-center h-full text-red-600 p-4">
            <AlertCircle className="h-12 w-12 mb-2" />
            <p className="text-lg font-semibold">Error displaying file</p>
            <p className="text-sm text-center">{viewerError}</p>
            <Button onClick={handleOpenInNewTab} variant="outline" size="sm" className="mt-4">
              <ExternalLink className="h-4 w-4 mr-2" />
              Open in New Tab
            </Button>
          </div>
        ) : (
          <DocViewer
            documents={documents}
            pluginRenderers={DocViewerRenderers}
            config={{
              header: {
                disableHeader: true,
                disableFileName: true,
                retainURLParams: false,
              },
              csvDelimiter: ',',
              pdfZoom: {
                defaultZoom: 1.0,
                zoomJump: 0.2,
              },
              pdfVerticalScrollByDefault: true,
            }}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: '#ffffff',
              overflow: 'auto'
            }}
            onError={(error) => {
              console.error('DocViewer error:', error);
              setViewerError(error?.message || 'Unknown error occurred while displaying the file');
            }}
          />
        )}
      </div>
    </div>
  );
};

export default FileViewer;
