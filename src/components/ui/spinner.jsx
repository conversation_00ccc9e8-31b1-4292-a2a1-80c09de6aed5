import React from 'react';
import { cva } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const spinnerVariants = cva(
  "animate-spin rounded-full border-2 border-current border-t-transparent",
  {
    variants: {
      size: {
        small: "h-4 w-4",
        default: "h-6 w-6",
        large: "h-8 w-8",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
);

const Spinner = React.forwardRef(({ className, size, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(spinnerVariants({ size }), className)}
    {...props}
  />
));
Spinner.displayName = "Spinner";

export { Spinner, spinnerVariants };
