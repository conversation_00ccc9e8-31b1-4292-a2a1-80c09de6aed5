import React, { useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import {
  Menu,
  ArrowLeft,
  Search,
  Bell,
  ChevronDown,
  Settings,
  HelpCircle,
  LogOut
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';

const Header = ({ onSidebarToggle, isSidebarCollapsed, isMobileSidebarOpen }) => {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const { logout, user } = useAuth();

  const getBreadcrumbItems = () => {
    const pathname = location.pathname;
    
    // Define the breadcrumb structure for all routes - starting with main navigation
    const pathMap = {
      '/dashboard': [
        { label: 'Dashboard', path: '/dashboard', isActive: true }
      ],
      '/agents': [
        { label: 'Agents', path: '/agents', isActive: true }
      ],
      '/agents/new': [
        { label: 'Agents', path: '/agents', isActive: false },
        { label: 'Create Agent', path: '/agents/new', isActive: true }
      ],
      '/resources': [
        { label: 'Resources', path: '/resources', isActive: true }
      ],
      '/resources/:id/view': [
        { label: 'Resources', path: '/resources', isActive: false },
        { label: 'View Resource', path: location.pathname, isActive: true }
      ],
      '/integrations': [
        { label: 'Integrations', path: '/integrations', isActive: true }
      ],
      '/settings': [
        { label: 'Profile Settings', path: '/settings', isActive: true }
      ],
      '/user-profile-settings': [
        { label: 'Profile Settings', path: '/user-profile-settings', isActive: true }
      ],
      '/api-service-layer': [
        { label: 'API Service Layer', path: '/api-service-layer', isActive: true }
      ]
    };

    // Handle dynamic routes with parameters
    if (pathname.includes('/resources/') && pathname.includes('/view')) {
      // Extract resource type and ID from URL
      const pathParts = pathname.split('/');
      const resourceTypeIndex = pathParts.indexOf('resources') + 1;
      const resourceIdIndex = resourceTypeIndex + 1;
      
      if (pathParts[resourceTypeIndex] && pathParts[resourceIdIndex]) {
        const resourceType = pathParts[resourceTypeIndex];
        const resourceId = pathParts[resourceIdIndex];
        
        return [
          { label: 'Resources', path: '/resources', isActive: false },
          { label: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} Resource`, path: location.pathname, isActive: true }
        ];
      }
      
      return pathMap['/resources/:id/view'];
    }

    // Return the mapped breadcrumbs or default
    return pathMap[pathname] || [
      { label: 'Dashboard', path: '/dashboard', isActive: true }
    ];
  };

  const breadcrumbItems = getBreadcrumbItems();
  const hasMultipleLevels = breadcrumbItems.length > 1;
  const isDeepLevel = breadcrumbItems.length > 1; // Changed from > 2 to > 1

  const handleNavigation = (path, isActive) => {
    if (!isActive) {
      // If navigating to Resources from ViewResource, preserve tab state
      if (path === '/resources' && location.pathname.includes('/resources/') && location.pathname.includes('/view')) {
        const urlParams = new URLSearchParams(location.search);
        const tab = urlParams.get('tab') || 'file';
        navigate(`/resources?tab=${tab}`);
      } else {
        navigate(path);
      }
    }
  };

  const handleBackClick = () => {
    // Go back to the previous breadcrumb level
    const currentIndex = breadcrumbItems.findIndex(item => item.isActive);
    if (currentIndex > 0) {
      const previousItem = breadcrumbItems[currentIndex - 1];
      
      // If navigating back to Resources from ViewResource, preserve tab state
      if (previousItem.path === '/resources' && location.pathname.includes('/resources/') && location.pathname.includes('/view')) {
        const urlParams = new URLSearchParams(location.search);
        const tab = urlParams.get('tab') || 'file';
        navigate(`/resources?tab=${tab}`);
      } else {
        navigate(previousItem.path);
      }
    } else {
      // Fallback to dashboard
      navigate('/dashboard');
    }
  };

  const handleUserMenuToggle = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // Force navigation even if logout fails
      navigate('/auth/login');
    } finally {
      setIsUserMenuOpen(false);
    }
  };

  const handleProfileClick = () => {
    navigate('/user-profile-settings');
    setIsUserMenuOpen(false);
  };

  return (
    <header className="flex-shrink-0 bg-surface border-b border-border shadow-sm h-16 z-30">
      <div className="flex items-center justify-between h-full px-4 sm:px-6">
        {/* Left Section - Mobile Menu Toggle and Breadcrumbs */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onSidebarToggle}
            className="p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200 lg:hidden"
            aria-label="Toggle sidebar"
          >
            <Menu size={20} />
          </button>
          
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-4">
            {/* Back Button - Show for deep levels (3+ levels) */}
            {isDeepLevel && (
              <button
                onClick={handleBackClick}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-surface-muted rounded-lg transition-colors duration-200"
                aria-label="Go back"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back</span>
              </button>
            )}

            {/* Breadcrumb Navigation */}
            {hasMultipleLevels && (
              <nav className="flex items-center space-x-2 text-sm" aria-label="Breadcrumb">
                <ol className="flex items-center space-x-2">
                  {breadcrumbItems.map((item, index) => (
                    <li key={item.path} className="flex items-center">
                      {index > 0 && (
                        <Icon 
                          name="ChevronRight" 
                          size={16} 
                          className="text-text-muted mx-2" 
                          aria-hidden="true"
                        />
                      )}
                      
                      {item.isActive ? (
                        <span 
                          className="text-text-primary font-medium"
                          aria-current="page"
                        >
                          {item.label}
                        </span>
                      ) : (
                        <button
                          onClick={() => handleNavigation(item.path, item.isActive)}
                          className="text-text-secondary hover:text-text-primary transition-colors duration-200 hover:underline"
                        >
                          {item.label}
                        </button>
                      )}
                    </li>
                  ))}
                </ol>
              </nav>
            )}
          </div>
        </div>

        {/* Right Section - Actions and User Menu */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Search Button - Hidden on mobile */}
          <button className="hidden sm:flex p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200">
            <Search size={20} />
          </button>

          {/* Notifications */}
          <button className="p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200 relative">
            <Bell size={20} />
            <span className="absolute top-1.5 right-1.5 w-2 h-2 bg-accent rounded-full"></span>
          </button>

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={handleUserMenuToggle}
              className="flex items-center space-x-2 p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200"
              aria-expanded={isUserMenuOpen}
              aria-haspopup="true"
            >
              <Avatar className="w-8 h-8">
                <AvatarImage 
                  src={user?.profile_image_url || user?.avatar} 
                  alt={user?.name || user?.email || 'User'} 
                />
                <AvatarFallback className="bg-gradient-to-br from-primary to-primary-600 text-white text-sm font-medium">
                  {user?.name 
                    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                    : user?.email
                    ? user.email[0].toUpperCase()
                    : 'U'}
                </AvatarFallback>
              </Avatar>
              <span className="hidden sm:block text-sm font-medium">
                {user?.name || user?.email || 'Admin'}
              </span>
              <ChevronDown
                size={16}
                className={`transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`}
              />
            </button>

            {/* User Dropdown Menu */}
            {isUserMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-surface rounded-lg shadow-lg border border-border z-50 py-1">
                <div className="px-4 py-3 border-b border-border">
                  <p className="text-sm font-medium text-text-primary">
                    {user?.name || 'Admin User'}
                  </p>
                  <p className="text-xs text-text-secondary">
                    {user?.email || '<EMAIL>'}
                  </p>
                </div>
                
                <button
                  onClick={handleProfileClick}
                  className="flex items-center w-full px-4 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200"
                >
                  <Settings size={16} className="mr-3" />
                  Profile Settings
                </button>
                
                <button
                  onClick={handleProfileClick}
                  className="flex items-center w-full px-4 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-surface-muted transition-colors duration-200"
                >
                  <HelpCircle size={16} className="mr-3" />
                  Help & Support
                </button>
                
                <div className="border-t border-border my-1"></div>
                
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-2 text-sm text-error hover:bg-error-50 transition-colors duration-200"
                >
                  <LogOut size={16} className="mr-3" />
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for mobile user menu */}
      {isUserMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsUserMenuOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;