import React, { useCallback, useEffect, useMemo } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot, $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND } from 'lexical';
import { cn } from '@/lib/utils';

const LexicalEditor = ({ 
  value, 
  onChange, 
  placeholder = "Start writing...", 
  className = "",
  readOnly = false,
  style = {},
  showToolbar = true 
}) => {
  // Simple theme configuration
  const theme = useMemo(() => ({
    text: {
      bold: 'font-bold',
      italic: 'italic',
      underline: 'underline',
      strikethrough: 'line-through',
    },
    paragraph: 'mb-2',
  }), []);

  // Error handler
  const onError = useCallback((error) => {
    console.error('[LexicalEditor] Error:', error);
  }, []);

  const initialConfig = useMemo(() => ({
    namespace: 'LexicalEditor',
    theme,
    onError,
  }), [theme, onError]);

  return (
    <div className={cn("border border-border rounded-lg bg-white flex flex-col", className)} style={style}>
      <LexicalComposer initialConfig={initialConfig}>
        {showToolbar && !readOnly && <SimpleToolbar />}
        <div className="flex-1 p-3 min-h-[200px]">
          <RichTextPlugin
            contentEditable={
              <ContentEditable 
                readOnly={readOnly}
                className="outline-none min-h-[180px] w-full"
                aria-placeholder={placeholder}
                placeholder={<div className="text-gray-400">{placeholder}</div>}
              />
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <HistoryPlugin />
          <AutoFocusPlugin />
          <ValueSyncPlugin value={value} onChange={onChange} />
        </div>
      </LexicalComposer>
    </div>
  );
};

// Simple toolbar with basic formatting
const SimpleToolbar = () => {
  const [editor] = useLexicalComposerContext();
  const [isBold, setIsBold] = React.useState(false);
  const [isItalic, setIsItalic] = React.useState(false);
  const [isUnderline, setIsUnderline] = React.useState(false);

  const $updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      setIsBold(selection.hasFormat('bold'));
      setIsItalic(selection.hasFormat('italic'));
      setIsUnderline(selection.hasFormat('underline'));
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        $updateToolbar();
      });
    });
  }, [editor, $updateToolbar]);

  const formatText = useCallback((format) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  }, [editor]);

  return (
    <div className="border-b border-border p-2 flex gap-1">
      <button
        type="button"
        className={cn(
          "px-2 py-1 rounded text-sm hover:bg-gray-100 transition-colors",
          isBold && "bg-gray-200"
        )}
        onMouseDown={(e) => {
          e.preventDefault();
          formatText('bold');
        }}
        title="Bold (Ctrl+B)"
      >
        <strong>B</strong>
      </button>
      
      <button
        type="button"
        className={cn(
          "px-2 py-1 rounded text-sm hover:bg-gray-100 transition-colors",
          isItalic && "bg-gray-200"
        )}
        onMouseDown={(e) => {
          e.preventDefault();
          formatText('italic');
        }}
        title="Italic (Ctrl+I)"
      >
        <em>I</em>
      </button>
      
      <button
        type="button"
        className={cn(
          "px-2 py-1 rounded text-sm hover:bg-gray-100 transition-colors",
          isUnderline && "bg-gray-200"
        )}
        onMouseDown={(e) => {
          e.preventDefault();
          formatText('underline');
        }}
        title="Underline (Ctrl+U)"
      >
        <u>U</u>
      </button>
    </div>
  );
};

// Plugin to sync value with external state
const ValueSyncPlugin = ({ value, onChange }) => {
  const [editor] = useLexicalComposerContext();
  
  // Convert Lexical editor state to string
  const convertToString = useCallback((editorState) => {
    try {
      let textContent = '';
      editorState.read(() => {
        const root = $getRoot();
        const children = root.getChildren();
        textContent = children.map(child => child.getTextContent()).join('\n');
      });
      return textContent;
    } catch (error) {
      console.error('[LexicalEditor] Error converting to string:', error);
      return '';
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      if (onChange) {
        const textContent = convertToString(editorState);
        onChange(textContent);
      }
    });
  }, [editor, onChange, convertToString]);

  return null;
};

export default LexicalEditor; 