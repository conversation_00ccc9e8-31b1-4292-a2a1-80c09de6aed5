import React from 'react';

/**
 * StandardPageHeader
 * A reusable header for pages, with title and description, for consistent look.
 * Props:
 *   - title: string
 *   - description: string (optional)
 *   - actions: ReactNode (optional, for right-aligned actions)
 *   - className: string (optional)
 */
const StandardPageHeader = ({ title, description, actions, className = '' }) => (
  <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 ${className}`}>
    <div>
      <h1 className="text-3xl font-bold text-text-primary mb-2">{title}</h1>
      {description && (
        <p className="text-base text-text-secondary">{description}</p>
      )}
    </div>
    {actions && (
      <div className="flex items-center space-x-4 mt-4 lg:mt-0">{actions}</div>
    )}
  </div>
);

export default StandardPageHeader;
