// shadcn/ui Components
// Use individual component imports from '@/components/ui/[component]' for better tree-shaking
export { Input } from './input';
export { 
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
} from './dropdown-menu';
export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from './dialog';
// Toast components are now handled by Sonner
export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
} from './select';
export { Avatar, AvatarImage, AvatarFallback } from './avatar';
export { Progress } from './progress';
export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from './tooltip';
export { ToggleGroup, ToggleGroupItem } from './toggle-group';

// shadcn/ui component exports
export { Alert, AlertTitle, AlertDescription } from './alert';
export {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel
} from './alert-dialog';
export { Badge, badgeVariants } from './badge';
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card';
export { Skeleton } from './skeleton';
export { Textarea } from './textarea';
export { Button, buttonVariants } from './button';
export { Checkbox } from './checkbox';
export { Label } from './label';
export { Separator } from './separator';
export { Tabs, TabsList, TabsTrigger, TabsContent } from './tabs';
export { Popover, PopoverTrigger, PopoverContent } from './popover';

// Legacy Components (keeping for backward compatibility)
export { Spinner, spinnerVariants } from './spinner';

// Legacy Components (keeping for backward compatibility)
export { default as FileIcon } from './FileIcon';
export { default as Header } from './Header';
export { default as LoadingSpinner } from './LoadingSpinner';
export { default as StandardPageHeader } from './StandardPageHeader';

export { default as ProtectedRoute, withAuth, useCanAccess } from './ProtectedRoute';
