import React from 'react';

const LoadingSpinner = ({ 
  size = 'medium', 
  text = 'Loading...', 
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8', 
    large: 'w-16 h-16'
  };

  const spinnerClass = sizeClasses[size] || sizeClasses.medium;

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      <div 
        className={`${spinnerClass} border-4 border-primary border-t-transparent rounded-full animate-spin`}
        role="status"
        aria-label={text}
      />
      {text && (
        <p className="text-text-secondary text-sm animate-pulse">
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;
