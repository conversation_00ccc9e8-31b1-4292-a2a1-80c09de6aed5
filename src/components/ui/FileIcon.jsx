import React from 'react';
import {
  FileText,
  File,
  Image,
  Archive,
  Code,
  Database,
  Presentation,
  Sheet,
  Music,
  Video,
  BookOpen,
  Package
} from 'lucide-react';

const FileIcon = ({ extension, size = 20, className = "" }) => {
  const getIconComponent = (ext) => {
    if (!ext) return File;
    
    const extension = ext.toLowerCase();
    
    // Document files
    if (['pdf', 'doc', 'docx', 'odt', 'rtf', 'txt', 'md', 'markdown'].includes(extension)) {
      return FileText;
    }
    
    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'svg', 'webp', 'ico'].includes(extension)) {
      return Image;
    }
    
    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(extension)) {
      return Archive;
    }
    
    // Code files
    if (['js', 'jsx', 'ts', 'tsx', 'html', 'css', 'scss', 'sass', 'less', 'php', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'go', 'rs', 'rb', 'swift', 'kt', 'scala', 'clj', 'hs', 'elm', 'dart', 'vue', 'svelte'].includes(extension)) {
      return Code;
    }
    
    // Data files
    if (['json', 'xml', 'yaml', 'yml', 'csv', 'sql', 'db', 'sqlite', 'mdb'].includes(extension)) {
      return Database;
    }
    
    // Spreadsheet files
    if (['xls', 'xlsx', 'ods', 'numbers'].includes(extension)) {
      return Sheet;
    }
    
    // Presentation files
    if (['ppt', 'pptx', 'odp', 'key'].includes(extension)) {
      return Presentation;
    }
    
    // Audio files
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'].includes(extension)) {
      return Music;
    }
    
    // Video files
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
      return Video;
    }
    
    // E-book files
    if (['epub', 'mobi', 'azw', 'azw3', 'fb2'].includes(extension)) {
      return BookOpen;
    }
    
    // Package/installer files
    if (['exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'appimage'].includes(extension)) {
      return Package;
    }
    
    // Default
    return File;
  };

  const getIconColor = (ext) => {
    if (!ext) return "text-gray-500";
    
    const extension = ext.toLowerCase();
    
    // PDF - red
    if (extension === 'pdf') return "text-red-500";
    
    // Word documents - blue
    if (['doc', 'docx'].includes(extension)) return "text-blue-500";
    
    // Excel - green
    if (['xls', 'xlsx'].includes(extension)) return "text-green-500";
    
    // PowerPoint - orange
    if (['ppt', 'pptx'].includes(extension)) return "text-orange-500";
    
    // Images - purple
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'svg', 'webp', 'ico'].includes(extension)) {
      return "text-purple-500";
    }
    
    // Code files - yellow
    if (['js', 'jsx', 'ts', 'tsx', 'html', 'css', 'scss', 'sass', 'less', 'php', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'go', 'rs', 'rb', 'swift', 'kt', 'scala', 'clj', 'hs', 'elm', 'dart', 'vue', 'svelte'].includes(extension)) {
      return "text-yellow-600";
    }
    
    // Archives - indigo
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(extension)) {
      return "text-indigo-500";
    }
    
    // Data files - cyan
    if (['json', 'xml', 'yaml', 'yml', 'csv', 'sql', 'db', 'sqlite', 'mdb'].includes(extension)) {
      return "text-cyan-500";
    }
    
    // Audio - pink
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'].includes(extension)) {
      return "text-pink-500";
    }
    
    // Video - violet
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
      return "text-violet-500";
    }
    
    // Text files - gray
    if (['txt', 'md', 'markdown', 'rtf', 'odt'].includes(extension)) {
      return "text-gray-600";
    }
    
    // Default
    return "text-gray-500";
  };

  const IconComponent = getIconComponent(extension);
  const iconColor = getIconColor(extension);
  
  return (
    <IconComponent 
      size={size} 
      className={`${iconColor} ${className}`}
    />
  );
};

export default FileIcon;
