import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { CheckCircle, Clock, AlertCircle, Loader, XCircle } from 'lucide-react'

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        // Status-based variants for compatibility with StatusBadge
        processed: "bg-green-100 text-green-800 border border-green-200 rounded-full px-3 py-1.5 gap-2",
        ready: "bg-green-100 text-green-800 border border-green-200 rounded-full px-3 py-1.5 gap-2",
        processing: "bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full px-3 py-1.5 gap-2",
        pending: "bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full px-3 py-1.5 gap-2",
        failed: "bg-red-100 text-red-800 border border-red-200 rounded-full px-3 py-1.5 gap-2",
        error: "bg-red-100 text-red-800 border border-red-200 rounded-full px-3 py-1.5 gap-2",
        unknown: "bg-gray-100 text-gray-800 border border-gray-200 rounded-full px-3 py-1.5 gap-2",
        status_default: "bg-gray-100 text-gray-800 border border-gray-200 rounded-full px-3 py-1.5 gap-2",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  status?: string
  size?: "sm" | "default"
}

// Utility function to get status icon
const getStatusIcon = (status: string, size: "sm" | "default" = "default") => {
  const iconSize = size === "sm" ? 14 : 16;

  switch (status?.toLowerCase()) {
    case 'processed':
    case 'ready':
    case 'completed':
      return <CheckCircle size={iconSize} />;
    case 'processing':
    case 'pending':
      return <Loader size={iconSize} className="animate-spin" />;
    case 'failed':
    case 'error':
      return <XCircle size={iconSize} />;
    case 'warning':
      return <AlertCircle size={iconSize} />;
    default:
      return <Clock size={iconSize} />;
  }
};

// Utility function to get status label
const getStatusLabel = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'processed':
      return 'Processed';
    case 'ready':
      return 'Ready';
    case 'processing':
      return 'Processing';
    case 'pending':
      return 'Pending';
    case 'failed':
      return 'Failed';
    case 'error':
      return 'Error';
    case 'warning':
      return 'Warning';
    default:
      return status || 'Unknown';
  }
};

// Utility function to get status variant
const getStatusVariant = (status: string) => {
  const normalizedStatus = status?.toLowerCase() || 'default';

  switch (normalizedStatus) {
    case 'processed':
    case 'ready':
    case 'completed':
      return 'processed';
    case 'processing':
    case 'pending':
      return 'processing';
    case 'failed':
    case 'error':
      return 'failed';
    case 'warning':
      return 'pending'; // Use pending styling for warnings
    default:
      return 'status_default';
  }
};

function Badge({ className, variant, status, size = "default", children, ...props }: BadgeProps) {
  // If status is provided, use status-based styling
  if (status) {
    const statusVariant = getStatusVariant(status);
    return (
      <div className={cn(badgeVariants({ variant: statusVariant as any }), className)} {...props}>
        {getStatusIcon(status, size)}
        <span className="capitalize">{getStatusLabel(status)}</span>
      </div>
    );
  }

  // Default Badge behavior
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props}>
      {children}
    </div>
  )
}

export { Badge, badgeVariants, getStatusIcon, getStatusLabel, getStatusVariant }
