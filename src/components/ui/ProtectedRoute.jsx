import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

/**
 * Protected Route Component
 * Provides comprehensive authentication and authorization checks
 * Follows modern React patterns with hooks and context
 */
const ProtectedRoute = ({ 
  children, 
  requiredPermissions = [], 
  requiredRole = null,
  requireSystemAccess = false,
  fallbackPath = '/auth/login'
}) => {
  const { 
    isAuthenticated, 
    isLoading, 
    user, 
    token,
    hasPermission, 
    hasRole, 
    hasSystemAccess,
    validateToken 
  } = useAuth();
  
  const location = useLocation();
  const [validationState, setValidationState] = useState({
    isValidating: false,
    isComplete: false,
    isValid: false
  });

  // Perform additional token validation on mount and when authentication changes
  useEffect(() => {
    const performValidation = async () => {
      // Skip validation if already loading or no token
      if (isLoading || !token || !isAuthenticated) {
        setValidationState({ isComplete: true, isValid: false, isValidating: false });
        return;
      }

      // Skip if already validated successfully
      if (validationState.isValid && validationState.isComplete) {
        return;
      }

      // If we have a user and token, assume valid to prevent unnecessary API calls
      if (user && token && isAuthenticated) {
        setValidationState({
          isValidating: false,
          isComplete: true,
          isValid: true
        });
        return;
      }

      setValidationState(prev => ({ ...prev, isValidating: true }));

      try {
        // Validate token with server
        const isValid = await validateToken();
        
        setValidationState({
          isValidating: false,
          isComplete: true,
          isValid: isValid
        });
      } catch (error) {
        console.warn('Token validation failed:', error);
        
        // On validation error, assume valid for better UX if we have user data
        setValidationState({
          isValidating: false,
          isComplete: true,
          isValid: !!user // If we have user data, assume valid
        });
      }
    };

    // Only perform validation when not loading and auth state is stable
    if (!isLoading) {
      performValidation();
    }
  }, [isAuthenticated, token, user, isLoading]); // Removed validationState.retryCount from deps

  // Show loading spinner while authentication is being checked
  if (isLoading || validationState.isValidating || !validationState.isComplete) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <LoadingSpinner 
          size="large" 
          text={
            isLoading 
              ? "Loading authentication..." 
              : validationState.isValidating 
                ? "Verifying access..." 
                : "Loading..."
          } 
        />
      </div>
    );
  }

  // Check if user is authenticated and validation passed
  if (!isAuthenticated || !user || !validationState.isValid) {
    console.warn('Authentication check failed:', {
      isAuthenticated,
      hasUser: !!user,
      validationPassed: validationState.isValid
    });
    
    // Redirect to login with return path
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // Check system access requirement
  if (requireSystemAccess && !hasSystemAccess()) {
    console.warn('Access denied: System access required');
    return (
      <Navigate 
        to="/unauthorized" 
        state={{ 
          from: location,
          reason: 'System access required'
        }} 
        replace 
      />
    );
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    console.warn(`Access denied: Role '${requiredRole}' required, user has '${user.role_name}'`);
    return (
      <Navigate 
        to="/unauthorized" 
        state={{ 
          from: location,
          reason: `Role '${requiredRole}' required`
        }} 
        replace 
      />
    );
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission => 
      hasPermission(permission)
    );

    if (!hasAllPermissions) {
      const missingPermissions = requiredPermissions.filter(permission => 
        !hasPermission(permission)
      );
      
      console.warn('Access denied: Missing permissions:', missingPermissions);
      return (
        <Navigate 
          to="/unauthorized" 
          state={{ 
            from: location,
            reason: `Missing permissions: ${missingPermissions.join(', ')}`
          }} 
          replace 
        />
      );
    }
  }

  // All checks passed, render the protected content
  return children;
};

/**
 * Higher-order component for protecting routes with specific requirements
 */
export const withAuth = (
  Component, 
  requirements = {}
) => {
  return (props) => (
    <ProtectedRoute {...requirements}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

/**
 * Hook for checking if current user can access a route
 */
export const useCanAccess = (requirements = {}) => {
  const { 
    isAuthenticated, 
    user, 
    hasPermission, 
    hasRole, 
    hasSystemAccess 
  } = useAuth();

  if (!isAuthenticated || !user) {
    return false;
  }

  if (requirements.requireSystemAccess && !hasSystemAccess()) {
    return false;
  }

  if (requirements.requiredRole && !hasRole(requirements.requiredRole)) {
    return false;
  }

  if (requirements.requiredPermissions) {
    const hasAllPermissions = requirements.requiredPermissions.every(permission => 
      hasPermission(permission)
    );
    if (!hasAllPermissions) {
      return false;
    }
  }

  return true;
};

export default ProtectedRoute;
