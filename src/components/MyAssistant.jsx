import { useLocal<PERSON><PERSON><PERSON>, AssistantRuntimeProvider } from "@assistant-ui/react";
import { Thread } from "./assistant-ui/thread";
import { MarkdownText } from "./assistant-ui/markdown-text";

export function MyAssistant({ agentId = null, useGraph = false, tools = [] }) {
  
  // Create an async iterator from the backend stream for LangGraph
  async function* createLangGraphStream({ messages, abortSignal, context }) {
    const formattedMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content
        ?.filter(part => part.type === "text")
        ?.map(part => ({ type: "text", text: part.text })) || []
    }));

    const response = await fetch("/api/agents/test-graph-chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        messages: formattedMessages,
        system: "",
        tools: context?.tools || []
      }),
      signal: abortSignal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error("No response body");
    }

    let buffer = "";
    let currentContent = "";
    // For tool call streaming
    let toolCallMap = {};

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          console.log('[LangGraphStream] Received line:', line);
          if (!line.trim()) continue;

          // Tool call streaming protocol: b:, c:, a:
          if (line.startsWith('b:')) {
            // Tool call begin
            try {
              const data = JSON.parse(line.slice(2));
              if (data.toolCallId && data.toolName) {
                toolCallMap[data.toolCallId] = {
                  toolName: data.toolName,
                  argsText: '',
                  result: undefined,
                };
              }
            } catch (e) {
              console.warn('Failed to parse b: line', line, e);
            }
            continue;
          }
          if (line.startsWith('c:')) {
            // Tool call args delta
            try {
              const data = JSON.parse(line.slice(2));
              if (data.toolCallId && data.argsTextDelta) {
                if (!toolCallMap[data.toolCallId]) {
                  toolCallMap[data.toolCallId] = { toolName: '', argsText: '', result: undefined };
                }
                toolCallMap[data.toolCallId].argsText += data.argsTextDelta;
              }
            } catch (e) {
              console.warn('Failed to parse c: line', line, e);
            }
            continue;
          }
          if (line.startsWith('a:')) {
            // Tool call result
            try {
              const data = JSON.parse(line.slice(2));
              if (data.toolCallId && data.result !== undefined) {
                const call = toolCallMap[data.toolCallId];
                if (call) {
                  call.result = data.result;
                  // Yield tool call in assistant-ui format
                  yield {
                    tool: {
                      name: call.toolName,
                      arguments: call.argsText ? JSON.parse(call.argsText) : {},
                      result: call.result,
                    },
                  };
                  // Remove from map
                  delete toolCallMap[data.toolCallId];
                }
              }
            } catch (e) {
              console.warn('Failed to parse a: line', line, e);
            }
            continue;
          }

          // Fallback: parse as text chunk (old format)
          if (line.startsWith('0:"')) {
            try {
              const textStart = line.indexOf('"') + 1;
              const textEnd = line.lastIndexOf('"');
              if (textStart > 0 && textEnd > textStart) {
                const encodedText = line.substring(textStart, textEnd);
                const decodedText = JSON.parse(`"${encodedText}"`);
                currentContent += decodedText;
                console.log('[LangGraphStream] Yielding content:', currentContent);
                yield {
                  content: [{ type: "text", text: currentContent }],
                };
              }
            } catch (e2) {
              console.warn("Fallback parsing also failed:", e2);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // Regular chat adapter for non-graph mode
  async function* createRegularStream({ messages, abortSignal, context }) {
    const formattedMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content
        ?.filter(part => part.type === "text")
        ?.map(part => ({ type: "text", text: part.text })) || []
    }));

    const response = await fetch("/api/agents/test-chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        messages: formattedMessages,
        system: "",
        tools: []
      }),
      signal: abortSignal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error("No response body");
    }

    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() && line.startsWith('0:"')) {
            try {
              const textStart = line.indexOf('"') + 1;
              const textEnd = line.lastIndexOf('"');
              if (textStart > 0 && textEnd > textStart) {
                const encodedText = line.substring(textStart, textEnd);
                const decodedText = JSON.parse(`"${encodedText}"`);
                
                yield {
                  choices: [{ delta: { content: decodedText } }]
                };
              }
            } catch (e) {
              console.warn("Failed to parse line:", line, e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  const chatAdapter = {
    async *run({ messages, abortSignal, context }) {
      try {
        if (useGraph) {
          // Use LangGraph streaming format
          const stream = createLangGraphStream({ messages, abortSignal, context });
          let content = "";
          const toolCalls = [];
          for await (const part of stream) {
            // Handle text content
            if (part.content && Array.isArray(part.content)) {
              const textPart = part.content.find(p => p.type === "text");
              if (textPart && textPart.text) {
                content = textPart.text;
              }
            }

            // Handle tool calls (from our custom stream)
            if (part.tool) {
              // Remove any previous tool call with same id (for idempotency)
              const idx = toolCalls.findIndex(tc => tc.toolCallId === (part.tool.toolCallId || part.tool.name));
              if (idx !== -1) {
                toolCalls.splice(idx, 1);
              }
              toolCalls.push({
                toolCallId: part.tool.toolCallId || part.tool.name,
                toolName: part.tool.name,
                args: part.tool.arguments || {},
                argsText: part.tool.arguments ? JSON.stringify(part.tool.arguments) : "",
                result: part.tool.result,
              });
            }

            // Yield current state
            yield {
              content: [
                ...(content ? [{ type: "text", text: content }] : []),
                ...toolCalls.map(tc => ({
                  type: "tool-call",
                  toolCallId: tc.toolCallId,
                  toolName: tc.toolName,
                  args: tc.args,
                  argsText: tc.argsText,
                  result: tc.result,
                })),
              ],
            };
          }
        } else {
          // Use regular streaming format
          const stream = createRegularStream({ messages, abortSignal, context });
          let text = "";
          for await (const part of stream) {
            text += part.choices[0]?.delta?.content || "";
            yield {
              content: [{ type: "text", text: text }],
            };
          }
        }
      } catch (error) {
        if (error.name === "AbortError") {
          return;
        }
        console.error("Chat adapter error:", error);
        yield {
          content: [{ type: "text", text: `Error: ${error.message}` }],
        };
      }
    },
  };

  const runtime = useLocalRuntime(chatAdapter, {
    maxToolRoundtrips: 5,
    context: { tools },
  });

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="h-full bg-white dark:bg-slate-900">
        <Thread />
      </div>
    </AssistantRuntimeProvider>
  );
}