import React from 'react';
import { Routes as RouterRoutes, Route } from 'react-router-dom';
import { publicRoutes } from './routes/public';
import { protectedRoutes } from './routes/protected';
import { ProtectedRoute } from './components/ui';
import ProtectedLayout from './layouts/ProtectedLayout';

const Routes = () => {
  return (
    <RouterRoutes>
      {/* Public Routes */}
      {publicRoutes.map(({ path, element: Element, title }) => (
        <Route
          key={path}
          path={path}
          element={<Element />}
        />
      ))}

      {/* Protected Routes */}
      {protectedRoutes.map(({ 
        path, 
        element: Element, 
        title,
        requiredPermissions = [],
        requiredRole = null,
        requireSystemAccess = false
      }) => (
        <Route
          key={path}
          path={path}
          element={
            <ProtectedRoute
              requiredPermissions={requiredPermissions}
              requiredRole={requiredRole}
              requireSystemAccess={requireSystemAccess}
            >
              <ProtectedLayout>
                <Element />
              </ProtectedLayout>
            </ProtectedRoute>
          }
        />
      ))}

      {/* Catch-all route for 404 */}
      <Route
        path="*"
        element={
          <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-text-primary mb-4">404</h1>
              <p className="text-text-secondary mb-6">Page not found</p>
              <a 
                href="/" 
                className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
              >
                Go Home
              </a>
            </div>
          </div>
        }
      />
    </RouterRoutes>
  );
};

export default Routes;