import { AppSidebar } from "../components/app-sidebar";
import { SiteHeader } from "../components/site-header";
import { SidebarProvider, SidebarInset } from "../components/ui/sidebar";
import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedLayout = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect to login if not authenticated
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!isLoading && !isAuthenticated) {
        navigate('/auth/login', {
          state: { from: location },
          replace: true,
        });
      }
    }, 1000);
    return () => clearTimeout(timeoutId);
  }, [isAuthenticated, isLoading, navigate, location]);

  // Optionally show a loading spinner while checking auth
  // if (isLoading) {
  //   return <div className="flex items-center justify-center min-h-screen bg-surface"><LoadingSpinner size="large" text="Verifying access..." /></div>;
  // }

  // If not authenticated, don't render the layout (navigation will happen in useEffect)
  if (!isAuthenticated && !isLoading) {
    return null;
  }

  return (
    <SidebarProvider
      style={{
        "--sidebar-width": "calc(var(--spacing) * 72)",
        "--header-height": "calc(var(--spacing) * 12)",
      }}
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2 p-6">
              {children}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default ProtectedLayout;
