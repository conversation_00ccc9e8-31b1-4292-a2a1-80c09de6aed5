import React, { createContext, useContext, useReducer, useEffect } from 'react';
import userService from '../services/userService';
import { toast } from 'sonner';

// Action types
const AuthActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_TOKEN: 'SET_TOKEN',
  LOGOUT: 'LOGOUT',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AuthActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
    case AuthActionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null
      };
    case AuthActionTypes.SET_TOKEN:
      return {
        ...state,
        token: action.payload
      };
    case AuthActionTypes.LOGOUT:
      return {
        ...initialState,
        isLoading: false
      };
    case AuthActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };
    case AuthActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };
    default:
      return state;
  }
};

// Auth context
const AuthContext = createContext();

// Auth provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        dispatch({ type: AuthActionTypes.SET_LOADING, payload: true });
        
        // First, sync auth state across storage mechanisms
        const syncSuccessful = userService.syncAuthState();
        if (!syncSuccessful) {
          dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
          return;
        }
        
        const token = userService.getToken();
        if (!token) {
          dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
          return;
        }

        // Set token first to avoid race conditions
        dispatch({ type: AuthActionTypes.SET_TOKEN, payload: token });

        // Get user data from token or storage
        const user = userService.getCurrentUser();
        if (user) {
          dispatch({ type: AuthActionTypes.SET_USER, payload: user });
        }

        // Validate token with server (with timeout protection)
        try {
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Token validation timeout')), 3000)
          );
          
          const validationPromise = userService.validateToken();
          const isValid = await Promise.race([validationPromise, timeoutPromise]);
          
          if (!isValid) {
            console.warn('Token validation failed, clearing auth');
            userService.clearAuth();
            dispatch({ type: AuthActionTypes.LOGOUT });
            return;
          }
          
          // Token is valid, ensure user is set if not already
          if (!user) {
            const validatedUser = userService.getCurrentUser();
            if (validatedUser) {
              dispatch({ type: AuthActionTypes.SET_USER, payload: validatedUser });
            }
          }
        } catch (validationError) {
          console.warn('Token validation error:', validationError);
          // If we have user data but validation fails due to network issues,
          // continue with cached data instead of logging out
          if (user) {
            console.log('Using cached user data due to validation error');
            // User is already set above, just continue
          } else {
            // No cached user data and validation failed - logout
            console.warn('No cached user data and validation failed, logging out');
            userService.clearAuth();
            dispatch({ type: AuthActionTypes.LOGOUT });
            return;
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        userService.clearAuth();
        dispatch({ type: AuthActionTypes.LOGOUT });
      } finally {
        dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
      }
    };

    initializeAuth();
  }, []);

  // Re-validate auth when user returns to the tab (with timeout) - throttled
  useEffect(() => {
    let lastValidation = 0;
    const VALIDATION_THROTTLE = 30000; // 30 seconds
    
    const handleVisibilityChange = async () => {
      const now = Date.now();
      if (!document.hidden && 
          state.isAuthenticated && 
          state.token && 
          (now - lastValidation) > VALIDATION_THROTTLE) {
        
        lastValidation = now;
        
        try {
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Visibility validation timeout')), 2000)
          );
          
          const validationPromise = userService.validateToken();
          const isValid = await Promise.race([validationPromise, timeoutPromise]);
          
          if (!isValid) {
            console.warn('Token validation failed on tab focus, clearing auth');
            userService.clearAuth();
            dispatch({ type: AuthActionTypes.LOGOUT });
          }
        } catch (error) {
          console.warn('Token validation failed on tab focus (ignoring):', error);
          // Don't clear auth for network errors or timeouts
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [state.isAuthenticated, state.token]);

  // Login function
  const login = async (email, password) => {
    try {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: true });
      dispatch({ type: AuthActionTypes.CLEAR_ERROR });

      const result = await userService.login(email, password);

      if (result.success) {
        dispatch({ type: AuthActionTypes.SET_USER, payload: result.user });
        dispatch({ type: AuthActionTypes.SET_TOKEN, payload: result.token });
        dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
        toast.success('Login successful!');
        return { success: true };
      } else {
        dispatch({ type: AuthActionTypes.SET_ERROR, payload: result.error });
        dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
        toast.error(result.error || 'Login failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error.message || 'An unexpected error occurred';
      dispatch({ type: AuthActionTypes.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: true });
      dispatch({ type: AuthActionTypes.CLEAR_ERROR });

      const result = await userService.register(userData);
      
      if (result.success) {
        toast.success(result.message || 'Registration successful!');
        return { success: true, data: result.data };
      } else {
        dispatch({ type: AuthActionTypes.SET_ERROR, payload: result.error });
        toast.error(result.error || 'Registration failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = error.message || 'An unexpected error occurred';
      dispatch({ type: AuthActionTypes.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await userService.logout();
      dispatch({ type: AuthActionTypes.LOGOUT });
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      userService.clearAuth();
      dispatch({ type: AuthActionTypes.LOGOUT });
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const result = await userService.refreshToken();
      if (result.success) {
        dispatch({ type: AuthActionTypes.SET_TOKEN, payload: result.token });
        return true;
      } else {
        logout();
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
      return false;
    }
  };

  // Update user function
  const updateUser = (userData) => {
    dispatch({ type: AuthActionTypes.SET_USER, payload: userData });
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AuthActionTypes.CLEAR_ERROR });
  };

  // Validate token function
  const validateToken = async () => {
    return await userService.validateToken();
  };

  // Check if user has permission
  const hasPermission = (permission) => {
    return state.user?.permissions?.includes(permission) || false;
  };

  // Check if user has role
  const hasRole = (role) => {
    return state.user?.role_name === role;
  };

  // Refresh authentication state manually
  const refreshAuthState = async () => {
    try {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: true });
      
      // Sync auth state
      const syncSuccessful = userService.syncAuthState();
      if (!syncSuccessful) {
        dispatch({ type: AuthActionTypes.LOGOUT });
        return false;
      }
      
      const token = userService.getToken();
      const user = userService.getCurrentUser();
      
      if (token && user) {
        dispatch({ type: AuthActionTypes.SET_TOKEN, payload: token });
        dispatch({ type: AuthActionTypes.SET_USER, payload: user });
        
        // Validate token
        const isValid = await userService.validateToken();
        if (!isValid) {
          userService.clearAuth();
          dispatch({ type: AuthActionTypes.LOGOUT });
          return false;
        }
        
        return true;
      } else {
        dispatch({ type: AuthActionTypes.LOGOUT });
        return false;
      }
    } catch (error) {
      console.error('Error refreshing auth state:', error);
      userService.clearAuth();
      dispatch({ type: AuthActionTypes.LOGOUT });
      return false;
    } finally {
      dispatch({ type: AuthActionTypes.SET_LOADING, payload: false });
    }
  };

  const contextValue = {
    // State
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    login,
    register,
    logout,
    refreshToken,
    updateUser,
    clearError,
    validateToken,
    
    // Utilities
    hasPermission,
    hasRole,
    refreshAuthState
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
