import React, { createContext, useContext, useState, useCallback } from 'react';
import { agentService, authService } from '../services';

/**
 * @typedef {Object} PlaygroundAgent
 * @property {string} id
 * @property {string} name
 * @property {string} description
 * @property {string} type
 * @property {'active' | 'inactive'} status
 * @property {string} [model]
 * @property {boolean} playground_ready
 */

/**
 * @typedef {Object} PlaygroundMessage
 * @property {'user' | 'assistant' | 'system'} role
 * @property {string} content
 * @property {Date} [timestamp]
 */

/**
 * @typedef {Object} PlaygroundState
 * @property {PlaygroundAgent | null} selectedAgent
 * @property {PlaygroundAgent[]} agents
 * @property {PlaygroundMessage[]} messages
 * @property {boolean} isLoading
 * @property {boolean} isStreaming
 * @property {string | null} error
 */

/**
 * @typedef {Object} PlaygroundActions
 * @property {function(PlaygroundAgent | null): void} selectAgent
 * @property {function(): Promise<void>} loadAgents
 * @property {function(string, boolean=): Promise<void>} sendMessage
 * @property {function(): void} clearMessages
 * @property {function(string | null): void} setError
 */

// Context
const PlaygroundContext = createContext(null);

// Provider
export function PlaygroundProvider({ children }) {
  const [state, setState] = useState({
    selectedAgent: null,
    agents: [],
    messages: [],
    isLoading: false,
    isStreaming: false,
    error: null,
  });

  // Load available agents
  const loadAgents = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await agentService.getPlaygroundAgents();
      if (response.data?.agents) {
        setState(prev => ({
          ...prev,
          agents: response.data.agents,
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Failed to load agents:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to load agents',
        isLoading: false
      }));
    }
  }, []);

  // Select an agent for testing
  const selectAgent = useCallback((agent) => {
    setState(prev => ({
      ...prev,
      selectedAgent: agent,
      messages: [], // Clear messages when switching agents
      error: null
    }));
  }, []);

  // Send message to playground
  const sendMessage = useCallback(async (message, stream = true) => {
    if (!message.trim()) return;

    const userMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isStreaming: true,
      error: null
    }));

    try {
      const requestBody = {
        messages: [...state.messages, userMessage].map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        stream
      };

      let response;
      
      if (state.selectedAgent) {
        // Agent-specific playground
        response = await agentService.playgroundChatWithAgent(
          state.selectedAgent.id,
          requestBody
        );
      } else {
        // General playground
        response = await agentService.generalPlaygroundChat(requestBody);
      }

      if (stream && response.body) {
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let assistantMessage = '';

        // Add assistant message placeholder
        setState(prev => ({
          ...prev,
          messages: [...prev.messages, {
            role: 'assistant',
            content: '',
            timestamp: new Date()
          }]
        }));

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.type === 'text' && data.content) {
                  assistantMessage += data.content;
                  
                  // Update the last message (assistant message)
                  setState(prev => ({
                    ...prev,
                    messages: prev.messages.map((msg, idx) => 
                      idx === prev.messages.length - 1 
                        ? { ...msg, content: assistantMessage }
                        : msg
                    )
                  }));
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e);
              }
            }
          }
        }
      } else {
        // Handle non-streaming response
        const data = await response.json();
        const assistantMessage = {
          role: 'assistant',
          content: data.message || 'No response received',
          timestamp: new Date()
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage]
        }));
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to send message. Please try again.'
      }));
    } finally {
      setState(prev => ({ ...prev, isStreaming: false }));
    }
  }, [state.messages, state.selectedAgent]);

  // Clear messages
  const clearMessages = useCallback(() => {
    setState(prev => ({ ...prev, messages: [], error: null }));
  }, []);

  // Set error
  const setError = useCallback((error) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const actions = {
    selectAgent,
    loadAgents,
    sendMessage,
    clearMessages,
    setError,
  };

  return (
    <PlaygroundContext.Provider value={{ state, actions }}>
      {children}
    </PlaygroundContext.Provider>
  );
}

// Hook
export function usePlayground() {
  const context = useContext(PlaygroundContext);
  if (!context) {
    throw new Error('usePlayground must be used within PlaygroundProvider');
  }
  return context;
}
