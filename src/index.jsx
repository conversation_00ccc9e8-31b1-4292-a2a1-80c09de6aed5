import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./styles/tailwind.css";
import "./styles/index.css";
import { Toaster } from './components/ui/sonner';

// Import react-pdf styles to fix TextLayer and AnnotationLayer warnings
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
  <>
    <App />
    <Toaster />
  </>
);
