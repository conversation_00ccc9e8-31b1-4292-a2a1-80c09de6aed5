// src/services/dashboardService.js
import api, { retryRequest } from './api';

/**
 * Dashboard Service
 * Handles all dashboard-related API calls for metrics and overview data
 */
class DashboardService {
  /**
   * Get dashboard overview metrics
   * @param {Object} params - Query parameters (timeRange, includeDetails, etc.)
   * @returns {Promise<Object>} Dashboard overview response
   */
  async getDashboardOverview(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/overview', { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Dashboard overview retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve dashboard overview',
        status: error.status
      };
    }
  }

  /**
   * Get KPI metrics
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} KPI metrics response
   */
  async getKPIMetrics(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/kpi', { params })
      );
      
      return {
        success: true,
        data: response.data.kpis || response.data,
        message: 'KPI metrics retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve KPI metrics',
        status: error.status
      };
    }
  }

  /**
   * Get performance chart data
   * @param {Object} params - Query parameters (timeRange, chartType, etc.)
   * @returns {Promise<Object>} Performance chart data response
   */
  async getPerformanceChartData(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/performance-charts', { params })
      );
      
      return {
        success: true,
        data: response.data.charts || response.data,
        message: 'Performance chart data retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve performance chart data',
        status: error.status
      };
    }
  }

  /**
   * Get recent activity feed
   * @param {Object} params - Query parameters (limit, offset, activityType, etc.)
   * @returns {Promise<Object>} Recent activity response
   */
  async getRecentActivity(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/recent-activity', { params })
      );
      
      return {
        success: true,
        data: response.data.activities || response.data,
        pagination: response.data.pagination,
        message: 'Recent activity retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve recent activity',
        status: error.status
      };
    }
  }

  /**
   * Get system notifications
   * @param {Object} params - Query parameters (limit, unreadOnly, etc.)
   * @returns {Promise<Object>} System notifications response
   */
  async getSystemNotifications(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/notifications', { params })
      );
      
      return {
        success: true,
        data: response.data.notifications || response.data,
        unreadCount: response.data.unread_count,
        message: 'System notifications retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve system notifications',
        status: error.status
      };
    }
  }

  /**
   * Mark notification as read
   * @param {string|number} notificationId - Notification ID
   * @returns {Promise<Object>} Mark as read response
   */
  async markNotificationAsRead(notificationId) {
    try {
      const response = await retryRequest(() => 
        api.put(`/dashboard/notifications/${notificationId}/read`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Notification marked as read'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to mark notification as read',
        status: error.status
      };
    }
  }

  /**
   * Mark all notifications as read
   * @returns {Promise<Object>} Mark all as read response
   */
  async markAllNotificationsAsRead() {
    try {
      const response = await retryRequest(() => 
        api.put('/dashboard/notifications/read-all')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'All notifications marked as read'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to mark all notifications as read',
        status: error.status
      };
    }
  }

  /**
   * Get system health status
   * @returns {Promise<Object>} System health response
   */
  async getSystemHealth() {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/system-health')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'System health status retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve system health status',
        status: error.status
      };
    }
  }

  /**
   * Get usage analytics
   * @param {Object} params - Query parameters (timeRange, groupBy, etc.)
   * @returns {Promise<Object>} Usage analytics response
   */
  async getUsageAnalytics(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/analytics', { params })
      );
      
      return {
        success: true,
        data: response.data.analytics || response.data,
        message: 'Usage analytics retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve usage analytics',
        status: error.status
      };
    }
  }

  /**
   * Get revenue metrics
   * @param {Object} params - Query parameters (timeRange, breakdown, etc.)
   * @returns {Promise<Object>} Revenue metrics response
   */
  async getRevenueMetrics(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/dashboard/revenue', { params })
      );
      
      return {
        success: true,
        data: response.data.revenue || response.data,
        message: 'Revenue metrics retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve revenue metrics',
        status: error.status
      };
    }
  }

  /**
   * Export dashboard data
   * @param {Object} exportConfig - Export configuration
   * @returns {Promise<Object>} Export response
   */
  async exportDashboardData(exportConfig) {
    try {
      const response = await retryRequest(() => 
        api.post('/dashboard/export', exportConfig, {
          responseType: 'blob'
        })
      );
      
      // Create download link for the blob
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', exportConfig.filename || 'dashboard-export.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return {
        success: true,
        message: 'Dashboard data exported successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to export dashboard data',
        status: error.status
      };
    }
  }

  /**
   * Get widget configuration
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise<Object>} Widget configuration response
   */
  async getWidgetConfiguration(userId) {
    try {
      const endpoint = userId ? `/dashboard/widgets/${userId}` : '/dashboard/widgets';
      const response = await retryRequest(() => api.get(endpoint));
      
      return {
        success: true,
        data: response.data.widgets || response.data,
        message: 'Widget configuration retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve widget configuration',
        status: error.status
      };
    }
  }

  /**
   * Update widget configuration
   * @param {Object} widgetConfig - Widget configuration data
   * @returns {Promise<Object>} Widget update response
   */
  async updateWidgetConfiguration(widgetConfig) {
    try {
      const response = await retryRequest(() => 
        api.put('/dashboard/widgets', widgetConfig)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Widget configuration updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update widget configuration',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }
}

// Export singleton instance
const dashboardService = new DashboardService();
export default dashboardService;