// src/services/userService.js
import api, { retryRequest } from './api';
import { setC<PERSON>ie, getCookie, removeCookie, areCookiesEnabled } from '../utils/cookies';
import { 
  isTokenExpired, 
  getUserFromToken, 
  validateTokenStructure, 
  shouldRefreshToken 
} from '../utils/tokenUtils';

// Constants
const TOKEN_COOKIE_NAME = 'assivy_auth_token';
const USER_STORAGE_KEY = 'assivy_user_data';
const REFRESH_TOKEN_COOKIE_NAME = 'assivy_refresh_token';

/**
 * Comprehensive User Service
 * Handles all user-related functionality including authentication, profile management, and settings
 * Implements secure token storage, validation, and refresh mechanisms
 */
class UserService {
  constructor() {
    this.refreshTokenPromise = null;
    this.tokenRefreshInterval = null;
    this.initializeTokenRefresh();
  }

  // ===============================
  // AUTHENTICATION METHODS
  // ===============================

  /**
   * User registration
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration response
   */
  async register(userData) {
    try {
      const response = await api.post('/users/register', {
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        company_name: userData.companyName
      });
      
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Registration successful'
      };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed',
        status: error.status,
        data: error.data
      };
    }
  }

  /**
   * User login with secure token storage
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Login response with user data and tokens
   */
  async login(email, password) {
    try {
      const response = await api.post('/auth/login', {
        email: email,
        password: password,
      });

      if (response.data.access_token) {
        // Store token securely
        this.setToken(response.data.access_token);
        
        // Store user data
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        // Store refresh token if provided
        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        // Start token refresh monitoring
        this.initializeTokenRefresh();

        return {
          success: true,
          token: response.data.access_token,
          user: response.data.user,
          message: 'Login successful'
        };
      }

      return {
        success: false,
        error: 'Invalid response from server'
      };
    } catch (error) {
      console.error('Login error:', error);

      // Handle specific error cases
      let errorMessage = 'Login failed';

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        if (status === 401) {
          errorMessage = data?.detail || 'Invalid email or password';
        } else if (status === 403) {
          errorMessage = data?.detail || 'Account access denied';
        } else if (status === 422) {
          errorMessage = 'Please check your email and password format';
        } else if (status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = data?.detail || data?.message || `Error ${status}`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Unable to connect to server. Please check your internet connection.';
      } else {
        // Other error
        errorMessage = error.message || 'An unexpected error occurred';
      }

      return {
        success: false,
        error: errorMessage,
        status: error.response?.status
      };
    }
  }

  /**
   * User logout with proper cleanup
   */
  async logout() {
    try {
      // Call logout endpoint if token exists
      const token = this.getToken();
      if (token) {
        try {
          await api.post('/auth/logout');
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn('Logout API call failed:', error);
        }
      }
    } catch (error) {
      console.warn('Logout error:', error);
    } finally {
      // Always clear local authentication data
      this.clearAuth();
    }
  }

  /**
   * Clear all authentication data
   */
  clearAuth() {
    // Clear tokens
    this.removeToken();
    this.removeRefreshToken();
    
    // Clear user data
    this.removeUserData();
    
    // Clear token refresh interval
    this.stopTokenRefresh();
  }

  /**
   * Get current authenticated user with validation
   * @returns {Object|null} User data or null if not authenticated
   */
  getCurrentUser() {
    try {
      // First check if token is valid
      const token = this.getToken();
      if (!token || isTokenExpired(token)) {
        return null;
      }

      // Try to get user from token first (most up-to-date)
      const userFromToken = getUserFromToken(token);
      if (userFromToken && userFromToken.id) {
        return userFromToken;
      }

      // Fallback to stored user data
      const userStr = this.getUserData();
      if (userStr) {
        return JSON.parse(userStr);
      }

      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Get user token with validation
   * @returns {string|null} User token or null if not authenticated or expired
   */
  getToken() {
    try {
      // Try to get from secure cookie first
      if (areCookiesEnabled()) {
        const token = getCookie(TOKEN_COOKIE_NAME);
        if (token && !isTokenExpired(token)) {
          return token;
        }
      }

      // Fallback to localStorage for compatibility
      const token = localStorage.getItem('authToken');
      if (token && !isTokenExpired(token)) {
        return token;
      }

      return null;
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated with comprehensive validation
   * @returns {boolean} True if user is authenticated and token is valid
   */
  isAuthenticated() {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    const validation = validateTokenStructure(token);
    return validation.isValid;
  }

  /**
   * Refresh access token
   * @returns {Promise<Object>} Refresh response
   */
  async refreshToken() {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshTokenPromise) {
      return this.refreshTokenPromise;
    }

    this.refreshTokenPromise = this._performTokenRefresh();
    
    try {
      const result = await this.refreshTokenPromise;
      return result;
    } finally {
      this.refreshTokenPromise = null;
    }
  }

  /**
   * Internal token refresh implementation
   * @private
   */
  async _performTokenRefresh() {
    try {
      const refreshToken = this.getRefreshToken() || this.getToken();
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post('/auth/refresh', {
        refresh_token: refreshToken
      });

      if (response.data.access_token) {
        this.setToken(response.data.access_token);
        
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        return {
          success: true,
          token: response.data.access_token,
          user: response.data.user
        };
      }

      throw new Error('Invalid refresh response');
    } catch (error) {
      console.error('Token refresh failed:', error);
      
      // If refresh fails, clear auth state
      this.clearAuth();
      
      return {
        success: false,
        error: error.message || 'Token refresh failed'
      };
    }
  }

  /**
   * Validate token by checking with server
   * @returns {Promise<boolean>} True if token is valid
   */
  async validateToken() {
    try {
      const token = this.getToken();
      if (!token) {
        return false;
      }

      // Check token structure first (local validation)
      const validation = validateTokenStructure(token);
      if (!validation.isValid) {
        console.warn('Token validation failed (structure):', validation.errors);
        return false;
      }

      // Perform server validation
      try {
        const response = await api.get('/auth/me');
        
        // Only update user data if it's significantly different to prevent loops
        if (response.data && response.data.user && response.data.user.id) {
          const currentUser = this.getCurrentUser();
          const userData = response.data.user;
          const needsUpdate = !currentUser ||
                            currentUser.id !== userData.id ||
                            currentUser.email !== userData.email ||
                            !currentUser.updated_at ||
                            (userData.updated_at && new Date(userData.updated_at) > new Date(currentUser.updated_at));

          if (needsUpdate) {
            this.setUserData(userData);
          }
        }
        
        return response.status === 200;
      } catch (error) {
        // Network errors shouldn't invalidate locally valid tokens
        if (error.status === 401) {
          return false;
        }
        
        // For other errors, assume token is valid locally
        console.warn('Token validation network error:', error);
        return true;
      }
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  // ===============================
  // PROFILE MANAGEMENT METHODS
  // ===============================

  /**
   * Get user profile by ID
   * @param {string|number} userId - User ID (optional, defaults to current user)
   * @returns {Promise<Object>} User profile response
   */
  async getUserProfile(userId) {
    try {
      const endpoint = userId ? `/users/${userId}` : '/users/me';
      const response = await retryRequest(() => api.get(endpoint));
      
      return {
        success: true,
        data: response.data,
        message: 'User profile retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve user profile',
        status: error.status
      };
    }
  }

  /**
   * Update user profile
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Profile update response
   */
  async updateUserProfile(profileData) {
    try {
      const response = await retryRequest(() => 
        api.put('/users/me', profileData)
      );
      
      // Update stored user data
      this.updateUserData(response.data);
      
      return {
        success: true,
        data: response.data,
        message: 'Profile updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update profile',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Upload profile image
   * @param {File} imageFile - Profile image file
   * @returns {Promise<Object>} Image upload response
   */
  async uploadProfileImage(imageFile) {
    try {
      const formData = new FormData();
      formData.append('file', imageFile);
      
      const response = await retryRequest(() => 
        api.post('/users/me/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Profile image uploaded successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to upload profile image',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Delete profile image
   * @returns {Promise<Object>} Delete response
   */
  async deleteProfileImage() {
    try {
      const response = await retryRequest(() => 
        api.delete('/users/me/image')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Profile image deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete profile image',
        status: error.status
      };
    }
  }

  // ===============================
  // PREFERENCES AND SETTINGS
  // ===============================

  /**
   * Get user preferences
   * @returns {Promise<Object>} User preferences response
   */
  async getUserPreferences() {
    try {
      const response = await retryRequest(() => 
        api.get('/users/me/preferences')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'User preferences retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve user preferences',
        status: error.status
      };
    }
  }

  /**
   * Update user preferences
   * @param {Object} preferences - Updated preferences
   * @returns {Promise<Object>} Update response
   */
  async updateUserPreferences(preferences) {
    try {
      const response = await retryRequest(() => 
        api.put('/users/me/preferences', preferences)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'User preferences updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update user preferences',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Get notification preferences
   * @returns {Promise<Object>} Notification preferences response
   */
  async getNotificationPreferences() {
    try {
      const response = await retryRequest(() => 
        api.get('/users/me/notification-preferences')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Notification preferences retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve notification preferences',
        status: error.status
      };
    }
  }

  /**
   * Update notification preferences
   * @param {Object} notificationPrefs - Updated notification preferences
   * @returns {Promise<Object>} Update response
   */
  async updateNotificationPreferences(notificationPrefs) {
    try {
      const response = await retryRequest(() => 
        api.put('/users/me/notification-preferences', notificationPrefs)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Notification preferences updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update notification preferences',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  // ===============================
  // SECURITY METHODS
  // ===============================

  /**
   * Get security settings
   * @returns {Promise<Object>} Security settings response
   */
  async getSecuritySettings() {
    try {
      const response = await retryRequest(() => 
        api.get('/users/me/security-settings')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Security settings retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve security settings',
        status: error.status
      };
    }
  }

  /**
   * Update security settings
   * @param {Object} securitySettings - Updated security settings
   * @returns {Promise<Object>} Update response
   */
  async updateSecuritySettings(securitySettings) {
    try {
      const response = await retryRequest(() => 
        api.put('/users/me/security-settings', securitySettings)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Security settings updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update security settings',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  // ===============================
  // TWO-FACTOR AUTHENTICATION
  // ===============================

  /**
   * Enable 2FA for current user
   * @returns {Promise<Object>} 2FA setup response
   */
  async enable2FA() {
    try {
      const response = await retryRequest(() => 
        api.post('/users/me/2fa/enable')
      );
      
      return {
        success: true,
        data: response.data,
        message: '2FA setup initiated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to enable 2FA',
        status: error.status
      };
    }
  }

  /**
   * Verify and confirm 2FA setup
   * @param {string} token - 2FA verification token
   * @returns {Promise<Object>} Verification response
   */
  async verify2FA(token) {
    try {
      const response = await retryRequest(() => 
        api.post('/users/me/2fa/verify', { token })
      );
      
      return {
        success: true,
        data: response.data,
        message: '2FA enabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to verify 2FA',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Disable 2FA for current user
   * @param {string} password - User password for confirmation
   * @returns {Promise<Object>} Disable response
   */
  async disable2FA(password) {
    try {
      const response = await retryRequest(() => 
        api.post('/users/me/2fa/disable', { password })
      );
      
      return {
        success: true,
        data: response.data,
        message: '2FA disabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to disable 2FA',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  // ===============================
  // API TOKEN MANAGEMENT
  // ===============================

  /**
   * Get API tokens for current user
   * @returns {Promise<Object>} API tokens response
   */
  async getAPITokens() {
    try {
      const response = await retryRequest(() => 
        api.get('/users/me/api-tokens')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'API tokens retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve API tokens',
        status: error.status
      };
    }
  }

  /**
   * Create new API token
   * @param {Object} tokenData - Token creation data
   * @returns {Promise<Object>} Token creation response
   */
  async createAPIToken(tokenData) {
    try {
      const response = await retryRequest(() => 
        api.post('/users/me/api-tokens', tokenData)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'API token created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to create API token',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Revoke API token
   * @param {string} tokenId - Token ID to revoke
   * @returns {Promise<Object>} Revocation response
   */
  async revokeAPIToken(tokenId) {
    try {
      const response = await retryRequest(() => 
        api.delete(`/users/me/api-tokens/${tokenId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'API token revoked successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to revoke API token',
        status: error.status
      };
    }
  }

  // ===============================
  // ACTIVITY AND ACCOUNT MANAGEMENT
  // ===============================

  /**
   * Get activity logs for current user
   * @param {Object} params - Query parameters for filtering
   * @returns {Promise<Object>} Activity logs response
   */
  async getActivityLogs(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/users/me/activity-logs', { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Activity logs retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve activity logs',
        status: error.status
      };
    }
  }

  /**
   * Delete user account
   * @param {Object} deleteData - Account deletion data including password
   * @returns {Promise<Object>} Account deletion response
   */
  async deleteAccount(deleteData) {
    try {
      const response = await retryRequest(() => 
        api.post('/users/me/delete-account', {
          password: deleteData.password,
          reason: deleteData.reason || '',
          feedback: deleteData.feedback || ''
        })
      );
      
      // Clear auth state after successful account deletion
      this.clearAuth();
      
      return {
        success: true,
        data: response.data,
        message: 'Account deletion initiated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete account',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  // ===============================
  // OAUTH METHODS
  // ===============================

  /**
   * Redirect to Google login page
   */
  async googleLogin() {
    try {
      const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      window.location.href = `${baseURL}/api/users/google/login`;
    } catch (error) {
      console.error('Google login redirect error:', error);
      throw new Error('Failed to redirect to Google login');
    }
  }

  /**
   * Handle Google login callback
   * @param {string} code - Google login code
   * @returns {Promise<Object>} Google login response
   */
  async handleGoogleCallback(code) {
    try {
      const response = await api.get(`/users/google/callback?code=${code}`);
      
      if (response.data.access_token) {
        this.setToken(response.data.access_token);
        
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        this.initializeTokenRefresh();

        return {
          success: true,
          token: response.data.access_token,
          user: response.data.user
        };
      }

      return {
        success: false,
        error: 'Invalid Google login response'
      };
    } catch (error) {
      console.error('Google callback error:', error);
      return {
        success: false,
        error: error.message || 'Google login failed'
      };
    }
  }

  // ===============================
  // PERMISSION AND ROLE METHODS
  // ===============================

  /**
   * Get user permissions
   * @returns {Array} Array of user permissions
   */
  getUserPermissions() {
    const user = this.getCurrentUser();
    return user?.permissions || [];
  }

  /**
   * Check if user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  hasPermission(permission) {
    const permissions = this.getUserPermissions();
    return permissions.includes(permission);
  }

  /**
   * Check if user has any of the specified permissions
   * @param {Array} permissions - Array of permissions to check
   * @returns {boolean} True if user has any of the permissions
   */
  hasAnyPermission(permissions) {
    const userPermissions = this.getUserPermissions();
    return permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * Check if user has all of the specified permissions
   * @param {Array} permissions - Array of permissions to check
   * @returns {boolean} True if user has all permissions
   */
  hasAllPermissions(permissions) {
    const userPermissions = this.getUserPermissions();
    return permissions.every(permission => userPermissions.includes(permission));
  }

  /**
   * Get user role
   * @returns {string|null} User role name
   */
  getUserRole() {
    const user = this.getCurrentUser();
    return user?.role_name || null;
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} True if user has role
   */
  hasRole(role) {
    return this.getUserRole() === role;
  }

  /**
   * Get user tenant ID
   * @returns {string|null} Tenant ID
   */
  getTenantId() {
    const user = this.getCurrentUser();
    return user?.tenant_id || null;
  }

  // ===============================
  // PRIVATE UTILITY METHODS
  // ===============================

  /**
   * Set token in secure storage
   * @private
   */
  setToken(token) {
    try {
      if (areCookiesEnabled()) {
        setCookie(TOKEN_COOKIE_NAME, token, {
          maxAge: 24 * 60 * 60, // 24 hours
          secure: import.meta.env.PROD,
          sameSite: 'strict'
        });
      }
      
      // Also store in localStorage for compatibility
      localStorage.setItem('authToken', token);
    } catch (error) {
      console.error('Error setting token:', error);
      // Fallback to localStorage only
      localStorage.setItem('authToken', token);
    }
  }

  /**
   * Remove token from all storage locations
   * @private
   */
  removeToken() {
    try {
      if (areCookiesEnabled()) {
        removeCookie(TOKEN_COOKIE_NAME);
      }
      localStorage.removeItem('authToken');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  /**
   * Set refresh token
   * @private
   */
  setRefreshToken(refreshToken) {
    try {
      if (areCookiesEnabled()) {
        setCookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
          maxAge: 30 * 24 * 60 * 60, // 30 days
          secure: import.meta.env.PROD,
          sameSite: 'strict'
        });
      }
      
      // Store in localStorage as fallback
      localStorage.setItem('refreshToken', refreshToken);
    } catch (error) {
      console.error('Error setting refresh token:', error);
      localStorage.setItem('refreshToken', refreshToken);
    }
  }

  /**
   * Get refresh token
   * @private
   */
  getRefreshToken() {
    try {
      if (areCookiesEnabled()) {
        const token = getCookie(REFRESH_TOKEN_COOKIE_NAME);
        if (token) return token;
      }
      
      return localStorage.getItem('refreshToken');
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return localStorage.getItem('refreshToken');
    }
  }

  /**
   * Remove refresh token
   * @private
   */
  removeRefreshToken() {
    try {
      if (areCookiesEnabled()) {
        removeCookie(REFRESH_TOKEN_COOKIE_NAME);
      }
      localStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Error removing refresh token:', error);
    }
  }

  /**
   * Set user data
   * @private
   */
  setUserData(userData) {
    try {
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
    } catch (error) {
      console.error('Error setting user data:', error);
    }
  }

  /**
   * Get user data
   * @private
   */
  getUserData() {
    try {
      return localStorage.getItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  /**
   * Remove user data
   * @private
   */
  removeUserData() {
    try {
      localStorage.removeItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Error removing user data:', error);
    }
  }

  /**
   * Update user data in storage
   * @param {Object} userData - Updated user data
   */
  updateUserData(userData) {
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      this.setUserData(updatedUser);
    }
  }

  /**
   * Synchronize authentication state across storage mechanisms
   * @returns {boolean} True if sync was successful
   */
  syncAuthState() {
    try {
      // Get token from both sources
      const cookieToken = areCookiesEnabled() ? getCookie(TOKEN_COOKIE_NAME) : null;
      const localStorageToken = localStorage.getItem('authToken');
      
      // Determine which token to use (prefer the one that's valid)
      let tokenToUse = null;
      
      if (cookieToken && !isTokenExpired(cookieToken)) {
        tokenToUse = cookieToken;
      } else if (localStorageToken && !isTokenExpired(localStorageToken)) {
        tokenToUse = localStorageToken;
      }
      
      if (tokenToUse) {
        // Sync the token to both storage mechanisms
        this.setToken(tokenToUse);
        
        // Ensure user data is available
        const user = this.getCurrentUser();
        if (!user) {
          const userFromToken = getUserFromToken(tokenToUse);
          if (userFromToken) {
            this.setUserData(userFromToken);
          }
        }
        
        return true;
      } else {
        // No valid token found, clear everything
        this.clearAuth();
        return false;
      }
    } catch (error) {
      console.error('Error syncing auth state:', error);
      return false;
    }
  }

  /**
   * Initialize automatic token refresh
   * @private
   */
  initializeTokenRefresh() {
    this.stopTokenRefresh();
    
    // Check every minute if token needs refresh
    this.tokenRefreshInterval = setInterval(() => {
      const token = this.getToken();
      if (token && shouldRefreshToken(token, 5)) { // Refresh 5 minutes before expiry
        this.refreshToken().catch(error => {
          console.error('Automatic token refresh failed:', error);
        });
      }
    }, 60000); // Check every minute
  }

  /**
   * Stop automatic token refresh
   * @private
   */
  stopTokenRefresh() {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }
}

// Export singleton instance
const userService = new UserService();
export default userService;
