// src/services/authService.js
import api from './api';
import { set<PERSON><PERSON>ie, getC<PERSON>ie, removeCookie, areCookiesEnabled } from '../utils/cookies';
import { 
  isTokenExpired, 
  getUserFromToken, 
  validateTokenStructure, 
  shouldRefreshToken 
} from '../utils/tokenUtils';

// Constants
const TOKEN_COOKIE_NAME = 'assivy_auth_token';
const USER_STORAGE_KEY = 'assivy_user_data';
const REFRESH_TOKEN_COOKIE_NAME = 'assivy_refresh_token';

/**
 * Modern Authentication Service
 * Implements secure token storage, validation, and refresh mechanisms
 * Following current web security best practices
 */
class AuthService {
  constructor() {
    this.refreshTokenPromise = null;
    this.tokenRefreshInterval = null;
    this.initializeTokenRefresh();
  }

  /**
   * User registration
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration response
   */
  async register(userData) {
    try {
      const response = await api.post('/users/register', {
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        company_name: userData.companyName
      });
      
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Registration successful'
      };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed',
        status: error.status,
        data: error.data
      };
    }
  }

  /**
   * User login with secure token storage
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Login response with user data and tokens
   */
  async login(email, password) {
    try {
      const response = await api.post('/users/token', 
        new URLSearchParams({
          username: email, // OAuth2 expects 'username' field
          password: password,
        }), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.access_token) {
        // Store token securely
        this.setToken(response.data.access_token);
        
        // Store user data
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        // Store refresh token if provided
        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        // Start token refresh monitoring
        this.initializeTokenRefresh();

        return {
          success: true,
          token: response.data.access_token,
          user: response.data.user,
          message: 'Login successful'
        };
      }

      return {
        success: false,
        error: 'Invalid response from server'
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed',
        status: error.status
      };
    }
  }

  /**
   * User logout with proper cleanup
   */
  async logout() {
    try {
      // Call logout endpoint if token exists
      const token = this.getToken();
      if (token) {
        try {
          await api.post('/users/logout', { token });
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn('Logout API call failed:', error);
        }
      }
    } catch (error) {
      console.warn('Logout error:', error);
    } finally {
      // Always clear local authentication data
      this.clearAuth();
    }
  }

  /**
   * Clear all authentication data
   */
  clearAuth() {
    // Clear tokens
    this.removeToken();
    this.removeRefreshToken();
    
    // Clear user data
    this.removeUserData();
    
    // Clear token refresh interval
    this.stopTokenRefresh();
  }

  /**
   * Get current authenticated user with validation
   * @returns {Object|null} User data or null if not authenticated
   */
  getCurrentUser() {
    try {
      // First check if token is valid
      const token = this.getToken();
      if (!token || isTokenExpired(token)) {
        return null;
      }

      // Try to get user from token first (most up-to-date)
      const userFromToken = getUserFromToken(token);
      if (userFromToken && userFromToken.id) {
        return userFromToken;
      }

      // Fallback to stored user data
      const userStr = this.getUserData();
      if (userStr) {
        return JSON.parse(userStr);
      }

      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Get user token with validation
   * @returns {string|null} User token or null if not authenticated or expired
   */
  getToken() {
    try {
      // Try to get from secure cookie first
      if (areCookiesEnabled()) {
        const token = getCookie(TOKEN_COOKIE_NAME);
        if (token && !isTokenExpired(token)) {
          return token;
        }
      }

      // Fallback to localStorage for compatibility
      const token = localStorage.getItem('authToken');
      if (token && !isTokenExpired(token)) {
        return token;
      }

      return null;
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated with comprehensive validation
   * @returns {boolean} True if user is authenticated and token is valid
   */
  isAuthenticated() {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    const validation = validateTokenStructure(token);
    return validation.isValid;
  }

  /**
   * Synchronize authentication state across storage mechanisms
   * This helps fix issues where localStorage and cookies get out of sync
   * @returns {boolean} True if sync was successful
   */
  syncAuthState() {
    try {
      // Get token from both sources
      const cookieToken = areCookiesEnabled() ? getCookie(TOKEN_COOKIE_NAME) : null;
      const localStorageToken = localStorage.getItem('authToken');
      
      // Determine which token to use (prefer the one that's valid)
      let tokenToUse = null;
      
      if (cookieToken && !isTokenExpired(cookieToken)) {
        tokenToUse = cookieToken;
      } else if (localStorageToken && !isTokenExpired(localStorageToken)) {
        tokenToUse = localStorageToken;
      }
      
      if (tokenToUse) {
        // Sync the token to both storage mechanisms
        this.setToken(tokenToUse);
        
        // Ensure user data is available
        const user = this.getCurrentUser();
        if (!user) {
          const userFromToken = getUserFromToken(tokenToUse);
          if (userFromToken) {
            this.setUserData(userFromToken);
          }
        }
        
        return true;
      } else {
        // No valid token found, clear everything
        this.clearAuth();
        return false;
      }
    } catch (error) {
      console.error('Error syncing auth state:', error);
      return false;
    }
  }

  /**
   * Validate token by checking with server
   * @returns {Promise<boolean>} True if token is valid
   */
  async validateToken() {
    try {
      const token = this.getToken();
      if (!token) {
        return false;
      }

      // Check token structure first (local validation)
      const validation = validateTokenStructure(token);
      if (!validation.isValid) {
        console.warn('Token validation failed (structure):', validation.errors);
        return false;
      }

      // For better UX, if token structure is valid and not expired,
      // perform server validation but don't block on network errors
      try {
        const response = await api.get('/users/me');
        
        // Update user data if response includes it
        if (response.data && response.data.id) {
          this.setUserData(response.data);
        }
        
        return response.status === 200;
      } catch (networkError) {
        console.warn('Token server validation failed, but token structure is valid:', networkError);
        
        // If token structure is valid but server validation fails due to network,
        // optimistically assume token is valid for better UX
        if (networkError.status === 401 || networkError.status === 403) {
          // These are authentication errors, not network errors
          return false;
        }
        
        // For network errors, assume token is valid if structure check passed
        // This prevents network issues from blocking access
        return true;
      }
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * Refresh the access token
   * @returns {Promise<Object>} Refresh result
   */
  async refreshToken() {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshTokenPromise) {
      return this.refreshTokenPromise;
    }

    this.refreshTokenPromise = this._performTokenRefresh();
    
    try {
      const result = await this.refreshTokenPromise;
      return result;
    } finally {
      this.refreshTokenPromise = null;
    }
  }

  /**
   * Internal token refresh implementation
   * @private
   */
  async _performTokenRefresh() {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        return { success: false, error: 'No refresh token available' };
      }

      const response = await api.post('/users/refresh', {
        refresh_token: refreshToken
      });

      if (response.data.access_token) {
        this.setToken(response.data.access_token);
        
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        return {
          success: true,
          token: response.data.access_token
        };
      }

      return { success: false, error: 'Invalid refresh response' };
    } catch (error) {
      console.error('Token refresh error:', error);
      
      // If refresh fails, clear all auth data
      if (error.status === 401 || error.status === 403) {
        this.clearAuth();
      }

      return {
        success: false,
        error: error.message || 'Token refresh failed'
      };
    }
  }

  /**
   * Set token with secure storage
   * @private
   */
  setToken(token) {
    try {
      // Store in secure cookie if available
      if (areCookiesEnabled()) {
        setCookie(TOKEN_COOKIE_NAME, token, {
          maxAge: 7 * 24 * 60 * 60, // 7 days
          secure: import.meta.env.PROD,
          sameSite: 'strict'
        });
      }
      
      // Also store in localStorage for compatibility
      localStorage.setItem('authToken', token);
    } catch (error) {
      console.error('Error setting token:', error);
      // Fallback to localStorage only
      localStorage.setItem('authToken', token);
    }
  }

  /**
   * Remove token from all storage locations
   * @private
   */
  removeToken() {
    try {
      if (areCookiesEnabled()) {
        removeCookie(TOKEN_COOKIE_NAME);
      }
      localStorage.removeItem('authToken');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  /**
   * Set refresh token
   * @private
   */
  setRefreshToken(refreshToken) {
    try {
      if (areCookiesEnabled()) {
        setCookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
          maxAge: 30 * 24 * 60 * 60, // 30 days
          secure: import.meta.env.PROD,
          sameSite: 'strict'
        });
      }
      
      // Store in localStorage as fallback
      localStorage.setItem('refreshToken', refreshToken);
    } catch (error) {
      console.error('Error setting refresh token:', error);
      localStorage.setItem('refreshToken', refreshToken);
    }
  }

  /**
   * Get refresh token
   * @private
   */
  getRefreshToken() {
    try {
      if (areCookiesEnabled()) {
        const token = getCookie(REFRESH_TOKEN_COOKIE_NAME);
        if (token) return token;
      }
      
      return localStorage.getItem('refreshToken');
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return localStorage.getItem('refreshToken');
    }
  }

  /**
   * Remove refresh token
   * @private
   */
  removeRefreshToken() {
    try {
      if (areCookiesEnabled()) {
        removeCookie(REFRESH_TOKEN_COOKIE_NAME);
      }
      localStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Error removing refresh token:', error);
    }
  }

  /**
   * Set user data
   * @private
   */
  setUserData(userData) {
    try {
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
    } catch (error) {
      console.error('Error setting user data:', error);
    }
  }

  /**
   * Get user data
   * @private
   */
  getUserData() {
    try {
      return localStorage.getItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  /**
   * Remove user data
   * @private
   */
  removeUserData() {
    try {
      localStorage.removeItem(USER_STORAGE_KEY);
    } catch (error) {
      console.error('Error removing user data:', error);
    }
  }

  /**
   * Initialize automatic token refresh
   * @private
   */
  initializeTokenRefresh() {
    this.stopTokenRefresh();
    
    // Check every minute if token needs refresh
    this.tokenRefreshInterval = setInterval(() => {
      const token = this.getToken();
      if (token && shouldRefreshToken(token, 5)) { // Refresh 5 minutes before expiry
        this.refreshToken().catch(error => {
          console.error('Automatic token refresh failed:', error);
        });
      }
    }, 60000); // Check every minute
  }

  /**
   * Stop automatic token refresh
   * @private
   */
  stopTokenRefresh() {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }
  /**
   * Redirect to Google login page
   */
  async googleLogin() {
    try {
      const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      window.location.href = `${baseURL}/api/users/google/login`;
    } catch (error) {
      console.error('Google login redirect error:', error);
      throw new Error('Failed to redirect to Google login');
    }
  }

  /**
   * Handle Google login callback
   * @param {string} code - Google login code
   * @returns {Promise<Object>} Google login response
   */
  async handleGoogleCallback(code) {
    try {
      const response = await api.get(`/users/google/callback?code=${code}`);
      
      if (response.data.access_token) {
        this.setToken(response.data.access_token);
        
        if (response.data.user) {
          this.setUserData(response.data.user);
        }

        if (response.data.refresh_token) {
          this.setRefreshToken(response.data.refresh_token);
        }

        this.initializeTokenRefresh();

        return {
          success: true,
          token: response.data.access_token,
          user: response.data.user
        };
      }

      return {
        success: false,
        error: 'Invalid Google login response'
      };
    } catch (error) {
      console.error('Google callback error:', error);
      return {
        success: false,
        error: error.message || 'Google login failed'
      };
    }
  }

  /**
   * Get user permissions
   * @returns {Array} Array of user permissions
   */
  getUserPermissions() {
    const user = this.getCurrentUser();
    return user?.permissions || [];
  }

  /**
   * Check if user has specific permission
   * @param {string} permission - Permission to check
   * @returns {boolean} True if user has permission
   */
  hasPermission(permission) {
    const permissions = this.getUserPermissions();
    return permissions.includes(permission);
  }

  /**
   * Check if user has any of the specified permissions
   * @param {Array} permissions - Array of permissions to check
   * @returns {boolean} True if user has any of the permissions
   */
  hasAnyPermission(permissions) {
    const userPermissions = this.getUserPermissions();
    return permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * Check if user has all of the specified permissions
   * @param {Array} permissions - Array of permissions to check
   * @returns {boolean} True if user has all permissions
   */
  hasAllPermissions(permissions) {
    const userPermissions = this.getUserPermissions();
    return permissions.every(permission => userPermissions.includes(permission));
  }

  /**
   * Get user role
   * @returns {string|null} User role name
   */
  getUserRole() {
    const user = this.getCurrentUser();
    return user?.role_name || null;
  }

  /**
   * Check if user has specific role
   * @param {string} role - Role to check
   * @returns {boolean} True if user has role
   */
  hasRole(role) {
    return this.getUserRole() === role;
  }

  /**
   * Get user tenant ID
   * @returns {string|null} Tenant ID
   */
  getTenantId() {
    const user = this.getCurrentUser();
    return user?.tenant_id || null;
  }

  /**
   * Update user data in storage
   * @param {Object} userData - Updated user data
   */
  updateUserData(userData) {
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      this.setUserData(updatedUser);
    }
  }
}

export default new AuthService();