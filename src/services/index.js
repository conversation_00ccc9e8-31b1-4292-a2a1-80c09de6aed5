// src/services/index.js
// Central export file for all services

export { default as api } from './api';
export { default as userService } from './userService';
// Backward compatibility alias
export { default as authService } from './userService';
export { default as agentService } from './agentService';
export { default as dashboardService } from './dashboardService';
export { default as resourceService } from './resourceService';
export { default as integrationService } from './integrationService';
export { default as organizationService } from './organizationService';
export { default as dataLibraryService } from './dataLibraryService';

// Re-export utility functions
export { retryRequest } from './api';

// Service types for TypeScript-like documentation
/**
 * @typedef {Object} ServiceResponse
 * @property {boolean} success - Indicates if the operation was successful
 * @property {*} data - Response data (varies by endpoint)
 * @property {string} message - Human-readable success message
 * @property {string} [error] - Error message (only present when success is false)
 * @property {number} [status] - HTTP status code
 * @property {Object} [validationErrors] - Validation errors object
 * @property {Object} [pagination] - Pagination information for list responses
 */

/**
 * @typedef {Object} PaginationInfo
 * @property {number} current_page - Current page number
 * @property {number} per_page - Items per page
 * @property {number} total - Total number of items
 * @property {number} total_pages - Total number of pages
 * @property {boolean} has_next - Whether there's a next page
 * @property {boolean} has_prev - Whether there's a previous page
 */

// Common service utilities
export const serviceUtils = {
  /**
   * Transform API response to consistent format
   * @param {Object} response - Raw API response
   * @param {string} successMessage - Success message
   * @returns {ServiceResponse} Transformed response
   */
  transformResponse: (response, successMessage = 'Operation completed successfully') => ({
    success: true,
    data: response.data,
    message: successMessage
  }),

  /**
   * Transform API error to consistent format
   * @param {Object} error - API error object
   * @param {string} defaultMessage - Default error message
   * @returns {ServiceResponse} Transformed error response
   */
  transformError: (error, defaultMessage = 'An error occurred') => ({
    success: false,
    error: error.userMessage || defaultMessage,
    status: error.status,
    validationErrors: error.data?.errors
  }),

  /**
   * Build query parameters for API requests
   * @param {Object} params - Parameters object
   * @returns {URLSearchParams} URL search parameters
   */
  buildQueryParams: (params = {}) => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, item));
        } else {
          searchParams.append(key, value);
        }
      }
    });
    
    return searchParams;
  },

  /**
   * Handle file download from blob response
   * @param {Blob} blob - Response blob
   * @param {string} filename - Download filename
   */
  downloadFile: (blob, filename) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  /**
   * Format error messages for user display
   * @param {Object} error - Error object
   * @returns {string} Formatted error message
   */
  formatErrorMessage: (error) => {
    if (error.validationErrors) {
      const errors = Object.values(error.validationErrors).flat();
      return errors.join(', ');
    }
    return error.error || 'An unexpected error occurred';
  },

  /**
   * Check if response indicates success
   * @param {ServiceResponse} response - Service response
   * @returns {boolean} Success status
   */
  isSuccess: (response) => response?.success === true,

  /**
   * Extract data from service response
   * @param {ServiceResponse} response - Service response
   * @returns {*} Response data or null
   */
  getData: (response) => (response?.success ? response.data : null),

  /**
   * Extract error message from service response
   * @param {ServiceResponse} response - Service response
   * @returns {string} Error message
   */
  getError: (response) => response?.error || 'Unknown error'
};