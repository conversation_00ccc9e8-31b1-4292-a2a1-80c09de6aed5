// src/services/agentService.js
import api, { retryRequest } from './api';

/**
 * Agent Service
 * Handles all AI agent-related API calls
 */
class AgentService {
  /**
   * Get all agents with pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Agents list response
   */
  async getAgents(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/agents', { params })
      );
      
      return {
        success: true,
        data: response.data.agents || response.data,
        pagination: response.data.pagination,
        message: 'Agents retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agents',
        status: error.status
      };
    }
  }

  /**
   * Get agent by ID
   * @param {string|number} agentId - Agent ID
   * @returns {Promise<Object>} Agent details response
   */
  async getAgentById(agentId) {
    try {
      const response = await retryRequest(() => 
        api.get(`/agents/${agentId}`)
      );
      
      return {
        success: true,
        data: response.data.agent || response.data,
        message: 'Agent details retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent details',
        status: error.status
      };
    }
  }

  /**
   * Create new agent
   * @param {Object} agentData - Agent creation data
   * @returns {Promise<Object>} Agent creation response
   */
  async createAgent(agentData) {
    try {
      const response = await retryRequest(() => 
        api.post('/agents', agentData)
      );
      
      return {
        success: true,
        data: response.data.agent || response.data,
        message: 'Agent created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to create agent',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Update existing agent
   * @param {string|number} agentId - Agent ID
   * @param {Object} agentData - Updated agent data
   * @returns {Promise<Object>} Agent update response
   */
  async updateAgent(agentId, agentData) {
    try {
      const response = await retryRequest(() => 
        api.put(`/agents/${agentId}`, agentData)
      );
      
      return {
        success: true,
        data: response.data.agent || response.data,
        message: 'Agent updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update agent',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Delete agent
   * @param {string|number} agentId - Agent ID
   * @returns {Promise<Object>} Agent deletion response
   */
  async deleteAgent(agentId) {
    try {
      const response = await retryRequest(() => 
        api.delete(`/agents/${agentId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Agent deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete agent',
        status: error.status
      };
    }
  }

  /**
   * Bulk delete agents
   * @param {Array} agentIds - Array of agent IDs
   * @returns {Promise<Object>} Bulk deletion response
   */
  async bulkDeleteAgents(agentIds) {
    try {
      const response = await retryRequest(() => 
        api.post('/agents/bulk-delete', { agent_ids: agentIds })
      );
      
      return {
        success: true,
        data: response.data,
        message: `${agentIds.length} agents deleted successfully`
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete agents',
        status: error.status
      };
    }
  }

  /**
   * Deploy agent
   * @param {string|number} agentId - Agent ID
   * @returns {Promise<Object>} Agent deployment response
   */
  async deployAgent(agentId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/agents/${agentId}/deploy`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Agent deployed successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to deploy agent',
        status: error.status
      };
    }
  }

  /**
   * Stop agent
   * @param {string|number} agentId - Agent ID
   * @returns {Promise<Object>} Agent stop response
   */
  async stopAgent(agentId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/agents/${agentId}/stop`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Agent stopped successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to stop agent',
        status: error.status
      };
    }
  }

  /**
   * Get agent performance metrics
   * @param {string|number} agentId - Agent ID
   * @param {Object} params - Query parameters (timeRange, metrics, etc.)
   * @returns {Promise<Object>} Agent metrics response
   */
  async getAgentMetrics(agentId, params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get(`/agents/${agentId}/metrics`, { params })
      );
      
      return {
        success: true,
        data: response.data.metrics || response.data,
        message: 'Agent metrics retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent metrics',
        status: error.status
      };
    }
  }

  /**
   * Get agent logs
   * @param {string|number} agentId - Agent ID
   * @param {Object} params - Query parameters (limit, offset, level, etc.)
   * @returns {Promise<Object>} Agent logs response
   */
  async getAgentLogs(agentId, params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get(`/agents/${agentId}/logs`, { params })
      );
      
      return {
        success: true,
        data: response.data.logs || response.data,
        pagination: response.data.pagination,
        message: 'Agent logs retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent logs',
        status: error.status
      };
    }
  }

  /**
   * Clone agent
   * @param {string|number} agentId - Agent ID to clone
   * @param {Object} cloneData - Clone configuration
   * @returns {Promise<Object>} Agent clone response
   */
  async cloneAgent(agentId, cloneData = {}) {
    try {
      const response = await retryRequest(() => 
        api.post(`/agents/${agentId}/clone`, cloneData)
      );
      
      return {
        success: true,
        data: response.data.agent || response.data,
        message: 'Agent cloned successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to clone agent',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Get agent templates
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Agent templates response
   */
  async getAgentTemplates(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/agents/templates', { params })
      );
      
      return {
        success: true,
        data: response.data.templates || response.data,
        message: 'Agent templates retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent templates',
        status: error.status
      };
    }
  }

  /**
   * Search agents
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Search results response
   */
  async searchAgents(query, filters = {}) {
    try {
      const params = {
        q: query,
        ...filters
      };
      
      const response = await retryRequest(() => 
        api.get('/agents/search', { params })
      );
      
      return {
        success: true,
        data: response.data.agents || response.data,
        pagination: response.data.pagination,
        message: 'Agent search completed successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to search agents',
        status: error.status
      };
    }
  }

  /**
   * Get available agents for playground
   * @returns {Promise<Object>} Playground agents response
   */
  async getPlaygroundAgents() {
    try {
      // For now, get all agents - in the future we can have a specific playground endpoint
      const response = await retryRequest(() => 
        api.get('/agents')
      );
      
      return {
        success: true,
        data: response.data.agents || response.data,
        message: 'Playground agents retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve playground agents',
        status: error.status
      };
    }
  }

  /**
   * Get agent playground information
   * @param {string|number} agentId - Agent ID
   * @returns {Promise<Object>} Agent playground info response
   */
  async getAgentPlaygroundInfo(agentId) {
    try {
      const response = await retryRequest(() => 
        api.get(`/agents/${agentId}/playground/info`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Agent playground info retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent playground info',
        status: error.status
      };
    }
  }

  /**
   * Chat with specific agent in playground (streaming)
   * @param {string|number} agentId - Agent ID
   * @param {string} message - User message
   * @param {Array} messageHistory - Previous messages
   * @returns {Promise<Response>} Streaming response
   */
  async chatWithAgent(agentId, message, messageHistory = []) {
    try {
      const response = await fetch(`${api.defaults.baseURL}/agents/${agentId}/playground/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...api.defaults.headers.common
        },
        body: JSON.stringify({
          message,
          message_history: messageHistory
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response; // Return the response for streaming
    } catch (error) {
      throw new Error(`Failed to chat with agent: ${error.message}`);
    }
  }

  /**
   * General playground chat (streaming)
   * @param {string} message - User message
   * @param {Array} messageHistory - Previous messages
   * @param {string|null} agentId - Optional agent ID
   * @returns {Promise<Response>} Streaming response
   */
  async playgroundChat(message, messageHistory = [], agentId = null) {
    try {
      const response = await fetch(`${api.defaults.baseURL}/playground/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...api.defaults.headers.common
        },
        body: JSON.stringify({
          message,
          conversation_history: messageHistory,
          agent_id: agentId,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response; // Return the response for streaming
    } catch (error) {
      throw new Error(`Failed to send playground message: ${error.message}`);
    }
  }

  /**
   * General chat using the agents/chat endpoint (for testing purposes)
   * @param {Object} request - Chat request object
   * @param {Array} request.messages - Messages in assistant-ui format
   * @param {string} [request.system] - System prompt
   * @param {Array} [request.tools] - Available tools
   * @returns {Promise<Response>} Streaming response
   */
  async generalChat(request) {
    try {
      const response = await fetch(`${api.defaults.baseURL}/agents/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...api.defaults.headers.common
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response; // Return the response for streaming
    } catch (error) {
      throw new Error(`Failed to send general chat message: ${error.message}`);
    }
  }

  /**
   * Test general chat using the agents/test-chat endpoint (no authentication required)
   * @param {Object} request - Chat request object
   * @param {Array} request.messages - Messages in assistant-ui format
   * @param {string} [request.system] - System prompt
   * @param {Array} [request.tools] - Available tools
   * @returns {Promise<Response>} Streaming response
   */
  async testGeneralChat(request) {
    try {
      const response = await fetch(`${api.defaults.baseURL}/agents/test-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // Note: No auth headers for test endpoint
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response; // Return the response for streaming
    } catch (error) {
      throw new Error(`Failed to send test chat message: ${error.message}`);
    }
  }

  /**
   * Process streaming response from playground chat
   * @param {Response} response - Fetch response object
   * @param {Function} onMessage - Callback for content chunks
   * @param {Function} onError - Callback for errors
   */
  async processStreamingResponse(response, onMessage, onError) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer
        
        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'content') {
                onMessage(parsed.content);
              } else if (parsed.type === 'error') {
                onError(new Error(parsed.content));
                return;
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }
    } catch (error) {
      onError(error);
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Test Gemini API connection
   * @returns {Promise<Object>} Test response
   */
  async testGeminiConnection() {
    try {
      const response = await retryRequest(() => 
        api.get('/playground/test-gemini')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Gemini API connection test completed'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to test Gemini API connection',
        status: error.status
      };
    }
  }

  /**
   * Get playground information
   * @returns {Promise<Object>} Playground info response
   */
  async getPlaygroundInfo() {
    try {
      const response = await retryRequest(() => 
        api.get('/playground/info')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Playground info retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve playground info',
        status: error.status
      };
    }
  }

  /**
   * Helper method for streaming chat with error handling
   * @param {string|null} agentId - Agent ID (null for general chat)
   * @param {string} message - User message
   * @param {Array} messageHistory - Previous messages
   * @param {Function} onMessage - Callback for content chunks
   * @param {Function} onError - Callback for errors
   */
  async streamChat(agentId, message, messageHistory, onMessage, onError) {
    try {
      const response = agentId 
        ? await this.chatWithAgent(agentId, message, messageHistory)
        : await this.playgroundChat(message, messageHistory);
      
      await this.processStreamingResponse(response, onMessage, onError);
    } catch (error) {
      onError(error);
    }
  }
}

// Export singleton instance
const agentService = new AgentService();
export default agentService;