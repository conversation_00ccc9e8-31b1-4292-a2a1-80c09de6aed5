// src/services/integrationService.js
import api, { retryRequest } from './api';

/**
 * Integration Service
 * Handles all integration and action-related API calls
 */
class IntegrationService {
  // ===== Integration Management =====
  
  /**
   * Get all available integrations
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Integrations list response
   */
  async getIntegrations(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/api/integrations', { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integrations retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve integrations',
        status: error.status
      };
    }
  }

  /**
   * Get integration by ID
   * @param {string} integrationId - Integration ID
   * @returns {Promise<Object>} Integration details response
   */
  async getIntegrationById(integrationId) {
    try {
      const response = await retryRequest(() => 
        api.get(`/api/integrations/${integrationId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration details retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve integration details',
        status: error.status
      };
    }
  }

  /**
   * Create new integration (admin only)
   * @param {Object} integrationData - Integration data
   * @returns {Promise<Object>} Creation response
   */
  async createIntegration(integrationData) {
    try {
      const response = await retryRequest(() => 
        api.post('/api/integrations', integrationData)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to create integration',
        status: error.status
      };
    }
  }

  /**
   * Update integration (admin only)
   * @param {string} integrationId - Integration ID
   * @param {Object} updates - Update data
   * @returns {Promise<Object>} Update response
   */
  async updateIntegration(integrationId, updates) {
    try {
      const response = await retryRequest(() => 
        api.put(`/api/integrations/${integrationId}`, updates)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update integration',
        status: error.status
      };
    }
  }

  /**
   * Delete integration (admin only)
   * @param {string} integrationId - Integration ID
   * @returns {Promise<Object>} Deletion response
   */
  async deleteIntegration(integrationId) {
    try {
      await retryRequest(() => 
        api.delete(`/api/integrations/${integrationId}`)
      );
      
      return {
        success: true,
        message: 'Integration deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete integration',
        status: error.status
      };
    }
  }

  // ===== Integration Actions =====

  /**
   * Get actions for an integration
   * @param {string} integrationId - Integration ID
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Actions list response
   */
  async getIntegrationActions(integrationId, params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get(`/api/integrations/${integrationId}/actions`, { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration actions retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve integration actions',
        status: error.status
      };
    }
  }

  /**
   * Add action to integration (admin only)
   * @param {string} integrationId - Integration ID
   * @param {string} actionId - Action ID
   * @returns {Promise<Object>} Response
   */
  async addActionToIntegration(integrationId, actionId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/integrations/${integrationId}/actions/${actionId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action added to integration successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to add action to integration',
        status: error.status
      };
    }
  }

  // ===== User Connections =====

  /**
   * Get user's integration connections
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Connections list response
   */
  async getUserConnections(params = {}) {
    try {
      const response = await retryRequest(() => 
        api.get('/api/integrations/connections', { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'User connections retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve user connections',
        status: error.status
      };
    }
  }

  /**
   * Get user connection for specific integration
   * @param {string} integrationId - Integration ID
   * @returns {Promise<Object>} Connection response
   */
  async getUserConnection(integrationId) {
    try {
      const response = await retryRequest(() => 
        api.get(`/api/integrations/connections/${integrationId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'User connection retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve user connection',
        status: error.status
      };
    }
  }

  /**
   * Create user connection to integration
   * @param {Object} connectionData - Connection data
   * @returns {Promise<Object>} Creation response
   */
  async createUserConnection(connectionData) {
    try {
      const response = await retryRequest(() => 
        api.post('/api/integrations/connections', connectionData)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Connection created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to create connection',
        status: error.status
      };
    }
  }

  /**
   * Update user connection
   * @param {string} connectionId - Connection ID
   * @param {Object} updates - Update data
   * @returns {Promise<Object>} Update response
   */
  async updateUserConnection(connectionId, updates) {
    try {
      const response = await retryRequest(() => 
        api.put(`/api/integrations/connections/${connectionId}`, updates)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Connection updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update connection',
        status: error.status
      };
    }
  }

  /**
   * Enable action for user connection
   * @param {string} connectionId - Connection ID
   * @param {string} actionName - Action name
   * @returns {Promise<Object>} Response
   */
  async enableConnectionAction(connectionId, actionName) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/integrations/connections/${connectionId}/actions/${actionName}/enable`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action enabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to enable action',
        status: error.status
      };
    }
  }

  /**
   * Disable action for user connection
   * @param {string} connectionId - Connection ID
   * @param {string} actionName - Action name
   * @returns {Promise<Object>} Response
   */
  async disableConnectionAction(connectionId, actionName) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/integrations/connections/${connectionId}/actions/${actionName}/disable`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action disabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to disable action',
        status: error.status
      };
    }
  }

  // ===== Agent Integration Management =====

  /**
   * Get agent's integrations
   * @param {string} agentId - Agent ID
   * @returns {Promise<Object>} Agent integrations response
   */
  async getAgentIntegrations(agentId) {
    try {
      const response = await retryRequest(() => 
        api.get(`/api/agents/${agentId}/integrations`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Agent integrations retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve agent integrations',
        status: error.status
      };
    }
  }

  /**
   * Add integration to agent
   * @param {string} agentId - Agent ID
   * @param {Object} integrationData - Integration configuration
   * @returns {Promise<Object>} Response
   */
  async addIntegrationToAgent(agentId, integrationData) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/agents/${agentId}/integrations`, integrationData)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration added to agent successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to add integration to agent',
        status: error.status
      };
    }
  }

  /**
   * Remove integration from agent
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Integration ID
   * @returns {Promise<Object>} Response
   */
  async removeIntegrationFromAgent(agentId, integrationId) {
    try {
      const response = await retryRequest(() => 
        api.delete(`/api/agents/${agentId}/integrations/${integrationId}`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Integration removed from agent successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to remove integration from agent',
        status: error.status
      };
    }
  }

  /**
   * Configure action for agent integration
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Integration ID
   * @param {Object} actionConfig - Action configuration
   * @returns {Promise<Object>} Response
   */
  async configureAgentAction(agentId, integrationId, actionConfig) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/agents/${agentId}/integrations/${integrationId}/actions`, actionConfig)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action configured successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to configure action',
        status: error.status
      };
    }
  }

  /**
   * Enable action for agent integration
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Integration ID
   * @param {string} actionId - Action ID
   * @returns {Promise<Object>} Response
   */
  async enableAgentAction(agentId, integrationId, actionId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/agents/${agentId}/integrations/${integrationId}/actions/${actionId}/enable`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action enabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to enable action',
        status: error.status
      };
    }
  }

  /**
   * Disable action for agent integration
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Integration ID
   * @param {string} actionId - Action ID
   * @returns {Promise<Object>} Response
   */
  async disableAgentAction(agentId, integrationId, actionId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/agents/${agentId}/integrations/${integrationId}/actions/${actionId}/disable`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action disabled successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to disable action',
        status: error.status
      };
    }
  }

  /**
   * Hide action for agent integration
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Integration ID
   * @param {string} actionId - Action ID
   * @returns {Promise<Object>} Response
   */
  async hideAgentAction(agentId, integrationId, actionId) {
    try {
      const response = await retryRequest(() => 
        api.post(`/api/agents/${agentId}/integrations/${integrationId}/actions/${actionId}/hide`)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Action hidden successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to hide action',
        status: error.status
      };
    }
  }

  /**
   * Get enabled actions for agent
   * @param {string} agentId - Agent ID
   * @param {string} integrationId - Optional integration filter
   * @returns {Promise<Object>} Enabled actions response
   */
  async getAgentEnabledActions(agentId, integrationId = null) {
    try {
      const params = integrationId ? { integration_id: integrationId } : {};
      const response = await retryRequest(() => 
        api.get(`/api/agents/${agentId}/actions/enabled`, { params })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Enabled actions retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve enabled actions',
        status: error.status
      };
    }
  }

  // ===== System Setup =====

  /**
   * Setup system integrations (admin only)
   * @returns {Promise<Object>} Setup response
   */
  async setupSystemIntegrations() {
    try {
      const response = await retryRequest(() => 
        api.post('/api/integrations/setup-system')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'System integrations set up successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to set up system integrations',
        status: error.status
      };
    }
  }
}

// Create and export a singleton instance
const integrationService = new IntegrationService();
export default integrationService;
