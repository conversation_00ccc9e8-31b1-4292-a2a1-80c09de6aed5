// src/services/api.js
import axios from 'axios';
import { toast } from 'sonner';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'),
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  withCredentials: true, // Enable sending cookies with cross-origin requests
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Track if we're currently refreshing token to prevent multiple simultaneous requests
let isRefreshing = false;
let failedQueue = [];

// Process the queue of failed requests after token refresh
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

// Request interceptor for authentication token injection and logging
api.interceptors.request.use(
  (config) => {
    // Dynamically import userService to avoid circular dependency
    const getToken = () => {
      try {
        // Try to get from secure cookie first
        const getCookie = (name) => {
          const value = `; ${document.cookie}`;
          const parts = value.split(`; ${name}=`);
          if (parts.length === 2) {
            const cookieValue = parts.pop().split(';').shift();
            return decodeURIComponent(cookieValue);
          }
          return null;
        };

        const cookieToken = getCookie('assivy_auth_token');
        if (cookieToken) return cookieToken;

        // Fallback to localStorage
        return localStorage.getItem('authToken');
      } catch (error) {
        console.error('Error getting token for request:', error);
        return localStorage.getItem('authToken');
      }
    };

    // Add auth token to requests if available
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Request logging for development
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
        headers: config.headers
      });
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
api.interceptors.response.use(
  (response) => {
    // Log successful responses in development
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data
      });
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle token expiration and refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If we're already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(() => {
          return api(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Dynamically import userService to avoid circular dependency
        const userServiceModule = await import('./userService');
        const userService = userServiceModule.default;

        const refreshResult = await userService.refreshToken();
        
        if (refreshResult.success) {
          processQueue(null, refreshResult.token);
          originalRequest.headers.Authorization = `Bearer ${refreshResult.token}`;
          return api(originalRequest);
        } else {
          // Refresh failed, redirect to login
          processQueue(new Error('Token refresh failed'), null);
          userService.clearAuth();
          
          // Only redirect if we're not already on a public page
          if (!window.location.pathname.startsWith('/auth/')) {
            window.location.href = '/auth/login';
          }
          
          return Promise.reject(new Error('Authentication failed'));
        }
      } catch (refreshError) {
        console.error('Token refresh error:', refreshError);
        processQueue(refreshError, null);
        
        // Clear auth and redirect to login
        try {
          const userServiceModule = await import('./userService');
          const userService = userServiceModule.default;
          userService.clearAuth();
        } catch (e) {
          // Fallback: clear localStorage manually
          localStorage.clear();
        }
        
        if (!window.location.pathname.startsWith('/auth/')) {
          window.location.href = '/auth/login';
        }
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // Create custom error object with enhanced information
    const customError = {
      message: error.response?.data?.detail || error.response?.data?.message || error.message || 'An error occurred',
      status: error.response?.status,
      data: error.response?.data,
      code: error.code,
      config: error.config
    };

    // Handle specific error cases with user-friendly messages
    switch (error.response?.status) {
      case 400:
        customError.message = error.response?.data?.detail || 'Bad request. Please check your input.';
        break;
      case 401:
        customError.message = 'Authentication required. Please log in.';
        break;
      case 403:
        customError.message = 'Access forbidden. You don\'t have permission to perform this action.';
        break;
      case 404:
        customError.message = 'Resource not found.';
        break;
      case 422:
        customError.message = 'Validation error. Please check your input.';
        if (error.response?.data?.detail) {
          // Handle FastAPI validation errors
          if (Array.isArray(error.response.data.detail)) {
            const validationErrors = error.response.data.detail.map(err => 
              `${err.loc?.join('.')} - ${err.msg}`
            ).join(', ');
            customError.message = `Validation error: ${validationErrors}`;
          }
        }
        break;
      case 429:
        customError.message = 'Too many requests. Please wait a moment and try again.';
        break;
      case 500:
        customError.message = 'Server error. Please try again later.';
        break;
      case 502:
        customError.message = 'Service temporarily unavailable.';
        break;
      case 503:
        customError.message = 'Service temporarily unavailable.';
        break;
      case 504:
        customError.message = 'Request timeout. Please try again.';
        break;
    }

    // Only show error toast for certain status codes to avoid duplicate notifications
    // Let the calling component handle specific error cases (like 409 conflicts)
    const shouldShowToast = error.response?.status !== 401 &&
                           error.response?.status !== 409 &&
                           !originalRequest._hideErrorToast;

    if (shouldShowToast) {
      toast.error(customError.message);
    }

    // Log errors in development
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.error('API Error:', {
        status: error.response?.status,
        url: error.config?.url,
        message: customError.message,
        data: error.response?.data
      });
    }

    return Promise.reject(customError);
  }
);

export default api;

// List of permanent error status codes that should not be retried
const PERMANENT_ERROR_STATUSES = new Set([
  400, // Bad Request - client error that won't be fixed by retrying
  401, // Unauthorized - authentication required
  403, // Forbidden - permission denied
  404, // Not Found - resource doesn't exist
  405, // Method Not Allowed
  409, // Conflict - resource conflict
  410, // Gone - resource permanently removed
  422, // Unprocessable Entity - validation error
  429, // Too Many Requests - rate limit exceeded
  451, // Unavailable For Legal Reasons
]);

// Check if an error is permanent and should not be retried
const isPermanentError = (error) => {
  // Check status code
  if (error.status && PERMANENT_ERROR_STATUSES.has(error.status)) {
    return true;
  }
  
  // Check for specific error messages that indicate permanent errors
  const permanentErrorMessages = [
    'validation error',
    'invalid input',
    'resource not found',
    'permission denied',
    'unauthorized',
    'conflict',
    'rate limit exceeded'
  ];
  
  if (error.message && permanentErrorMessages.some(msg => 
    error.message.toLowerCase().includes(msg)
  )) {
    return true;
  }
  
  return false;
};

// Utility function for retrying failed requests
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry if it's a permanent error
      if (isPermanentError(error)) {
        console.warn('Permanent error detected, skipping retry:', error);
        throw error;
      }
      
      // Only retry if we haven't reached max retries
      if (i < maxRetries - 1) {
        const backoffDelay = delay * Math.pow(2, i); // Exponential backoff
        console.info(`Retrying request (attempt ${i + 1}/${maxRetries}) after ${backoffDelay}ms`);
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
      }
    }
  }
  
  throw lastError;
};

// Toast notifications are now handled by Sonner directly.