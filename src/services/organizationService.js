// src/services/organizationService.js
import api, { retryRequest } from './api';

/**
 * Organization Service
 * Handles all organization-related API calls for settings and configuration
 */
class OrganizationService {
  /**
   * Get organization settings
   * @returns {Promise<Object>} Organization settings response
   */
  async getOrganizationSettings() {
    try {
      const response = await retryRequest(() => api.get('/organization/settings'));
      
      return {
        success: true,
        data: response.data,
        message: 'Organization settings retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to retrieve organization settings',
        status: error.status
      };
    }
  }

  /**
   * Update organization settings
   * @param {Object} organizationData - Updated organization data
   * @returns {Promise<Object>} Organization update response
   */
  async updateOrganizationSettings(organizationData) {
    try {
      const response = await retryRequest(() => 
        api.put('/organization/settings', organizationData)
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Organization settings updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to update organization settings',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Upload organization logo
   * @param {File} logoFile - Logo image file
   * @returns {Promise<Object>} Logo upload response
   */
  async uploadOrganizationLogo(logoFile) {
    try {
      const formData = new FormData();
      formData.append('file', logoFile);
      
      const response = await retryRequest(() => 
        api.post('/organization/logo', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Organization logo uploaded successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to upload organization logo',
        status: error.status,
        validationErrors: error.data?.errors
      };
    }
  }

  /**
   * Delete organization logo
   * @returns {Promise<Object>} Delete response
   */
  async deleteOrganizationLogo() {
    try {
      const response = await retryRequest(() => 
        api.delete('/organization/logo')
      );
      
      return {
        success: true,
        data: response.data,
        message: 'Organization logo deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.userMessage || 'Failed to delete organization logo',
        status: error.status
      };
    }
  }
}

// Export singleton instance
const organizationService = new OrganizationService();
export default organizationService;
