import api from './api';

// Mock data for development
const MOCK_LIBRARIES = [
  {
    id: '1',
    name: 'Research Documents',
    description: 'Collection of research papers and academic documents',
    resource_count: 15,
    total_size: 2048000,
    file_ids: ['f1', 'f2', 'f3'],
    article_ids: ['a1', 'a2'],
    webpage_ids: ['w1'],
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z',
    owner_name: '<PERSON>'
  },
  {
    id: '2',
    name: 'Training Materials',
    description: 'Employee training and onboarding resources',
    resource_count: 8,
    total_size: 1024000,
    file_ids: ['f4', 'f5'],
    article_ids: ['a3', 'a4', 'a5'],
    webpage_ids: ['w2', 'w3'],
    created_at: '2024-01-10T09:15:00Z',
    updated_at: '2024-01-18T16:20:00Z',
    owner_name: '<PERSON>'
  },
  {
    id: '3',
    name: 'Product Documentation',
    description: 'Technical documentation and user guides',
    resource_count: 22,
    total_size: 3072000,
    file_ids: ['f6', 'f7', 'f8', 'f9'],
    article_ids: ['a6', 'a7', 'a8'],
    webpage_ids: ['w4', 'w5'],
    created_at: '2024-01-05T11:00:00Z',
    updated_at: '2024-01-22T13:30:00Z',
    owner_name: 'Mike Johnson'
  },
  {
    id: '4',
    name: 'test',
    description: 'test',
    resource_count: 0,
    total_size: 0,
    file_ids: [],
    article_ids: [],
    webpage_ids: [],
    created_at: '2025-07-25T00:00:00Z',
    updated_at: '2025-07-25T00:00:00Z',
    owner_name: 'Current User'
  }
];

const MOCK_RESOURCES = [
  { id: 'f1', name: 'Research Paper 1.pdf', type: 'file', description: 'AI research findings' },
  { id: 'f2', name: 'Data Analysis.xlsx', type: 'file', description: 'Statistical analysis' },
  { id: 'a1', name: 'Literature Review', type: 'article', description: 'Comprehensive review' },
  { id: 'w1', name: 'Research Portal', type: 'web', description: 'Online research database' }
];

class DataLibraryService {
  constructor() {
    this.baseURL = '/libraries';
    this.useMockData = false; // Always use real API data
  }

  // Error handling helper
  handleError(error) {
    // Don't log here - let the api.js interceptor handle logging to avoid duplicate logs
    // The error is already processed by api.js interceptor, so just pass it through

    if (error.response) {
      const { status, data } = error.response;
      const message = data?.detail || data?.message || `HTTP ${status} Error`;
      return {
        message,
        status,
        data: data,
        details: data
      };
    }

    if (error.request) {
      return {
        message: 'Network error - please check your connection',
        status: 0
      };
    }

    // For other errors, preserve the original status if available
    return {
      message: error.message || 'An unexpected error occurred',
      status: error.status || error.code || 500
    };
  }

  // Library Management Methods

  // Create a new library
  async createLibrary(libraryData) {
    if (this.useMockData) {
      const newLibrary = {
        id: Date.now().toString(),
        ...libraryData,
        resource_count: 0,
        total_size: 0,
        file_ids: [],
        article_ids: [],
        webpage_ids: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        owner_name: 'Current User'
      };
      MOCK_LIBRARIES.push(newLibrary);
      return Promise.resolve(newLibrary);
    }

    try {
      const response = await api.post(this.baseURL, libraryData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get all libraries with pagination and filtering
  async getLibraries(options = {}) {
    if (this.useMockData) {
      return this.getMockLibraries(options);
    }

    try {
      const {
        skip = 0,
        limit = 20,
        search = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;

      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (search) params.append('search', search);

      const response = await api.get(`${this.baseURL}?${params}`);

      // Handle different response formats
      let libraries, total;
      if (Array.isArray(response.data)) {
        // Direct array response from backend (legacy format)
        libraries = response.data;
        total = response.data.length;
      } else {
        // Object response with libraries property (new format)
        libraries = response.data.libraries || response.data.results || [];
        total = response.data.total || libraries.length;
      }

      return {
        libraries,
        total,
        skip: response.data.skip || skip,
        limit: response.data.limit || limit
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get a specific library by ID
  async getLibrary(libraryId) {
    if (this.useMockData) {
      return this.getMockLibrary(libraryId);
    }

    try {
      const response = await api.get(`${this.baseURL}/${libraryId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Update a library
  async updateLibrary(libraryId, updateData) {
    if (this.useMockData) {
      const index = MOCK_LIBRARIES.findIndex(lib => lib.id === libraryId);
      if (index === -1) {
        throw { message: 'Library not found', status: 404 };
      }
      MOCK_LIBRARIES[index] = {
        ...MOCK_LIBRARIES[index],
        ...updateData,
        updated_at: new Date().toISOString()
      };
      return Promise.resolve(MOCK_LIBRARIES[index]);
    }

    try {
      const response = await api.put(`${this.baseURL}/${libraryId}`, updateData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Delete a library
  async deleteLibrary(libraryId) {
    if (this.useMockData) {
      const index = MOCK_LIBRARIES.findIndex(lib => lib.id === libraryId);
      if (index === -1) {
        throw { message: 'Library not found', status: 404 };
      }
      MOCK_LIBRARIES.splice(index, 1);
      return Promise.resolve({ success: true });
    }

    try {
      const response = await api.delete(`${this.baseURL}/${libraryId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Resource Management within Libraries

  // Get resources in a library
  async getLibraryResources(libraryId, options = {}) {
    if (this.useMockData) {
      return this.getMockLibraryResources(libraryId, options);
    }

    try {
      const {
        skip = 0,
        limit = 20,
        resourceType = '',
        search = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;

      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (search) params.append('search', search);
      if (resourceType) params.append('resource_type', resourceType);

      const response = await api.get(`${this.baseURL}/${libraryId}/resources?${params}`);
      return {
        resources: response.data.resources || response.data.results || [],
        total: response.data.total || 0,
        skip,
        limit
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Deprecated: Use addResourceToShelf instead to enforce shelf-first architecture
  // Add resource to library (will be assigned to appropriate shelf)
  async addResourceToLibrary(libraryId, resourceId) {
    console.warn('addResourceToLibrary is deprecated. Use addResourceToShelf to enforce shelf-first architecture.');
    try {
      const response = await api.post(`${this.baseURL}/${libraryId}/resources/${resourceId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Remove resource from library
  async removeResourceFromLibrary(libraryId, resourceId) {
    try {
      const response = await api.delete(`${this.baseURL}/${libraryId}/resources/${resourceId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Update resource in library
  async updateLibraryResource(libraryId, resourceId, updateData) {
    try {
      const response = await api.put(`${this.baseURL}/${libraryId}/resources/${resourceId}`, updateData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Shelf Management Methods

  // Get shelves in a library
  async getLibraryShelves(libraryId, resourceType = null) {
    try {
      const params = new URLSearchParams();
      if (resourceType) params.append('resource_type', resourceType);

      const url = `${this.baseURL}/${libraryId}/shelves${params.toString() ? `?${params}` : ''}`;
      const response = await api.get(url);
      return {
        shelves: response.data.shelves || [],
        total: response.data.total || 0
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Create a new shelf (RESTful: POST /libraries/{libraryId}/shelves)
  async createShelf(shelfData) {
    try {
      const { library_id, ...shelfPayload } = shelfData;
      const response = await api.post(`${this.baseURL}/${library_id}/shelves`, shelfPayload);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get shelf details
  async getShelf(shelfId) {
    try {
      const response = await api.get(`/shelves/${shelfId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Update shelf
  async updateShelf(shelfId, updateData) {
    try {
      const response = await api.put(`/shelves/${shelfId}`, updateData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Delete shelf
  async deleteShelf(shelfId) {
    try {
      const response = await api.delete(`/shelves/${shelfId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Add resource to shelf
  async addResourceToShelf(shelfId, resourceId) {
    try {
      const response = await api.post(`/shelves/${shelfId}/resources`, {
        resource_id: resourceId
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get resources in a shelf
  async getShelfResources(shelfId, options = {}) {
    try {
      const { search = '', limit = 50 } = options;
      const params = new URLSearchParams();

      if (search) params.append('search', search);
      if (limit) params.append('limit', limit.toString());

      const url = `/shelves/${shelfId}/resources${params.toString() ? `?${params}` : ''}`;
      const response = await api.get(url);

      return {
        resources: response.data.resources || response.data || [],
        total: response.data.total || 0
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Tags Management

  // Get available tags
  async getTags() {
    try {
      const response = await api.get('/tags');
      return response.data.tags || response.data.results || [];
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Library Statistics

  // Get library statistics
  async getLibraryStats(libraryId) {
    try {
      const response = await api.get(`${this.baseURL}/${libraryId}/stats`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Bulk Operations

  // Bulk delete libraries
  async bulkDeleteLibraries(libraryIds) {
    try {
      const response = await api.post(`${this.baseURL}/bulk-delete`, { library_ids: libraryIds });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Deprecated: Use addResourceToShelf instead to enforce shelf-first architecture
  // Bulk add resources to library
  async bulkAddResources(libraryId, resourceIds) {
    console.warn('bulkAddResources is deprecated. Use addResourceToShelf to enforce shelf-first architecture.');
    try {
      const response = await api.post(`${this.baseURL}/${libraryId}/resources/bulk`, resourceIds);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Search and Discovery

  // Search across all libraries
  async searchLibraries(query, options = {}) {
    if (this.useMockData) {
      const filtered = MOCK_LIBRARIES.filter(lib =>
        lib.name.toLowerCase().includes(query.toLowerCase()) ||
        lib.description.toLowerCase().includes(query.toLowerCase())
      );
      return { libraries: filtered, total: filtered.length };
    }

    try {
      const { limit = 20, includeResources = false } = options;
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
        include_resources: includeResources.toString()
      });

      const response = await api.get(`${this.baseURL}/search?${params}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Mock data methods for development
  getMockLibraries(options = {}) {
    const {
      skip = 0,
      limit = 20,
      search = ''
    } = options;

    let filtered = [...MOCK_LIBRARIES];

    // Apply search filter
    if (search) {
      filtered = filtered.filter(lib =>
        lib.name.toLowerCase().includes(search.toLowerCase()) ||
        lib.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply pagination
    const paginatedLibraries = filtered.slice(skip, skip + limit);

    return Promise.resolve({
      libraries: paginatedLibraries,
      total: filtered.length,
      skip,
      limit
    });
  }

  getMockLibrary(libraryId) {
    const library = MOCK_LIBRARIES.find(lib => lib.id === libraryId);
    if (!library) {
      throw { message: 'Library not found', status: 404 };
    }
    return Promise.resolve(library);
  }

  getMockLibraryResources(libraryId, options = {}) {
    const library = MOCK_LIBRARIES.find(lib => lib.id === libraryId);
    if (!library) {
      throw { message: 'Library not found', status: 404 };
    }

    // Get resources for this library
    const allResourceIds = [
      ...(library.file_ids || []),
      ...(library.article_ids || []),
      ...(library.webpage_ids || [])
    ];

    let resources = MOCK_RESOURCES.filter(resource =>
      allResourceIds.includes(resource.id)
    );

    const { search = '', resourceType = '' } = options;

    // Apply filters
    if (search) {
      resources = resources.filter(resource =>
        resource.name.toLowerCase().includes(search.toLowerCase()) ||
        resource.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (resourceType) {
      resources = resources.filter(resource => resource.type === resourceType);
    }

    return Promise.resolve({
      resources,
      total: resources.length,
      skip: 0,
      limit: 50
    });
  }
}

// Create and export a singleton instance
const dataLibraryService = new DataLibraryService();
export default dataLibraryService;
