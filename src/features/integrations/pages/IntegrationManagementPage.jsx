import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Refresh<PERSON>w, 
  Plug, 
  AlertCircle,
  CheckCircle,
  Settings,
  ExternalLink
} from 'lucide-react';
import { integrationService } from '../../../services';
import IntegrationCard from '../components/IntegrationCard';
import ActionConfigModal from '../components/ActionConfigModal';
import DemoSetup from '../components/DemoSetup';
import StandardPageHeader from 'components/ui/StandardPageHeader';

const IntegrationManagementPage = ({ agentId }) => {
  const [integrations, setIntegrations] = useState([]);
  const [connections, setConnections] = useState([]);
  const [agentIntegrations, setAgentIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  
  // Modal states
  const [showActionConfig, setShowActionConfig] = useState(false);
  const [selectedAction, setSelectedAction] = useState(null);
  const [selectedIntegration, setSelectedIntegration] = useState(null);

  // Integration types for filtering
  const integrationTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'email', label: 'Email' },
    { value: 'calendar', label: 'Calendar' },
    { value: 'crm', label: 'CRM' },
    { value: 'communication', label: 'Communication' },
    { value: 'productivity', label: 'Productivity' },
    { value: 'storage', label: 'Storage' }
  ];

  const statusFilters = [
    { value: 'all', label: 'All Status' },
    { value: 'connected', label: 'Connected' },
    { value: 'not_connected', label: 'Not Connected' },
    { value: 'enabled', label: 'Enabled for Agent' },
    { value: 'disabled', label: 'Disabled for Agent' }
  ];

  useEffect(() => {
    loadData();
  }, [agentId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load all data in parallel
      const [integrationsRes, connectionsRes, agentIntegrationsRes] = await Promise.all([
        integrationService.getIntegrations({ is_active: true }),
        integrationService.getUserConnections(),
        agentId ? integrationService.getAgentIntegrations(agentId) : Promise.resolve({ success: true, data: [] })
      ]);

      if (integrationsRes.success) {
        setIntegrations(integrationsRes.data);
      } else {
        throw new Error(integrationsRes.error);
      }

      if (connectionsRes.success) {
        setConnections(connectionsRes.data);
      } else {
        console.warn('Failed to load connections:', connectionsRes.error);
        setConnections([]);
      }

      if (agentIntegrationsRes.success) {
        setAgentIntegrations(agentIntegrationsRes.data);
      } else {
        console.warn('Failed to load agent integrations:', agentIntegrationsRes.error);
        setAgentIntegrations([]);
      }
    } catch (err) {
      setError(err.message || 'Failed to load integration data');
      console.error('Error loading integration data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async (integrationId) => {
    try {
      // For demo purposes, we'll simulate OAuth flow
      // In a real app, this would redirect to OAuth provider
      const authData = {
        access_token: 'demo_token_' + Date.now(),
        refresh_token: 'demo_refresh_' + Date.now(),
        expires_in: 3600
      };

      const result = await integrationService.createUserConnection({
        integration_id: integrationId,
        auth_data: authData,
        enabled_actions: []
      });

      if (result.success) {
        await loadData(); // Reload data
      } else {
        alert('Failed to connect: ' + result.error);
      }
    } catch (error) {
      alert('Failed to connect: ' + error.message);
    }
  };

  const handleDisconnect = async (integrationId) => {
    if (!confirm('Are you sure you want to disconnect this integration?')) {
      return;
    }

    try {
      const connection = connections.find(c => c.integration_id === integrationId);
      if (connection) {
        const result = await integrationService.updateUserConnection(connection.id, {
          is_connected: false
        });

        if (result.success) {
          await loadData(); // Reload data
        } else {
          alert('Failed to disconnect: ' + result.error);
        }
      }
    } catch (error) {
      alert('Failed to disconnect: ' + error.message);
    }
  };

  const handleToggleAction = async (actionId, type, value) => {
    if (!agentId || !selectedIntegration) return;

    try {
      let result;
      if (type === 'enabled') {
        if (value) {
          result = await integrationService.enableAgentAction(agentId, selectedIntegration, actionId);
        } else {
          result = await integrationService.disableAgentAction(agentId, selectedIntegration, actionId);
        }
      } else if (type === 'visibility') {
        if (!value) {
          result = await integrationService.hideAgentAction(agentId, selectedIntegration, actionId);
        } else {
          // To show an action, we need to enable it first
          result = await integrationService.enableAgentAction(agentId, selectedIntegration, actionId);
        }
      }

      if (result.success) {
        await loadData(); // Reload data
      } else {
        alert('Failed to update action: ' + result.error);
      }
    } catch (error) {
      alert('Failed to update action: ' + error.message);
    }
  };

  const handleConfigureAction = (action) => {
    setSelectedAction(action);
    setShowActionConfig(true);
  };

  const handleSaveActionConfig = async (actionId, config) => {
    if (!agentId || !selectedIntegration) return;

    try {
      const result = await integrationService.configureAgentAction(agentId, selectedIntegration, {
        action_id: actionId,
        ...config
      });

      if (result.success) {
        await loadData(); // Reload data
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      throw error;
    }
  };

  const handleAddIntegrationToAgent = async (integrationId) => {
    if (!agentId) return;

    try {
      const connection = connections.find(c => c.integration_id === integrationId);
      const result = await integrationService.addIntegrationToAgent(agentId, {
        integration_id: integrationId,
        user_connection_id: connection?.id
      });

      if (result.success) {
        await loadData(); // Reload data
      } else {
        alert('Failed to add integration to agent: ' + result.error);
      }
    } catch (error) {
      alert('Failed to add integration to agent: ' + error.message);
    }
  };

  // Filter integrations based on search and filters
  const filteredIntegrations = integrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.provider.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || integration.integration_type === filterType;

    let matchesStatus = true;
    if (filterStatus !== 'all') {
      const connection = connections.find(c => c.integration_id === integration.id);
      const agentConfig = agentIntegrations.find(ai => ai.integration?.id === integration.id);
      
      switch (filterStatus) {
        case 'connected':
          matchesStatus = connection?.is_connected && connection?.is_authorized;
          break;
        case 'not_connected':
          matchesStatus = !connection?.is_connected || !connection?.is_authorized;
          break;
        case 'enabled':
          matchesStatus = agentConfig?.config?.is_enabled === true;
          break;
        case 'disabled':
          matchesStatus = !agentConfig || agentConfig?.config?.is_enabled !== true;
          break;
      }
    }

    return matchesSearch && matchesType && matchesStatus;
  });

  // Group integrations by connection status
  const connectedIntegrations = filteredIntegrations.filter(integration => {
    const connection = connections.find(c => c.integration_id === integration.id);
    return connection?.is_connected && connection?.is_authorized;
  });

  const availableIntegrations = filteredIntegrations.filter(integration => {
    const connection = connections.find(c => c.integration_id === integration.id);
    return !connection?.is_connected || !connection?.is_authorized;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-lg text-gray-600">Loading integrations...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Integrations</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={loadData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <StandardPageHeader
          title="Integration Management"
          description="Connect, configure, and manage your integrations."
          actions={
            <button
              onClick={loadData}
              className="p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-secondary-100 transition-colors duration-200"
              title="Refresh Integrations"
            >
              <RefreshCw size={18} />
            </button>
          }
        />

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {agentId ? 'Agent Integrations' : 'Integration Management'}
              </h1>
              <p className="text-gray-600 mt-2">
                {agentId 
                  ? 'Configure integrations and actions for your agent'
                  : 'Connect and manage your third-party service integrations'
                }
              </p>
            </div>
            <button
              onClick={loadData}
              className="p-2 text-gray-600 hover:text-gray-800 rounded-lg"
              title="Refresh"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <Plug className="w-8 h-8 text-blue-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Total Integrations</p>
                  <p className="text-2xl font-semibold text-gray-900">{integrations.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Connected</p>
                  <p className="text-2xl font-semibold text-gray-900">{connectedIntegrations.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <Settings className="w-8 h-8 text-purple-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Agent Enabled</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {agentIntegrations.filter(ai => ai.config?.is_enabled).length}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center">
                <AlertCircle className="w-8 h-8 text-yellow-600" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Available</p>
                  <p className="text-2xl font-semibold text-gray-900">{availableIntegrations.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Demo Setup */}
        {integrations.length === 0 && (
          <DemoSetup />
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search integrations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Type Filter */}
            <div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {integrationTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusFilters.map(status => (
                  <option key={status.value} value={status.value}>{status.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Integrations Grid */}
        {filteredIntegrations.length === 0 ? (
          <div className="text-center py-12">
            <Plug className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No integrations found</h3>
            <p className="text-gray-600">
              {searchTerm || filterType !== 'all' || filterStatus !== 'all'
                ? 'Try adjusting your search or filters'
                : 'No integrations are available at this time'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Connected Integrations */}
            {connectedIntegrations.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Connected Integrations</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {connectedIntegrations.map((integration) => {
                    const connection = connections.find(c => c.integration_id === integration.id);
                    const agentIntegrationData = agentIntegrations.find(ai => ai.integration?.id === integration.id);
                    
                    return (
                      <IntegrationCard
                        key={integration.id}
                        integration={integration}
                        connection={connection}
                        actions={agentIntegrationData?.actions || []}
                        agentConfig={agentIntegrationData?.config}
                        onConnect={handleConnect}
                        onDisconnect={handleDisconnect}
                        onToggleAction={(actionId, type, value) => {
                          setSelectedIntegration(integration.id);
                          handleToggleAction(actionId, type, value);
                        }}
                        onConfigureAction={(action) => {
                          setSelectedIntegration(integration.id);
                          handleConfigureAction(action);
                        }}
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {/* Available Integrations */}
            {availableIntegrations.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Integrations</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {availableIntegrations.map((integration) => {
                    const connection = connections.find(c => c.integration_id === integration.id);
                    
                    return (
                      <IntegrationCard
                        key={integration.id}
                        integration={integration}
                        connection={connection}
                        actions={[]}
                        onConnect={handleConnect}
                        onDisconnect={handleDisconnect}
                        showActions={false}
                      />
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Action Configuration Modal */}
        <ActionConfigModal
          isOpen={showActionConfig}
          onClose={() => {
            setShowActionConfig(false);
            setSelectedAction(null);
            setSelectedIntegration(null);
          }}
          action={selectedAction}
          currentConfig={
            selectedAction && selectedIntegration 
              ? agentIntegrations
                  .find(ai => ai.integration?.id === selectedIntegration)
                  ?.config?.action_configs
                  ?.find(ac => ac.action_id === selectedAction.id)
              : null
          }
          onSave={handleSaveActionConfig}
        />
      </div>
    </div>
  );
};

export default IntegrationManagementPage;
