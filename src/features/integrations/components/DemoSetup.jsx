import React, { useState } from 'react';
import { Play, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { integrationService } from '../../../services';

const DemoSetup = () => {
  const [isSetup, setIsSetup] = useState(false);
  const [setupStatus, setSetupStatus] = useState('idle'); // idle, running, success, error
  const [message, setMessage] = useState('');

  const handleSetupDemo = async () => {
    setSetupStatus('running');
    setMessage('Setting up demo integrations...');

    try {
      // Setup system integrations
      const result = await integrationService.setupSystemIntegrations();

      if (result.success) {
        setSetupStatus('success');
        setMessage('Demo integrations set up successfully! You can now connect to Gmail, Google Calendar, Salesforce, and Slack.');
        setIsSetup(true);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      setSetupStatus('error');
      setMessage('Failed to set up demo integrations: ' + error.message);
    }
  };

  if (isSetup) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <p className="text-sm text-green-700">
            Demo integrations are ready! Visit the Integrations page to connect your accounts.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Demo Integrations Setup</h3>
        <p className="text-gray-600 mb-4">
          Set up demo integrations (Gmail, Google Calendar, Salesforce, Slack) to explore the integration features.
        </p>

        {setupStatus === 'idle' && (
          <button
            onClick={handleSetupDemo}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2 mx-auto"
          >
            <Play className="w-4 h-4" />
            <span>Setup Demo Integrations</span>
          </button>
        )}

        {setupStatus === 'running' && (
          <div className="flex items-center justify-center space-x-2">
            <Loader className="w-5 h-5 animate-spin text-blue-600" />
            <span className="text-blue-700">{message}</span>
          </div>
        )}

        {setupStatus === 'success' && (
          <div className="flex items-center justify-center space-x-2 text-green-700">
            <CheckCircle className="w-5 h-5" />
            <span>{message}</span>
          </div>
        )}

        {setupStatus === 'error' && (
          <div>
            <div className="flex items-center justify-center space-x-2 text-red-700 mb-2">
              <AlertCircle className="w-5 h-5" />
              <span>{message}</span>
            </div>
            <button
              onClick={handleSetupDemo}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DemoSetup;
