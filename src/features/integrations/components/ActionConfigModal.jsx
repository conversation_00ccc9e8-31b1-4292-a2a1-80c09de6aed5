import React, { useState, useEffect } from 'react';
import { X, Save, Eye, EyeOff, Check, AlertCircle } from 'lucide-react';

const ActionConfigModal = ({ 
  isOpen, 
  onClose, 
  action, 
  currentConfig,
  onSave 
}) => {
  const [config, setConfig] = useState({
    is_enabled: true,
    is_visible: true,
    display_name: '',
    display_order: null,
    custom_parameters: {}
  });
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (action && isOpen) {
      setConfig({
        is_enabled: currentConfig?.is_enabled ?? true,
        is_visible: currentConfig?.is_visible ?? true,
        display_name: currentConfig?.display_name || action.name,
        display_order: currentConfig?.display_order || null,
        custom_parameters: currentConfig?.custom_parameters || {}
      });
      setErrors({});
    }
  }, [action, currentConfig, isOpen]);

  if (!isOpen || !action) return null;

  const handleSave = async () => {
    setIsSaving(true);
    setErrors({});

    try {
      // Validate display order
      if (config.display_order !== null && config.display_order < 1) {
        setErrors({ display_order: 'Display order must be greater than 0' });
        return;
      }

      await onSave(action.id, config);
      onClose();
    } catch (error) {
      setErrors({ general: error.message || 'Failed to save configuration' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleParameterChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      custom_parameters: {
        ...prev.custom_parameters,
        [key]: value
      }
    }));
  };

  const addCustomParameter = () => {
    const key = prompt('Enter parameter name:');
    if (key && !config.custom_parameters[key]) {
      handleParameterChange(key, '');
    }
  };

  const removeCustomParameter = (key) => {
    setConfig(prev => {
      const newParams = { ...prev.custom_parameters };
      delete newParams[key];
      return {
        ...prev,
        custom_parameters: newParams
      };
    });
  };

  // Get parameter schema from action for validation
  const parameterSchema = action.input_schema?.properties || {};

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Configure Action: {action.name}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* General Error */}
          {errors.general && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <p className="text-sm text-red-700">{errors.general}</p>
              </div>
            </div>
          )}

          {/* Action Info */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Action Details</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600 mb-2">{action.description}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="capitalize">{action.action_type.replace('_', ' ')}</span>
                {action.category && (
                  <>
                    <span>•</span>
                    <span className="capitalize">{action.category}</span>
                  </>
                )}
                {action.requires_auth && (
                  <>
                    <span>•</span>
                    <span className="text-blue-600">Requires Auth</span>
                  </>
                )}
              </div>
              {action.tags && action.tags.length > 0 && (
                <div className="flex items-center space-x-1 mt-2">
                  {action.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Configuration Options */}
          <div className="space-y-6">
            {/* Display Settings */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Display Settings</h4>
              <div className="space-y-4">
                {/* Display Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name
                  </label>
                  <input
                    type="text"
                    value={config.display_name}
                    onChange={(e) => setConfig(prev => ({ ...prev, display_name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={action.name}
                  />
                </div>

                {/* Display Order */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Order (optional)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={config.display_order || ''}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      display_order: e.target.value ? parseInt(e.target.value) : null 
                    }))}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.display_order ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="1"
                  />
                  {errors.display_order && (
                    <p className="text-sm text-red-600 mt-1">{errors.display_order}</p>
                  )}
                </div>

                {/* Toggles */}
                <div className="flex items-center space-x-6">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.is_enabled}
                      onChange={(e) => setConfig(prev => ({ ...prev, is_enabled: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Check className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-700">Enabled</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.is_visible}
                      onChange={(e) => setConfig(prev => ({ ...prev, is_visible: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    {config.is_visible ? (
                      <Eye className="w-4 h-4 text-blue-600" />
                    ) : (
                      <EyeOff className="w-4 h-4 text-gray-400" />
                    )}
                    <span className="text-sm text-gray-700">Visible</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Custom Parameters */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Custom Parameters</h4>
                <button
                  onClick={addCustomParameter}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + Add Parameter
                </button>
              </div>

              {Object.keys(config.custom_parameters).length === 0 ? (
                <p className="text-sm text-gray-500 italic">No custom parameters configured</p>
              ) : (
                <div className="space-y-3">
                  {Object.entries(config.custom_parameters).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2">
                      <div className="flex-1">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {key}
                          {parameterSchema[key] && (
                            <span className="text-xs text-gray-500 ml-1">
                              ({parameterSchema[key].type})
                            </span>
                          )}
                        </label>
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => handleParameterChange(key, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder={parameterSchema[key]?.description || 'Enter value'}
                        />
                      </div>
                      <button
                        onClick={() => removeCustomParameter(key)}
                        className="p-2 text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Parameter Schema Info */}
            {Object.keys(parameterSchema).length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Available Parameters</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-2">
                    {Object.entries(parameterSchema).map(([key, schema]) => (
                      <div key={key} className="text-sm">
                        <span className="font-medium text-gray-900">{key}</span>
                        <span className="text-gray-500 ml-2">({schema.type})</span>
                        {schema.description && (
                          <p className="text-gray-600 ml-4">{schema.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save Configuration'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ActionConfigModal;
