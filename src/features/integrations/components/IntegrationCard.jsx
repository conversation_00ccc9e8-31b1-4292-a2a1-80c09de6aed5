import React, { useState } from 'react';
import { 
  Settings, 
  ToggleLeft,
  ToggleRight,
  Eye, 
  EyeOff, 
  ChevronDown, 
  ChevronUp,
  Check,
  X,
  Plug,
  AlertCircle,
  ExternalLink
} from 'lucide-react';

const IntegrationCard = ({ 
  integration, 
  connection, 
  actions = [], 
  agentConfig,
  onConnect, 
  onDisconnect, 
  onToggleAction,
  onConfigureAction,
  showActions = true 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  const isConnected = connection?.is_connected && connection?.is_authorized;
  const isEnabled = agentConfig?.is_enabled || false;
  const connectionHealth = connection?.is_healthy || false;

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      await onConnect(integration.id);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    await onDisconnect(integration.id);
  };

  const getStatusColor = () => {
    if (!isConnected) return 'bg-gray-100 text-gray-600';
    if (!connectionHealth) return 'bg-yellow-100 text-yellow-600';
    return 'bg-green-100 text-green-600';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Not Connected';
    if (!connectionHealth) return 'Connection Issues';
    return 'Connected';
  };

  const getActionConfig = (actionId) => {
    return agentConfig?.action_configs?.find(config => config.action_id === actionId);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
      {/* Header */}
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            {/* Integration Icon */}
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: integration.color + '20' || '#f3f4f6' }}
            >
              {integration.icon_url ? (
                <img 
                  src={integration.icon_url} 
                  alt={integration.name}
                  className="w-8 h-8"
                />
              ) : (
                <Plug className="w-6 h-6" style={{ color: integration.color || '#6b7280' }} />
              )}
            </div>

            {/* Integration Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold text-gray-900">{integration.name}</h3>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
                  {getStatusText()}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{integration.description}</p>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{integration.provider}</span>
                <span>•</span>
                <span className="capitalize">{integration.integration_type.replace('_', ' ')}</span>
                {actions.length > 0 && (
                  <>
                    <span>•</span>
                    <span>{actions.length} actions</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Connection Controls */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <>
                <button
                  onClick={handleDisconnect}
                  className="px-3 py-1.5 text-sm text-red-600 border border-red-200 rounded-md hover:bg-red-50 transition-colors"
                >
                  Disconnect
                </button>
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                </button>
              </>
            ) : (
              <button
                onClick={handleConnect}
                disabled={isConnecting}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isConnecting ? 'Connecting...' : 'Connect'}
              </button>
            )}
          </div>
        </div>

        {/* Connection Health Warning */}
        {isConnected && !connectionHealth && connection?.last_error && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Connection Issue</p>
                <p className="text-sm text-yellow-700 mt-1">{connection.last_error}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Actions Section */}
      {isExpanded && isConnected && showActions && actions.length > 0 && (
        <div className="border-t border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-medium text-gray-900">Available Actions</h4>
            <span className="text-sm text-gray-500">{actions.length} total</span>
          </div>

          <div className="space-y-3">
            {actions.map((action) => {
              const actionConfig = getActionConfig(action.id);
              const isActionEnabled = actionConfig?.is_enabled || false;
              const isActionVisible = actionConfig?.is_visible !== false;

              return (
                <div key={action.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h5 className="text-sm font-medium text-gray-900">
                        {actionConfig?.display_name || action.name}
                      </h5>
                      {action.requires_auth && (
                        <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded">
                          Auth Required
                        </span>
                      )}
                      {action.category && (
                        <span className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                          {action.category}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                    {action.tags && action.tags.length > 0 && (
                      <div className="flex items-center space-x-1 mt-2">
                        {action.tags.slice(0, 3).map((tag) => (
                          <span key={tag} className="px-1.5 py-0.5 text-xs bg-gray-200 text-gray-600 rounded">
                            {tag}
                          </span>
                        ))}
                        {action.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{action.tags.length - 3} more</span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {/* Visibility Toggle */}
                    <button
                      onClick={() => onToggleAction(action.id, 'visibility', !isActionVisible)}
                      className={`p-1.5 rounded ${isActionVisible ? 'text-gray-600 hover:text-gray-800' : 'text-gray-400 hover:text-gray-600'}`}
                      title={isActionVisible ? 'Hide action' : 'Show action'}
                    >
                      {isActionVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                    </button>

                    {/* Enable/Disable Toggle */}
                    <button
                      onClick={() => onToggleAction(action.id, 'enabled', !isActionEnabled)}
                      className={`p-1.5 rounded ${isActionEnabled ? 'text-green-600 hover:text-green-800' : 'text-gray-400 hover:text-gray-600'}`}
                      title={isActionEnabled ? 'Disable action' : 'Enable action'}
                    >
                      {isActionEnabled ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
                    </button>

                    {/* Configure Button */}
                    <button
                      onClick={() => onConfigureAction(action)}
                      className="p-1.5 text-gray-600 hover:text-gray-800 rounded"
                      title="Configure action"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Documentation Link */}
          {integration.documentation_url && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <a
                href={integration.documentation_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="w-4 h-4 mr-1" />
                View Documentation
              </a>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default IntegrationCard;
