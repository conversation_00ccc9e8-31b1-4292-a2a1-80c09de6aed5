import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Layers, User, LayoutDashboard, BookOpen, Bot, Workflow, BarChart } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/animations.css';

// Icon component to map icon names to Lucide React icons
const Icon = ({ name, size = 24 }) => {
  const iconMap = {
    BookOpen,
    Bot,
    Workflow,
    BarChart
  };

  const IconComponent = iconMap[name];
  return IconComponent ? <IconComponent size={size} /> : null;
};

const LandingPage = () => {
  const { isAuthenticated, user } = useAuth();
  const [prompt, setPrompt] = useState('');
  const [response, setResponse] = useState('Your Assivy AI Agent\'s response will appear here...');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const features = [
    {
      icon: 'BookOpen',
      title: 'Knowledge Base Management',
      description: 'Easily upload and manage your FAQs, help articles, and customer interaction histories for instant agent access.',
      colorClass: 'bg-indigo-100 text-indigo-700',
      animationClass: 'stagger-animate-1'
    },
    {
      icon: 'Bot',
      title: 'AI-Powered Support Agents',
      description: 'Create and configure intelligent AI agents to automate responses to common customer inquiries 24/7.',
      colorClass: 'bg-green-100 text-green-700',
      animationClass: 'stagger-animate-2'
    },
    {
      icon: 'Workflow',
      title: 'Automated Workflows & Escalations',
      description: 'Define custom workflows for ticket routing, issue resolution, and seamless human agent escalations.',
      colorClass: 'bg-yellow-100 text-yellow-700',
      animationClass: 'stagger-animate-3'
    },
    {
      icon: 'BarChart',
      title: 'Customer Satisfaction Analytics',
      description: 'Track customer satisfaction, agent performance, and key support metrics with comprehensive dashboards.',
      colorClass: 'bg-red-100 text-red-700',
      animationClass: 'stagger-animate-4'
    }
  ];

  const handleAskAgent = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt for the AI Agent.');
      return;
    }

    setIsLoading(true);
    setError('');
    
    try {
      // Simulate API call - Replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1500));
      setResponse('Thank you for your question! This is a simulated response from the Assivy AI Agent. In a production environment, this would be integrated with your chosen AI service provider.');
    } catch (err) {
      setError('An error occurred while connecting to the AI Agent. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Helmet>
        <title>Assivy</title>
      </Helmet>
      <header className="bg-white shadow-sm py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Layers size={24} className="text-indigo-600" />
            <span className="font-bold text-xl">Assivy</span>
          </div>
          <div className="flex gap-4">
            {isAuthenticated && user ? (
              // Show user menu and dashboard button when logged in
              <>
                <span className="text-gray-600 px-4 py-2 flex items-center gap-2">
                  <User size={16} />
                  Welcome, {user.first_name || user.email || 'User'}
                </span>
                <Link
                  to="/dashboard"
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-300 flex items-center gap-2"
                >
                  <LayoutDashboard size={16} />
                  Go to Dashboard
                </Link>
              </>
            ) : (
              // Show login and register buttons when not logged in
              <>
                <Link
                  to="/auth/login"
                  className="border border-indigo-600 text-indigo-600 px-4 py-2 rounded-md hover:bg-indigo-50 transition duration-300"
                >
                  Login
                </Link>
                <Link
                  to="/auth/register"
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-300"
                >
                  Get Started
                </Link>
              </>
            )}
          </div>
        </div>
      </header>
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white py-20 rounded-b-3xl shadow-lg">
          <div className="container mx-auto px-4 max-w-4xl text-center">
            <div className="animate-fade-in-up">
              <h1 className="text-4xl md:text-6xl font-extrabold leading-tight mb-6">
                Assivy: Elevate Your Customer Support with AI Agents
              </h1>
              <p className="text-lg md:text-xl mb-10 opacity-90">
                Automate customer inquiries, manage knowledge, and gain insights to deliver exceptional support experiences.
              </p>
              {isAuthenticated && user ? (
                <Link
                  to="/dashboard"
                  className="inline-flex items-center bg-white text-indigo-700 font-bold px-8 py-4 rounded-full shadow-xl hover:bg-gray-100 transform hover:scale-105 transition duration-300"
                >
                  <LayoutDashboard size={20} className="mr-2" />
                  Go to Your Dashboard
                </Link>
              ) : (
                <Link
                  to="/auth/register"
                  className="inline-flex items-center bg-white text-indigo-700 font-bold px-8 py-4 rounded-full shadow-xl hover:bg-gray-100 transform hover:scale-105 transition duration-300"
                >
                  Start Your Free Trial
                </Link>
              )}
            </div>
          </div>
        </section>
        
        {/* Features Section */}
        <section id="features" className="py-16 container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
            Key Features for Superior Support
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`feature-card bg-white rounded-lg shadow-md hover:shadow-xl transition duration-300 transform hover:-translate-y-2 ${feature.animationClass}`}
              >
                <div className="p-8 flex flex-col items-center text-center">
                  <div className={`w-20 h-20 ${feature.colorClass} rounded-full flex items-center justify-center mb-6 transition-transform duration-300 hover:scale-110`}>
                    <Icon name={feature.icon} size={40} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Demo Section */}
        <section id="demo" className="py-16 bg-white">
          <div className="container mx-auto px-4 max-w-3xl">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
              Experience Assivy's AI Support in Action ✨
            </h2>
            <div className="bg-gray-50 rounded-lg shadow-md p-8">
              <p className="text-lg text-gray-700 mb-6 text-center">
                Ask our Assivy AI Agent a customer support related question below and see how it responds!
              </p>
              <div className="mb-6">
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="e.g., 'What is your return policy?' or 'How do I reset my password?'"
                  rows={4}
                  className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                />
              </div>
              <div className="flex justify-center mb-6">
                <button
                  onClick={handleAskAgent}
                  disabled={isLoading}
                  className="bg-indigo-600 text-white font-bold px-8 py-3 rounded-lg shadow hover:bg-indigo-700 transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <span className="mr-2">Processing</span>
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
                    </div>
                  ) : (
                    "Ask Assivy Agent ✨"
                  )}
                </button>
              </div>
              <div className="bg-white rounded-lg shadow p-6 min-h-[100px]">
                <p className="text-gray-800 leading-relaxed">{response}</p>
              </div>
              {error && (
                <p className="text-red-500 text-center mt-4">{error}</p>
              )}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-indigo-700 text-white py-20 rounded-t-3xl shadow-lg mt-16">
          <div className="container mx-auto px-4 max-w-3xl text-center">
            <h2 className="text-3xl md:text-5xl font-extrabold mb-6">
              Ready to Transform Your Customer Support?
            </h2>
            <p className="text-lg md:text-xl mb-10 opacity-90">
              Join countless businesses already leveraging Assivy to provide faster, smarter, and more efficient customer service.
            </p>
            <Link
              to="/register"
              className="inline-flex items-center bg-white text-indigo-700 font-bold px-8 py-4 rounded-full shadow-xl hover:bg-gray-100 transform hover:scale-105 transition duration-300"
            >
              Get Started with Assivy
            </Link>
          </div>
        </section>
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center text-center md:text-left">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center justify-center md:justify-start gap-2">
                <Layers size={24} className="text-indigo-400" />
                <span className="font-bold text-xl">Assivy</span>
              </div>
              <p className="text-gray-400 text-sm">© {new Date().getFullYear()} All rights reserved.</p>
            </div>
            <div className="flex gap-6">
              <Link to="/privacy" className="text-gray-400 hover:text-white transition duration-300">Privacy Policy</Link>
              <Link to="/terms" className="text-gray-400 hover:text-white transition duration-300">Terms of Service</Link>
              <Link to="/support" className="text-gray-400 hover:text-white transition duration-300">Support</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
