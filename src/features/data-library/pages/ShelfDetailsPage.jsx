import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Package,
  FileText,
  Globe,
  File,
  Edit,
  Trash2,
  Eye,
  MoreVertical,
  Upload,
  AlertCircle,
  RefreshCw,
  List,
  LayoutGrid
} from 'lucide-react';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Skeleton,
  Alert,
  AlertDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  ToggleGroup,
  ToggleGroupItem
} from 'components/ui';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import ResourceCreator from '../../resources/components/ResourceCreator';
import ResourceList from '../../resources/components/ResourceList';
import { dataLibraryService, resourceService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const ShelfDetailsPage = () => {
  const { libraryId, shelfId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // State management
  const [shelf, setShelf] = useState(null);
  const [library, setLibrary] = useState(null);
  const [resources, setResources] = useState([]);
  const [filteredResources, setFilteredResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [resourcesLoading, setResourcesLoading] = useState(false);
  const [error, setError] = useState(null);

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreator, setShowCreator] = useState(false);
  const [selectedResources, setSelectedResources] = useState([]);
  const [viewMode, setViewMode] = useState('list'); // grid or list
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedType, setSelectedType] = useState('all');
  const [pagination, setPagination] = useState({ skip: 0, limit: 10, total: 0 });
  const [refreshing, setRefreshing] = useState(false);
  const [refreshingResources, setRefreshingResources] = useState(new Set());

  // Resource type configuration
  const resourceTypes = [
    { value: 'all', label: 'All Resources', icon: Package },
    { value: 'file', label: 'Files', icon: Upload },
    { value: 'article', label: 'Articles', icon: FileText },
    { value: 'web', label: 'Web Pages', icon: Globe }
  ];

  // Load shelf and library details
  const loadShelfDetails = async () => {
    if (!shelfId || !libraryId) return;

    setLoading(true);
    setError(null);

    try {
      // Load shelf details and library details in parallel
      const [shelfData, libraryData] = await Promise.all([
        dataLibraryService.getShelf(shelfId),
        dataLibraryService.getLibrary(libraryId)
      ]);

      setShelf(shelfData);
      setLibrary(libraryData);
    } catch (error) {
      console.error('Failed to load shelf details:', error);
      setError(error);
      toast.error('Failed to load shelf details');
    } finally {
      setLoading(false);
    }
  };

  // Load resources in the shelf
  const loadShelfResources = useCallback(async () => {
    if (!shelfId) return;

    setResourcesLoading(true);
    try {
      const response = await dataLibraryService.getShelfResources(shelfId, {
        search: searchQuery,
        limit: pagination.limit,
        skip: pagination.skip,
        sort_by: sortBy,
        sort_order: sortOrder
      });

      const resourcesData = response.resources || [];
      setResources(resourcesData);
      setPagination(prev => ({
        ...prev,
        total: response.total || resourcesData.length
      }));
    } catch (error) {
      console.error('Failed to load shelf resources:', error);
      toast.error('Failed to load shelf resources');
      setResources([]);
    } finally {
      setResourcesLoading(false);
    }
  }, [shelfId, searchQuery, pagination.limit, pagination.skip, sortBy, sortOrder]);

  // Filter resources based on selected type
  useEffect(() => {
    let filtered = [...resources];

    if (selectedType !== 'all') {
      filtered = filtered.filter(resource => resource.type === selectedType);
    }

    setFilteredResources(filtered);
  }, [resources, selectedType]);

  useEffect(() => {
    loadShelfDetails();
  }, [shelfId, libraryId]);

  useEffect(() => {
    if (shelf) {
      loadShelfResources();
    }
  }, [shelf, loadShelfResources]);

  // Helper functions
  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <File className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const configs = {
      file: { label: 'File', className: 'bg-purple-100 text-purple-800 border-purple-200' },
      article: { label: 'Article', className: 'bg-orange-100 text-orange-800 border-orange-200' },
      web: { label: 'Web', className: 'bg-cyan-100 text-cyan-800 border-cyan-200' }
    };

    const config = configs[type] || configs.file;
    return (
      <Badge variant="outline" className={cn("text-xs", config.className)}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleAddResources = () => {
    navigate(`/data-library/library/${libraryId}/shelf/${shelfId}/add-resources`);
  };

  const handleBackToLibrary = () => {
    navigate(`/data-library/library/${libraryId}`);
  };

  // Event handlers
  const handleSearch = useCallback((query) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, skip: 0 }));
  }, []);

  const handleTabChange = (value) => {
    setSelectedType(value);
    setPagination(prev => ({ ...prev, skip: 0 }));
  };

  const handleResourceSelect = (resourceId) => {
    setSelectedResources(prev =>
      prev.includes(resourceId)
        ? prev.filter(id => id !== resourceId)
        : [...prev, resourceId]
    );
  };

  const handleResourceAction = async (action, resource) => {
    switch (action) {
      case 'view':
        navigate(`/resources/${resource.type}/${resource.id}/view`);
        break;
      case 'edit':
        // Handle edit action
        break;
      case 'delete':
        await handleDeleteResource(resource.id);
        break;
      case 'refresh':
        await handleRefreshResource(resource.id);
        break;
      default:
        break;
    }
  };

  const handleDeleteResource = async (resourceId) => {
    try {
      // Find the resource to get its type
      const resource = resources.find(r => r.id === resourceId);
      if (!resource) {
        toast.error('Resource not found');
        return;
      }

      await resourceService.deleteResource(resource.type, resourceId);
      toast.success('Resource deleted successfully');
      loadShelfResources();
    } catch (error) {
      console.error('Failed to delete resource:', error);
      toast.error('Failed to delete resource');
    }
  };

  const handleRefreshResource = async (resourceId) => {
    setRefreshingResources(prev => new Set([...prev, resourceId]));
    try {
      // Find the resource to get its type
      const resource = resources.find(r => r.id === resourceId);
      if (!resource) {
        toast.error('Resource not found');
        return;
      }

      // For now, just reload the resource data since there's no specific refresh endpoint
      await resourceService.getResource(resource.type, resourceId);
      toast.success('Resource refreshed successfully');
      loadShelfResources();
    } catch (error) {
      console.error('Failed to refresh resource:', error);
      toast.error('Failed to refresh resource');
    } finally {
      setRefreshingResources(prev => {
        const newSet = new Set(prev);
        newSet.delete(resourceId);
        return newSet;
      });
    }
  };

  const handleResourceCreated = async (newResource) => {
    try {
      // Add the new resource to the shelf
      await dataLibraryService.addResourceToShelf(shelfId, newResource.id);
      toast.success('Resource added to shelf successfully');
      loadShelfResources();
      setShowCreator(false);
    } catch (error) {
      console.error('Failed to add resource to shelf:', error);
      toast.error('Failed to add resource to shelf');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'processing':
        return 'text-yellow-600';
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getEmptyStateMessage = (type) => {
    const typeMessages = {
      file: "No files found in this shelf. Upload files to get started.",
      article: "No articles found in this shelf. Create articles to get started.",
      web: "No web pages found in this shelf. Save web content to get started.",
      all: "This shelf is empty. Add resources to get started."
    };
    return typeMessages[type] || typeMessages.all;
  };

  const getEmptyStateIcon = (type) => {
    const typeIcons = {
      file: Upload,
      article: FileText,
      web: Globe,
      all: Package
    };
    return typeIcons[type] || typeIcons.all;
  };

  const getEmptyStateButtonText = (type) => {
    const typeTexts = {
      file: "Upload File",
      article: "Create Article",
      web: "Save Web Page",
      all: "Add Resource"
    };
    return typeTexts[type] || typeTexts.all;
  };

  const handleEmptyStateAction = () => {
    setShowCreator(true);
  };

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Skeleton className="h-32 w-full" />
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error || !shelf || !library) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={handleBackToLibrary}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Library
        </Button>
        <Card>
          <CardContent className="text-center py-12">
            <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Shelf not found</h3>
            <p className="text-muted-foreground mb-4">
              The shelf you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={handleBackToLibrary}>
              Return to Library
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Back Navigation */}
      <Button variant="ghost" onClick={handleBackToLibrary}>
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to {library.name}
      </Button>

      {/* Shelf Header */}
      <StandardPageHeader
        title={shelf.name}
        description={shelf.description || `Resources in ${library.name}`}
        icon={Package}
        actions={
          <div className="flex items-center gap-3">
            <ToggleGroup
              type="single"
              value={viewMode}
              onValueChange={setViewMode}
              size="default"
              className={cn("gap-0")}
            >
              <ToggleGroupItem
                value="list"
                variant="default"
                size="default"
                aria-label="List"
                className="gap-2"
              >
                <List className="h-4 w-4" />
                <span className="hidden sm:inline">List</span>
              </ToggleGroupItem>
              <ToggleGroupItem
                value="grid"
                variant="default"
                size="default"
                aria-label="Grid"
                className="gap-2"
              >
                <LayoutGrid className="h-4 w-4" />
                <span className="hidden sm:inline">Grid</span>
              </ToggleGroupItem>
            </ToggleGroup>
            <Button
              color="blue"
              size="2"
              onClick={() => setShowCreator(!showCreator)}
            >
              <Plus size={18} />
              Add Resource
            </Button>
            <Button
              variant="outline"
              onClick={handleAddResources}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Existing
            </Button>
          </div>
        }
      />

      {/* Shelf Info Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Package className="w-4 h-4" />
                <span className="font-medium">
                  {shelf.resource_count || 0} {(shelf.resource_count || 0) === 1 ? 'resource' : 'resources'}
                </span>
              </div>
              {shelf.resource_type && (
                <Badge variant="outline" className="text-xs">
                  {shelf.resource_type.charAt(0).toUpperCase() + shelf.resource_type.slice(1)} Shelf
                </Badge>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              Created {formatDate(shelf.created_at)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resource Creator */}
      {showCreator && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Resource</CardTitle>
          </CardHeader>
          <CardContent>
            <ResourceCreator
              onResourceCreated={handleResourceCreated}
              onClear={() => setShowCreator(false)}
              selectedType={selectedType === 'all' ? 'file' : selectedType}
            />
          </CardContent>
        </Card>
      )}

      {/* Search and Filters Section */}
      <div className="bg-surface border border-border rounded-lg p-4 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={20} />
          <Input
            placeholder="Search resources by name, description, or tags..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 pr-4 py-3 w-full bg-surface border-border focus:border-primary focus:ring-primary/20"
          />
        </div>
        {/* Filter and Sort Controls */}
        <div className="flex flex-col gap-4">
          {/* Resource Type Tabs */}
          <Tabs value={selectedType} onValueChange={handleTabChange}>
            <TabsList>
              {resourceTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <TabsTrigger key={type.value} value={type.value} className="flex items-center gap-2">
                    <Icon className="w-4 h-4" />
                    {type.label}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="min-h-[400px]">
        {/* Resource List */}
        {!showCreator && (
          <ResourceList
            resources={filteredResources}
            loading={resourcesLoading}
            viewMode={viewMode}
            selectedResources={selectedResources}
            onResourceSelect={handleResourceSelect}
            onResourceAction={handleResourceAction}
            getStatusColor={getStatusColor}
            refreshingResources={refreshingResources}
            emptyStateMessage={getEmptyStateMessage(selectedType)}
            emptyStateAction={handleEmptyStateAction}
            emptyStateIcon={getEmptyStateIcon(selectedType)}
            emptyStateButtonText={getEmptyStateButtonText(selectedType)}
            currentTab={selectedType}
          />
        )}

        {/* List View when creator is shown */}
        {showCreator && (
          <ResourceList
            resources={filteredResources}
            loading={resourcesLoading}
            viewMode={viewMode}
            selectedResources={selectedResources}
            onResourceSelect={handleResourceSelect}
            onResourceAction={handleResourceAction}
            getStatusColor={getStatusColor}
            refreshingResources={refreshingResources}
            emptyStateMessage={getEmptyStateMessage(selectedType)}
            emptyStateAction={handleEmptyStateAction}
            emptyStateIcon={getEmptyStateIcon(selectedType)}
            emptyStateButtonText={getEmptyStateButtonText(selectedType)}
            currentTab={selectedType}
          />
        )}
      </div>
    </div>
  );
};

export default ShelfDetailsPage;
