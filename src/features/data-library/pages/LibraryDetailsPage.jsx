import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  <PERSON>Left, 
  Edit, 
  Trash2, 
  Settings,
  BarChart3,
  Database,
  Share2,
  Download
} from 'lucide-react';
import { 
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Skeleton
} from 'components/ui';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import ShelfManager from '../components/ShelfManager';
import LibraryForm from '../components/LibraryForm';
import LibraryStats from '../components/LibraryStats';
import DataLibraryErrorBoundary from '../components/DataLibraryErrorBoundary';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';

const LibraryDetailsPage = () => {
  const { libraryId } = useParams();
  const navigate = useNavigate();
  
  // State management
  const [library, setLibrary] = useState(null);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Dialog states
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('shelves');

  // Load library details
  const loadLibrary = async () => {
    if (!libraryId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const libraryData = await dataLibraryService.getLibrary(libraryId);
      setLibrary(libraryData);
    } catch (error) {
      console.error('Failed to load library:', error);
      setError(error);
      toast.error('Failed to load library details');
    } finally {
      setLoading(false);
    }
  };

  // Load library stats
  const loadStats = async () => {
    if (!libraryId) return;
    
    setStatsLoading(true);
    try {
      const statsData = await dataLibraryService.getLibraryStats(libraryId);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load library stats:', error);
      // Create basic stats from library data if API fails
      if (library) {
        setStats({
          total_resources: library.resource_count || 0,
          total_size: library.total_size || 0,
          file_resources: library.file_ids?.length || 0,
          article_resources: library.article_ids?.length || 0,
          web_resources: library.webpage_ids?.length || 0
        });
      }
    } finally {
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    loadLibrary();
  }, [libraryId]);

  useEffect(() => {
    if (library && activeTab === 'overview') {
      loadStats();
    }
  }, [library, activeTab]);

  // Handle library editing
  const handleEditLibrary = async (libraryData) => {
    setFormLoading(true);
    try {
      const updatedLibrary = await dataLibraryService.updateLibrary(libraryId, libraryData);
      setLibrary(updatedLibrary);
      toast.success('Library updated successfully');
      setShowEditDialog(false);
    } catch (error) {
      console.error('Failed to update library:', error);
      toast.error(error.message || 'Failed to update library');
      throw error;
    } finally {
      setFormLoading(false);
    }
  };

  // Handle library deletion
  const handleDeleteLibrary = async () => {
    try {
      await dataLibraryService.deleteLibrary(libraryId);
      toast.success('Library deleted successfully');
      navigate('/data-library');
    } catch (error) {
      console.error('Failed to delete library:', error);
      toast.error(error.message || 'Failed to delete library');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-64" />
        </div>
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !library) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={() => navigate('/data-library')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Libraries
        </Button>
        <Card>
          <CardContent className="text-center py-12">
            <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Library not found</h3>
            <p className="text-muted-foreground mb-4">
              The library you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => navigate('/data-library')}>
              Return to Libraries
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <Button variant="ghost" onClick={() => navigate('/data-library')}>
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Libraries
      </Button>

      {/* Library Header */}
      <StandardPageHeader
        title={library.name}
        description={library.description || 'No description provided'}
        icon={Database}
        actions={
          <div className="flex flex-wrap items-center gap-2 sm:gap-3">
            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowEditDialog(true)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Library</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{library.name}"? This action cannot be undone 
                    and will remove all resources from this library.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteLibrary}>
                    Delete Library
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        }
      />

      {/* Library Metadata */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Resources</h4>
              <p className="text-lg font-semibold">{library.resource_count || 0}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Total Size</h4>
              <p className="text-lg font-semibold">{formatFileSize(library.total_size)}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Created</h4>
              <p className="text-sm">{formatDate(library.created_at)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="shelves">
            <Database className="w-4 h-4 mr-2" />
            Shelves
          </TabsTrigger>
          <TabsTrigger value="overview">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="shelves">
          <ShelfManager libraryId={libraryId} library={library} />
        </TabsContent>

        <TabsContent value="overview">
          <LibraryStats stats={stats} loading={statsLoading} />
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Library Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Library settings and configuration options will be available here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Library Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Library</DialogTitle>
          </DialogHeader>
          <LibraryForm
            library={library}
            onSubmit={handleEditLibrary}
            onCancel={() => setShowEditDialog(false)}
            isLoading={formLoading}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default function LibraryDetailsPageWithErrorBoundary(props) {
  return (
    <DataLibraryErrorBoundary>
      <LibraryDetailsPage {...props} />
    </DataLibraryErrorBoundary>
  );
}
