import React, { useState, useEffect, useCallback } from 'react';
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Database,
  Settings,
  BarChart3,
  Trash2,
  AlertCircle
} from 'lucide-react';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
  Alert,
  AlertDescription,
  Spinner
} from 'components/ui';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import LibraryList from '../components/LibraryList';
import LibraryForm from '../components/LibraryForm';
import LibraryStats from '../components/LibraryStats';
import DataLibraryErrorBoundary from '../components/DataLibraryErrorBoundary';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';

const DataLibraryPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();

  // State management
  const [libraries, setLibraries] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // UI state
  const [activeTab, setActiveTab] = useState('libraries');
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLibraries, setSelectedLibraries] = useState([]);
  
  // Dialog states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingLibrary, setEditingLibrary] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // Pagination
  const [pagination, setPagination] = useState({
    skip: 0,
    limit: 20,
    total: 0
  });

  // Load libraries
  const loadLibraries = useCallback(async (resetPagination = false) => {
    // Don't load if not authenticated
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const skip = resetPagination ? 0 : pagination.skip;
      const response = await dataLibraryService.getLibraries({
        skip,
        limit: pagination.limit,
        search: searchQuery
      });

      setLibraries(response.libraries || []);
      setPagination(prev => ({
        ...prev,
        skip,
        total: response.total || 0
      }));
    } catch (error) {
      console.error('Failed to load libraries:', error);
      setError(error);

      // Handle authentication errors specifically
      if (error.status === 401 || error.status === 403) {
        toast.error('Authentication required. Please log in to access the data library.');
      } else if (error.status === 500) {
        toast.error('Server error. Please check if you are logged in and try again.');
      } else {
        toast.error('Failed to load libraries');
      }
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.skip, pagination.limit, isAuthenticated]);

  // Load stats
  const loadStats = useCallback(async () => {
    // Don't load if not authenticated
    if (!isAuthenticated) {
      setStatsLoading(false);
      return;
    }

    setStatsLoading(true);
    try {
      // Since there's no global stats endpoint, we'll calculate from libraries
      const response = await dataLibraryService.getLibraries({ limit: 1000 });
      const allLibraries = response.libraries || [];
      
      const calculatedStats = {
        total_libraries: allLibraries.length,
        total_resources: allLibraries.reduce((sum, lib) => sum + (lib.resource_count || 0), 0),
        total_size: allLibraries.reduce((sum, lib) => sum + (lib.total_size || 0), 0),
        file_resources: allLibraries.reduce((sum, lib) => sum + (lib.file_ids?.length || 0), 0),
        article_resources: allLibraries.reduce((sum, lib) => sum + (lib.article_ids?.length || 0), 0),
        web_resources: allLibraries.reduce((sum, lib) => sum + (lib.webpage_ids?.length || 0), 0)
      };
      
      setStats(calculatedStats);
    } catch (error) {
      console.error('Failed to load stats:', error);
    } finally {
      setStatsLoading(false);
    }
  }, [isAuthenticated]);

  // Initial load
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      loadLibraries(true);
      if (activeTab === 'overview') {
        loadStats();
      }
    }
  }, [searchQuery, activeTab, isAuthenticated, authLoading, loadLibraries, loadStats]);

  // Handle library creation
  const handleCreateLibrary = async (libraryData) => {
    // Prevent double submission
    if (formLoading) {
      console.warn('Library creation already in progress, ignoring duplicate request');
      return;
    }

    setFormLoading(true);
    try {
      await dataLibraryService.createLibrary(libraryData);
      toast.success('Library created successfully');
      setShowCreateDialog(false);

      // Reload libraries to show the newly created library
      loadLibraries(true);
      if (activeTab === 'overview') {
        loadStats();
      }
    } catch (error) {
      // Handle specific error cases with user-friendly messages
      if (error.status === 409) {
        toast.error(`A library with the name "${libraryData.name}" already exists. Please choose a different name.`);
      } else if (error.status === 401 || error.status === 403) {
        toast.error('You do not have permission to create libraries.');
      } else {
        toast.error(error.message || 'Failed to create library. Please try again.');
        // Don't log here - api.js already handles logging to avoid duplicate logs
      }

      // Don't re-throw the error to prevent duplicate error handling
      // The form will handle the UI state reset
    } finally {
      setFormLoading(false);
    }
  };

  // Handle library editing
  const handleEditLibrary = async (libraryData) => {
    if (!editingLibrary) return;
    
    setFormLoading(true);
    try {
      await dataLibraryService.updateLibrary(editingLibrary.id, libraryData);
      toast.success('Library updated successfully');
      setShowEditDialog(false);
      setEditingLibrary(null);
      loadLibraries();
    } catch (error) {
      console.error('Failed to update library:', error);
      toast.error(error.message || 'Failed to update library');
      throw error;
    } finally {
      setFormLoading(false);
    }
  };

  // Handle library deletion
  const handleDeleteLibrary = async (library) => {
    try {
      await dataLibraryService.deleteLibrary(library.id);
      toast.success('Library deleted successfully');
      loadLibraries();
      if (activeTab === 'overview') {
        loadStats();
      }
    } catch (error) {
      console.error('Failed to delete library:', error);
      toast.error(error.message || 'Failed to delete library');
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedLibraries.length === 0) return;
    
    try {
      await dataLibraryService.bulkDeleteLibraries(selectedLibraries);
      toast.success(`Deleted ${selectedLibraries.length} libraries`);
      setSelectedLibraries([]);
      loadLibraries();
      if (activeTab === 'overview') {
        loadStats();
      }
    } catch (error) {
      console.error('Failed to delete libraries:', error);
      toast.error('Failed to delete libraries');
    }
  };

  // Handle library view
  const handleViewLibrary = (library) => {
    navigate(`/data-library/library/${library.id}`);
  };

  // Handle resources added to library
  const handleResourcesAdded = useCallback(() => {
    // Reload libraries to update resource counts
    loadLibraries();
    if (activeTab === 'overview') {
      loadStats();
    }
  }, [loadLibraries, loadStats, activeTab]);

  // Handle library edit
  const handleEditClick = (library) => {
    setEditingLibrary(library);
    setShowEditDialog(true);
  };

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="large" text="Loading..." />
      </div>
    );
  }

  // Show authentication message if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <StandardPageHeader
          title="Data Library"
          description="Organize and manage your data resources in structured libraries"
        />
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need to be logged in to access the Data Library. Please log in to continue.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Authentication Notice */}
      {!user && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Loading user information...
          </AlertDescription>
        </Alert>
      )}

      {/* Development Notice */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 rounded-xl p-4 shadow-sm">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 rounded-lg p-2">
              <Database className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-blue-900 mb-1">Real Data Mode</h4>
              <p className="text-sm text-blue-700 leading-relaxed">
                Connected to backend API. All data operations are persisted to the database.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Page Header */}
      <StandardPageHeader
        title="Data Library"
        description="Organize and manage your data resources in structured libraries"
        icon={Database}
        actions={
          <div className="flex items-center space-x-3">
            {selectedLibraries.length > 0 && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete ({selectedLibraries.length})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Libraries</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete {selectedLibraries.length} selected libraries? 
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleBulkDelete}>
                      Delete Libraries
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            
            <Button variant="outline" onClick={() => loadLibraries(true)}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Library
            </Button>
          </div>
        }
      />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="libraries">
            <Database className="w-4 h-4 mr-2" />
            Libraries
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <LibraryStats stats={stats} loading={statsLoading} />
        </TabsContent>

        <TabsContent value="libraries" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
            <div className="flex-1 sm:max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search libraries..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  aria-label="Search libraries"
                />
              </div>
            </div>


          </div>

          {/* Libraries List */}
          <LibraryList
            libraries={libraries}
            loading={loading}
            error={error}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            selectedLibraries={selectedLibraries}
            onLibrarySelect={setSelectedLibraries}
            onLibraryView={handleViewLibrary}
            onLibraryEdit={handleEditClick}
            onLibraryDelete={handleDeleteLibrary}
            onResourcesAdded={handleResourcesAdded}
            onCreateNew={() => setShowCreateDialog(true)}
          />
        </TabsContent>
      </Tabs>

      {/* Create Library Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Library</DialogTitle>
          </DialogHeader>
          <LibraryForm
            onSubmit={handleCreateLibrary}
            onCancel={() => setShowCreateDialog(false)}
            isLoading={formLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Library Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Library</DialogTitle>
          </DialogHeader>
          <LibraryForm
            library={editingLibrary}
            onSubmit={handleEditLibrary}
            onCancel={() => {
              setShowEditDialog(false);
              setEditingLibrary(null);
            }}
            isLoading={formLoading}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default function DataLibraryPageWithErrorBoundary(props) {
  return (
    <DataLibraryErrorBoundary>
      <DataLibraryPage {...props} />
    </DataLibraryErrorBoundary>
  );
}
