import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Alert,
  AlertDescription
} from 'components/ui';
import {
  ArrowLeft,
  Search,
  Plus,
  Upload,
  FileText,
  Globe,
  Check,
  Loader2,
  AlertCircle,
  Package,
  Database
} from 'lucide-react';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import ResourceCreator from '../../resources/components/ResourceCreator';
import BulkResourceSelector from '../components/BulkResourceSelector';
import { dataLibraryService, resourceService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const AddResourcesPage = () => {
  const { libraryId } = useParams();
  const navigate = useNavigate();
  
  // Library state
  const [library, setLibrary] = useState(null);
  const [shelves, setShelves] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // UI state
  const [activeTab, setActiveTab] = useState('existing');
  const [selectedResources, setSelectedResources] = useState([]);
  const [selectedShelf, setSelectedShelf] = useState(null);
  const [selectedShelfData, setSelectedShelfData] = useState(null);
  const [addingResources, setAddingResources] = useState(false);
  const [showResourceSelection, setShowResourceSelection] = useState(false);
  
  // Load library data
  useEffect(() => {
    loadLibrary();
  }, [libraryId]);

  const loadLibrary = async () => {
    if (!libraryId) {
      setError('Library ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const libraryData = await dataLibraryService.getLibrary(libraryId);
      setLibrary(libraryData);

      // Load shelves
      const shelvesResponse = await dataLibraryService.getLibraryShelves(libraryId);
      setShelves(shelvesResponse.shelves || []);
    } catch (err) {
      console.error('Failed to load library:', err);
      setError('Failed to load library details');
      toast.error('Failed to load library');
    } finally {
      setLoading(false);
    }
  };

  const handleResourceCreated = async (resource) => {
    if (!selectedShelf) {
      toast.error('Please select a shelf before adding resources');
      return;
    }

    try {
      // Add the newly created resource to the selected shelf
      await dataLibraryService.addResourceToShelf(selectedShelf, resource.id);
      toast.success(`Created and added "${resource.name}" to shelf`);

      // Navigate back to library
      navigate(`/data-library/library/${libraryId}`);
    } catch (error) {
      console.error('Failed to add created resource to shelf:', error);
      toast.error('Resource created but failed to add to shelf');
    }
  };

  const handleAddSelectedResources = async (resourceIds) => {
    if (resourceIds.length === 0) return;

    if (!selectedShelf) {
      toast.error('Please select a shelf before adding resources');
      return;
    }

    setAddingResources(true);
    try {
      // Add each resource to the selected shelf
      for (const resourceId of resourceIds) {
        await dataLibraryService.addResourceToShelf(selectedShelf, resourceId);
      }
      toast.success(`Added ${resourceIds.length} resource(s) to shelf`);

      // Navigate back to library
      navigate(`/data-library/library/${libraryId}`);
    } catch (error) {
      console.error('Failed to add resources to shelf:', error);
      toast.error('Failed to add resources to shelf');
    } finally {
      setAddingResources(false);
    }
  };

  const handleSelectionChange = (newSelection) => {
    setSelectedResources(newSelection);
  };

  const handleShelfSelection = (shelfId) => {
    const shelf = shelves.find(s => s.id === shelfId);
    setSelectedShelf(shelfId);
    setSelectedShelfData(shelf);
    setShowResourceSelection(true);
    setSelectedResources([]); // Reset selected resources when changing shelf
  };

  const handleBackToShelfSelection = () => {
    setShowResourceSelection(false);
    setSelectedShelf(null);
    setSelectedShelfData(null);
    setSelectedResources([]);
  };

  const handleGoBack = () => {
    navigate(`/data-library/library/${libraryId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading library...</p>
        </div>
      </div>
    );
  }

  if (error || !library) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Library not found'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <StandardPageHeader
        title={`Add Resources to "${library.name}"`}
        description="Add existing resources or create new ones and organize them in shelves"
        actions={
          <Button
            variant="outline"
            onClick={handleGoBack}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Library
          </Button>
        }
      />

      {/* Library Info Card */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">{library.name}</h3>
              <p className="text-sm text-muted-foreground">
                {library.description || 'No description'}
              </p>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <span>{library.resource_count || 0} resources</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>{((library.total_size || 0) / 1024 / 1024).toFixed(1)} MB</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      {!showResourceSelection ? (
        /* Shelf Selection Step */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Step 1: Select Target Shelf</span>
            </CardTitle>
            <p className="text-muted-foreground">
              Choose which shelf you want to add resources to.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {shelves.length === 0 ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No shelves available in this library. Please create shelves first to organize your resources.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {shelves.map((shelf) => (
                  <Card
                    key={shelf.id}
                    className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/50"
                    onClick={() => handleShelfSelection(shelf.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Package className="w-5 h-5 text-blue-600" />
                          <h3 className="font-semibold">{shelf.name}</h3>
                        </div>
                      </div>

                      {shelf.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {shelf.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          {shelf.resource_count || 0} resources
                        </span>
                        <span className="text-primary font-medium">
                          Select →
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        /* Resource Selection Step */
        <div className="space-y-6">
          {/* Selected Shelf Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBackToShelfSelection}
                    className="gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Change Shelf
                  </Button>
                  <div className="flex items-center space-x-2">
                    <Package className="w-5 h-5 text-blue-600" />
                    <div>
                      <h3 className="font-semibold">Adding to: {selectedShelfData?.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Showing all available resources
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resource Selection Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="existing" className="flex items-center space-x-2">
                <Search className="w-4 h-4" />
                <span>Select Existing Resources</span>
              </TabsTrigger>
              <TabsTrigger value="create" className="flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Create New Resource</span>
              </TabsTrigger>
            </TabsList>

            {/* Existing Resources Tab */}
            <TabsContent value="existing" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Resource Selector */}
                <div className="lg:col-span-2">
                  <BulkResourceSelector
                    library={library}
                    selectedResources={selectedResources}
                    onSelectionChange={handleSelectionChange}
                    onAdd={handleAddSelectedResources}
                    loading={addingResources}
                    resourceTypeFilter={null}
                  />
                </div>

                {/* Selection Summary */}
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Selection Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-primary">
                          {selectedResources.length}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {selectedShelfData?.resource_type} resources selected
                        </p>
                      </div>

                      <div className="p-3 bg-muted rounded-lg">
                        <div className="flex items-center space-x-2 mb-1">
                          {selectedShelfData?.resource_type === 'file' && <FileText className="w-4 h-4 text-purple-600" />}
                          {selectedShelfData?.resource_type === 'article' && <FileText className="w-4 h-4 text-orange-600" />}
                          {selectedShelfData?.resource_type === 'web' && <Globe className="w-4 h-4 text-cyan-600" />}
                          <span className="font-medium">{selectedShelfData?.name}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Target shelf for selected resources
                        </p>
                      </div>

                      {selectedResources.length > 0 && (
                        <Button
                          onClick={() => handleAddSelectedResources(selectedResources)}
                          disabled={addingResources}
                          className="w-full"
                          size="lg"
                        >
                          {addingResources && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                          Add {selectedResources.length} Resource{selectedResources.length !== 1 ? 's' : ''} to Shelf
                        </Button>
                      )}

                      <div className="pt-4 border-t">
                        <h4 className="font-medium mb-2">Quick Actions</h4>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => setActiveTab('create')}
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Create New Resource
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={handleBackToShelfSelection}
                          >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Change Shelf
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
          </div>
        </TabsContent>

            {/* Create New Resource Tab */}
            <TabsContent value="create" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Resource Creator */}
                <div className="lg:col-span-2">
                  <ResourceCreator
                    onResourceCreated={handleResourceCreated}
                    onClear={() => setActiveTab('existing')}
                    selectedType="file"
                    disabled={false}
                  />
                </div>

                {/* Resource Organization Info */}
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Resource Organization</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="p-3 bg-muted rounded-lg">
                        <div className="flex items-center space-x-2 mb-1">
                          <Package className="w-4 h-4 text-blue-600" />
                          <span className="font-medium">{selectedShelfData?.name}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          New resource will be added to this shelf
                        </p>
                      </div>

                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Create any type of resource for this shelf.
                        </AlertDescription>
                      </Alert>

                      <div className="pt-4 border-t">
                        <h4 className="font-medium mb-2">Quick Actions</h4>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => setActiveTab('existing')}
                          >
                            <Search className="w-4 h-4 mr-2" />
                            Select Existing Resources
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={handleBackToShelfSelection}
                          >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Change Shelf
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default AddResourcesPage;
