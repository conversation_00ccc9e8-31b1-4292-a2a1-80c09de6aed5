import React, { useState, useEffect } from 'react';
import {
  Package,
  Upload,
  FileText,
  Globe,
  Plus,
  Loader2
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Badge,
  Alert,
  AlertDescription
} from 'components/ui';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const ShelfSelector = ({ 
  libraryId,
  value,
  onValueChange,
  resourceType = null,
  placeholder = "Select a shelf",
  allowCreate = false,
  className,
  disabled = false
}) => {
  const [shelves, setShelves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [creating, setCreating] = useState(false);

  // Load shelves for the library
  const loadShelves = async () => {
    if (!libraryId) return;
    
    setLoading(true);
    try {
      const response = await dataLibraryService.getLibraryShelves(
        libraryId,
        resourceType
      );
      setShelves(response.shelves || []);
    } catch (error) {
      console.error('Failed to load shelves:', error);
      toast.error('Failed to load shelves');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadShelves();
  }, [libraryId, resourceType]);

  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <Upload className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const variants = {
      file: 'default',
      article: 'secondary',
      web: 'outline'
    };
    
    return (
      <Badge variant={variants[type] || 'default'} className="text-xs">
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const handleCreateShelf = async (shelfData) => {
    setCreating(true);
    try {
      const newShelf = await dataLibraryService.createShelf({
        library_id: libraryId,
        ...shelfData
      });
      
      toast.success('Shelf created successfully');
      setShowCreateDialog(false);
      
      // Reload shelves and select the new one
      await loadShelves();
      onValueChange?.(newShelf.id);
    } catch (error) {
      console.error('Failed to create shelf:', error);
      toast.error('Failed to create shelf');
    } finally {
      setCreating(false);
    }
  };

  const filteredShelves = resourceType 
    ? shelves.filter(shelf => shelf.resource_type === resourceType)
    : shelves;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <Select 
          value={value || ''} 
          onValueChange={onValueChange}
          disabled={disabled || loading}
        >
          <SelectTrigger>
            <SelectValue placeholder={loading ? "Loading shelves..." : placeholder} />
          </SelectTrigger>
          <SelectContent>
            {filteredShelves.length === 0 ? (
              <div className="p-2 text-center text-sm text-muted-foreground">
                {loading ? "Loading..." : "No shelves available"}
              </div>
            ) : (
              filteredShelves.map((shelf) => (
                <SelectItem key={shelf.id} value={shelf.id}>
                  <div className="flex items-center space-x-2">
                    {getResourceTypeIcon(shelf.resource_type)}
                    <span>{shelf.name}</span>
                    <Badge variant="outline" className="text-xs ml-auto">
                      {shelf.resource_count || 0}
                    </Badge>
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>

        {allowCreate && (
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" disabled={disabled}>
                <Plus className="w-4 h-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Shelf</DialogTitle>
              </DialogHeader>
              <ShelfForm
                onSubmit={handleCreateShelf}
                onCancel={() => setShowCreateDialog(false)}
                loading={creating}
                defaultResourceType={resourceType}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {resourceType && filteredShelves.length === 0 && !loading && (
        <Alert>
          <AlertDescription>
            No shelves found for {resourceType} resources. 
            {allowCreate && " Create a new shelf to organize your resources."}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

// Simple shelf form for creating new shelves
const ShelfForm = ({ 
  onSubmit, 
  onCancel, 
  loading = false,
  defaultResourceType = null
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Shelf name is required');
      return;
    }
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium mb-2 block">Name</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Enter shelf name"
          className="w-full px-3 py-2 border border-input rounded-md"
          required
          disabled={loading}
        />
      </div>
      
      <div>
        <label className="text-sm font-medium mb-2 block">Description</label>
        <input
          type="text"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Enter shelf description (optional)"
          className="w-full px-3 py-2 border border-input rounded-md"
          disabled={loading}
        />
      </div>
      

      
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          Create Shelf
        </Button>
      </div>
    </form>
  );
};

export default ShelfSelector;
