import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Database,
  FileText,
  Globe,
  File,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Plus
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from 'components/ui';
import QuickAddResourceButton from './QuickAddResourceButton';
import { cn } from '@/lib/utils';

const LibraryCard = ({
  library,
  isSelected = false,
  onSelect,
  onView,
  onEdit,
  onDelete,
  onResourcesAdded,
  className
}) => {
  const navigate = useNavigate();
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <File className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <Database className="w-4 h-4" />;
    }
  };

  const handleCardClick = (e) => {
    // Don't trigger card click if clicking on dropdown or checkbox
    if (e.target.closest('[data-dropdown-trigger]') || e.target.closest('input[type="checkbox"]')) {
      return;
    }
    onView?.(library);
  };

  return (
    <Card
      className={cn(
        "group cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1 border-0 bg-gradient-to-br from-white to-gray-50/50 backdrop-blur-sm",
        isSelected && "ring-2 ring-primary ring-offset-2 shadow-lg shadow-primary/20",
        className
      )}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick(e);
        }
      }}
      aria-label={`Library: ${library.name}. ${library.description || 'No description'}. ${library.resource_count || 0} resources.`}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            {onSelect && (
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => onSelect(library, e.target.checked)}
                className="mt-1.5 w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                onClick={(e) => e.stopPropagation()}
              />
            )}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <Database className="w-5 h-5 text-primary flex-shrink-0" />
                <CardTitle className="text-lg font-bold text-foreground truncate">
                  {library.name}
                </CardTitle>
              </div>
              {library.description && (
                <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                  {library.description}
                </p>
              )}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild data-dropdown-trigger>
              <Button 
                variant="ghost" 
                size="sm" 
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="w-4 h-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => onView?.(library)}>
                <Eye className="w-4 h-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(library)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Library
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete?.(library)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Library
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-4">
        {/* Resource Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-lg p-3 text-center border border-blue-200/50">
            <div className="text-2xl font-bold text-blue-700 mb-1">
              {library.resource_count || 0}
            </div>
            <div className="text-xs font-medium text-blue-600">Resources</div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100/50 rounded-lg p-3 text-center border border-green-200/50">
            <div className="text-2xl font-bold text-green-700 mb-1">
              {formatFileSize(library.total_size)}
            </div>
            <div className="text-xs font-medium text-green-600">Total Size</div>
          </div>
        </div>

        {/* Resource Type Breakdown - Updated for shelf-based organization */}
        {library.resource_count > 0 && (
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
              <Database className="w-3 h-3 mr-1" />
              {library.resource_count} Resources
            </Badge>
            {/* In a future update, we could show breakdown by shelf types */}
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex items-center justify-between pt-3">
          <QuickAddResourceButton
            library={library}
            onResourceAdded={onResourcesAdded}
            variant="outline"
            size="sm"
            className="text-xs"
          />
          <Button
            variant="ghost"
            size="sm"
            className="text-xs"
            onClick={() => navigate(`/data-library/library/${library.id}/add-resources`)}
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Resources
          </Button>
        </div>

        {/* Metadata */}
        <div className="pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(library.created_at)}</span>
            </div>
            {library.owner_name && (
              <div className="flex items-center space-x-1">
                <User className="w-3 h-3" />
                <span className="font-medium">{library.owner_name}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LibraryCard;
