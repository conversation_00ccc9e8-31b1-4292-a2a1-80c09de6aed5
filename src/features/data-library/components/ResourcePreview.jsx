import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Button,
  Skeleton,
  Alert,
  AlertDescription
} from 'components/ui';
import {
  File,
  FileText,
  Globe,
  Calendar,
  User,
  Eye,
  ExternalLink,
  AlertCircle,
  Check,
  X
} from 'lucide-react';
import { resourceService } from 'services';
import { cn } from '@/lib/utils';

const ResourcePreview = ({ 
  resource, 
  showActions = true,
  onAdd,
  onRemove,
  isAdded = false,
  loading = false,
  className 
}) => {
  const [resourceDetails, setResourceDetails] = useState(resource);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [error, setError] = useState(null);

  // Load full resource details if only basic info is provided
  useEffect(() => {
    if (resource && !resource.content && !resource.description) {
      loadResourceDetails();
    }
  }, [resource]);

  const loadResourceDetails = async () => {
    if (!resource?.id || !resource?.type) return;

    setLoadingDetails(true);
    setError(null);
    try {
      const details = await resourceService.getResource(resource.type, resource.id);
      setResourceDetails(details);
    } catch (err) {
      console.error('Failed to load resource details:', err);
      setError('Failed to load resource details');
    } finally {
      setLoadingDetails(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getResourceIcon = (type) => {
    switch (type) {
      case 'file': return <File className="w-5 h-5" />;
      case 'article': return <FileText className="w-5 h-5" />;
      case 'web': return <Globe className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const colors = {
      file: 'bg-blue-100 text-blue-700 border-blue-200',
      article: 'bg-green-100 text-green-700 border-green-200',
      web: 'bg-purple-100 text-purple-700 border-purple-200'
    };
    
    const labels = {
      file: 'File',
      article: 'Text',
      web: 'Web Page'
    };
    
    return (
      <Badge className={cn('text-xs', colors[type] || colors.article)}>
        {labels[type] || type}
      </Badge>
    );
  };

  const getStatusBadge = (status) => {
    const colors = {
      completed: 'bg-green-100 text-green-700 border-green-200',
      processing: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      pending: 'bg-gray-100 text-gray-700 border-gray-200',
      failed: 'bg-red-100 text-red-700 border-red-200'
    };
    
    return (
      <Badge className={cn('text-xs', colors[status] || colors.pending)}>
        {status || 'pending'}
      </Badge>
    );
  };

  const truncateText = (text, maxLength = 150) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (!resource) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center text-muted-foreground">
          <AlertCircle className="w-8 h-8 mx-auto mb-2" />
          <p>No resource selected</p>
        </CardContent>
      </Card>
    );
  }

  if (loadingDetails) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <div className="flex items-center space-x-3">
            <Skeleton className="w-5 h-5" />
            <Skeleton className="h-6 w-48" />
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "w-full transition-all duration-200",
      isAdded && "ring-2 ring-green-500 bg-green-50/50",
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <div className="text-primary mt-0.5">
              {getResourceIcon(resource.type)}
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate mb-2">
                {resourceDetails.name || resourceDetails.title || 'Untitled'}
              </CardTitle>
              <div className="flex items-center space-x-2 mb-2">
                {getResourceTypeBadge(resource.type)}
                {resourceDetails.status && getStatusBadge(resourceDetails.status)}
                {isAdded && (
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    <Check className="w-3 h-3 mr-1" />
                    Added
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Description */}
        {resourceDetails.description && (
          <div>
            <h4 className="text-sm font-medium mb-1">Description</h4>
            <p className="text-sm text-muted-foreground">
              {truncateText(resourceDetails.description)}
            </p>
          </div>
        )}

        {/* Content Preview */}
        {resource.type === 'web' && resourceDetails.url && (
          <div>
            <h4 className="text-sm font-medium mb-1">URL</h4>
            <div className="flex items-center space-x-2">
              <ExternalLink className="w-4 h-4 text-muted-foreground" />
              <a 
                href={resourceDetails.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-primary hover:underline truncate"
              >
                {resourceDetails.url}
              </a>
            </div>
          </div>
        )}

        {resource.type === 'article' && resourceDetails.content && (
          <div>
            <h4 className="text-sm font-medium mb-1">Content Preview</h4>
            <div className="bg-muted/50 rounded-lg p-3">
              <p className="text-sm text-muted-foreground">
                {truncateText(resourceDetails.content, 200)}
              </p>
            </div>
          </div>
        )}

        {resource.type === 'file' && (
          <div>
            <h4 className="text-sm font-medium mb-1">File Information</h4>
            <div className="grid grid-cols-2 gap-3 text-sm">
              {resourceDetails.file_size && (
                <div>
                  <span className="text-muted-foreground">Size:</span>
                  <span className="ml-1 font-medium">
                    {formatFileSize(resourceDetails.file_size)}
                  </span>
                </div>
              )}
              {resourceDetails.file_type && (
                <div>
                  <span className="text-muted-foreground">Type:</span>
                  <span className="ml-1 font-medium">
                    {resourceDetails.file_type}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="pt-3 border-t border-border">
          <div className="grid grid-cols-2 gap-3 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Calendar className="w-3 h-3" />
              <span>Created {formatDate(resourceDetails.created_at)}</span>
            </div>
            {resourceDetails.owner_name && (
              <div className="flex items-center space-x-1">
                <User className="w-3 h-3" />
                <span>{resourceDetails.owner_name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-3 border-t border-border">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Navigate to resource view
                const urlType = resource.type === 'article' ? 'text' : resource.type;
                window.open(`/resources/${urlType}/${resource.id}/view`, '_blank');
              }}
            >
              <Eye className="w-4 h-4 mr-1" />
              View
            </Button>

            {isAdded ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRemove?.(resource)}
                disabled={loading}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="w-4 h-4 mr-1" />
                Remove
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={() => onAdd?.(resource)}
                disabled={loading}
              >
                <Check className="w-4 h-4 mr-1" />
                Add to Library
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ResourcePreview;
