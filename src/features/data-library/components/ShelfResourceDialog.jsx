import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Alert,
  AlertDescription,
  Separator
} from 'components/ui';
import {
  ArrowLeft,
  Package,
  Plus,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import ShelfSelector from './ShelfSelector';
import BulkResourceSelector from './BulkResourceSelector';
import ResourceCreator from 'features/resources/components/ResourceCreator';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const ShelfResourceDialog = ({
  open,
  onOpenChange,
  libraryId,
  library,
  onResourcesAdded
}) => {
  const [currentStep, setCurrentStep] = useState('shelf-selection'); // 'shelf-selection' | 'resource-selection' | 'resource-creation'
  const [selectedShelf, setSelectedShelf] = useState(null);
  const [selectedShelfData, setSelectedShelfData] = useState(null);
  const [selectedResources, setSelectedResources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [shelves, setShelves] = useState([]);

  // Load shelves when dialog opens
  useEffect(() => {
    if (open && libraryId) {
      loadShelves();
    }
  }, [open, libraryId]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setCurrentStep('shelf-selection');
      setSelectedShelf(null);
      setSelectedShelfData(null);
      setSelectedResources([]);
    }
  }, [open]);

  const loadShelves = async () => {
    try {
      const response = await dataLibraryService.getLibraryShelves(libraryId);
      setShelves(response.shelves || []);
    } catch (error) {
      console.error('Failed to load shelves:', error);
      toast.error('Failed to load shelves');
    }
  };

  const handleShelfSelect = (shelfId) => {
    const shelf = shelves.find(s => s.id === shelfId);
    setSelectedShelf(shelfId);
    setSelectedShelfData(shelf);
    setCurrentStep('resource-selection');
    setSelectedResources([]); // Reset selected resources when changing shelf
  };

  const handleBackToShelfSelection = () => {
    setCurrentStep('shelf-selection');
    setSelectedShelf(null);
    setSelectedShelfData(null);
    setSelectedResources([]);
  };

  const handleResourceSelectionChange = (newSelection) => {
    setSelectedResources(newSelection);
  };

  const handleAddSelectedResources = async (resourceIds) => {
    if (resourceIds.length === 0 || !selectedShelf) return;

    setLoading(true);
    try {
      // Add each resource to the selected shelf
      for (const resourceId of resourceIds) {
        await dataLibraryService.addResourceToShelf(selectedShelf, resourceId);
      }
      
      toast.success(`Added ${resourceIds.length} resource(s) to shelf "${selectedShelfData?.name}"`);
      
      // Close dialog and notify parent
      onOpenChange(false);
      onResourcesAdded?.();
    } catch (error) {
      console.error('Failed to add resources to shelf:', error);
      toast.error('Failed to add resources to shelf');
    } finally {
      setLoading(false);
    }
  };

  const handleResourceCreated = async (resource) => {
    if (!selectedShelf) {
      toast.error('Please select a shelf before creating resources');
      return;
    }

    try {
      // Add the newly created resource to the selected shelf
      await dataLibraryService.addResourceToShelf(selectedShelf, resource.id);
      toast.success(`Created and added "${resource.name}" to shelf "${selectedShelfData?.name}"`);

      // Close dialog and notify parent
      onOpenChange(false);
      onResourcesAdded?.();
    } catch (error) {
      console.error('Failed to add created resource to shelf:', error);
      toast.error('Resource created but failed to add to shelf');
    }
  };

  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file': return '📁';
      case 'article': return '📄';
      case 'web': return '🌐';
      default: return '📄';
    }
  };

  const getResourceTypeLabel = (type) => {
    switch (type) {
      case 'file': return 'Files';
      case 'article': return 'Articles';
      case 'web': return 'Web Pages';
      default: return type;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {currentStep !== 'shelf-selection' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToShelfSelection}
                className="p-1 h-auto mr-2"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
            )}
            <span>
              {currentStep === 'shelf-selection' && 'Select a Shelf'}
              {currentStep === 'resource-selection' && `Add Resources to "${selectedShelfData?.name}"`}
              {currentStep === 'resource-creation' && `Create Resource for "${selectedShelfData?.name}"`}
            </span>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          {currentStep === 'shelf-selection' && (
            <div className="space-y-4">
              <Alert>
                <Package className="h-4 w-4" />
                <AlertDescription>
                  Resources must be organized within shelves. Select a shelf to add resources that match its type.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                {shelves.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>No shelves available</p>
                    <p className="text-sm">Create shelves first to organize your resources</p>
                  </div>
                ) : (
                  shelves.map((shelf) => (
                    <Card 
                      key={shelf.id} 
                      className="cursor-pointer hover:shadow-sm transition-shadow"
                      onClick={() => handleShelfSelect(shelf.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-2xl">
                              {getResourceTypeIcon(shelf.resource_type)}
                            </div>
                            <div>
                              <h3 className="font-medium">{shelf.name}</h3>
                              {shelf.description && (
                                <p className="text-sm text-muted-foreground">{shelf.description}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">
                              {getResourceTypeLabel(shelf.resource_type)}
                            </Badge>
                            <Badge variant="secondary">
                              {shelf.resource_count || 0} resources
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          )}

          {currentStep === 'resource-selection' && selectedShelfData && (
            <div className="space-y-6">
              {/* Selected Shelf Info */}
              <Card className="bg-muted/30">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">
                        {getResourceTypeIcon(selectedShelfData.resource_type)}
                      </div>
                      <div>
                        <h3 className="font-medium">Adding to: {selectedShelfData.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Only {getResourceTypeLabel(selectedShelfData.resource_type).toLowerCase()} can be added to this shelf
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentStep('resource-creation')}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Create New
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Resource Selector */}
              <BulkResourceSelector
                library={library}
                selectedResources={selectedResources}
                onSelectionChange={handleResourceSelectionChange}
                onAdd={handleAddSelectedResources}
                loading={loading}
                resourceTypeFilter={selectedShelfData.resource_type}
              />
            </div>
          )}

          {currentStep === 'resource-creation' && selectedShelfData && (
            <div className="space-y-6">
              {/* Selected Shelf Info */}
              <Card className="bg-muted/30">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {getResourceTypeIcon(selectedShelfData.resource_type)}
                    </div>
                    <div>
                      <h3 className="font-medium">Creating for: {selectedShelfData.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Creating a {getResourceTypeLabel(selectedShelfData.resource_type).toLowerCase().slice(0, -1)} resource
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Resource Creator */}
              <ResourceCreator
                onResourceCreated={handleResourceCreated}
                onClear={() => setCurrentStep('resource-selection')}
                selectedType={selectedShelfData.resource_type}
                disabled={false}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShelfResourceDialog;
