import React, { useState, useCallback } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Button,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
  Textarea
} from 'components/ui';
import {
  Plus,
  Upload,
  FileText,
  Globe,
  Link as LinkIcon,
  Zap,
  Loader2
} from 'lucide-react';
import { resourceService, dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const QuickAddResourceButton = ({ 
  library, 
  onResourceAdded, 
  variant = "default",
  size = "default",
  className 
}) => {
  const [quickCreateOpen, setQuickCreateOpen] = useState(false);
  const [quickCreateType, setQuickCreateType] = useState('article');
  const [quickData, setQuickData] = useState({ name: '', content: '' });
  const [creating, setCreating] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const handleQuickCreate = async () => {
    if (!quickData.name.trim()) {
      toast.error('Name is required');
      return;
    }

    if (quickCreateType !== 'file' && !quickData.content.trim()) {
      toast.error('Content is required');
      return;
    }

    setCreating(true);
    try {
      // Create resource
      const resourceData = {
        name: quickData.name,
        content: quickData.content,
        description: quickData.description || ''
      };

      const createdResource = await resourceService.createResource(quickCreateType, resourceData);
      
      // Add to library
      await dataLibraryService.bulkAddResources(library.id, [createdResource.id]);
      
      toast.success(`Added "${quickData.name}" to library`);
      
      // Reset and close
      setQuickData({ name: '', content: '', description: '' });
      setQuickCreateOpen(false);
      onResourceAdded?.();
    } catch (error) {
      console.error('Failed to create resource:', error);
      toast.error('Failed to create resource');
    } finally {
      setCreating(false);
    }
  };

  const handleFileUpload = async (file) => {
    if (!file) return;

    setCreating(true);
    try {
      const resourceData = {
        name: file.name,
        file: file,
        description: `Uploaded file: ${file.name}`
      };

      const createdResource = await resourceService.createResource('file', resourceData);
      await dataLibraryService.bulkAddResources(library.id, [createdResource.id]);
      
      toast.success(`Uploaded "${file.name}" to library`);
      onResourceAdded?.();
    } catch (error) {
      console.error('Failed to upload file:', error);
      toast.error('Failed to upload file');
    } finally {
      setCreating(false);
    }
  };

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  }, []);

  const quickCreateTypes = [
    { 
      type: 'article', 
      label: 'Quick Note', 
      icon: FileText, 
      placeholder: 'Write your note here...',
      description: 'Create a quick text note'
    },
    { 
      type: 'web', 
      label: 'Web Page', 
      icon: Globe, 
      placeholder: 'https://example.com',
      description: 'Save a web page'
    }
  ];

  return (
    <>
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          "relative",
          dragActive && "ring-2 ring-primary ring-offset-2 rounded-lg"
        )}
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant={variant} 
              size={size} 
              className={cn(
                "relative",
                dragActive && "bg-primary/10 border-primary",
                className
              )}
              disabled={creating}
            >
              {creating ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Plus className="w-4 h-4 mr-2" />
              )}
              {creating ? 'Adding...' : 'Quick Add'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem
              onClick={() => document.getElementById('quick-file-input')?.click()}
              className="cursor-pointer"
            >
              <Upload className="w-4 h-4 mr-2" />
              <div>
                <div className="font-medium">Upload File</div>
                <div className="text-xs text-muted-foreground">
                  Documents, images, etc.
                </div>
              </div>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            {quickCreateTypes.map(({ type, label, icon: Icon, description }) => (
              <DropdownMenuItem
                key={type}
                onClick={() => {
                  setQuickCreateType(type);
                  setQuickCreateOpen(true);
                }}
                className="cursor-pointer"
              >
                <Icon className="w-4 h-4 mr-2" />
                <div>
                  <div className="font-medium">{label}</div>
                  <div className="text-xs text-muted-foreground">
                    {description}
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Hidden file input */}
        <input
          id="quick-file-input"
          type="file"
          className="hidden"
          onChange={(e) => handleFileUpload(e.target.files[0])}
        />

        {/* Drag overlay */}
        {dragActive && (
          <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center pointer-events-none">
            <div className="text-center">
              <Upload className="w-8 h-8 mx-auto mb-2 text-primary" />
              <p className="text-sm font-medium text-primary">Drop file to add to library</p>
            </div>
          </div>
        )}
      </div>

      {/* Quick Create Dialog */}
      <Dialog open={quickCreateOpen} onOpenChange={setQuickCreateOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5" />
              <span>Quick Add {quickCreateTypes.find(t => t.type === quickCreateType)?.label}</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="quick-name">Name *</Label>
              <Input
                id="quick-name"
                value={quickData.name}
                onChange={(e) => setQuickData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter name"
                autoFocus
              />
            </div>

            <div>
              <Label htmlFor="quick-content">
                {quickCreateType === 'web' ? 'URL *' : 'Content *'}
              </Label>
              {quickCreateType === 'web' ? (
                <div className="relative">
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="quick-content"
                    value={quickData.content}
                    onChange={(e) => setQuickData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="https://example.com"
                    className="pl-10"
                  />
                </div>
              ) : (
                <Textarea
                  id="quick-content"
                  value={quickData.content}
                  onChange={(e) => setQuickData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder={quickCreateTypes.find(t => t.type === quickCreateType)?.placeholder}
                  rows={4}
                />
              )}
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setQuickCreateOpen(false)}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleQuickCreate}
                disabled={creating || !quickData.name.trim() || !quickData.content.trim()}
              >
                {creating && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Add to Library
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QuickAddResourceButton;
