import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Globe,
  Upload,
  Search,
  Filter,
  MoreVertical,
  FolderOpen,
  Package
} from 'lucide-react';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Badge,
  Skeleton,
  Alert,
  AlertDescription
} from 'components/ui';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const ShelfManager = ({
  libraryId,
  library = null,
  className
}) => {
  const navigate = useNavigate();
  const [shelves, setShelves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [resourceTypeFilter, setResourceTypeFilter] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingShelf, setEditingShelf] = useState(null);
  const [expandedShelves, setExpandedShelves] = useState(new Set());

  // Load library shelves
  const loadShelves = async () => {
    if (!libraryId) return;
    
    setLoading(true);
    try {
      const response = await dataLibraryService.getLibraryShelves(
        libraryId,
        resourceTypeFilter === 'all' ? null : resourceTypeFilter
      );
      setShelves(response.shelves || []);
    } catch (error) {
      console.error('Failed to load library shelves:', error);
      toast.error('Failed to load shelves');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadShelves();
  }, [libraryId, resourceTypeFilter]);

  const handleCreateShelf = async (shelfData) => {
    try {
      await dataLibraryService.createShelf({
        library_id: libraryId,
        ...shelfData
      });
      toast.success('Shelf created successfully');
      setShowCreateDialog(false);
      loadShelves();
    } catch (error) {
      console.error('Failed to create shelf:', error);
      toast.error('Failed to create shelf');
    }
  };

  const handleUpdateShelf = async (shelfId, updateData) => {
    try {
      await dataLibraryService.updateShelf(shelfId, updateData);
      toast.success('Shelf updated successfully');
      setEditingShelf(null);
      loadShelves();
    } catch (error) {
      console.error('Failed to update shelf:', error);
      toast.error('Failed to update shelf');
    }
  };

  const handleDeleteShelf = async (shelfId) => {
    try {
      await dataLibraryService.deleteShelf(shelfId);
      toast.success('Shelf deleted successfully');
      loadShelves();
    } catch (error) {
      console.error('Failed to delete shelf:', error);
      toast.error('Failed to delete shelf');
    }
  };

  const toggleShelfExpansion = (shelfId) => {
    const newExpanded = new Set(expandedShelves);
    if (newExpanded.has(shelfId)) {
      newExpanded.delete(shelfId);
    } else {
      newExpanded.add(shelfId);
    }
    setExpandedShelves(newExpanded);
  };

  const handleShelfClick = (shelf) => {
    navigate(`/data-library/library/${libraryId}/shelf/${shelf.id}`);
  };

  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <Upload className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const variants = {
      file: 'default',
      article: 'secondary',
      web: 'outline'
    };
    
    return (
      <Badge variant={variants[type] || 'default'} className="text-xs">
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const filteredShelves = shelves.filter(shelf =>
    shelf.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    shelf.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-2/3" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-1/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search shelves..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
            <SelectTrigger className="w-[180px]">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="file">Files</SelectItem>
              <SelectItem value="article">Articles</SelectItem>
              <SelectItem value="web">Web Pages</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Shelf
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Shelf</DialogTitle>
            </DialogHeader>
            <ShelfForm
              onSubmit={handleCreateShelf}
              onCancel={() => setShowCreateDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Shelves List */}
      {filteredShelves.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FolderOpen className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No shelves found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery || resourceTypeFilter !== 'all' 
                ? 'No shelves match your current filters.'
                : 'Create your first shelf to organize resources.'
              }
            </p>
            {!searchQuery && resourceTypeFilter === 'all' && (
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Shelf
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredShelves.map((shelf) => (
            <Card
              key={shelf.id}
              className="hover:shadow-sm transition-shadow cursor-pointer"
              onClick={(e) => {
                // Don't trigger navigation if clicking on action buttons
                if (!e.target.closest('[data-action-button]')) {
                  handleShelfClick(shelf);
                }
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleShelfExpansion(shelf.id);
                      }}
                      className="p-1 h-auto"
                      data-action-button
                    >
                      <FolderOpen className="w-5 h-5 text-muted-foreground" />
                    </Button>
                    <div>
                      <h3 className="font-medium">{shelf.name}</h3>
                      {shelf.description && (
                        <p className="text-sm text-muted-foreground">
                          {shelf.description}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {shelf.resource_count} {shelf.resource_count === 1 ? 'resource' : 'resources'}
                    </Badge>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          data-action-button
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditingShelf(shelf)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Shelf
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteShelf(shelf.id)}
                          className="text-destructive"
                          disabled={shelf.resource_count > 0}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Shelf
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              
              {expandedShelves.has(shelf.id) && (
                <CardContent className="pt-0">
                  <div className="border-t pt-4">
                    <p className="text-sm text-muted-foreground">
                      Resources in this shelf will be displayed here in a future update.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Edit Shelf Dialog */}
      <Dialog open={!!editingShelf} onOpenChange={() => setEditingShelf(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Shelf</DialogTitle>
          </DialogHeader>
          {editingShelf && (
            <ShelfForm
              initialData={editingShelf}
              onSubmit={(data) => handleUpdateShelf(editingShelf.id, data)}
              onCancel={() => setEditingShelf(null)}
              isEditing
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Simple shelf form component
const ShelfForm = ({ initialData = {}, onSubmit, onCancel, isEditing = false }) => {
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    description: initialData.description || ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      toast.error('Shelf name is required');
      return;
    }
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium mb-2 block">Name</label>
        <Input
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Enter shelf name"
          required
        />
      </div>
      
      <div>
        <label className="text-sm font-medium mb-2 block">Description</label>
        <Input
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Enter shelf description (optional)"
        />
      </div>
      

      
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEditing ? 'Update' : 'Create'} Shelf
        </Button>
      </div>
    </form>
  );
};

export default ShelfManager;
