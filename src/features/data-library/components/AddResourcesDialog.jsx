import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Button,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Input,
  Label,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  Badge,
  Checkbox,
  Alert,
  AlertDescription,
  Separator
} from 'components/ui';
import {
  Plus,
  Upload,
  FileText,
  Globe,
  Search,
  X,
  Check,
  Loader2,
  AlertCircle,
  File,
  Link as LinkIcon,
  MousePointer,
  ArrowLeft
} from 'lucide-react';
import { resourceService, dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import BulkResourceSelector from './BulkResourceSelector';

const AddResourcesDialog = ({
  library,
  onResourcesAdded,
  trigger,
  className
}) => {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('existing');
  const [loading, setLoading] = useState(false);

  // Shelf selection
  const [shelves, setShelves] = useState([]);
  const [selectedShelf, setSelectedShelf] = useState(null);
  const [selectedShelfData, setSelectedShelfData] = useState(null);
  const [loadingShelves, setLoadingShelves] = useState(false);
  const [showResourceSelection, setShowResourceSelection] = useState(false);

  // Existing resources tab
  const [existingResources, setExistingResources] = useState([]);
  const [selectedResources, setSelectedResources] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [resourceTypeFilter, setResourceTypeFilter] = useState('all');
  const [loadingExisting, setLoadingExisting] = useState(false);

  // Create new resource tab
  const [newResourceType, setNewResourceType] = useState('file');
  const [newResourceData, setNewResourceData] = useState({
    name: '',
    description: '',
    content: '',
    file: null,
    websiteType: 'single'
  });
  const [dragActive, setDragActive] = useState(false);
  const [creatingResource, setCreatingResource] = useState(false);

  // Load shelves when dialog opens
  useEffect(() => {
    if (open) {
      loadShelves();
    }
  }, [open]);

  // Load existing resources when dialog opens
  useEffect(() => {
    if (open && activeTab === 'existing') {
      loadExistingResources();
    }
  }, [open, activeTab, searchQuery, resourceTypeFilter]);

  const loadShelves = async () => {
    setLoadingShelves(true);
    try {
      const response = await dataLibraryService.getLibraryShelves(library.id);
      setShelves(response.shelves || []);
    } catch (error) {
      console.error('Failed to load shelves:', error);
      toast.error('Failed to load shelves');
    } finally {
      setLoadingShelves(false);
    }
  };

  const loadExistingResources = async () => {
    setLoadingExisting(true);
    try {
      const response = await resourceService.getAllResources({ 
        limit: 100,
        search: searchQuery 
      });
      
      // Filter out resources already in the library
      const libraryResourceIds = new Set([
        ...(library.file_ids || []),
        ...(library.article_ids || []),
        ...(library.webpage_ids || [])
      ]);
      
      const availableResources = response.resources.filter(resource => 
        !libraryResourceIds.has(resource.id) &&
        (resourceTypeFilter === 'all' || resource.type === resourceTypeFilter)
      );
      
      setExistingResources(availableResources);
    } catch (error) {
      console.error('Failed to load resources:', error);
      toast.error('Failed to load available resources');
    } finally {
      setLoadingExisting(false);
    }
  };

  const handleResourceSelect = (resourceId, selected) => {
    setSelectedResources(prev => 
      selected 
        ? [...prev, resourceId]
        : prev.filter(id => id !== resourceId)
    );
  };

  const handleSelectAll = () => {
    const allSelected = selectedResources.length === existingResources.length;
    setSelectedResources(allSelected ? [] : existingResources.map(r => r.id));
  };

  const handleAddExistingResources = async () => {
    if (selectedResources.length === 0) return;

    if (!selectedShelf) {
      toast.error('Please select a shelf before adding resources');
      return;
    }

    setLoading(true);
    try {
      // Add each resource to the selected shelf
      for (const resourceId of selectedResources) {
        await dataLibraryService.addResourceToShelf(selectedShelf, resourceId);
      }
      toast.success(`Added ${selectedResources.length} resource(s) to shelf`);

      // Reset state and close dialog
      setSelectedResources([]);
      setSelectedShelf(null);
      setOpen(false);
      onResourcesAdded?.();
    } catch (error) {
      console.error('Failed to add resources:', error);
      toast.error('Failed to add resources to shelf');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAndAddResource = async () => {
    if (!newResourceData.name.trim()) {
      toast.error('Resource name is required');
      return;
    }

    if (!selectedShelf) {
      toast.error('Please select a shelf before creating resources');
      return;
    }

    setCreatingResource(true);
    try {
      // Create the resource first using the selected shelf's resource type
      const resourceType = selectedShelfData?.resource_type || 'file';
      const resourceData = {
        name: newResourceData.name,
        description: newResourceData.description,
        content: resourceType === 'web' ? newResourceData.content : undefined,
        file: resourceType === 'file' ? newResourceData.file : undefined,
        websiteType: resourceType === 'web' ? newResourceData.websiteType : undefined
      };

      const createdResource = await resourceService.createResource(resourceType, resourceData);

      // Add the resource to the selected shelf
      await dataLibraryService.addResourceToShelf(selectedShelf, createdResource.id);

      toast.success(`Created and added "${newResourceData.name}" to shelf`);

      // Reset form and close dialog
      setNewResourceData({
        name: '',
        description: '',
        content: '',
        file: null,
        websiteType: 'single'
      });
      setSelectedShelf(null);
      setOpen(false);
      onResourcesAdded?.();
    } catch (error) {
      console.error('Failed to create and add resource:', error);
      toast.error('Failed to create resource');
    } finally {
      setCreatingResource(false);
    }
  };

  const handleShelfSelection = (shelfId) => {
    const shelf = shelves.find(s => s.id === shelfId);
    setSelectedShelf(shelfId);
    setSelectedShelfData(shelf);
    setShowResourceSelection(true);
    setSelectedResources([]); // Reset selected resources when changing shelf
  };

  const handleBackToShelfSelection = () => {
    setShowResourceSelection(false);
    setSelectedShelf(null);
    setSelectedShelfData(null);
    setSelectedResources([]);
  };

  const handleFileChange = (file) => {
    setNewResourceData(prev => ({ ...prev, file }));
    if (file && !newResourceData.name) {
      setNewResourceData(prev => ({ ...prev, name: file.name }));
    }
  };

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      setNewResourceType('file');
      setActiveTab('create');
      handleFileChange(files[0]);
    }
  }, []);

  const getResourceIcon = (type) => {
    switch (type) {
      case 'file': return <File className="w-4 h-4" />;
      case 'article': return <FileText className="w-4 h-4" />;
      case 'web': return <Globe className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const colors = {
      file: 'bg-blue-100 text-blue-700',
      article: 'bg-green-100 text-green-700',
      web: 'bg-purple-100 text-purple-700'
    };
    
    return (
      <Badge className={cn('text-xs', colors[type] || colors.article)}>
        {type === 'article' ? 'Text' : type}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className={className}>
            <Plus className="w-4 h-4 mr-2" />
            Add Resources
          </Button>
        )}
      </DialogTrigger>
      <DialogContent 
        className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Plus className="w-5 h-5" />
            <span>Add Resources to "{library.name}"</span>
          </DialogTitle>
        </DialogHeader>

        {!showResourceSelection ? (
          /* Shelf Selection Step */
          <div className="flex-1 overflow-auto p-1">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Step 1: Select Target Shelf</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Choose which shelf you want to add resources to. Only resources matching the shelf's type will be available.
                </p>
              </div>

              {loadingShelves ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin mr-2" />
                  <span>Loading shelves...</span>
                </div>
              ) : shelves.length === 0 ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No shelves available in this library. Please create shelves first.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {shelves.map((shelf) => (
                    <Card
                      key={shelf.id}
                      className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary/50"
                      onClick={() => handleShelfSelection(shelf.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {shelf.resource_type === 'file' && <File className="w-4 h-4 text-purple-600" />}
                            {shelf.resource_type === 'article' && <FileText className="w-4 h-4 text-orange-600" />}
                            {shelf.resource_type === 'web' && <Globe className="w-4 h-4 text-cyan-600" />}
                            <h4 className="font-medium">{shelf.name}</h4>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {shelf.resource_type}
                          </Badge>
                        </div>

                        {shelf.description && (
                          <p className="text-xs text-muted-foreground mb-2">
                            {shelf.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">
                            {shelf.resource_count || 0} resources
                          </span>
                          <span className="text-primary font-medium">
                            Select →
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          /* Resource Selection Step */
          <div className="flex-1 flex flex-col">
            {/* Selected Shelf Info */}
            <div className="p-4 bg-muted/50 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBackToShelfSelection}
                    className="gap-2"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Change Shelf
                  </Button>
                  <div className="flex items-center space-x-2">
                    {selectedShelfData?.resource_type === 'file' && <File className="w-4 h-4 text-purple-600" />}
                    {selectedShelfData?.resource_type === 'article' && <FileText className="w-4 h-4 text-orange-600" />}
                    {selectedShelfData?.resource_type === 'web' && <Globe className="w-4 h-4 text-cyan-600" />}
                    <div>
                      <h4 className="font-medium">Adding to: {selectedShelfData?.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        Showing only {selectedShelfData?.resource_type} resources
                      </p>
                    </div>
                  </div>
                </div>
                <Badge variant="outline">
                  {selectedShelfData?.resource_type}
                </Badge>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
                <TabsTrigger value="existing" className="flex items-center space-x-2">
                  <MousePointer className="w-4 h-4" />
                  <span>Select Existing</span>
                </TabsTrigger>
                <TabsTrigger value="create" className="flex items-center space-x-2">
                  <Plus className="w-4 h-4" />
                  <span>Create New</span>
                </TabsTrigger>
              </TabsList>

              {/* Existing Resources Tab */}
              <TabsContent value="existing" className="flex-1 flex flex-col">
                <div className="flex-1 p-4">
                  <BulkResourceSelector
                    library={library}
                    selectedResources={selectedResources}
                    onSelectionChange={setSelectedResources}
                    onAdd={handleAddExistingResources}
                    loading={loading}
                    resourceTypeFilter={selectedShelfData?.resource_type}
                    className="h-full"
                  />
                </div>
              </TabsContent>




              {/* Create New Resource Tab */}
              <TabsContent value="create" className="flex-1 flex flex-col space-y-4">
                {/* Resource Type Info */}
                <div className="p-3 bg-muted rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    {selectedShelfData?.resource_type === 'file' && <Upload className="w-4 h-4 text-purple-600" />}
                    {selectedShelfData?.resource_type === 'article' && <FileText className="w-4 h-4 text-orange-600" />}
                    {selectedShelfData?.resource_type === 'web' && <Globe className="w-4 h-4 text-cyan-600" />}
                    <span className="font-medium">
                      Creating {selectedShelfData?.resource_type} resource for "{selectedShelfData?.name}"
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Resource type is automatically set to match the selected shelf
                  </p>
                </div>

            {/* Resource Creation Form */}
            <div className="flex-1 overflow-auto space-y-4">
              {/* Common Fields */}
              <div className="space-y-3">
                <div>
                  <Label htmlFor="resource-name">Name *</Label>
                  <Input
                    id="resource-name"
                    value={newResourceData.name}
                    onChange={(e) => setNewResourceData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter resource name"
                  />
                </div>
                <div>
                  <Label htmlFor="resource-description">Description</Label>
                  <Textarea
                    id="resource-description"
                    value={newResourceData.description}
                    onChange={(e) => setNewResourceData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Optional description"
                    rows={2}
                  />
                </div>
              </div>

                {/* Type-specific Fields */}
                {selectedShelfData?.resource_type === 'file' && (
                <div className={cn(
                  "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                  dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                  newResourceData.file && "border-primary bg-primary/5"
                )}>
                  {newResourceData.file ? (
                    <div className="space-y-2">
                      <File className="w-8 h-8 mx-auto text-primary" />
                      <p className="font-medium">{newResourceData.file.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {(newResourceData.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleFileChange(null)}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
                      <p>Drop files here or click to browse</p>
                      <input
                        type="file"
                        className="hidden"
                        id="file-input"
                        onChange={(e) => handleFileChange(e.target.files[0])}
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('file-input').click()}
                      >
                        Browse Files
                      </Button>
                    </div>
                  )}
                </div>
              )}

                {selectedShelfData?.resource_type === 'article' && (
                <div>
                  <Label htmlFor="article-content">Content *</Label>
                  <Textarea
                    id="article-content"
                    value={newResourceData.content}
                    onChange={(e) => setNewResourceData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Enter your text content here..."
                    rows={8}
                  />
                </div>
              )}

                {selectedShelfData?.resource_type === 'web' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="web-url">URL *</Label>
                    <div className="relative">
                      <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        id="web-url"
                        value={newResourceData.content}
                        onChange={(e) => setNewResourceData(prev => ({ ...prev, content: e.target.value }))}
                        placeholder="https://example.com"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="website-type">Crawl Type</Label>
                    <Select
                      value={newResourceData.websiteType}
                      onValueChange={(value) => setNewResourceData(prev => ({ ...prev, websiteType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="single">Single Page</SelectItem>
                        <SelectItem value="recursive">Recursive Crawl</SelectItem>
                        <SelectItem value="sitemap">Sitemap</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>

            {/* Shelf Selection for New Resource */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Target Shelf *
              </label>
              {loadingShelves ? (
                <div className="flex items-center space-x-2 p-2 border rounded">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading shelves...</span>
                </div>
              ) : shelves.length > 0 ? (
                <Select value={selectedShelf || ''} onValueChange={setSelectedShelf}>
                  <SelectTrigger className={!selectedShelf ? 'border-destructive' : ''}>
                    <SelectValue placeholder="Select a shelf" />
                  </SelectTrigger>
                  <SelectContent>
                    {shelves.map((shelf) => (
                      <SelectItem key={shelf.id} value={shelf.id}>
                        <div className="flex items-center space-x-2">
                          <span>{shelf.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {shelf.resource_type}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No shelves available. Please create shelves first.
                  </AlertDescription>
                </Alert>
              )}
              {!selectedShelf && shelves.length > 0 && (
                <p className="text-xs text-destructive">
                  Please select a shelf to organize your new resource
                </p>
              )}
            </div>

            {/* Create Button */}
            <div className="flex justify-end pt-4 border-t">
              <Button
                onClick={handleCreateAndAddResource}
                disabled={!newResourceData.name.trim() || creatingResource || !selectedShelf || shelves.length === 0}
              >
                {creatingResource && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Create & Add to Shelf
              </Button>
            </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddResourcesDialog;
