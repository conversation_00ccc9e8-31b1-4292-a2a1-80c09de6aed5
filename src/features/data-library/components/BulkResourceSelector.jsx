import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Checkbox,
  Badge,
  Skeleton,
  Alert,
  AlertDescription,
  Separator
} from 'components/ui';
import {
  Search,
  Filter,
  Check,
  X,
  File,
  FileText,
  Globe,
  Users,
  Calendar,
  Loader2
} from 'lucide-react';
import { resourceService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const BulkResourceSelector = ({
  library,
  selectedResources = [],
  onSelectionChange,
  onAdd,
  loading = false,
  resourceTypeFilter = null, // Filter resources by type (file, article, web)
  className
}) => {
  const [resources, setResources] = useState([]);
  const [loadingResources, setLoadingResources] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [error, setError] = useState(null);

  // Load available resources
  useEffect(() => {
    loadResources();
  }, [searchQuery, typeFilter, statusFilter, resourceTypeFilter]);

  const loadResources = async () => {
    setLoadingResources(true);
    setError(null);
    try {
      const response = await resourceService.getAllResources({ 
        limit: 100,
        search: searchQuery 
      });
      
      // Filter out resources already in the library
      const libraryResourceIds = new Set([
        ...(library.file_ids || []),
        ...(library.article_ids || []),
        ...(library.webpage_ids || [])
      ]);
      
      let availableResources = response.resources.filter(resource => 
        !libraryResourceIds.has(resource.id)
      );

      // Apply filters
      // First apply the shelf type filter if provided
      if (resourceTypeFilter) {
        availableResources = availableResources.filter(r => r.type === resourceTypeFilter);
      }

      // Then apply user-selected type filter (only if no shelf type filter)
      if (!resourceTypeFilter && typeFilter !== 'all') {
        availableResources = availableResources.filter(r => r.type === typeFilter);
      }

      if (statusFilter !== 'all') {
        availableResources = availableResources.filter(r => r.status === statusFilter);
      }
      
      setResources(availableResources);
    } catch (err) {
      console.error('Failed to load resources:', err);
      setError('Failed to load available resources');
      toast.error('Failed to load resources');
    } finally {
      setLoadingResources(false);
    }
  };

  // Computed values
  const selectedCount = selectedResources.length;
  const totalCount = resources.length;
  const isAllSelected = selectedCount === totalCount && totalCount > 0;
  const isPartiallySelected = selectedCount > 0 && selectedCount < totalCount;

  // Group resources by type for better organization
  const groupedResources = useMemo(() => {
    const groups = {
      file: [],
      article: [],
      web: []
    };

    resources.forEach(resource => {
      if (groups[resource.type]) {
        groups[resource.type].push(resource);
      }
    });

    return groups;
  }, [resources]);

  const handleSelectAll = () => {
    if (isAllSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(resources.map(r => r.id));
    }
  };

  const handleResourceSelect = (resourceId, selected) => {
    const newSelection = selected 
      ? [...selectedResources, resourceId]
      : selectedResources.filter(id => id !== resourceId);
    
    onSelectionChange(newSelection);
  };

  const getResourceIcon = (type) => {
    switch (type) {
      case 'file': return <File className="w-4 h-4" />;
      case 'article': return <FileText className="w-4 h-4" />;
      case 'web': return <Globe className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const colors = {
      file: 'bg-blue-100 text-blue-700',
      article: 'bg-green-100 text-green-700',
      web: 'bg-purple-100 text-purple-700'
    };
    
    const labels = {
      file: 'File',
      article: 'Text',
      web: 'Web'
    };
    
    return (
      <Badge className={cn('text-xs', colors[type] || colors.article)}>
        {labels[type] || type}
      </Badge>
    );
  };

  const getStatusBadge = (status) => {
    const colors = {
      completed: 'bg-green-100 text-green-700',
      processing: 'bg-yellow-100 text-yellow-700',
      pending: 'bg-gray-100 text-gray-700',
      failed: 'bg-red-100 text-red-700'
    };
    
    return (
      <Badge className={cn('text-xs', colors[status] || colors.pending)}>
        {status || 'pending'}
      </Badge>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            onClick={loadResources}
            className="mt-3"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <span>Select Resources</span>
          <Badge variant="outline">
            {selectedCount} of {totalCount} selected
          </Badge>
        </CardTitle>
        
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search resources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          {resourceTypeFilter ? (
            <div className="flex items-center space-x-2 px-3 py-2 bg-primary/10 rounded-md border border-primary/20">
              <Filter className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-primary">
                {resourceTypeFilter === 'file' && 'Shelf accepts: Files Only'}
                {resourceTypeFilter === 'article' && 'Shelf accepts: Articles Only'}
                {resourceTypeFilter === 'web' && 'Shelf accepts: Web Pages Only'}
              </span>
            </div>
          ) : (
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="file">Files</SelectItem>
                <SelectItem value="article">Text</SelectItem>
                <SelectItem value="web">Web</SelectItem>
              </SelectContent>
            </Select>
          )}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Selection Controls */}
        {totalCount > 0 && (
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <div className="flex items-center space-x-3">
              <Checkbox
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isPartiallySelected;
                }}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm font-medium">
                {isAllSelected ? 'Deselect All' : 'Select All'}
              </span>
            </div>
            <Button
              onClick={() => onAdd(selectedResources)}
              disabled={selectedCount === 0 || loading}
              size="sm"
            >
              {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Add Selected ({selectedCount})
            </Button>
          </div>
        )}

        {/* Resources List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {loadingResources ? (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <Skeleton className="w-4 h-4" />
                  <Skeleton className="w-4 h-4" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="w-16 h-5" />
                </div>
              ))}
            </div>
          ) : totalCount === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No compatible resources found</p>
              <p className="text-sm">
                {resourceTypeFilter
                  ? `No ${resourceTypeFilter} resources are available that can be added to this shelf`
                  : searchQuery || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'Try adjusting your filters'
                    : 'All resources are already in this library'
                }
              </p>
              {resourceTypeFilter && (
                <p className="text-xs mt-2 text-muted-foreground">
                  This shelf only accepts {resourceTypeFilter} resources. Create a new {resourceTypeFilter} resource or select a different shelf.
                </p>
              )}
            </div>
          ) : (
            Object.entries(groupedResources).map(([type, typeResources]) => {
              if (typeResources.length === 0) return null;
              
              return (
                <div key={type}>
                  <div className="flex items-center space-x-2 mb-3">
                    {getResourceIcon(type)}
                    <h3 className="font-medium text-sm">
                      {type === 'article' ? 'Text Documents' : 
                       type === 'file' ? 'Files' : 'Web Pages'} 
                      ({typeResources.length})
                    </h3>
                  </div>
                  
                  <div className="space-y-2 ml-6">
                    {typeResources.map((resource) => {
                      const isSelected = selectedResources.includes(resource.id);
                      
                      return (
                        <div
                          key={resource.id}
                          className={cn(
                            "flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50",
                            isSelected && "bg-primary/5 border-primary"
                          )}
                          onClick={() => handleResourceSelect(resource.id, !isSelected)}
                        >
                          <Checkbox checked={isSelected} readOnly />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="font-medium text-sm truncate">
                                {resource.name || resource.title}
                              </h4>
                              {getResourceTypeBadge(resource.type)}
                              {resource.status && getStatusBadge(resource.status)}
                            </div>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-3 h-3" />
                                <span>{formatDate(resource.created_at)}</span>
                              </div>
                              {resource.owner_name && (
                                <div className="flex items-center space-x-1">
                                  <Users className="w-3 h-3" />
                                  <span>{resource.owner_name}</span>
                                </div>
                              )}
                            </div>
                            {resource.description && (
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-1">
                                {resource.description}
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  
                  {Object.keys(groupedResources).indexOf(type) < Object.keys(groupedResources).length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default BulkResourceSelector;
