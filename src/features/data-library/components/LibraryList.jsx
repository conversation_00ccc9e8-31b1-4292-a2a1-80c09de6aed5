import React from 'react';
import { 
  Database, 
  Search, 
  Plus,
  LayoutGrid,
  List as ListIcon,
  AlertCircle
} from 'lucide-react';
import { 
  Button,
  ToggleGroup,
  ToggleGroupItem,
  Skeleton,
  Alert,
  AlertDescription
} from 'components/ui';
import LibraryCard from './LibraryCard';
import LibraryDataTable from './LibraryDataTable';
import { cn } from '@/lib/utils';

const LibraryList = ({
  libraries = [],
  loading = false,
  error = null,
  viewMode = 'grid',
  onViewModeChange,
  selectedLibraries = [],
  onLibrarySelect,
  onLibraryView,
  onLibraryEdit,
  onLibraryDelete,
  onResourcesAdded,
  onCreateNew,
  emptyStateMessage = "No libraries found",
  emptyStateAction = null,
  className
}) => {
  const handleSelectAll = (checked) => {
    if (checked) {
      const allIds = libraries.map(lib => lib.id);
      onLibrarySelect?.(allIds);
    } else {
      onLibrarySelect?.([]);
    }
  };

  const handleLibrarySelect = (library, checked) => {
    if (checked) {
      onLibrarySelect?.([...selectedLibraries, library.id]);
    } else {
      onLibrarySelect?.(selectedLibraries.filter(id => id !== library.id));
    }
  };

  // Handle selection from data table (receives array of IDs)
  const handleDataTableSelect = (selectedIds) => {
    onLibrarySelect?.(selectedIds);
  };

  const isAllSelected = libraries.length > 0 && selectedLibraries.length === libraries.length;
  const isPartiallySelected = selectedLibraries.length > 0 && selectedLibraries.length < libraries.length;

  // Loading state
  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
        
        {/* Grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="h-48 w-full rounded-lg" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("space-y-4", className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error.message || 'Failed to load libraries. Please try again.'}
          </AlertDescription>
        </Alert>
        {onCreateNew && (
          <div className="text-center">
            <Button onClick={onCreateNew} variant="outline">
              Try Again
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Empty state
  if (!libraries || libraries.length === 0) {
    return (
      <div className={cn("text-center py-16", className)}>
        <div className="mx-auto w-32 h-32 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full flex items-center justify-center mb-6 shadow-lg shadow-blue-100/50">
          <Database className="w-16 h-16 text-blue-600" />
        </div>
        <h3 className="text-xl font-bold text-foreground mb-3">
          {emptyStateMessage}
        </h3>
        <p className="text-muted-foreground mb-8 max-w-md mx-auto leading-relaxed">
          Create your first data library to organize and manage your resources efficiently.
          Libraries help you categorize and structure your content for better accessibility.
        </p>
        {(onCreateNew || emptyStateAction) && (
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {onCreateNew && (
              <Button onClick={onCreateNew} size="lg" className="shadow-lg hover:shadow-xl transition-shadow">
                <Plus className="w-5 h-5 mr-2" />
                Create Your First Library
              </Button>
            )}
            {emptyStateAction}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* List Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Bulk selection - only show for grid view */}
          {onLibrarySelect && viewMode === 'grid' && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isPartiallySelected;
                }}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
              />
              <span className="text-sm text-muted-foreground">
                {selectedLibraries.length > 0
                  ? `${selectedLibraries.length} selected`
                  : 'Select all'
                }
              </span>
            </div>
          )}

          {/* Selection count for list view */}
          {viewMode === 'list' && selectedLibraries.length > 0 && (
            <div className="text-sm text-muted-foreground">
              {selectedLibraries.length} of {libraries.length} selected
            </div>
          )}
        </div>

        {/* View mode toggle */}
        {onViewModeChange && (
          <ToggleGroup
            type="single"
            value={viewMode}
            onValueChange={onViewModeChange}
            className="border rounded-md"
          >
            <ToggleGroupItem value="grid" aria-label="Grid view" size="sm">
              <LayoutGrid className="w-4 h-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List view" size="sm">
              <ListIcon className="w-4 h-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        )}
      </div>

      {/* Libraries Grid/List */}
      {viewMode === 'grid' ? (
        <div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6"
          role="grid"
          aria-label="Libraries displayed in grid view"
        >
          {libraries.map((library) => (
            <LibraryCard
              key={library.id}
              library={library}
              isSelected={selectedLibraries.includes(library.id)}
              onSelect={onLibrarySelect ? handleLibrarySelect : undefined}
              onView={onLibraryView}
              onEdit={onLibraryEdit}
              onDelete={onLibraryDelete}
              onResourcesAdded={onResourcesAdded}
            />
          ))}
        </div>
      ) : (
        <LibraryDataTable
          libraries={libraries}
          selectedLibraries={selectedLibraries}
          onLibrarySelect={handleDataTableSelect}
          onLibraryView={onLibraryView}
          onLibraryEdit={onLibraryEdit}
          onLibraryDelete={onLibraryDelete}
          onResourcesAdded={onResourcesAdded}
          loading={loading}
        />
      )}

      {/* Results summary */}
      <div className="text-center text-sm text-muted-foreground">
        Showing {libraries.length} {libraries.length === 1 ? 'library' : 'libraries'}
      </div>
    </div>
  );
};

export default LibraryList;
