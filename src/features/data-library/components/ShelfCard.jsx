import React from 'react';
import {
  Edit,
  Trash2,
  MoreVertical,
  FolderOpen,
  Package,
  Upload,
  FileText,
  Globe
} from 'lucide-react';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  Badge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from 'components/ui';
import { cn } from '@/lib/utils';

const ShelfCard = ({ 
  shelf, 
  onEdit, 
  onDelete, 
  onClick,
  showActions = true,
  className 
}) => {
  const getResourceTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return <Upload className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getResourceTypeBadge = (type) => {
    const variants = {
      file: 'default',
      article: 'secondary',
      web: 'outline'
    };
    
    return (
      <Badge variant={variants[type] || 'default'} className="text-xs">
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const handleCardClick = (e) => {
    // Don't trigger onClick if clicking on action buttons
    if (e.target.closest('[data-action-button]')) {
      return;
    }
    onClick?.(shelf);
  };

  return (
    <Card 
      className={cn(
        "hover:shadow-sm transition-shadow",
        onClick && "cursor-pointer",
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-muted/50">
              <FolderOpen className="w-5 h-5 text-muted-foreground" />
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-medium">{shelf.name}</h3>
                {getResourceTypeBadge(shelf.resource_type)}
              </div>
              {shelf.description && (
                <p className="text-sm text-muted-foreground">
                  {shelf.description}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {shelf.resource_count || 0} {(shelf.resource_count || 0) === 1 ? 'resource' : 'resources'}
            </Badge>
            
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0"
                    data-action-button
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(shelf)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Shelf
                    </DropdownMenuItem>
                  )}
                  {onEdit && onDelete && <DropdownMenuSeparator />}
                  {onDelete && (
                    <DropdownMenuItem 
                      onClick={() => onDelete(shelf)}
                      className="text-destructive"
                      disabled={(shelf.resource_count || 0) > 0}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Shelf
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            {getResourceTypeIcon(shelf.resource_type)}
            <span>
              {shelf.resource_type.charAt(0).toUpperCase() + shelf.resource_type.slice(1)} Resources
            </span>
          </div>
          
          {shelf.created_at && (
            <span>
              Created {new Date(shelf.created_at).toLocaleDateString()}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ShelfCard;
