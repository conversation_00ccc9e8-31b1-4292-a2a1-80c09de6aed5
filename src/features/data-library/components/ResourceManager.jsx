import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  FileText,
  Globe,
  File,
  Trash2,
  Edit,
  Eye,
  MoreVertical,
  Package,
  AlertCircle
} from 'lucide-react';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  CardHeader,
  Badge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  Alert,
  AlertDescription,
  Skeleton
} from 'components/ui';
import { dataLibraryService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import ShelfResourceDialog from './ShelfResourceDialog';

const ResourceManager = ({
  libraryId,
  library = null,
  className
}) => {
  const navigate = useNavigate();
  const [resources, setResources] = useState([]);
  const [shelves, setShelves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [resourceTypeFilter, setResourceTypeFilter] = useState('all');
  const [showShelfResourceDialog, setShowShelfResourceDialog] = useState(false);

  // Load library resources and shelves
  const loadResources = async () => {
    if (!libraryId) return;

    setLoading(true);
    try {
      // Load resources
      const resourcesResponse = await dataLibraryService.getLibraryResources(libraryId, {
        search: searchQuery,
        resourceType: resourceTypeFilter === 'all' ? '' : resourceTypeFilter,
        limit: 50
      });
      setResources(resourcesResponse.resources || []);

      // Load shelves
      const shelvesResponse = await dataLibraryService.getLibraryShelves(
        libraryId,
        resourceTypeFilter === 'all' ? null : resourceTypeFilter
      );
      setShelves(shelvesResponse.shelves || []);
    } catch (error) {
      console.error('Failed to load library data:', error);
      toast.error('Failed to load library data');
    } finally {
      setLoading(false);
    }
  };

  // Handle resources added through shelf dialog
  const handleResourcesAdded = () => {
    loadResources(); // Reload resources to show the newly added ones
  };

  useEffect(() => {
    loadResources();
  }, [libraryId, searchQuery, resourceTypeFilter]);

  const handleShelfClick = (shelf) => {
    navigate(`/data-library/library/${libraryId}/shelf/${shelf.id}`);
  };



  const getResourceIcon = (type) => {
    switch (type) {
      case 'file':
        return <File className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'web':
        return <Globe className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };



  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Library Resources</h3>
          <p className="text-sm text-muted-foreground">
            Manage resources in {library?.name || 'this library'}
          </p>
        </div>
        
        <div className="flex flex-col items-end space-y-1">
          <Button onClick={() => setShowShelfResourceDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Resources
          </Button>
          <p className="text-xs text-muted-foreground">
            Select a shelf first to add compatible resources
          </p>
        </div>

        <ShelfResourceDialog
          open={showShelfResourceDialog}
          onOpenChange={setShowShelfResourceDialog}
          libraryId={libraryId}
          library={library}
          onResourcesAdded={handleResourcesAdded}
        />
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search resources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="All types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All types</SelectItem>
            <SelectItem value="file">Files</SelectItem>
            <SelectItem value="article">Articles</SelectItem>
            <SelectItem value="web">Web Pages</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Resources List */}
      {loading ? (
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      ) : resources.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No resources found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || resourceTypeFilter !== 'all'
                ? 'No resources match your current filters'
                : 'This library doesn\'t have any resources yet'
              }
            </p>
            {!searchQuery && resourceTypeFilter === 'all' && (
              <Alert className="max-w-md mx-auto mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Resources must be organized within shelves. Select a shelf first to add compatible resources.
                </AlertDescription>
              </Alert>
            )}
            <Button onClick={() => setShowShelfResourceDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Resources
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {shelves.map((shelf) => {
            const shelfResources = resources.filter(r => r.shelf_id === shelf.id);
            if (shelfResources.length === 0 && (searchQuery || resourceTypeFilter !== 'all')) {
              return null; // Hide empty shelves when filtering
            }

            return (
              <Card
                key={shelf.id}
                className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleShelfClick(shelf)}
              >
                <CardHeader className="pb-3 bg-muted/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Package className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <h3 className="font-medium">{shelf.name}</h3>
                        {shelf.description && (
                          <p className="text-sm text-muted-foreground">{shelf.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {shelfResources.length} {shelfResources.length === 1 ? 'resource' : 'resources'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="p-0">
                  {shelfResources.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No resources in this shelf</p>
                    </div>
                  ) : (
                    <div className="divide-y">
                      {shelfResources.map((resource) => (
                        <div key={resource.id} className="p-4 hover:bg-muted/30 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 flex-1 min-w-0">
                              {getResourceIcon(resource.type)}
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">
                                  {resource.name || resource.title}
                                </h4>
                                <p className="text-sm text-muted-foreground truncate">
                                  {resource.description || 'No description'}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Added {formatDate(resource.created_at)}
                                </p>
                              </div>
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Resource
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="w-4 h-4 mr-2" />
                                  Edit Resource
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Remove from Library
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ResourceManager;
