import React, { useState, useEffect } from 'react';
import {
  Button,
  Input,
  Label,
  Textarea,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription
} from 'components/ui';
import { AlertCircle, Loader2 } from 'lucide-react';
import { dataLibraryService } from 'services';

const LibraryForm = ({ 
  library = null, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    settings: {}
  });
  const [errors, setErrors] = useState({});

  // Load existing library data for editing
  useEffect(() => {
    if (library) {
      setFormData({
        name: library.name || '',
        description: library.description || '',
        settings: library.settings || {}
      });
    }
  }, [library]);



  const validateField = (name, value) => {
    switch (name) {
      case 'name':
        if (!value?.trim()) return 'Library name is required';
        if (value.length < 2) return 'Library name must be at least 2 characters';
        if (value.length > 100) return 'Library name must be less than 100 characters';
        return '';
      case 'description':
        if (value && value.length > 500) return 'Description must be less than 500 characters';
        return '';
      default:
        return '';
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    Object.keys(formData).forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) newErrors[field] = error;
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Prevent double submission if already loading
    if (isLoading) {
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      // Error is already handled by the parent component
      // No need to log here to avoid duplicate logs
    }
  };

  const isEditing = !!library;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>
          {isEditing ? 'Edit Library' : 'Create New Library'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6" noValidate>
          {/* Library Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              Library Name *
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter library name"
              className={errors.name ? 'border-destructive' : ''}
              disabled={isLoading}
              aria-describedby={errors.name ? 'name-error' : undefined}
            />
            {errors.name && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription id="name-error">
                  {errors.name}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter library description (optional)"
              rows={3}
              className={errors.description ? 'border-destructive' : ''}
              disabled={isLoading}
              aria-describedby={errors.description ? 'description-error' : undefined}
            />
            {errors.description && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription id="description-error">
                  {errors.description}
                </AlertDescription>
              </Alert>
            )}
          </div>



          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditing ? 'Update Library' : 'Create Library'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default LibraryForm;
