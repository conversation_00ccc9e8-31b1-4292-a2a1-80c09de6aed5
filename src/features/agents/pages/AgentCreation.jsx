import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Check, AlertCircle, CheckCircle } from 'lucide-react';
import agentService from 'services/agentService';
import AgentDetailsForm from '../components/AgentDetailsForm';
import AgentActionsForm from '../components/AgentActionsForm';
import AgentResourcesForm from '../components/AgentResourcesForm';

const STEPS = [
  { id: 'details', name: 'Agent Details' },
  { id: 'actions', name: 'Actions' },
  { id: 'resources', name: 'Resources' },
];

const AgentCreation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [agentData, setAgentData] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState('details');

  // Check if we're editing an existing agent
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const editId = params.get('edit');
    
    if (editId) {
      setIsEditing(true);
      fetchAgentDetails(editId);
    }
  }, [location]);

  const fetchAgentDetails = async (id) => {
    setLoading(true);
    try {
      // Replace with actual API call when implemented
      const result = await agentService.getAgentById(id);
      if (result?.success) {
        setAgentData(result.data);
      } else {
        // Fallback to mock data for demo purposes
        // Remove this in production and use proper error handling
        const mockAgent = {
          id: parseInt(id),
          name: "Customer Support Bot",
          description: "Handles customer inquiries and support tickets with natural language processing capabilities.",
          model: {
            provider: "OpenAI",
            model_name: "gpt-4",
            temperature: 0.7
          },
          is_active: true
        };
        setAgentData(mockAgent);
      }
    } catch (err) {
      setError('An error occurred while fetching agent details');
      console.error('Error fetching agent:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = (data) => {
    setAgentData(prev => ({ ...prev, ...data }));
    const currentIndex = STEPS.findIndex(step => step.id === currentStep);
    if (currentIndex < STEPS.length - 1) {
      setCurrentStep(STEPS[currentIndex + 1].id);
    }
  };

  const handleBack = () => {
    const currentIndex = STEPS.findIndex(step => step.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1].id);
    }
  };

  const handleSave = async (finalData) => {
    const finalAgentData = { ...agentData, ...finalData };
    setSubmitting(true);
    setError('');
    setSuccess('');
    
    try {
      let result;
      
      if (isEditing && finalAgentData?.id) {
        result = await agentService.updateAgent(finalAgentData.id, finalAgentData);
      } else {
        result = await agentService.createAgent(finalAgentData);
      }
      
      setSubmitting(false);
      
      if (result.success) {
        setSuccess(isEditing ? 'Agent updated successfully!' : 'Agent created successfully!');
        setTimeout(() => {
          navigate(isEditing ? `/agents/${finalAgentData.id}` : `/agents/${result.data.id}`);
        }, 1500);
      } else {
        setError(result.error || `Failed to ${isEditing ? 'update' : 'create'} agent`);
      }
    } catch (err) {
      setSubmitting(false);
      setError(`An error occurred while ${isEditing ? 'updating' : 'creating'} the agent`);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'details':
        return (
          <AgentDetailsForm 
            onNext={handleNext}
            initialData={agentData}
            isEditing={isEditing}
            onCancel={() => navigate('/agents')}
          />
        );
      case 'actions':
        return (
          <AgentActionsForm
            onNext={handleNext}
            onBack={handleBack}
            initialData={agentData.actions || []}
          />
        );
      case 'resources':
        return (
          <AgentResourcesForm
            onSave={handleSave}
            onBack={handleBack}
            loading={submitting}
            initialData={agentData.resources || []}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen pb-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        {/* Page header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text-primary">
            {isEditing ? 'Edit Agent' : 'Create New Agent'}
          </h1>
          <p className="mt-2 text-text-secondary">
            {isEditing 
              ? "Update your agent's configuration and settings." 
              : "Configure a new AI agent with your desired settings and capabilities."}
          </p>
        </div>

        {/* Form card */}
        <div className="bg-surface rounded-lg border border-border shadow-sm">
          {/* Step Indicator */}
          <div className="p-4 border-b border-border">
            <ol className="flex items-center w-full">
              {STEPS.map((step, index) => {
                const isActive = currentStep === step.id;
                const isCompleted = STEPS.findIndex(s => s.id === currentStep) > index;
                return (
                  <li key={step.id} className={`flex w-full items-center ${index < STEPS.length - 1 ? "after:content-[''] after:w-full after:h-1 after:border-b after:border-border after:border-4 after:inline-block" : ""}`}>
                    <span className={`flex items-center justify-center w-10 h-10 rounded-full lg:h-12 lg:w-12 shrink-0 ${isActive ? 'bg-primary text-white' : isCompleted ? 'bg-green-500 text-white' : 'bg-secondary-200 text-text-secondary'}`}>
                      {isCompleted ? <Check size={24} /> : <span className="font-bold">{index + 1}</span>}
                    </span>
                  </li>
                );
              })}
            </ol>
            <div className="mt-2 text-center font-medium text-text-primary">
              {STEPS.find(s => s.id === currentStep)?.name}
            </div>
          </div>

          {/* Status messages */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 mx-6 mt-6 rounded">
              <div className="flex items-center">
                <AlertCircle size={20} className="text-red-500 mr-3" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6 mx-6 mt-6 rounded">
              <div className="flex items-center">
                <CheckCircle size={20} className="text-green-500 mr-3" />
                <p className="text-green-700">{success}</p>
              </div>
            </div>
          )}

          {/* Loading state */}
          {loading ? (
            <div className="flex justify-center items-center p-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="p-6">
              {renderStepContent()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentCreation;
