import { useParams } from "react-router-dom";
import { MyAssistant } from "../../../components/MyAssistant";

export default function AgentPlaygroundPage() {
  const { id: agentId } = useParams();

  return (
    <div className="-mx-4 -my-6 sm:-mx-6 lg:-mx-8 h-[calc(100vh-4rem)] grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <h2 className="text-lg font-bold mb-2">Classic Playground Chat</h2>
        <MyAssistant agentId={agentId} />
      </div>
      <div>
        <h2 className="text-lg font-bold mb-2">Graph Playground Chat (ReAct)</h2>
        <MyAssistant agentId={agentId} useGraph={true} />
      </div>
    </div>
  );
}