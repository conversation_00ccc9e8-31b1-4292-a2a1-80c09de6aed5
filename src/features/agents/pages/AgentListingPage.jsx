import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Play, Bot, ChevronLeft, ChevronRight, List, LayoutGrid } from 'lucide-react';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import { ToggleGroup, ToggleGroupItem } from 'components/ui';
import { cn } from '@/lib/utils';

import AgentTable from '../components/AgentTable';
import AgentFilters from '../components/AgentFilters';
import BulkActions from '../components/BulkActions';
import AgentCard from '../components/AgentCard';

const AgentListing = () => {
  const navigate = useNavigate();
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgents, setSelectedAgents] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    type: 'all',
    dateRange: 'all'
  });
  const [viewMode, setViewMode] = useState('list'); // list or grid

  // Mock agent data
  const mockAgents = [
    {
      id: 1,
      name: "Customer Support Bot",
      type: "Conversational AI",
      status: "active",
      createdDate: new Date('2024-01-15'),
      lastActivity: new Date('2024-01-20T10:30:00'),
      performance: 94.5,
      description: "Handles customer inquiries and support tickets with natural language processing capabilities.",
      avatar: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 15420,
      successRate: 94.5,
      avgResponseTime: "2.3s"
    },
    {
      id: 2,
      name: "Data Analytics Agent",
      type: "Analytics AI",
      status: "active",
      createdDate: new Date('2024-01-10'),
      lastActivity: new Date('2024-01-20T09:15:00'),
      performance: 87.2,
      description: "Processes and analyzes large datasets to generate business insights and reports.",
      avatar: "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 8750,
      successRate: 87.2,
      avgResponseTime: "5.1s"
    },
    {
      id: 3,
      name: "Content Generator",
      type: "Creative AI",
      status: "inactive",
      createdDate: new Date('2024-01-08'),
      lastActivity: new Date('2024-01-18T14:22:00'),
      performance: 91.8,
      description: "Creates marketing content, blog posts, and social media content based on brand guidelines.",
      avatar: "https://images.pixabay.com/photo/2023/04/06/15/32/ai-generated-7904344_1280.jpg?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 3240,
      successRate: 91.8,
      avgResponseTime: "8.7s"
    },
    {
      id: 4,
      name: "Security Monitor",
      type: "Security AI",
      status: "error",
      createdDate: new Date('2024-01-12'),
      lastActivity: new Date('2024-01-19T16:45:00'),
      performance: 76.3,
      description: "Monitors system security, detects anomalies, and alerts administrators of potential threats.",
      avatar: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 12890,
      successRate: 76.3,
      avgResponseTime: "1.8s"
    },
    {
      id: 5,
      name: "Email Assistant",
      type: "Productivity AI",
      status: "active",
      createdDate: new Date('2024-01-05'),
      lastActivity: new Date('2024-01-20T11:20:00'),
      performance: 89.7,
      description: "Manages email responses, schedules meetings, and handles routine administrative tasks.",
      avatar: "https://images.pexels.com/photos/8849295/pexels-photo-8849295.jpeg?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 6780,
      successRate: 89.7,
      avgResponseTime: "3.2s"
    },
    {
      id: 6,
      name: "Language Translator",
      type: "Language AI",
      status: "active",
      createdDate: new Date('2024-01-03'),
      lastActivity: new Date('2024-01-20T08:45:00'),
      performance: 96.1,
      description: "Provides real-time translation services across 50+ languages with context awareness.",
      avatar: "https://images.pixabay.com/photo/2023/01/26/22/14/ai-generated-7747171_1280.jpg?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 22150,
      successRate: 96.1,
      avgResponseTime: "1.5s"
    },
    {
      id: 7,
      name: "Code Review Bot",
      type: "Development AI",
      status: "active",
      createdDate: new Date('2024-01-01'),
      lastActivity: new Date('2024-01-20T07:30:00'),
      performance: 92.4,
      description: "Reviews code submissions, identifies bugs, and suggests improvements following best practices.",
      avatar: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 4560,
      successRate: 92.4,
      avgResponseTime: "12.3s"
    },
    {
      id: 8,
      name: "Sales Predictor",
      type: "Predictive AI",
      status: "inactive",
      createdDate: new Date('2023-12-28'),
      lastActivity: new Date('2024-01-17T13:15:00'),
      performance: 84.6,
      description: "Analyzes sales data and market trends to predict future sales performance and opportunities.",
      avatar: "https://images.pexels.com/photos/8386434/pexels-photo-8386434.jpeg?w=100&h=100&fit=crop&crop=face",
      totalInteractions: 1890,
      successRate: 84.6,
      avgResponseTime: "15.7s"
    }
  ];

  useEffect(() => {
    // Simulate loading
    const loadAgents = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAgents(mockAgents);
      setFilteredAgents(mockAgents);
      setLoading(false);
    };

    loadAgents();
  }, []);

  useEffect(() => {
    let filtered = [...agents];

    // Apply search filter
    if (filters.search) {
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        agent.description.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(agent => agent.status === filters.status);
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(agent => agent.type === filters.type);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        default:
          break;
      }
      
      if (filters.dateRange !== 'all') {
        filtered = filtered.filter(agent => agent.lastActivity >= filterDate);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      if (aValue instanceof Date) {
        aValue = aValue.getTime();
        bValue = bValue.getTime();
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortConfig.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredAgents(filtered);
    setCurrentPage(1);
  }, [agents, filters, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAgent = (agentId) => {
    setSelectedAgents(prev =>
      prev.includes(agentId)
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const handleSelectAll = () => {
    const currentPageAgents = getCurrentPageAgents();
    const allSelected = currentPageAgents.every(agent => selectedAgents.includes(agent.id));
    
    if (allSelected) {
      setSelectedAgents(prev => prev.filter(id => !currentPageAgents.some(agent => agent.id === id)));
    } else {
      const newSelections = currentPageAgents.map(agent => agent.id);
      setSelectedAgents(prev => [...new Set([...prev, ...newSelections])]);
    }
  };

  const handleBulkAction = (action) => {
    switch (action) {
      case 'activate':
        setAgents(prev => prev.map(agent =>
          selectedAgents.includes(agent.id) ? { ...agent, status: 'active' } : agent
        ));
        break;
      case 'deactivate':
        setAgents(prev => prev.map(agent =>
          selectedAgents.includes(agent.id) ? { ...agent, status: 'inactive' } : agent
        ));
        break;
      case 'delete':
        setAgents(prev => prev.filter(agent => !selectedAgents.includes(agent.id)));
        break;
      default:
        break;
    }
    setSelectedAgents([]);
  };

  const handleAgentAction = (agentId, action) => {
    switch (action) {
      case 'playground':
        navigate(`/agent-playground/${agentId}`);
        break;
      case 'edit':
        navigate(`/agents/new?edit=${agentId}`);
        break;
      case 'delete':
        setAgents(prev => prev.filter(agent => agent.id !== agentId));
        break;
      case 'toggle':
        setAgents(prev => prev.map(agent =>
          agent.id === agentId
            ? { ...agent, status: agent.status === 'active' ? 'inactive' : 'active' }
            : agent
        ));
        break;
      default:
        break;
    }
  };

  const getCurrentPageAgents = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAgents.slice(startIndex, endIndex);
  };

  const totalPages = Math.ceil(filteredAgents.length / pageSize);

  const handleCreateAgent = () => {
    navigate('/agents/new');
  };

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <StandardPageHeader
          title="AI Agents"
          description={`Manage and monitor your AI agents. ${filteredAgents.length} of ${agents.length} agents shown.`}
          actions={
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate('/agent-playground')}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <Play size={16} className="mr-2" />
                Playground
              </button>
              <ToggleGroup
                type="single"
                value={viewMode}
                onValueChange={setViewMode}
                size="default"
                className={cn("gap-0 hidden sm:flex")}
              >
                <ToggleGroupItem
                  value="list"
                  variant="default"
                  size="default"
                  aria-label="List"
                  className="gap-2"
                >
                  <List className="h-4 w-4" />
                  <span className="hidden sm:inline">List</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                  value="grid"
                  variant="default"
                  size="default"
                  aria-label="Grid"
                  className="gap-2"
                >
                  <LayoutGrid className="h-4 w-4" />
                  <span className="hidden sm:inline">Grid</span>
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
          }
        />
        {/* Filters */}
        <AgentFilters
          filters={filters}
          onFiltersChange={setFilters}
          agentTypes={[...new Set(agents.map(agent => agent.type))]}
        />

        {/* Bulk Actions */}
        {selectedAgents.length > 0 && (
          <BulkActions
            selectedCount={selectedAgents.length}
            onBulkAction={handleBulkAction}
          />
        )}

        {/* Content */}
        {loading ? (
          <div className="bg-surface rounded-lg border border-border p-8">
            <div className="animate-pulse space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-4 h-4 bg-secondary-200 rounded"></div>
                  <div className="w-12 h-12 bg-secondary-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-secondary-200 rounded w-1/4"></div>
                    <div className="h-3 bg-secondary-200 rounded w-1/2"></div>
                  </div>
                  <div className="w-20 h-6 bg-secondary-200 rounded"></div>
                  <div className="w-24 h-4 bg-secondary-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredAgents.length === 0 ? (
          <div className="bg-surface rounded-lg border border-border p-12 text-center">
            <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Bot size={32} className="text-secondary-400" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">No agents found</h3>
            <p className="text-text-secondary mb-6">
              {filters.search || filters.status !== 'all' || filters.type !== 'all' ?'Try adjusting your filters to see more results.' :'Get started by creating your first AI agent.'}
            </p>
            <button
              onClick={handleCreateAgent}
              className="bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200"
            >
              Create Your First Agent
            </button>
          </div>
        ) : (
          <>
            {/* Desktop and Tablet Views - controlled by viewMode */}
            <div className="hidden sm:block">
              {viewMode === 'list' ? (
                <AgentTable
                  agents={getCurrentPageAgents()}
                  selectedAgents={selectedAgents}
                  sortConfig={sortConfig}
                  onSort={handleSort}
                  onSelectAgent={handleSelectAgent}
                  onSelectAll={handleSelectAll}
                  onAgentAction={handleAgentAction}
                  allSelected={getCurrentPageAgents().every(agent => selectedAgents.includes(agent.id))}
                />
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                  {getCurrentPageAgents().map(agent => (
                    <AgentCard
                      key={agent.id}
                      agent={agent}
                      isSelected={selectedAgents.includes(agent.id)}
                      onSelect={() => handleSelectAgent(agent.id)}
                      onAction={(action) => handleAgentAction(agent.id, action)}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Mobile View - always cards */}
            <div className="sm:hidden space-y-4">
              {getCurrentPageAgents().map(agent => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  isSelected={selectedAgents.includes(agent.id)}
                  onSelect={() => handleSelectAgent(agent.id)}
                  onAction={(action) => handleAgentAction(agent.id, action)}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-text-secondary">Show</span>
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="border border-border rounded-md px-3 py-1 text-sm bg-surface"
                  >
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                  <span className="text-sm text-text-secondary">per page</span>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-text-secondary">
                    Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredAgents.length)} of {filteredAgents.length} results
                  </span>
                </div>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="p-2 rounded-md border border-border bg-surface text-text-secondary hover:text-text-primary hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <ChevronLeft size={16} />
                  </button>

                  {[...Array(Math.min(5, totalPages))].map((_, index) => {
                    let pageNumber;
                    if (totalPages <= 5) {
                      pageNumber = index + 1;
                    } else if (currentPage <= 3) {
                      pageNumber = index + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + index;
                    } else {
                      pageNumber = currentPage - 2 + index;
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => setCurrentPage(pageNumber)}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                          currentPage === pageNumber
                            ? 'bg-primary text-white' :'border border-border bg-surface text-text-secondary hover:text-text-primary hover:bg-secondary-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-md border border-border bg-surface text-text-secondary hover:text-text-primary hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AgentListing;