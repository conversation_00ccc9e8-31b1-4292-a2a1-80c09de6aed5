import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { Input } from 'components/ui/Input';
import { Button } from 'components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from 'components/ui/select';
import AgentImageUpload from './AgentImageUpload';

const defaultModel = {
  provider: '',
  model_name: '',
  temperature: 0.7
};

const modelOptions = [
  { provider: 'OpenAI', models: ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'] },
  { provider: 'Anthropic', models: ['claude-3-opus', 'claude-3-sonnet', 'claude-2'] },
  { provider: 'Gemini', models: ['gemini-pro', 'gemini-pro-vision'] },
  { provider: 'Mistral', models: ['mistral-large', 'mistral-medium', 'mistral-small'] },
  { provider: 'Custom', models: [] },
];

const AgentDetailsForm = ({ onNext, onCancel, initialData, isEditing = false }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [avatar, setAvatar] = useState(null);
  const [model, setModel] = useState(defaultModel);
  const [isActive, setIsActive] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState('');
  const [customModel, setCustomModel] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Load initial data if editing
  useEffect(() => {
    if (initialData) {
      setName(initialData.name || '');
      setDescription(initialData.description || '');
      setAvatar(initialData.avatar || null);
      setModel(initialData.model || defaultModel);
      setIsActive(initialData.is_active !== undefined ? initialData.is_active : true);
      
      // Set selected provider based on model data
      if (initialData.model?.provider) {
        setSelectedProvider(initialData.model.provider);
        
        // If provider exists in our options but model doesn't, it's likely a custom model
        const providerExists = modelOptions.some(p => p.provider === initialData.model.provider);
        const modelExists = modelOptions.find(p => p.provider === initialData.model.provider)?.models
          .includes(initialData.model.model_name);
          
        if (providerExists && !modelExists) {
          setCustomModel(initialData.model.model_name);
        }
      }
    }
  }, [initialData]);

  const validate = () => {
    const errors = {};
    
    if (!name.trim()) {
      errors.name = 'Agent name is required';
    }
    
    if (!model.provider) {
      errors.provider = 'Model provider is required';
    }
    
    if (!model.model_name) {
      errors.model_name = 'Model name is required';
    }
    
    if (model.temperature < 0 || model.temperature > 1) {
      errors.temperature = 'Temperature must be between 0 and 1';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = (e) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    onNext({
      name,
      description,
      avatar,
      model,
      is_active: isActive
    });
  };

  const handleProviderChange = (provider) => {
    setSelectedProvider(provider);
    
    if (provider === 'Custom') {
      setModel({
        ...model,
        provider,
        model_name: customModel
      });
    } else {
      setModel({
        ...model,
        provider,
        model_name: modelOptions.find(p => p.provider === provider)?.models[0] || ''
      });
    }
  };

  const handleModelChange = (modelName) => {
    if (selectedProvider === 'Custom') {
      setCustomModel(modelName);
    }
    
    setModel({
      ...model,
      model_name: modelName
    });
  };

  return (
    <form onSubmit={handleNext} className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-3">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Basic Information</h3>
        </div>
        
        {/* Agent Image Upload */}
        <div className="md:col-span-1">
          <AgentImageUpload value={avatar} onChange={setAvatar} />
        </div>

        {/* Right Column for Name and Description */}
        <div className="md:col-span-2 space-y-6">
          {/* Agent Name */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-1">
              Agent Name <span className="text-red-500">*</span>
            </label>
            <Input
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter a descriptive name"
              required
              className={validationErrors.name ? "border-red-500" : ""}
            />
            {validationErrors.name && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.name}</p>
            )}
            <p className="mt-1 text-xs text-text-secondary">
              Choose a clear, descriptive name that identifies your agent's purpose
            </p>
          </div>
          
          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-1">
              Description
            </label>
            <textarea
              className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[100px] md:text-sm"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Describe what this agent does and its capabilities"
            />
            <p className="mt-1 text-xs text-text-secondary">
              Provide details about what this agent does, its purpose, and who should use it
            </p>
          </div>
        </div>

        <div className="md:col-span-3 pt-4">
          <h3 className="text-lg font-semibold text-text-primary mb-4">AI Model Configuration</h3>
        </div>
        
        {/* Model Provider */}
        <div className="md:col-span-1">
          <label className="block text-sm font-medium text-text-primary mb-1">
            Model Provider <span className="text-red-500">*</span>
          </label>
          <Select value={selectedProvider} onValueChange={handleProviderChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              {modelOptions.map(provider => (
                <SelectItem key={provider.provider} value={provider.provider}>
                  {provider.provider}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.provider && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.provider}</p>
          )}
        </div>
        
        {/* Model Name */}
        <div className="md:col-span-2">
          {selectedProvider && selectedProvider !== 'Custom' ? (
            <>
              <label className="block text-sm font-medium text-text-primary mb-1">
                Model Name <span className="text-red-500">*</span>
              </label>
              <Select value={model.model_name} onValueChange={handleModelChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  {modelOptions.find(p => p.provider === selectedProvider)?.models.map(modelName => (
                    <SelectItem key={modelName} value={modelName}>
                      {modelName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          ) : selectedProvider === 'Custom' ? (
            <>
              <label className="block text-sm font-medium text-text-primary mb-1">
                Custom Model Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={customModel}
                onChange={e => handleModelChange(e.target.value)}
                placeholder="Enter model identifier"
                required
              />
            </>
          ) : (
            <div className="h-10 flex items-end">
              <p className="text-text-secondary text-sm italic">
                Select a provider first
              </p>
            </div>
          )}
          {validationErrors.model_name && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.model_name}</p>
          )}
        </div>

        {/* Advanced Settings Toggle */}
        <div className="md:col-span-3 pt-2">
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-primary hover:text-primary-600 flex items-center text-sm font-medium"
          >
            {showAdvanced ? (
              <ChevronUp size={16} className="mr-1" />
            ) : (
              <ChevronDown size={16} className="mr-1" />
            )}
            {showAdvanced ? "Hide" : "Show"} Advanced Settings
          </button>
        </div>

        {/* Advanced Settings */}
        {showAdvanced && (
          <>
            {/* Temperature */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-text-primary mb-1">
                Temperature
              </label>
              <div className="flex items-center space-x-4">
                <Input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full h-2"
                  value={model.temperature}
                  onChange={e => setModel({ 
                    ...model, 
                    temperature: parseFloat(e.target.value) 
                  })}
                />
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  className="w-20 text-center"
                  value={model.temperature}
                  onChange={e => setModel({ 
                    ...model, 
                    temperature: parseFloat(e.target.value) 
                  })}
                />
              </div>
              <p className="mt-1 text-xs text-text-secondary flex justify-between">
                <span>More deterministic (0)</span>
                <span>More creative (1)</span>
              </p>
              {validationErrors.temperature && (
                <p className="mt-1 text-sm text-red-500">{validationErrors.temperature}</p>
              )}
            </div>
            
            {/* Active Status */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Status
              </label>
              <div className="flex items-center space-x-2">
                <div 
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${isActive ? 'bg-primary' : 'bg-secondary-300'}
                  `}
                  onClick={() => setIsActive(!isActive)}
                >
                  <span 
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${isActive ? 'translate-x-6' : 'translate-x-1'}
                    `}
                  />
                </div>
                <span className="text-sm font-medium text-text-primary">
                  {isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <p className="mt-1 text-xs text-text-secondary">
                {isActive 
                  ? 'Agent is active and will be available for use' 
                  : 'Agent is inactive and will not be available for use'}
              </p>
            </div>
          </>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-between pt-6 border-t border-border">
        <Button 
          variant="outline" 
          onClick={onCancel}
          type="button"
        >
          Cancel
        </Button>
        <Button
          type="submit"
        >
          Next
        </Button>
      </div>
    </form>
  );
};

export default AgentDetailsForm;
