import React, { useState, useRef, useEffect } from 'react';
import { Play, Edit, MessageSquare, Pause, Trash2, CheckCircle, AlertCircle, Circle, ChevronUp, ChevronDown } from 'lucide-react';
import Image from 'components/AppImage';
import { Avatar, AvatarImage, AvatarFallback } from 'components/ui/avatar';

const Tooltip = ({ children, content }) => {
  const [visible, setVisible] = useState(false);
  return (
    <span className="relative">
      <span
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
        className="cursor-help"
      >
        {children}
      </span>
      {visible && (
        <span className="absolute z-10 left-1/2 -translate-x-1/2 mt-2 px-2 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-pre-line min-w-[120px] max-w-xs">
          {content}
        </span>
      )}
    </span>
  );
};

const AgentCard = ({ agent, isSelected, onSelect, onAction }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const descRef = useRef(null);
  const [isEllipsed, setIsEllipsed] = useState(false);

  useEffect(() => {
    const el = descRef.current;
    if (el) {
      setIsEllipsed(el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth);
    }
  }, [agent.description]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-700';
      case 'inactive':
        return 'bg-secondary-100 text-secondary-700';
      case 'error':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'inactive':
        return Pause;
      case 'error':
        return AlertCircle;
      default:
        return Circle;
    }
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className={`bg-surface rounded-lg border transition-all duration-200 h-full flex flex-col min-h-[280px] ${
      isSelected ? 'border-primary shadow-sm' : 'border-border hover:border-secondary-300'
    }`}>
      {/* Card Header */}
      <div className="p-4 flex-1 flex flex-col">
        {/* Top Section - Fixed Height */}
        <div className="flex items-start space-x-3 h-28">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="mt-1 w-4 h-4 text-primary bg-surface border-border rounded focus:ring-primary focus:ring-2"
          />
          
          <Avatar className="w-12 h-12">
            <AvatarImage 
              src={agent.avatar} 
              alt={agent.name}
            />
            <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/30 text-primary font-medium">
              {agent.name
                ? agent.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                : 'AI'}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div className="min-w-0 flex-1">
                <h3 className="text-base font-semibold text-text-primary truncate">
                  {agent.name}
                </h3>
                <p className="text-sm text-text-secondary">{agent.type}</p>
              </div>
            </div>
            
            {isEllipsed ? (
              <Tooltip content={agent.description}>
                <div
                  ref={descRef}
                  className="text-sm text-text-muted line-clamp-3 overflow-hidden cursor-help"
                >
                  {agent.description}
                </div>
              </Tooltip>
            ) : (
              <div
                ref={descRef}
                className="text-sm text-text-muted line-clamp-3 overflow-hidden"
              >
                {agent.description}
              </div>
            )}
          </div>
        </div>

        {/* Performance Bar - Fixed Height */}
        <div className="mt-4 h-12">
          <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
            <span>Performance</span>
            <span className="font-medium">{agent.performance}%</span>
          </div>
          <div className="w-full bg-secondary-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                agent.performance >= 90 ? 'bg-success' :
                agent.performance >= 80 ? 'bg-warning' : 'bg-error'
              }`}
              style={{ width: `${agent.performance}%` }}
            />
          </div>
        </div>

        {/* Quick Stats - Fixed Height */}
        <div className="grid grid-cols-3 gap-3 mt-4 pt-4 border-t border-border h-20">
          <div>
            <p className="text-xs text-text-secondary">Last Activity</p>
            <p className="text-sm font-medium text-text-primary">
              {formatDate(agent.lastActivity)}
            </p>
          </div>
          <div>
            <p className="text-xs text-text-secondary">Created</p>
            <p className="text-sm font-medium text-text-primary">
              {formatDate(agent.createdDate)}
            </p>
          </div>
          <div>
            <p className="text-xs text-text-secondary">Status</p>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
              {React.createElement(getStatusIcon(agent.status), { size: 10, className: "mr-1" })}
              {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
            </span>
          </div>
        </div>
      </div>

      {/* Show More Button - Always at bottom */}
      <div className="p-4 pt-0 mt-auto h-14 flex items-end">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-center space-x-2 py-2 text-sm text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-secondary-50 transition-colors duration-200"
        >
          <span>{isExpanded ? 'Show Less' : 'Show More'}</span>
          {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </button>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="px-4 pb-4 border-t border-border">
          <div className="pt-4 space-y-4">
            {/* Detailed Stats */}
            <div className="grid grid-cols-1 gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-text-secondary">Total Interactions</span>
                <span className="text-sm font-medium text-text-primary">
                  {agent.totalInteractions.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-text-secondary">Success Rate</span>
                <span className="text-sm font-medium text-text-primary">
                  {agent.successRate}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-text-secondary">Avg Response Time</span>
                <span className="text-sm font-medium text-text-primary">
                  {agent.avgResponseTime}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 pt-2">
              <button
                onClick={() => onAction('playground')}
                className="flex-1 flex items-center justify-center space-x-2 py-2 px-3 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                <Play size={16} />
                <span>Playground</span>
              </button>
              
              <button
                onClick={() => onAction('edit')}
                className="flex items-center justify-center p-2 border border-border rounded-md text-text-secondary hover:text-primary hover:border-primary hover:bg-primary-50 transition-colors duration-200"
                title="Edit Agent"
              >
                <Edit size={16} />
              </button>
              
              <button
                onClick={() => onAction('playground')}
                className="flex items-center justify-center p-2 border border-border rounded-md text-text-secondary hover:text-blue-600 hover:border-blue-600 hover:bg-blue-50 transition-colors duration-200"
                title="Open Playground"
              >
                <MessageSquare size={16} />
              </button>
              
              <button
                onClick={() => onAction('toggle')}
                className="flex items-center justify-center p-2 border border-border rounded-md text-text-secondary hover:text-accent hover:border-accent hover:bg-accent-50 transition-colors duration-200"
                title={agent.status === 'active' ? 'Deactivate' : 'Activate'}
              >
                {agent.status === 'active' ? <Pause size={16} /> : <Play size={16} />}
              </button>
              
              <button
                onClick={() => onAction('delete')}
                className="flex items-center justify-center p-2 border border-border rounded-md text-text-secondary hover:text-error hover:border-error hover:bg-error-50 transition-colors duration-200"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentCard;