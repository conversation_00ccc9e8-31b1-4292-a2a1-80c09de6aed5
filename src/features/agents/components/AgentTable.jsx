import React from 'react';
import { Play, Edit, Trash2, <PERSON><PERSON><PERSON>cle, Pause, AlertCircle, Circle, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import Image from 'components/AppImage';
import { Avatar, AvatarImage, AvatarFallback } from 'components/ui/avatar';

const AgentTable = ({
  agents,
  selectedAgents,
  sortConfig,
  onSort,
  onSelectAgent,
  onSelectAll,
  onAgentAction,
  allSelected
}) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-success-100 text-success-700';
      case 'inactive':
        return 'bg-secondary-100 text-secondary-700';
      case 'error':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'inactive':
        return Pause;
      case 'error':
        return AlertCircle;
      default:
        return Circle;
    }
  };

  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return ArrowUpDown;
    }
    return sortConfig.direction === 'asc' ? ArrowUp : ArrowDown;
  };

  return (
    <div className="bg-surface rounded-lg border border-border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-secondary-50 border-b border-border">
            <tr>
              <th className="w-12 px-6 py-4">
                <input
                  type="checkbox"
                  checked={allSelected && agents.length > 0}
                  onChange={onSelectAll}
                  className="w-4 h-4 text-primary bg-surface border-border rounded focus:ring-primary focus:ring-2"
                />
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('name')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Agent</span>
                  {React.createElement(getSortIcon('name'), { size: 14 })}
                </button>
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('type')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Type</span>
                  {React.createElement(getSortIcon('type'), { size: 14 })}
                </button>
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('status')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Status</span>
                  {React.createElement(getSortIcon('status'), { size: 14 })}
                </button>
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('performance')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Performance</span>
                  {React.createElement(getSortIcon('performance'), { size: 14 })}
                </button>
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('lastActivity')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Last Activity</span>
                  {React.createElement(getSortIcon('lastActivity'), { size: 14 })}
                </button>
              </th>
              <th className="text-left px-6 py-4">
                <button
                  onClick={() => onSort('createdDate')}
                  className="flex items-center space-x-2 text-sm font-semibold text-text-primary hover:text-primary transition-colors duration-200"
                >
                  <span>Created</span>
                  {React.createElement(getSortIcon('createdDate'), { size: 14 })}
                </button>
              </th>
              <th className="text-right px-6 py-4">
                <span className="text-sm font-semibold text-text-primary">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {agents.map((agent) => (
              <tr
                key={agent.id}
                className="hover:bg-secondary-50 transition-colors duration-200"
              >
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedAgents.includes(agent.id)}
                    onChange={() => onSelectAgent(agent.id)}
                    className="w-4 h-4 text-primary bg-surface border-border rounded focus:ring-primary focus:ring-2"
                  />
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage 
                        src={agent.avatar} 
                        alt={agent.name}
                      />
                      <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/30 text-primary font-medium text-sm">
                        {agent.name
                          ? agent.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                          : 'AI'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-text-primary truncate">
                        {agent.name}
                      </p>
                      <p className="text-xs text-text-secondary truncate">
                        {agent.description}
                      </p>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className="text-sm text-text-secondary">{agent.type}</span>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                    {React.createElement(getStatusIcon(agent.status), { size: 12, className: "mr-1" })}
                    {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-secondary-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          agent.performance >= 90 ? 'bg-success' :
                          agent.performance >= 80 ? 'bg-warning' : 'bg-error'
                        }`}
                        style={{ width: `${agent.performance}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-text-primary min-w-0">
                      {agent.performance}%
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className="text-sm text-text-secondary">
                    {formatDate(agent.lastActivity)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <span className="text-sm text-text-secondary">
                    {formatDate(agent.createdDate)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onAgentAction(agent.id, 'playground')}
                      className="p-1.5 rounded-md text-blue-600 hover:text-blue-700 hover:bg-blue-50 transition-colors duration-200"
                      title="Open in playground"
                    >
                      <Play size={16} />
                    </button>
                    <button
                      onClick={() => onAgentAction(agent.id, 'edit')}
                      className="p-1.5 rounded-md text-text-secondary hover:text-primary hover:bg-primary-50 transition-colors duration-200"
                      title="Edit agent"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => onAgentAction(agent.id, 'toggle')}
                      className="p-1.5 rounded-md text-text-secondary hover:text-accent hover:bg-accent-50 transition-colors duration-200"
                      title={agent.status === 'active' ? 'Deactivate agent' : 'Activate agent'}
                    >
                      {agent.status === 'active' ? <Pause size={16} /> : <Play size={16} />}
                    </button>
                    <button
                      onClick={() => onAgentAction(agent.id, 'delete')}
                      className="p-1.5 rounded-md text-text-secondary hover:text-error hover:bg-error-50 transition-colors duration-200"
                      title="Delete agent"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AgentTable;