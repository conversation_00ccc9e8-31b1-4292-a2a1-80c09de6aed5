import React, { useState, useRef } from 'react';
import { Bot, Upload, Trash2 } from 'lucide-react';
import { Button } from 'components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from 'components/ui/avatar';

const AgentImageUpload = ({ value, onChange }) => {
  const [preview, setPreview] = useState(value);
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
        onChange(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setPreview(null);
    onChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  return (
    <div>
      <label className="block text-sm font-medium text-text-primary mb-1">Agent Image</label>
      <div className="flex items-center gap-4">
        <Avatar className="w-24 h-24 border border-border">
          <AvatarImage src={preview} alt="Agent Preview" />
          <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/30 text-primary">
            <Bot size={32} />
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept="image/png, image/jpeg, image/gif"
          />
          <Button type="button" variant="outline" onClick={triggerFileInput}>
            <Upload size={16} className="mr-2" />
            Upload Image
          </Button>
          {preview && (
            <Button type="button" variant="ghost" onClick={handleRemoveImage} className="text-red-500">
              <Trash2 size={16} className="mr-2" />
              Remove
            </Button>
          )}
        </div>
      </div>
      <p className="mt-2 text-xs text-text-secondary">
        Recommended size: 256x256px. Supports PNG, JPG, GIF.
      </p>
    </div>
  );
};

export default AgentImageUpload;
