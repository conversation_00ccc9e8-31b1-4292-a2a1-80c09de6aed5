import React, { useState } from 'react';
import { CheckSquare, Play, Pause, Trash2, AlertTriangle } from 'lucide-react';

const BulkActions = ({ selectedCount, onBulkAction }) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  const handleBulkAction = (action) => {
    if (action === 'delete') {
      setPendingAction(action);
      setShowConfirmDialog(true);
    } else {
      onBulkAction(action);
    }
  };

  const confirmAction = () => {
    if (pendingAction) {
      onBulkAction(pendingAction);
    }
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  const cancelAction = () => {
    setShowConfirmDialog(false);
    setPendingAction(null);
  };

  return (
    <>
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-primary-100 rounded-full">
              <CheckSquare size={16} className="text-primary" />
            </div>
            <div>
              <p className="text-sm font-medium text-primary-900">
                {selectedCount} agent{selectedCount !== 1 ? 's' : ''} selected
              </p>
              <p className="text-xs text-primary-700">
                Choose an action to apply to all selected agents
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleBulkAction('activate')}
              className="flex items-center space-x-2 px-3 py-2 bg-success text-white rounded-md text-sm font-medium hover:bg-success-600 transition-colors duration-200"
            >
              <Play size={16} />
              <span>Activate</span>
            </button>

            <button
              onClick={() => handleBulkAction('deactivate')}
              className="flex items-center space-x-2 px-3 py-2 bg-warning text-white rounded-md text-sm font-medium hover:bg-warning-600 transition-colors duration-200"
            >
              <Pause size={16} />
              <span>Deactivate</span>
            </button>

            <button
              onClick={() => handleBulkAction('delete')}
              className="flex items-center space-x-2 px-3 py-2 bg-error text-white rounded-md text-sm font-medium hover:bg-error-600 transition-colors duration-200"
            >
              <Trash2 size={16} />
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
          <div className="bg-surface rounded-lg shadow-elevation max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex items-center justify-center w-10 h-10 bg-error-100 rounded-full">
                <AlertTriangle size={20} className="text-error" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-text-primary">Confirm Deletion</h3>
                <p className="text-sm text-text-secondary">This action cannot be undone</p>
              </div>
            </div>

            <p className="text-text-secondary mb-6">
              Are you sure you want to delete {selectedCount} selected agent{selectedCount !== 1 ? 's' : ''}? 
              This will permanently remove {selectedCount !== 1 ? 'them' : 'it'} from the system.
            </p>

            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={cancelAction}
                className="px-4 py-2 text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-secondary-50 transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={confirmAction}
                className="px-4 py-2 bg-error text-white rounded-md hover:bg-error-600 transition-colors duration-200"
              >
                Delete {selectedCount} Agent{selectedCount !== 1 ? 's' : ''}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BulkActions;