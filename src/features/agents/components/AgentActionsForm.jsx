import React from 'react';
import { But<PERSON> } from 'components/ui/button';
import { Plus, PlusCircle, ArrowLeft, ArrowRight } from 'lucide-react';

const AgentActionsForm = ({ onNext, onBack }) => {
  // Placeholder for actions state
  const [actions, setActions] = React.useState([]);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-text-primary">Define Agent Actions</h3>
        <p className="text-sm text-text-secondary mt-1">
          Specify the actions that this agent can perform. Actions can be API calls, functions, or other automated tasks.
        </p>
      </div>

      <div className="border border-dashed border-border rounded-lg p-8 text-center">
        <PlusCircle size={40} className="mx-auto text-text-secondary" />
        <h4 className="mt-4 text-md font-semibold text-text-primary">No Actions Defined</h4>
        <p className="mt-1 text-sm text-text-secondary">Get started by adding the first action.</p>
        <Button variant="outline" className="mt-4">
          <Plus size={16} className="mr-2" />
          Add Action
        </Button>
      </div>

      <div className="flex justify-between pt-6 border-t border-border">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft size={16} className="mr-2" />
          Back
        </Button>
        <Button onClick={onNext}>
          Next
          <ArrowRight size={16} className="ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default AgentActionsForm;
