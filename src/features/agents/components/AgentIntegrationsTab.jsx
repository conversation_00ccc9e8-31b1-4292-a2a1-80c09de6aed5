import React, { useState, useEffect } from 'react';
import { Settings, Plus, Plug, AlertCircle } from 'lucide-react';
import { integrationService } from '../../../services';
import IntegrationCard from '../../integrations/components/IntegrationCard';
import ActionConfigModal from '../../integrations/components/ActionConfigModal';

const AgentIntegrationsTab = ({ agentId }) => {
  const [integrations, setIntegrations] = useState([]);
  const [connections, setConnections] = useState([]);
  const [agentIntegrations, setAgentIntegrations] = useState([]);
  const [availableIntegrations, setAvailableIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Modal states
  const [showActionConfig, setShowActionConfig] = useState(false);
  const [selectedAction, setSelectedAction] = useState(null);
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    if (agentId) {
      loadData();
    }
  }, [agentId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load all data in parallel
      const [integrationsRes, connectionsRes, agentIntegrationsRes] = await Promise.all([
        integrationService.getIntegrations({ is_active: true }),
        integrationService.getUserConnections({ is_connected: true }),
        integrationService.getAgentIntegrations(agentId)
      ]);

      if (integrationsRes.success) {
        setIntegrations(integrationsRes.data);
      }

      if (connectionsRes.success) {
        setConnections(connectionsRes.data);
      }

      if (agentIntegrationsRes.success) {
        setAgentIntegrations(agentIntegrationsRes.data);
        
        // Find available integrations (connected but not added to agent)
        const agentIntegrationIds = agentIntegrationsRes.data.map(ai => ai.integration?.id);
        const connectedIntegrationIds = connectionsRes.data.map(c => c.integration_id);
        
        const available = integrationsRes.data.filter(integration => 
          connectedIntegrationIds.includes(integration.id) && 
          !agentIntegrationIds.includes(integration.id)
        );
        setAvailableIntegrations(available);
      }
    } catch (err) {
      setError(err.message || 'Failed to load integration data');
      console.error('Error loading integration data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddIntegration = async (integrationId) => {
    try {
      const connection = connections.find(c => c.integration_id === integrationId);
      const result = await integrationService.addIntegrationToAgent(agentId, {
        integration_id: integrationId,
        user_connection_id: connection?.id
      });

      if (result.success) {
        await loadData(); // Reload data
        setShowAddModal(false);
      } else {
        alert('Failed to add integration: ' + result.error);
      }
    } catch (error) {
      alert('Failed to add integration: ' + error.message);
    }
  };

  const handleRemoveIntegration = async (integrationId) => {
    if (!confirm('Are you sure you want to remove this integration from the agent?')) {
      return;
    }

    try {
      const result = await integrationService.removeIntegrationFromAgent(agentId, integrationId);

      if (result.success) {
        await loadData(); // Reload data
      } else {
        alert('Failed to remove integration: ' + result.error);
      }
    } catch (error) {
      alert('Failed to remove integration: ' + error.message);
    }
  };

  const handleToggleAction = async (actionId, type, value) => {
    if (!selectedIntegration) return;

    try {
      let result;
      if (type === 'enabled') {
        if (value) {
          result = await integrationService.enableAgentAction(agentId, selectedIntegration, actionId);
        } else {
          result = await integrationService.disableAgentAction(agentId, selectedIntegration, actionId);
        }
      } else if (type === 'visibility') {
        if (!value) {
          result = await integrationService.hideAgentAction(agentId, selectedIntegration, actionId);
        } else {
          result = await integrationService.enableAgentAction(agentId, selectedIntegration, actionId);
        }
      }

      if (result.success) {
        await loadData(); // Reload data
      } else {
        alert('Failed to update action: ' + result.error);
      }
    } catch (error) {
      alert('Failed to update action: ' + error.message);
    }
  };

  const handleConfigureAction = (action) => {
    setSelectedAction(action);
    setShowActionConfig(true);
  };

  const handleSaveActionConfig = async (actionId, config) => {
    if (!selectedIntegration) return;

    try {
      const result = await integrationService.configureAgentAction(agentId, selectedIntegration, {
        action_id: actionId,
        ...config
      });

      if (result.success) {
        await loadData(); // Reload data
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading integrations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Integrations</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Agent Integrations</h3>
          <p className="text-gray-600">Configure third-party service integrations for this agent</p>
        </div>
        {availableIntegrations.length > 0 && (
          <button
            onClick={() => setShowAddModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Integration</span>
          </button>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Plug className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Active Integrations</p>
              <p className="text-2xl font-semibold text-gray-900">
                {agentIntegrations.filter(ai => ai.config?.is_enabled).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Settings className="w-8 h-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Enabled Actions</p>
              <p className="text-2xl font-semibold text-gray-900">
                {agentIntegrations.reduce((total, ai) => 
                  total + (ai.config?.action_configs?.filter(ac => ac.is_enabled).length || 0), 0
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Plus className="w-8 h-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Available to Add</p>
              <p className="text-2xl font-semibold text-gray-900">{availableIntegrations.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Integrations */}
      {agentIntegrations.length > 0 ? (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4">Current Integrations</h4>
          <div className="space-y-4">
            {agentIntegrations.map((agentIntegration) => {
              const integration = agentIntegration.integration;
              const connection = connections.find(c => c.integration_id === integration.id);
              
              return (
                <div key={integration.id} className="relative">
                  <IntegrationCard
                    integration={integration}
                    connection={connection}
                    actions={agentIntegration.actions || []}
                    agentConfig={agentIntegration.config}
                    onConnect={() => {}} // Not applicable for agent view
                    onDisconnect={() => handleRemoveIntegration(integration.id)}
                    onToggleAction={(actionId, type, value) => {
                      setSelectedIntegration(integration.id);
                      handleToggleAction(actionId, type, value);
                    }}
                    onConfigureAction={(action) => {
                      setSelectedIntegration(integration.id);
                      handleConfigureAction(action);
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Plug className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Integrations Configured</h3>
          <p className="text-gray-600 mb-4">
            Add integrations to extend your agent's capabilities with third-party services
          </p>
          {availableIntegrations.length > 0 && (
            <button
              onClick={() => setShowAddModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Integration
            </button>
          )}
        </div>
      )}

      {/* Add Integration Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Add Integration</h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
              >
                ×
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {availableIntegrations.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-600">No connected integrations available to add.</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Connect to integrations first in the Integrations page.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {availableIntegrations.map((integration) => (
                    <div key={integration.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-10 h-10 rounded-lg flex items-center justify-center"
                          style={{ backgroundColor: integration.color + '20' || '#f3f4f6' }}
                        >
                          {integration.icon_url ? (
                            <img 
                              src={integration.icon_url} 
                              alt={integration.name}
                              className="w-6 h-6"
                            />
                          ) : (
                            <Plug className="w-5 h-5" style={{ color: integration.color || '#6b7280' }} />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{integration.name}</h4>
                          <p className="text-sm text-gray-600">{integration.description}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleAddIntegration(integration.id)}
                        className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                      >
                        Add
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Action Configuration Modal */}
      <ActionConfigModal
        isOpen={showActionConfig}
        onClose={() => {
          setShowActionConfig(false);
          setSelectedAction(null);
          setSelectedIntegration(null);
        }}
        action={selectedAction}
        currentConfig={
          selectedAction && selectedIntegration 
            ? agentIntegrations
                .find(ai => ai.integration?.id === selectedIntegration)
                ?.config?.action_configs
                ?.find(ac => ac.action_id === selectedAction.id)
            : null
        }
        onSave={handleSaveActionConfig}
      />
    </div>
  );
};

export default AgentIntegrationsTab;
