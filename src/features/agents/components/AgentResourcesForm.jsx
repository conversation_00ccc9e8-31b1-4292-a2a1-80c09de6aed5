import React from 'react';
import { Button } from 'components/ui/button';
import { Plus, Database, ArrowLeft, Save } from 'lucide-react';

const AgentResourcesForm = ({ onSave, onBack, loading }) => {
  // Placeholder for resources state
  const [resources, setResources] = React.useState([]);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-text-primary">Assign Resources</h3>
        <p className="text-sm text-text-secondary mt-1">
          Provide the agent with access to necessary resources like databases, APIs, or file systems.
        </p>
      </div>

      <div className="border border-dashed border-border rounded-lg p-8 text-center">
        <Database size={40} className="mx-auto text-text-secondary" />
        <h4 className="mt-4 text-md font-semibold text-text-primary">No Resources Assigned</h4>
        <p className="mt-1 text-sm text-text-secondary">Assign resources to allow the agent to function.</p>
        <Button variant="outline" className="mt-4">
          <Plus size={16} className="mr-2" />
          Add Resource
        </Button>
      </div>

      <div className="flex justify-between pt-6 border-t border-border">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft size={16} className="mr-2" />
          Back
        </Button>
        <Button 
          onClick={onSave} 
          disabled={loading}
          iconName={loading ? "Loader" : "Save"}
          loading={loading}
        >
          {loading ? 'Saving...' : 'Save Agent'}
        </Button>
      </div>
    </div>
  );
};

export default AgentResourcesForm;
