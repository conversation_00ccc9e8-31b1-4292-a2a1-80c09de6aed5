// src/pages/api-service-layer/index.jsx
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import ServiceOverview from '../components/ServiceOverview';
import APIDocumentation from '../components/APIDocumentation';
import ConfigurationPanel from '../components/ConfigurationPanel';
import ErrorHandlingDisplay from '../components/ErrorHandlingDisplay';
import TestingInterface from '../components/TestingInterface';

const APIServiceLayer = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [serviceStatus, setServiceStatus] = useState({
    auth: 'healthy',
    agents: 'healthy',
    dashboard: 'healthy',
    users: 'healthy'
  });

  useEffect(() => {
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const tabs = [
    {
      id: 'overview',
      label: 'Service Overview',
      icon: '🏗️',
      description: 'Architecture and service status'
    },
    {
      id: 'documentation',
      label: 'API Documentation',
      icon: '📚',
      description: 'Endpoint documentation and examples'
    },
    {
      id: 'configuration',
      label: 'Configuration',
      icon: '⚙️',
      description: 'Service configuration and settings'
    },
    {
      id: 'error-handling',
      label: 'Error Handling',
      icon: '🛡️',
      description: 'Error management and debugging'
    },
    {
      id: 'testing',
      label: 'API Testing',
      icon: '🧪',
      description: 'Interactive API testing interface'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <ServiceOverview serviceStatus={serviceStatus} />;
      case 'documentation':
        return <APIDocumentation />;
      case 'configuration':
        return <ConfigurationPanel />;
      case 'error-handling':
        return <ErrorHandlingDisplay />;
      case 'testing':
        return <TestingInterface />;
      default:
        return <ServiceOverview serviceStatus={serviceStatus} />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading API Service Layer...
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>API Service Layer | AI Agent Dashboard</title>
        <meta name="description" content="Centralized backend communication infrastructure for the AI Agent Dashboard with Axios HTTP client and structured service architecture." />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">API Service Layer</h1>
                  <p className="mt-2 text-sm text-gray-600">
                    Centralized backend communication infrastructure with structured service architecture
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span className="w-2 h-2 mr-1 bg-green-400 rounded-full"></span>
                    Services Active
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              {tabs?.map((tab) => (
                <button
                  key={tab?.id}
                  onClick={() => setActiveTab(tab?.id)}
                  className={`
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                    ${activeTab === tab?.id
                      ? 'border-blue-500 text-blue-600' :'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                  aria-current={activeTab === tab?.id ? 'page' : undefined}
                >
                  <span className="mr-2 text-lg">{tab?.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{tab?.label}</div>
                    <div className="text-xs text-gray-400 hidden sm:block">{tab?.description}</div>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </>
  );
};

export default APIServiceLayer;