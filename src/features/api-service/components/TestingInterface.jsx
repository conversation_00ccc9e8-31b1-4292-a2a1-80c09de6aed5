// src/pages/api-service-layer/components/TestingInterface.jsx
import React, { useState } from 'react';
import { Play, Copy, Download, RotateCcw, CheckCircle, XCircle, Clock } from 'lucide-react';
import { userService as authService, agentService, dashboardService, userService } from 'services';

const TestingInterface = () => {
  const [selectedService, setSelectedService] = useState('auth');
  const [selectedMethod, setSelectedMethod] = useState('');
  const [requestParams, setRequestParams] = useState('');
  const [response, setResponse] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [requestHistory, setRequestHistory] = useState([]);

  const services = {
    auth: {
      name: 'Authentication Service',
      methods: [
        {
          name: 'login',
          description: 'User login with email and password',
          parameters: '{ "email": "<EMAIL>", "password": "password123" }',
          call: (params) => authService.login(params.email, params.password)
        },
        {
          name: 'register',
          description: 'Register new user account',
          parameters: '{ "email": "<EMAIL>", "password": "password123", "name": "John Doe" }',
          call: (params) => authService.register(params)
        },
        {
          name: 'getCurrentUser',
          description: 'Get current user profile',
          parameters: '{}',
          call: () => authService.getCurrentUser()
        },
        {
          name: 'logout',
          description: 'Logout current user',
          parameters: '{}',
          call: () => authService.logout()
        }
      ]
    },
    agents: {
      name: 'Agent Service',
      methods: [
        {
          name: 'getAgents',
          description: 'Get all agents with optional filtering',
          parameters: '{ "page": 1, "limit": 10, "status": "active" }',
          call: (params) => agentService.getAgents(params)
        },
        {
          name: 'createAgent',
          description: 'Create new AI agent',
          parameters: '{ "name": "Test Agent", "type": "chatbot", "description": "Test agent description" }',
          call: (params) => agentService.createAgent(params)
        },
        {
          name: 'getAgentById',
          description: 'Get agent details by ID',
          parameters: '{ "agentId": "123" }',
          call: (params) => agentService.getAgentById(params.agentId)
        },
        {
          name: 'searchAgents',
          description: 'Search agents by query',
          parameters: '{ "query": "chatbot", "filters": { "type": "chatbot" } }',
          call: (params) => agentService.searchAgents(params.query, params.filters)
        }
      ]
    },
    dashboard: {
      name: 'Dashboard Service',
      methods: [
        {
          name: 'getDashboardOverview',
          description: 'Get dashboard overview metrics',
          parameters: '{ "timeRange": "7d", "includeDetails": true }',
          call: (params) => dashboardService.getDashboardOverview(params)
        },
        {
          name: 'getKPIMetrics',
          description: 'Get KPI metrics',
          parameters: '{}',
          call: (params) => dashboardService.getKPIMetrics(params)
        },
        {
          name: 'getSystemNotifications',
          description: 'Get system notifications',
          parameters: '{ "limit": 5, "unreadOnly": true }',
          call: (params) => dashboardService.getSystemNotifications(params)
        },
        {
          name: 'getRecentActivity',
          description: 'Get recent activity feed',
          parameters: '{ "limit": 10, "activityType": "all" }',
          call: (params) => dashboardService.getRecentActivity(params)
        }
      ]
    },
    users: {
      name: 'User Service',
      methods: [
        {
          name: 'getUserProfile',
          description: 'Get user profile information',
          parameters: '{}',
          call: (params) => userService.getUserProfile(params.userId)
        },
        {
          name: 'updateUserProfile',
          description: 'Update user profile',
          parameters: '{ "name": "Updated Name", "email": "<EMAIL>" }',
          call: (params) => userService.updateUserProfile(params)
        },
        {
          name: 'getUserPreferences',
          description: 'Get user preferences',
          parameters: '{}',
          call: () => userService.getUserPreferences()
        },
        {
          name: 'getAPITokens',
          description: 'Get user API tokens',
          parameters: '{}',
          call: () => userService.getAPITokens()
        }
      ]
    }
  };

  const executeRequest = async () => {
    if (!selectedMethod) return;

    setIsLoading(true);
    const startTime = Date.now();
    
    try {
      let params = {};
      if (requestParams.trim()) {
        params = JSON.parse(requestParams);
      }
      
      const method = services[selectedService]?.methods?.find(m => m.name === selectedMethod);
      const result = await method?.call(params);
      const endTime = Date.now();
      
      const historyItem = {
        id: Date.now(),
        service: selectedService,
        method: selectedMethod,
        params,
        response: result,
        duration: endTime - startTime,
        timestamp: new Date().toLocaleTimeString(),
        success: result?.success
      };
      
      setRequestHistory(prev => [historyItem, ...prev.slice(0, 9)]);
      setResponse(result);
    } catch (error) {
      const endTime = Date.now();
      const errorResult = {
        success: false,
        error: error.message || 'Request failed',
        details: error
      };
      
      const historyItem = {
        id: Date.now(),
        service: selectedService,
        method: selectedMethod,
        params: requestParams ? JSON.parse(requestParams) : {},
        response: errorResult,
        duration: endTime - startTime,
        timestamp: new Date().toLocaleTimeString(),
        success: false
      };
      
      setRequestHistory(prev => [historyItem, ...prev.slice(0, 9)]);
      setResponse(errorResult);
    } finally {
      setIsLoading(false);
    }
  };

  const copyResponse = () => {
    if (response) {
      navigator.clipboard?.writeText(JSON.stringify(response, null, 2));
    }
  };

  const downloadResponse = () => {
    if (response) {
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `api-response-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const resetInterface = () => {
    setResponse(null);
    setRequestParams('');
    setSelectedMethod('');
  };

  const loadFromHistory = (historyItem) => {
    setSelectedService(historyItem.service);
    setSelectedMethod(historyItem.method);
    setRequestParams(JSON.stringify(historyItem.params, null, 2));
    setResponse(historyItem.response);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">API Testing Interface</h2>
        <p className="text-gray-600">
          Interactive testing interface for all API service methods with real-time execution.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Request Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Selection */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Selection</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(services)?.map(([key, service]) => (
                <button
                  key={key}
                  onClick={() => {
                    setSelectedService(key);
                    setSelectedMethod('');
                    setRequestParams('');
                    setResponse(null);
                  }}
                  className={`p-3 text-center rounded-lg border-2 transition-colors duration-200 ${
                    selectedService === key
                      ? 'border-blue-500 bg-blue-50 text-blue-700' :'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="font-medium text-sm">{service?.name}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {service?.methods?.length} methods
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Method Selection */}
          {selectedService && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Method Selection</h3>
              <div className="space-y-2">
                {services[selectedService]?.methods?.map((method) => (
                  <button
                    key={method?.name}
                    onClick={() => {
                      setSelectedMethod(method?.name);
                      setRequestParams(method?.parameters);
                      setResponse(null);
                    }}
                    className={`w-full text-left p-3 rounded-lg border transition-colors duration-200 ${
                      selectedMethod === method?.name
                        ? 'border-blue-500 bg-blue-50' :'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{method?.name}</div>
                    <div className="text-sm text-gray-600 mt-1">{method?.description}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Parameters */}
          {selectedMethod && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Parameters</h3>
              <textarea
                value={requestParams}
                onChange={(e) => setRequestParams(e.target.value)}
                placeholder="Enter request parameters as JSON"
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Enter parameters in JSON format
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={resetInterface}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </button>
                  <button
                    onClick={executeRequest}
                    disabled={!selectedMethod || isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Executing...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        Execute
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Response */}
          {response && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Response</h3>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    response?.success
                      ? 'bg-green-100 text-green-800' :'bg-red-100 text-red-800'
                  }`}>
                    {response?.success ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : (
                      <XCircle className="w-3 h-3 mr-1" />
                    )}
                    {response?.success ? 'Success' : 'Error'}
                  </span>
                  <button
                    onClick={copyResponse}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Copy response"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={downloadResponse}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Download response"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto max-h-96">
                <pre className="text-gray-100 text-sm">
                  <code>{JSON.stringify(response, null, 2)}</code>
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Request History */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Request History</h3>
          <div className="space-y-3">
            {requestHistory?.length === 0 ? (
              <p className="text-gray-500 text-sm text-center py-8">
                No requests yet. Execute a method to see history.
              </p>
            ) : (
              requestHistory?.map((item) => (
                <div
                  key={item?.id}
                  onClick={() => loadFromHistory(item)}
                  className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-sm text-gray-900">
                      {item?.service}.{item?.method}
                    </span>
                    <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
                      item?.success
                        ? 'bg-green-100 text-green-800' :'bg-red-100 text-red-800'
                    }`}>
                      {item?.success ? <CheckCircle className="w-3 h-3" /> : <XCircle className="w-3 h-3" />}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {item?.duration}ms
                    </span>
                    <span>{item?.timestamp}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestingInterface;