// src/pages/api-service-layer/components/ServiceOverview.jsx
import React from 'react';
import { CheckCircle, AlertCircle, XCircle, Activity, Database, Shield, Users } from 'lucide-react';

const ServiceOverview = ({ serviceStatus }) => {
  const services = [
    {
      name: 'Authentication Service',
      description: 'Handles login, registration, and token management',
      status: serviceStatus?.auth || 'healthy',
      icon: Shield,
      endpoints: 8,
      lastUpdate: '2 minutes ago'
    },
    {
      name: 'Agent Service',
      description: 'CRUD operations and management for AI agents',
      status: serviceStatus?.agents || 'healthy',
      icon: Activity,
      endpoints: 12,
      lastUpdate: '1 minute ago'
    },
    {
      name: 'Dashboard Service',
      description: 'Metrics, analytics, and overview data',
      status: serviceStatus?.dashboard || 'healthy',
      icon: Database,
      endpoints: 10,
      lastUpdate: '30 seconds ago'
    },
    {
      name: 'User Service',
      description: 'Profile management and user settings',
      status: serviceStatus?.users || 'healthy',
      icon: Users,
      endpoints: 14,
      lastUpdate: '45 seconds ago'
    }
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case 'healthy':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'warning':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'error':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-green-100 text-green-800`;
    }
  };

  const architectureFeatures = [
    {
      title: 'Axios HTTP Client',
      description: 'Promise-based HTTP client with interceptors and automatic retry logic',
      icon: '🌐'
    },
    {
      title: 'Authentication Interceptors',
      description: 'Automatic JWT token injection and refresh token handling',
      icon: '🔐'
    },
    {
      title: 'Error Handling',
      description: 'Centralized error processing with user-friendly message transformation',
      icon: '🛡️'
    },
    {
      title: 'Response Caching',
      description: 'Optimized performance through intelligent caching strategies',
      icon: '⚡'
    },
    {
      title: 'TypeScript Support',
      description: 'Type-safe interfaces ensuring consistent data structures',
      icon: '📝'
    },
    {
      title: 'Modular Architecture',
      description: 'Organized service modules for easy testing and maintenance',
      icon: '🧩'
    }
  ];

  return (
    <div className="p-6 space-y-8">
      {/* Architecture Overview */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Service Architecture</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {architectureFeatures?.map((feature, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{feature?.icon}</span>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">{feature?.title}</h3>
                  <p className="text-sm text-gray-600">{feature?.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Service Status */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Service Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {services?.map((service, index) => {
            const IconComponent = service?.icon;
            return (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <IconComponent className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{service?.name}</h3>
                      <p className="text-sm text-gray-600 mb-3">{service?.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{service?.endpoints} endpoints</span>
                        <span>•</span>
                        <span>Updated {service?.lastUpdate}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    {getStatusIcon(service?.status)}
                    <span className={getStatusBadge(service?.status)}>
                      {service?.status?.charAt(0)?.toUpperCase() + service?.status?.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* API Statistics */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">API Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">Total Endpoints</p>
                <p className="text-3xl font-bold">44</p>
              </div>
              <div className="p-3 bg-blue-400 bg-opacity-30 rounded-lg">
                <Database className="w-8 h-8" />
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">Success Rate</p>
                <p className="text-3xl font-bold">99.8%</p>
              </div>
              <div className="p-3 bg-green-400 bg-opacity-30 rounded-lg">
                <CheckCircle className="w-8 h-8" />
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">Avg Response</p>
                <p className="text-3xl font-bold">142ms</p>
              </div>
              <div className="p-3 bg-purple-400 bg-opacity-30 rounded-lg">
                <Activity className="w-8 h-8" />
              </div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">Active Services</p>
                <p className="text-3xl font-bold">4</p>
              </div>
              <div className="p-3 bg-orange-400 bg-opacity-30 rounded-lg">
                <Shield className="w-8 h-8" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Implementation Details */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Implementation Details</h2>
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Configuration</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Base URL: <code className="bg-gray-200 px-1 rounded">{`process.env.REACT_APP_API_BASE_URL`}</code></li>
                <li>• Timeout: <code className="bg-gray-200 px-1 rounded">30 seconds</code></li>
                <li>• Retry Logic: <code className="bg-gray-200 px-1 rounded">3 attempts</code></li>
                <li>• Content Type: <code className="bg-gray-200 px-1 rounded">application/json</code></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Features</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Request/Response logging in development</li>
                <li>• Automatic authentication token injection</li>
                <li>• Token refresh workflow handling</li>
                <li>• Standardized error message transformation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceOverview;