// src/pages/api-service-layer/components/APIDocumentation.jsx
import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Copy, ExternalLink } from 'lucide-react';

const APIDocumentation = () => {
  const [expandedSections, setExpandedSections] = useState({
    auth: false,
    agents: false,
    dashboard: false,
    users: false
  });

  const [copiedCode, setCopiedCode] = useState('');

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const copyToClipboard = (code, id) => {
    navigator.clipboard?.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const apiSections = [
    {
      id: 'auth',
      title: 'Authentication Service',
      description: 'User authentication, registration, and token management',
      endpoints: [
        {
          method: 'POST',
          path: '/users/token',
          description: 'User login with email and password',
          example: `// Login user
const result = await authService.login('<EMAIL>', 'password123');
if (result.success) {
  console.log('User logged in:', result.user);
} else {
  console.error('Login failed:', result.error);
}`
        },
        {
          method: 'POST',
          path: '/users/register',
          description: 'User registration with profile data',
          example: `// Register new user
const userData = {
  email: '<EMAIL>',
  password: 'securePassword',
  name: 'John Doe'
};
const result = await authService.register(userData);`
        },
        {
          method: 'POST',
          path: '/users/logout',
          description: 'User logout and token cleanup',
          example: `// Logout user
const result = await authService.logout();
if (result.success) {
  // Redirect to login page
}`
        },
        {
          method: 'GET',
          path: '/users/me',
          description: 'Get current user profile',
          example: `// Get current user
const result = await authService.getCurrentUser();
if (result.success) {
  console.log('Current user:', result.data);
}`
        }
      ]
    },
    {
      id: 'agents',
      title: 'Agent Service',
      description: 'AI agent CRUD operations and management',
      endpoints: [
        {
          method: 'GET',
          path: '/agents',
          description: 'Get all agents with pagination and filtering',
          example: `// Get agents with filters
const params = {
  page: 1,
  limit: 10,
  status: 'active',
  search: 'chatbot'
};
const result = await agentService.getAgents(params);`
        },
        {
          method: 'POST',
          path: '/agents',
          description: 'Create new AI agent',
          example: `// Create new agent
const agentData = {
  name: 'Customer Support Bot',
  type: 'chatbot',
  configuration: {
    model: 'gpt-4',
    temperature: 0.7
  }
};
const result = await agentService.createAgent(agentData);`
        },
        {
          method: 'PUT',
          path: '/agents/:id',
          description: 'Update existing agent',
          example: `// Update agent
const agentId = '123';
const updateData = {
  name: 'Updated Agent Name',
  status: 'active'
};
const result = await agentService.updateAgent(agentId, updateData);`
        },
        {
          method: 'DELETE',
          path: '/agents/:id',
          description: 'Delete agent by ID',
          example: `// Delete agent
const result = await agentService.deleteAgent('123');
if (result.success) {
  console.log('Agent deleted successfully');
}`
        }
      ]
    },
    {
      id: 'dashboard',
      title: 'Dashboard Service',
      description: 'Metrics, analytics, and overview data',
      endpoints: [
        {
          method: 'GET',
          path: '/dashboard/overview',
          description: 'Get dashboard overview metrics',
          example: `// Get dashboard overview
const params = {
  timeRange: '7d',
  includeDetails: true
};
const result = await dashboardService.getDashboardOverview(params);`
        },
        {
          method: 'GET',
          path: '/dashboard/kpi',
          description: 'Get KPI metrics',
          example: `// Get KPI metrics
const result = await dashboardService.getKPIMetrics();
if (result.success) {
  console.log('KPIs:', result.data);
}`
        },
        {
          method: 'GET',
          path: '/dashboard/performance-charts',
          description: 'Get performance chart data',
          example: `// Get chart data
const params = {
  timeRange: '30d',
  chartType: 'line'
};
const result = await dashboardService.getPerformanceChartData(params);`
        },
        {
          method: 'GET',
          path: '/dashboard/notifications',
          description: 'Get system notifications',
          example: `// Get notifications
const params = {
  limit: 10,
  unreadOnly: true
};
const result = await dashboardService.getSystemNotifications(params);`
        }
      ]
    },
    {
      id: 'users',
      title: 'User Service',
      description: 'User profile management and settings',
      endpoints: [
        {
          method: 'GET',
          path: '/users/profile',
          description: 'Get user profile',
          example: `// Get user profile
const result = await userService.getUserProfile();
if (result.success) {
  console.log('Profile:', result.data);
}`
        },
        {
          method: 'PUT',
          path: '/users/profile',
          description: 'Update user profile',
          example: `// Update profile
const profileData = {
  name: 'John Smith',
  email: '<EMAIL>',
  bio: 'Software Developer'
};
const result = await userService.updateUserProfile(profileData);`
        },
        {
          method: 'GET',
          path: '/users/preferences',
          description: 'Get user preferences',
          example: `// Get preferences
const result = await userService.getUserPreferences();
if (result.success) {
  console.log('Preferences:', result.data);
}`
        },
        {
          method: 'POST',
          path: '/users/api-tokens',
          description: 'Create new API token',
          example: `// Create API token
const tokenData = {
  name: 'My Integration Token',
  permissions: ['read:agents', 'write:agents']
};
const result = await userService.createAPIToken(tokenData);`
        }
      ]
    }
  ];

  const getMethodColor = (method) => {
    switch (method) {
      case 'GET':
        return 'bg-green-100 text-green-800';
      case 'POST':
        return 'bg-blue-100 text-blue-800';
      case 'PUT':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">API Documentation</h2>
        <p className="text-gray-600">
          Complete documentation for all available API endpoints and service methods.
        </p>
      </div>

      {/* Quick Start Guide */}
      <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Quick Start</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">1. Import Services</h4>
            <div className="bg-blue-900 rounded p-3 relative">
              <code className="text-blue-100 text-sm">
                {`import { authService, agentService, dashboardService, userService } from 'services'
;`}
              </code>
              <button
                onClick={() => copyToClipboard(`import { authService, agentService, dashboardService, userService } from 'services'
;`, 'import')}
                className="absolute top-2 right-2 p-1 text-blue-300 hover:text-blue-100"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">2. Make API Calls</h4>
            <div className="bg-blue-900 rounded p-3 relative">
              <code className="text-blue-100 text-sm whitespace-pre">
                {`const result = await authService.login(email, password);
if (result.success) {
  // Handle success
  console.log(result.data);
} else {
  // Handle error
  console.error(result.error);
}`}
              </code>
              <button
                onClick={() => copyToClipboard(`const result = await authService.login(email, password);
if (result.success) {
  // Handle success
  console.log(result.data);
} else {
  // Handle error
  console.error(result.error);
}`, 'quickstart')}
                className="absolute top-2 right-2 p-1 text-blue-300 hover:text-blue-100"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* API Sections */}
      <div className="space-y-6">
        {apiSections?.map((section) => (
          <div key={section?.id} className="border border-gray-200 rounded-lg">
            <button
              onClick={() => toggleSection(section?.id)}
              className="w-full px-6 py-4 flex items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 rounded-t-lg"
            >
              <div className="text-left">
                <h3 className="text-lg font-semibold text-gray-900">{section?.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{section?.description}</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {section?.endpoints?.length} endpoints
                </span>
                {expandedSections[section?.id] ? (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronRight className="w-5 h-5 text-gray-500" />
                )}
              </div>
            </button>

            {expandedSections[section?.id] && (
              <div className="divide-y divide-gray-200">
                {section?.endpoints?.map((endpoint, index) => (
                  <div key={index} className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getMethodColor(endpoint?.method)}`}>
                          {endpoint?.method}
                        </span>
                        <code className="text-sm font-mono text-gray-700 bg-gray-100 px-2 py-1 rounded">
                          {endpoint?.path}
                        </code>
                      </div>
                      <button className="text-gray-400 hover:text-gray-600">
                        <ExternalLink className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{endpoint?.description}</p>
                    
                    <div className="bg-gray-900 rounded-lg p-4 relative">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-300 text-sm font-medium">Example Usage</span>
                        <button
                          onClick={() => copyToClipboard(endpoint?.example, `${section?.id}-${index}`)}
                          className="flex items-center space-x-1 text-gray-400 hover:text-gray-200 text-sm"
                        >
                          <Copy className="w-4 h-4" />
                          <span>{copiedCode === `${section?.id}-${index}` ? 'Copied!' : 'Copy'}</span>
                        </button>
                      </div>
                      <pre className="text-gray-100 text-sm overflow-x-auto">
                        <code>{endpoint?.example}</code>
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Response Format */}
      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Standard Response Format</h3>
        <p className="text-gray-600 mb-4">
          All API service methods return a standardized response object:
        </p>
        <div className="bg-gray-900 rounded-lg p-4">
          <pre className="text-gray-100 text-sm overflow-x-auto">
            <code>{`{
  "success": boolean,           // Operation success status
  "data": any,                 // Response data (varies by endpoint)
  "message": string,           // Human-readable success message
  "error": string,             // Error message (only when success is false)
  "status": number,            // HTTP status code
  "validationErrors": object,  // Validation errors (when applicable)
  "pagination": object         // Pagination info (for list responses)
}`}</code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default APIDocumentation;