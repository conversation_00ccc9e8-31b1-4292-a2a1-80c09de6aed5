// src/pages/api-service-layer/components/ConfigurationPanel.jsx
import React, { useState } from 'react';
import { Save, RotateCcw, AlertTriangle, CheckCircle, Eye, EyeOff } from 'lucide-react';

const ConfigurationPanel = () => {
  const [config, setConfig] = useState({
    baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    enableLogging: true,
    enableCaching: true,
    cacheTimeout: 300000,
    authTokenKey: 'authToken',
    refreshTokenKey: 'refreshToken'
  });

  const [envVars, setEnvVars] = useState({
    REACT_APP_API_BASE_URL: process.env.REACT_APP_API_BASE_URL || '',
    REACT_APP_API_TIMEOUT: process.env.REACT_APP_API_TIMEOUT || '',
    REACT_APP_ENABLE_API_LOGGING: process.env.REACT_APP_ENABLE_API_LOGGING || 'true'
  });

  const [showSecrets, setShowSecrets] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
    setIsDirty(true);
  };

  const handleEnvChange = (key, value) => {
    setEnvVars(prev => ({
      ...prev,
      [key]: value
    }));
    setIsDirty(true);
  };

  const handleSave = () => {
    // In a real application, this would save to backend or update environment
    setSaveStatus('saving');
    setTimeout(() => {
      setSaveStatus('success');
      setIsDirty(false);
      setTimeout(() => setSaveStatus(''), 3000);
    }, 1000);
  };

  const handleReset = () => {
    setConfig({
      baseURL: 'http://localhost:8000/api',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      enableLogging: true,
      enableCaching: true,
      cacheTimeout: 300000,
      authTokenKey: 'authToken',
      refreshTokenKey: 'refreshToken'
    });
    setEnvVars({
      REACT_APP_API_BASE_URL: '',
      REACT_APP_API_TIMEOUT: '',
      REACT_APP_ENABLE_API_LOGGING: 'true'
    });
    setIsDirty(false);
  };

  const configSections = [
    {
      title: 'Network Configuration',
      description: 'Basic network and connection settings',
      fields: [
        {
          key: 'baseURL',
          label: 'Base URL',
          type: 'text',
          description: 'The base URL for all API requests',
          placeholder: 'https://api.example.com'
        },
        {
          key: 'timeout',
          label: 'Request Timeout (ms)',
          type: 'number',
          description: 'Maximum time to wait for API response',
          min: 1000,
          max: 120000
        }
      ]
    },
    {
      title: 'Retry Configuration',
      description: 'Settings for automatic retry logic',
      fields: [
        {
          key: 'retryAttempts',
          label: 'Maximum Retry Attempts',
          type: 'number',
          description: 'Number of times to retry failed requests',
          min: 0,
          max: 10
        },
        {
          key: 'retryDelay',
          label: 'Retry Delay (ms)',
          type: 'number',
          description: 'Base delay between retry attempts',
          min: 100,
          max: 10000
        }
      ]
    },
    {
      title: 'Performance & Caching',
      description: 'Performance optimization settings',
      fields: [
        {
          key: 'enableCaching',
          label: 'Enable Response Caching',
          type: 'boolean',
          description: 'Cache API responses for better performance'
        },
        {
          key: 'cacheTimeout',
          label: 'Cache Timeout (ms)',
          type: 'number',
          description: 'How long to keep cached responses',
          min: 60000,
          max: 3600000
        }
      ]
    },
    {
      title: 'Development & Debugging',
      description: 'Settings for development and debugging',
      fields: [
        {
          key: 'enableLogging',
          label: 'Enable API Logging',
          type: 'boolean',
          description: 'Log API requests and responses in development'
        }
      ]
    },
    {
      title: 'Authentication',
      description: 'Authentication and token storage settings',
      fields: [
        {
          key: 'authTokenKey',
          label: 'Auth Token Storage Key',
          type: 'text',
          description: 'LocalStorage key for authentication token'
        },
        {
          key: 'refreshTokenKey',
          label: 'Refresh Token Storage Key',
          type: 'text',
          description: 'LocalStorage key for refresh token'
        }
      ]
    }
  ];

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">API Configuration</h2>
          <p className="text-gray-600 mt-1">
            Configure API service settings and environment variables
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {isDirty && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Unsaved changes
            </span>
          )}
          {saveStatus === 'success' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Settings saved
            </span>
          )}
          <button
            onClick={handleReset}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </button>
          <button
            onClick={handleSave}
            disabled={!isDirty || saveStatus === 'saving'}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="w-4 h-4 mr-2" />
            {saveStatus === 'saving' ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Environment Variables */}
      <div className="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-yellow-900">Environment Variables</h3>
          <button
            onClick={() => setShowSecrets(!showSecrets)}
            className="inline-flex items-center text-sm text-yellow-700 hover:text-yellow-900"
          >
            {showSecrets ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
            {showSecrets ? 'Hide Values' : 'Show Values'}
          </button>
        </div>
        <p className="text-yellow-700 text-sm mb-4">
          These variables should be set in your .env file for proper configuration.
        </p>
        <div className="space-y-4">
          {Object.entries(envVars)?.map(([key, value]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-yellow-900 mb-1">
                {key}
              </label>
              <input
                type={showSecrets ? 'text' : 'password'}
                value={value}
                onChange={(e) => handleEnvChange(key, e.target.value)}
                className="w-full px-3 py-2 border border-yellow-300 rounded-md shadow-sm focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 bg-white"
                placeholder={`Enter ${key}`}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Configuration Sections */}
      <div className="space-y-8">
        {configSections?.map((section, sectionIndex) => (
          <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900">{section?.title}</h3>
              <p className="text-sm text-gray-600 mt-1">{section?.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {section?.fields?.map((field) => (
                <div key={field?.key}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {field?.label}
                  </label>
                  
                  {field?.type === 'boolean' ? (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config[field?.key]}
                        onChange={(e) => handleConfigChange(field?.key, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-600">
                        {field?.description}
                      </span>
                    </div>
                  ) : (
                    <>
                      <input
                        type={field?.type}
                        value={config[field?.key]}
                        onChange={(e) => handleConfigChange(
                          field?.key,
                          field?.type === 'number' ? parseInt(e.target.value) || 0 : e.target.value
                        )}
                        min={field?.min}
                        max={field?.max}
                        placeholder={field?.placeholder}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                      {field?.description && (
                        <p className="mt-1 text-xs text-gray-500">{field?.description}</p>
                      )}
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Current Configuration Display */}
      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Configuration</h3>
        <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre className="text-gray-100 text-sm">
            <code>{JSON.stringify(config, null, 2)}</code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ConfigurationPanel;