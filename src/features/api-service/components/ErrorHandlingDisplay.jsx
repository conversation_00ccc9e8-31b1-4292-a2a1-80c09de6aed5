// src/pages/api-service-layer/components/ErrorHandlingDisplay.jsx
import React, { useState } from 'react';
import { AlertTriangle, XCircle, AlertCircle, Info, RefreshCw, Copy } from 'lucide-react';

const ErrorHandlingDisplay = () => {
  const [selectedError, setSelectedError] = useState(null);
  const [copiedCode, setCopiedCode] = useState('');

  const copyToClipboard = (code, id) => {
    navigator.clipboard?.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const errorTypes = [
    {
      id: 'network',
      title: 'Network Errors',
      description: 'Connection and network-related errors',
      icon: AlertTriangle,
      color: 'red',
      examples: [
        {
          status: 'NETWORK_ERROR',
          message: 'Network connection error. Please check your internet connection.',
          originalError: 'Network Error',
          handling: `// Automatic retry for network errors
const result = await retryRequest(() => 
  api.get('/endpoint')
);`
        },
        {
          status: 'ECONNABORTED',
          message: 'Request timeout. Please try again.',
          originalError: 'timeout of 30000ms exceeded',
          handling: `// Timeout handling with custom timeout
const result = await api.get('/endpoint', {
  timeout: 60000 // 60 seconds
});`
        }
      ]
    },
    {
      id: 'authentication',
      title: 'Authentication Errors',
      description: 'Authentication and authorization failures',
      icon: XCircle,
      color: 'orange',
      examples: [
        {
          status: 401,
          message: 'Authentication failed. Please log in again.',
          originalError: 'Unauthorized',
          handling: `// Automatic token refresh on 401
if (error.status === 401) {
  const refreshResult = await authService.refreshToken();
  if (refreshResult.success) {
    // Retry original request
    return api(originalRequest);
  } else {
    // Redirect to login
    window.location.href = '/login-screen';
  }
}`
        },
        {
          status: 403,
          message: 'You do not have permission to perform this action.',
          originalError: 'Forbidden',
          handling: `// Handle permission errors
if (result.status === 403) {
  // Show permission denied message
  showErrorMessage(result.error);
  // Optionally redirect to dashboard
  navigate('/dashboard-overview');
}`
        }
      ]
    },
    {
      id: 'validation',
      title: 'Validation Errors',
      description: 'Input validation and data format errors',
      icon: AlertCircle,
      color: 'yellow',
      examples: [
        {
          status: 422,
          message: 'Validation failed. Please check your input.',
          originalError: 'Unprocessable Entity',
          validationErrors: {
            email: ['The email field is required.'],
            password: ['The password must be at least 8 characters.']
          },
          handling: `// Handle validation errors
if (!result.success && result.validationErrors) {
  Object.entries(result.validationErrors).forEach(([field, errors]) => {
    // Display field-specific errors
    setFieldError(field, errors[0]);
  });
}`
        },
        {
          status: 400,
          message: 'Invalid request. Please check your input and try again.',
          originalError: 'Bad Request',
          handling: `// Handle bad request errors
if (result.status === 400) {
  // Show user-friendly error message
  showErrorMessage(result.error);
  // Reset form or clear invalid data
  resetForm();
}`
        }
      ]
    },
    {
      id: 'server',
      title: 'Server Errors',
      description: 'Server-side errors and service unavailability',
      icon: Info,
      color: 'blue',
      examples: [
        {
          status: 500,
          message: 'Server error. Please try again later.',
          originalError: 'Internal Server Error',
          handling: `// Automatic retry for 5xx errors
const result = await retryRequest(() => 
  api.post('/endpoint', data),
  3, // max retries
  2000 // delay between retries
);`
        },
        {
          status: 503,
          message: 'Service maintenance in progress. Please try again later.',
          originalError: 'Service Unavailable',
          handling: `// Handle service unavailable
if (result.status === 503) {
  // Show maintenance message
  showMaintenanceMessage();
  // Disable features temporarily
  setMaintenanceMode(true);
}`
        }
      ]
    }
  ];

  const getIconColor = (color) => {
    switch (color) {
      case 'red':
        return 'text-red-600 bg-red-100';
      case 'orange':
        return 'text-orange-600 bg-orange-100';
      case 'yellow':
        return 'text-yellow-600 bg-yellow-100';
      case 'blue':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getBorderColor = (color) => {
    switch (color) {
      case 'red':
        return 'border-red-200 hover:border-red-300';
      case 'orange':
        return 'border-orange-200 hover:border-orange-300';
      case 'yellow':
        return 'border-yellow-200 hover:border-yellow-300';
      case 'blue':
        return 'border-blue-200 hover:border-blue-300';
      default:
        return 'border-gray-200 hover:border-gray-300';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Handling</h2>
        <p className="text-gray-600">
          Comprehensive error handling strategies and debugging information for the API service layer.
        </p>
      </div>

      {/* Error Handling Strategy Overview */}
      <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Error Handling Strategy</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Automatic Retry Logic</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Network errors and timeouts</li>
              <li>• Server errors (5xx status codes)</li>
              <li>• Exponential backoff delay</li>
              <li>• Maximum retry limit (3 attempts)</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Authentication Flow</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Automatic token refresh on 401</li>
              <li>• Logout and redirect on refresh failure</li>
              <li>• Token cleanup on errors</li>
              <li>• Seamless user experience</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Error Types */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {errorTypes?.map((errorType) => {
          const IconComponent = errorType?.icon;
          return (
            <div
              key={errorType?.id}
              className={`border-2 rounded-lg p-6 cursor-pointer transition-all duration-200 ${getBorderColor(errorType?.color)} ${selectedError?.id === errorType?.id ? 'ring-2 ring-blue-500' : ''}`}
              onClick={() => setSelectedError(errorType)}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${getIconColor(errorType?.color)}`}>
                  <IconComponent className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{errorType?.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{errorType?.description}</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {errorType?.examples?.length} examples
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Error Examples Detail */}
      {selectedError && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className={`p-2 rounded-lg ${getIconColor(selectedError?.color)}`}>
              {selectedError.icon && <selectedError.icon className="w-6 h-6" />}
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{selectedError?.title}</h3>
              <p className="text-gray-600">{selectedError?.description}</p>
            </div>
          </div>

          <div className="space-y-6">
            {selectedError?.examples?.map((example, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">
                      Status: {example?.status}
                    </h4>
                    <span className="text-sm text-gray-500">
                      Example {index + 1}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">
                    <strong>User Message:</strong> {example?.message}
                  </p>
                  <p className="text-gray-600 text-sm">
                    <strong>Original Error:</strong> {example?.originalError}
                  </p>
                  
                  {example?.validationErrors && (
                    <div className="mt-3">
                      <p className="text-gray-700 font-medium mb-2">Validation Errors:</p>
                      <div className="bg-red-50 border border-red-200 rounded p-3">
                        <pre className="text-red-800 text-sm">
                          {JSON.stringify(example.validationErrors, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900">Error Handling Code</h5>
                    <button
                      onClick={() => copyToClipboard(example?.handling, `${selectedError?.id}-${index}`)}
                      className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 text-sm"
                    >
                      <Copy className="w-4 h-4" />
                      <span>{copiedCode === `${selectedError?.id}-${index}` ? 'Copied!' : 'Copy'}</span>
                    </button>
                  </div>
                  <div className="bg-gray-900 rounded p-4">
                    <pre className="text-gray-100 text-sm overflow-x-auto">
                      <code>{example?.handling}</code>
                    </pre>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Best Practices */}
      <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-900 mb-4">Error Handling Best Practices</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-green-800 mb-2">User Experience</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Always show user-friendly error messages</li>
              <li>• Provide actionable steps when possible</li>
              <li>• Use appropriate visual indicators</li>
              <li>• Avoid technical jargon in user-facing messages</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-green-800 mb-2">Development</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Log detailed error information for debugging</li>
              <li>• Use consistent error response format</li>
              <li>• Implement proper error boundaries</li>
              <li>• Test error scenarios thoroughly</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Debugging Tools */}
      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Debugging Tools</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <RefreshCw className="w-5 h-5 text-gray-600" />
            <div>
              <p className="font-medium text-gray-900">Console Logging</p>
              <p className="text-sm text-gray-600">
                Enable detailed API logging in development mode to track requests and responses
              </p>
            </div>
          </div>
          <div className="bg-gray-900 rounded p-4">
            <code className="text-gray-100 text-sm">
              {`// Enable in development environment
if (process.env.NODE_ENV === 'development') {
  console.log('API Request:', config);
  console.log('API Response:', response);
}`}
            </code>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorHandlingDisplay;