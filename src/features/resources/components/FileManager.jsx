import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Upload, 
  File, 
  FileText, 
  Image, 
  Archive, 
  Code, 
  Database, 
  Presentation, 
  Sheet,
  Search,
  Download,
  Eye,
  Trash2,
  Edit,
  MoreVertical,
  Calendar,
  Tag,
  AlertCircle,
  RefreshCw,
  Plus,
  ExternalLink
} from 'lucide-react';
import { Button, Badge, AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel, Popover, PopoverTrigger, PopoverContent } from 'components/ui';
import { Skeleton } from 'components/ui/skeleton';
import FileViewer from 'components/ui/FileViewer';
import { resourceService } from 'services';
import FileIcon from 'components/ui/FileIcon';

const FileManager = () => {
  const navigate = useNavigate();
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const [selectedFile, setSelectedFile] = useState(null);
  const [docViewerError, setDocViewerError] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [refreshingFiles, setRefreshingFiles] = useState(new Set());

  // Fetch file resources
  const fetchFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await resourceService.listResources('file', {
        skip: 0,
        limit: 100,
        search: searchQuery
      });
      const filesData = response.resources || response.results || [];
      const filesWithType = filesData.map(file => ({
        ...file,
        type: file.type || 'file'
      }));
      setFiles(filesWithType);
    } catch (err) {
      console.error('Failed to fetch files:', err);
      setError(err.message || 'Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [searchQuery]);

  const handleFileSelect = (fileId, selected) => {
    setSelectedFiles(prev => 
      selected 
        ? [...prev, fileId]
        : prev.filter(id => id !== fileId)
    );
  };

  const handleViewFile = async (file) => {
    setSelectedFile(file);
    setDocViewerError(false);
  };

  const handleAction = async (action, file) => {
    switch (action) {
      case 'view':
        handleViewFile(file);
        break;
      case 'edit':
        navigate(`/resources/file/${file.id}/edit`);
        break;
      case 'delete':
        setFileToDelete(file);
        setDeleteDialogOpen(true);
        break;
      case 'refresh':
        try {
          setRefreshingFiles(prev => new Set([...prev, file.id]));
          await resourceService.refreshResource('file', file.id);
          await fetchFiles();
        } catch (err) {
          console.error('Failed to refresh file:', err);
        } finally {
          setRefreshingFiles(prev => {
            const newSet = new Set(prev);
            newSet.delete(file.id);
            return newSet;
          });
        }
        break;
      case 'download':
        if (file.download_url) {
          window.open(file.download_url, '_blank');
        } else if (file.storage_key) {
          const downloadUrl = getFileUrlWithCredentials(file);
          if (downloadUrl) {
            window.open(downloadUrl, '_blank');
          }
        }
        break;
    }
  };

  const handleDeleteConfirm = async () => {
    if (fileToDelete) {
      try {
        await resourceService.deleteResource('file', fileToDelete.id);
        setFiles(prev => prev.filter(f => f.id !== fileToDelete.id));
        if (selectedFile?.id === fileToDelete.id) {
          setSelectedFile(null);
        }
      } catch (err) {
        console.error('Failed to delete file:', err);
      }
    }
    setDeleteDialogOpen(false);
    setFileToDelete(null);
  };

  const handleBulkDelete = async () => {
    if (!window.confirm(`Delete ${selectedFiles.length} selected files?`)) {
      return;
    }

    try {
      await Promise.all(
        selectedFiles.map(id => resourceService.deleteResource('file', id))
      );
      setFiles(prev => prev.filter(file => !selectedFiles.includes(file.id)));
      setSelectedFiles([]);
      if (selectedFile && selectedFiles.includes(selectedFile.id)) {
        setSelectedFile(null);
      }
    } catch (err) {
      console.error('Failed to delete files:', err);
    }
  };
  
  const getFileIcon = (fileType) => {
    if (fileType?.includes('image/')) return Image;
    if (fileType?.includes('pdf') || fileType?.includes('document')) return FileText;
    if (fileType?.includes('archive') || fileType?.includes('zip')) return Archive;
    if (fileType?.includes('text/') || fileType?.includes('code')) return Code;
    if (fileType?.includes('spreadsheet') || fileType?.includes('excel')) return Sheet;
    if (fileType?.includes('presentation') || fileType?.includes('powerpoint')) return Presentation;
    if (fileType?.includes('json') || fileType?.includes('csv') || fileType?.includes('xml')) return Database;
    return File;
  };

  const getFileExtension = (filename) => {
    if (!filename) return '';
    const parts = filename.split('.');
    return parts.length > 1 ? parts.pop().toLowerCase() : '';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const filteredFiles = files.filter(file =>
    file.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Loading skeleton
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-surface rounded-lg border border-border p-4">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <Skeleton className="w-12 h-12 rounded-lg" />
                  <Skeleton className="w-6 h-6 rounded" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="w-16 h-5 rounded-full" />
                  <Skeleton className="w-16 h-4" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Helper to get the correct file URL
  const getFileUrl = (file) => {
    if (!file) return null;
    if (file.file_url && (file.file_url.startsWith('http://') || file.file_url.startsWith('https://') || file.file_url.startsWith('/api/files/'))) {
      return file.file_url;
    }
    if (file.storage_key) {
      return `/api/files/${file.storage_key}`;
    }
    return null;
  };

  // Helper to get file URL with credentials
  const getFileUrlWithCredentials = (file) => {
    const baseUrl = getFileUrl(file);
    if (!baseUrl) return null;
    
    // If it's a relative URL, add the base URL
    if (baseUrl.startsWith('/')) {
      return `${window.location.origin}${baseUrl}`;
    }
    
    return baseUrl;
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-text-secondary">
        <button
          onClick={() => navigate('/resources?tab=file')}
          className="hover:text-text-primary transition-colors"
        >
          Resources
        </button>
        <span>/</span>
        <span className="text-text-primary">File Manager</span>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">File Manager</h1>
          <p className="text-text-secondary mt-1">
            {filteredFiles.length} file{filteredFiles.length !== 1 ? 's' : ''} found
          </p>
          </div>
        <div className="flex items-center gap-3">
          <Button
            variant={viewMode === 'grid' ? 'solid' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'solid' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            List
          </Button>
          <Button color="blue" size="2" onClick={() => navigate('/resources?tab=file')}>
            <Plus size={18} />
            Add File
          </Button>
        </div>
      </div>
      
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={20} />
        <input
          type="text"
          placeholder="Search files by name or description..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-2 bg-surface border border-border rounded-lg focus:border-primary focus:ring-1 focus:ring-primary/20"
        />
          </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-error mr-2" />
            <p className="text-error font-medium">Error</p>
          </div>
          <p className="text-error-700 mt-1">{error}</p>
          <Button variant="outline" size="1" onClick={fetchFiles}>
            Retry
          </Button>
          </div>
      )}

      {/* Bulk Actions */}
      {selectedFiles.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-surface border border-border rounded-lg">
          <span className="text-sm text-text-secondary">
            {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
          </span>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="1"
              onClick={handleBulkDelete}
            >
              <Trash2 size={16} />
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* File List */}
        <div>
          <h2 className="text-lg font-semibold text-text-primary mb-4">Files</h2>
          
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {filteredFiles.map((file) => {
                const isSelected = selectedFiles.includes(file.id);
                const FileTypeIcon = getFileIcon(file.file_type);

                return (
                  <div
                    key={file.id}
                    className={`
                      bg-surface rounded-lg border transition-all duration-200 group relative cursor-pointer
                      ${isSelected ? 'border-primary shadow-sm' : 'border-border hover:border-secondary-300'}
                      ${selectedFile?.id === file.id ? 'ring-2 ring-primary' : ''}
                    `}
                    onClick={() => handleViewFile(file)}
                  >
                    {/* More Actions Popover */}
                    <div className="absolute top-2 right-2 z-10">
                      <Popover>
                        <PopoverTrigger asChild>
                          <button
                            className="p-1 rounded hover:bg-surface-muted transition-colors opacity-0 group-hover:opacity-100"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical size={14} className="text-text-tertiary" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-32 p-1"
                          align="end"
                          sideOffset={5}
                        >
                            <div className="flex flex-col">
                              <button 
                                onClick={() => handleAction('view', file)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                              >
                                <Eye size={14} className="mr-2" />
                                View
                              </button>
                              <button 
                                onClick={() => handleAction('download', file)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                              >
                                <Download size={14} className="mr-2" />
                                Download
                              </button>
                              <button 
                                onClick={() => handleAction('refresh', file)}
                                disabled={refreshingFiles.has(file.id)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left disabled:opacity-50"
                              >
                                <RefreshCw size={14} className={`mr-2 ${refreshingFiles.has(file.id) ? 'animate-spin' : ''}`} />
                                {refreshingFiles.has(file.id) ? 'Refreshing...' : 'Refresh'}
                              </button>
                              <div className="border-t border-border my-1"></div>
                              <button 
                                onClick={() => handleAction('delete', file)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-red-50 rounded transition-colors text-left text-red-600 hover:text-red-700"
                              >
                                <Trash2 size={14} className="mr-2" />
                                Delete
                              </button>
                            </div>
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="p-4">
                      {/* Status Badge */}
                      <div className="flex items-center justify-end mb-3">
                        <Badge status={file.status} size="sm" />
                      </div>

                      {/* Icon and Content */}
                      <div className="flex items-start gap-3 mb-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-lg flex-shrink-0">
                          {file.filename ? (
                            <div className="w-8 h-8 flex items-center justify-center">
                              <FileIcon extension={getFileExtension(file.filename)} size={20} />
                            </div>
                          ) : (
                            <FileTypeIcon size={20} className="text-secondary-600" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-text-primary mb-1 line-clamp-2 text-sm">
                            {file.name}
                          </h3>
                          {file.description && (
                            <p className="text-xs text-text-secondary line-clamp-2">{file.description}</p>
                          )}
                        </div>
                      </div>

                      {/* Meta Info */}
                      <div className="text-xs text-text-tertiary border-t border-secondary-200 pt-3 space-y-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Calendar size={12} className="mr-1" />
                            {formatDate(file.created_at)}
                          </div>
                          {file.size && (
                            <div>{formatFileSize(file.size)}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="bg-surface border border-border rounded-lg overflow-hidden">
              <div className="px-4 py-3 bg-surface-muted border-b border-border">
                <div className="grid grid-cols-12 gap-4 text-xs font-medium text-text-secondary uppercase tracking-wide">
                  <div className="col-span-6">Name</div>
                  <div className="col-span-2">Status</div>
                  <div className="col-span-2">Modified</div>
                  <div className="col-span-2">Size</div>
                </div>
              </div>
              <div className="divide-y divide-border">
                {filteredFiles.map((file) => {
                  const isSelected = selectedFiles.includes(file.id);
                  const FileTypeIcon = getFileIcon(file.file_type);

                  return (
                    <div
                      key={file.id}
                      className={`
                        px-4 py-3 hover:bg-surface-muted transition-colors cursor-pointer
                        ${isSelected ? 'bg-primary-50' : ''}
                        ${selectedFile?.id === file.id ? 'bg-primary-50 border-l-4 border-primary' : ''}
                      `}
                      onClick={() => handleViewFile(file)}
                    >
                      <div className="grid grid-cols-12 gap-4 items-center">
                        <div className="col-span-6">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-surface-muted rounded-lg flex items-center justify-center">
                              {file.filename ? (
                                <div className="w-6 h-6 flex items-center justify-center">
                                  <FileIcon extension={getFileExtension(file.filename)} size={16} />
                                </div>
                              ) : (
                                <FileTypeIcon size={16} className="text-text-secondary" />
                              )}
                            </div>
                            <div className="min-w-0 flex-1">
                              <p className="text-sm font-medium text-text-primary truncate">
                                {file.name}
                              </p>
                              {file.description && (
                                <p className="text-xs text-text-secondary truncate">{file.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="col-span-2">
                          <Badge status={file.status} size="default" />
                        </div>
                        <div className="col-span-2">
                          <div className="text-sm text-text-secondary">
                            {formatDate(file.updated_at)}
                          </div>
                        </div>
                        <div className="col-span-2">
                          <div className="text-sm text-text-secondary">
                            {file.size ? formatFileSize(file.size) : '-'}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {filteredFiles.length === 0 && !loading && (
            <div className="text-center py-16">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary-100 rounded-full mb-4">
                <Upload className="w-8 h-8 text-secondary-500" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                No files found
              </h3>
              <p className="text-text-secondary mb-4 max-w-sm mx-auto">
                Upload your first file to get started. Files will be automatically processed and indexed for search.
              </p>
              <Button color="blue" size="2" onClick={() => navigate('/resources?tab=file')}>
                <Plus size={18} />
                Add File
              </Button>
            </div>
          )}
        </div>

        {/* File Viewer */}
        <div>
          <h2 className="text-lg font-semibold text-text-primary mb-4">Preview</h2>
          
          {selectedFile ? (
            <div className="bg-surface rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <span className="text-lg font-semibold truncate">{selectedFile.name}</span>
                <div className="flex items-center gap-2">
                  <Badge variant="soft">{selectedFile.type}</Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAction('download', selectedFile)}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
              
              {getFileUrl(selectedFile) ? (
                <div className="h-[80vh] rounded-lg border overflow-hidden">
                  {selectedFile.status === 'failed' && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center">
                        <AlertCircle className="w-4 h-4 text-yellow-600 mr-2" />
                        <span className="text-sm text-yellow-800">
                          File processing failed, but the file is available for viewing/download.
                        </span>
                      </div>
                    </div>
                  )}
                  
                  {!docViewerError ? (
                    <FileViewer
                      file={selectedFile}
                      fileUrl={getFileUrlWithCredentials(selectedFile)}
                      onError={(error) => {
                        console.error('FileViewer error:', error);
                        setDocViewerError(true);
                      }}
                    />
                  ) : (
                    <div className="border rounded-lg h-full flex items-center justify-center bg-gray-50">
                      <div className="text-center">
                        <File className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-gray-600 mb-4">Unable to preview this file format</p>
                        <div className="space-x-2">
                          <Button 
                            onClick={() => window.open(getFileUrlWithCredentials(selectedFile), '_blank')}
                            variant="outline"
                          >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Open in New Tab
                          </Button>
                          <Button 
                            onClick={() => setDocViewerError(false)}
                            variant="ghost"
                            size="sm"
                          >
                            Try Again
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No file content available</p>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-surface rounded-lg border p-12">
              <div className="text-center">
                <File className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold text-text-primary mb-2">
                  Select a file to preview
                </h3>
                <p className="text-text-secondary">
                  Click on any file from the list to view its contents here.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete File</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{fileToDelete?.name}"?
              This action cannot be undone and the file will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete File
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FileManager;
