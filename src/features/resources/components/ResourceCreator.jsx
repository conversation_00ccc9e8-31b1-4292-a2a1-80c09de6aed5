import React, { useState, useRef, useEffect } from 'react';
import { Upload, Globe, FileText, X, AlertCircle, Check, Loader, Network, Map } from 'lucide-react';
import LexicalEditor from 'components/ui/LexicalEditor';
import { Button } from 'components/ui';
import { Input } from 'components/ui';
import { resourceService } from 'services';

const ResourceCreator = ({ onResourceCreated, onClear, selectedType = 'file', existingFiles = [] }) => {
  const [resourceType, setResourceType] = useState(selectedType);
  const [name, setName] = useState('');
  const [content, setContent] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [file, setFile] = useState(null);
  const [websiteType, setWebsiteType] = useState('single');
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [errors, setErrors] = useState({});
  const [showSuccess, setShowSuccess] = useState(false);
  const [nameManuallyEdited, setNameManuallyEdited] = useState(false);
  const [fetchingTitle, setFetchingTitle] = useState(false);
  const [titleFetched, setTitleFetched] = useState(false);
  const fileInputRef = useRef();

  // Website type options
  const websiteTypes = [
    {
      value: 'single',
      label: 'Single Web Page',
      description: 'Scrape content from a single webpage',
      icon: 'Globe'
    },
    {
      value: 'recursive',
      label: 'Recursive URL',
      description: 'Recursively scrapes all child links from a root URL',
      icon: 'Network'
    },
    {
      value: 'sitemap',
      label: 'Sitemap',
      description: 'Scrapes all pages on a given sitemap',
      icon: 'Map'
    }
  ];

  // Update resource type when selectedType prop changes
  useEffect(() => {
    setResourceType(selectedType);
  }, [selectedType]);

  const getResourceTypeInfo = (type) => {
    const typeInfo = {
      file: {
        label: 'Upload File',
        icon: Upload,
        description: 'Upload documents, PDFs, images, or other files',
        color: 'text-primary bg-primary-50 border-primary-200',
        activeColor: 'ring-primary-500 bg-primary-100',
        headerDescription: 'Upload files to your knowledge base',
        placeholder: 'Enter a descriptive name for your file',
        contentLabel: 'File Upload *',
        contentPlaceholder: 'Click to upload or drag and drop',
        contentNote: 'PDF, DOC, TXT, or any file up to 50MB'
      },
      article: {
        label: 'Add Text',
        icon: FileText,
        description: 'Create or paste text content, articles, or notes',
        color: 'text-accent bg-accent-50 border-accent-200',
        activeColor: 'ring-accent-500 bg-accent-100',
        headerDescription: 'Create text documents and articles',
        placeholder: 'Enter a title for your document',
        contentLabel: 'Text Content *',
        contentPlaceholder: 'Start writing your content here...',
        contentNote: 'Use the toolbar above to format your text'
      },
      web: {
        label: 'Web Resource',
        icon: Globe,
        description: 'Add websites, documentation, or online content',
        color: 'text-primary bg-primary-50 border-primary-200',
        activeColor: 'ring-primary-500 bg-primary-100',
        headerDescription: 'Add websites and web content',
        placeholder: 'Enter a name for this web resource (auto-filled from page title)',
        contentLabel: 'Website URL *',
        contentPlaceholder: 'https://example.com',
        contentNote: 'We\'ll crawl and index the content automatically. Resource name will be auto-filled from the page title.'
      }
    };
    return typeInfo[type] || typeInfo.file;
  };

  const getWebsiteTypeInfo = (type) => {
    const typeInfo = {
      single: {
        description: 'Perfect for individual articles, documentation pages, or specific content you want to capture.',
        placeholder: 'https://example.com/article',
        note: 'Only the content from this specific page will be indexed.'
      },
      recursive: {
        description: 'Ideal for capturing entire sections of a website by following links from the root URL.',
        placeholder: 'https://example.com/docs',
        note: 'Will follow links within the same domain, up to a reasonable depth limit.'
      },
      sitemap: {
        description: 'Best for comprehensive website coverage using the site\'s XML sitemap.',
        placeholder: 'https://example.com/sitemap.xml',
        note: 'Requires the website to have a valid XML sitemap for optimal results.'
      }
    };
    return typeInfo[type] || typeInfo.single;
  };

  const currentTypeInfo = getResourceTypeInfo(resourceType);
  const currentWebsiteTypeInfo = getWebsiteTypeInfo(websiteType);

  const validateForm = () => {
    const newErrors = {};

    if (!name.trim()) {
      newErrors.name = 'Resource name is required';
    } else if (name.trim().length < 3) {
      newErrors.name = 'Resource name must be at least 3 characters long';
    }

    if (resourceType === 'file' && !file) {
      newErrors.file = 'Please select a file to upload';
    } else if (resourceType === 'file' && file) {
      // Additional file validation
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        newErrors.file = 'File size must be less than 50MB';
      }
    }

    if (resourceType === 'article' && !content.trim()) {
      newErrors.content = 'Text content is required';
    } else if (resourceType === 'article' && content.trim().length < 10) {
      newErrors.content = 'Text content must be at least 10 characters long';
    }

    if (resourceType === 'web' && !content.trim()) {
      newErrors.content = 'Website URL is required';
    } else if (resourceType === 'web' && content.trim()) {
      try {
        const url = new URL(content.trim());
        if (!['http:', 'https:'].includes(url.protocol)) {
          newErrors.content = 'URL must use HTTP or HTTPS protocol';
        }
      } catch {
        newErrors.content = 'Please enter a valid URL (e.g., https://example.com)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreate = async () => {
    if (!validateForm()) return;

    setUploading(true);

    try {
      const newResource = {
        name: name.trim(),
        type: resourceType,
        content: resourceType === 'article' || resourceType === 'web' ? content : null,
        description: description.trim(),
        tags: tags.split(',').map(tag => tag.trim()).filter(Boolean),
        file: resourceType === 'file' ? file : null,
        websiteType: resourceType === 'web' ? websiteType : null
      };

      await onResourceCreated(newResource);

      // Only reset form on successful creation
      setName('');
      setContent('');
      setDescription('');
      setTags('');
      setFile(null);
      setWebsiteType('single');
      setErrors({});
      setShowSuccess(true);
      setNameManuallyEdited(false);
      setFetchingTitle(false);
      setTitleFetched(false);
      
      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error creating resource:', error);
      // Don't reset form on error - keep field values
      setErrors({ submit: error.message || 'Failed to create resource. Please try again.' });
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (selectedFile) => {
    setFile(selectedFile);
    
    // Check for potential duplicates
    if (selectedFile && existingFiles && existingFiles.length > 0) {
      const fileName = selectedFile.name;
      const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, "");
      
      // Check for exact filename matches
      const exactMatch = existingFiles.find(file => 
        file.filename === fileName || file.name === fileName || file.name === nameWithoutExtension
      );
      
      // Check for similar filename matches (same name, different extension)
      const similarMatch = existingFiles.find(file => {
        const existingNameWithoutExt = (file.filename || file.name || '').replace(/\.[^/.]+$/, "");
        return existingNameWithoutExt.toLowerCase() === nameWithoutExtension.toLowerCase();
      });
      
      if (exactMatch) {
        setErrors(prev => ({ 
          ...prev, 
          file: `A file with the name "${fileName}" already exists. Consider renaming this file or deleting the existing one.`,
          submit: null 
        }));
      } else if (similarMatch) {
        setErrors(prev => ({ 
          ...prev, 
          file: `A similar file "${similarMatch.filename || similarMatch.name}" already exists. You may want to check if this is a duplicate.`,
          submit: null 
        }));
      }
    }
    
    // Auto-fill resource name from filename (remove extension)
    if (selectedFile) {
      const fileName = selectedFile.name;
      const nameWithoutExtension = fileName.replace(/\.[^/.]+$/, ""); // Remove file extension
      
      // Only auto-fill if name is empty or if user hasn't manually edited it
      if (!name || !nameManuallyEdited) {
        setName(nameWithoutExtension);
        setNameManuallyEdited(false); // Reset the flag since we're auto-filling
      }
    }
    
    // Only clear file error if there's no duplicate detection issue
    if (!errors.file || !errors.file.includes('already exists')) {
      setErrors(prev => ({ ...prev, file: null, submit: null }));
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Fetch webpage title for auto-filling resource name
  const fetchWebPageTitle = async (url) => {
    setFetchingTitle(true);
    setTitleFetched(false);
    setErrors(prev => ({ ...prev, name: null, submit: null }));

    try {
      const response = await resourceService.fetchPageTitle(url);
      if (response.success && response.title) {
        setName(response.title);
        setNameManuallyEdited(false);
        setTitleFetched(true);
      } else {
        setErrors(prev => ({ ...prev, name: 'Failed to fetch title. Please enter manually.' }));
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, name: 'Error fetching title. Please try again.' }));
    } finally {
      setFetchingTitle(false);
    }
  };

  // Auto-fill resource name from webpage title
  const handleUrlBlur = async (url) => {
    // Only auto-fill if the URL is valid and name is empty or hasn't been manually edited
    if (!url || (!name || !nameManuallyEdited)) {
      try {
        // Validate URL format first
        new URL(url.trim());
        
        if (!['http:', 'https:'].includes(new URL(url.trim()).protocol)) {
          return; // Invalid protocol, don't fetch
        }

        setFetchingTitle(true);
        setTitleFetched(false);
        
        const result = await resourceService.fetchWebpageTitle(url.trim());
        
        if (result.title) {
          // Only auto-fill if name is empty or user hasn't manually edited it
          if (!name || !nameManuallyEdited) {
            setName(result.title);
            setNameManuallyEdited(false);
            setTitleFetched(true);
          }
        }
      } catch (error) {
        console.warn('Invalid URL or failed to fetch title:', error);
      } finally {
        setFetchingTitle(false);
      }
    }
  };

  return (
    <div className="bg-surface border border-secondary-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-50 to-accent-50 px-6 py-4 border-b border-secondary-200">
        <div className="flex items-center space-x-3">
          <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg ${currentTypeInfo.color}`}>
            <currentTypeInfo.icon size={20} />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-text-primary">Create New {currentTypeInfo.label}</h2>
            <p className="text-text-secondary mt-1">{currentTypeInfo.headerDescription}</p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Website Type Selection - Only for web resources */}
        {resourceType === 'web' && (
          <div className="space-y-3">
            <label className="block text-sm font-medium text-text-primary">Website Type</label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {websiteTypes.map((type) => {
                const Icon = type.icon === 'Globe' ? Globe : type.icon === 'Network' ? Network : Map;
                const isActive = websiteType === type.value;
                
                return (
                  <button
                    key={type.value}
                    onClick={() => setWebsiteType(type.value)}
                    disabled={uploading}
                    className={`
                      relative p-4 rounded-lg border-2 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed
                      ${isActive 
                        ? 'bg-primary-50 border-primary-200 text-primary ring-2 ring-primary-200' 
                        : 'bg-secondary-50 border-secondary-200 text-text-secondary hover:bg-secondary-100 hover:border-secondary-300'
                      }
                    `}
                  >
                    <div className="flex items-start space-x-3">
                      <Icon size={24} className="flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium">{type.label}</h3>
                        <p className="text-sm opacity-75 mt-1">{type.description}</p>
                      </div>
                    </div>
                    {isActive && (
                      <div className="absolute top-2 right-2">
                        <Check size={16} className="text-current" />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
            
            {/* Website Type Description */}
            <div className="p-3 bg-secondary-50 rounded-lg">
              <p className="text-xs text-text-secondary">
                <strong>About this type:</strong> {currentWebsiteTypeInfo.description}
              </p>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-8">
          {/* File Upload - Show First for File Resources */}
          {resourceType === 'file' && (
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                {currentTypeInfo.contentLabel}
              </label>
              <div
                className={`
                  relative border-2 border-dashed rounded-lg p-12 transition-colors bg-secondary-50
                  ${dragActive 
                    ? 'border-primary bg-primary-50' 
                    : errors.file 
                      ? 'border-error bg-error-50' 
                      : 'border-secondary-300 hover:border-secondary-400 hover:bg-secondary-100'
                  }
                `}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={(e) => handleFileChange(e.target.files[0])}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  accept=".pdf,.doc,.docx,.txt,.md,.rtf,.odt,.py,.js,.ts,.html,.css,.java,.cpp,.c,.go,.rs,.json,.csv,.xml,.yaml,.yml,.jsonl,.xlsx,.xls,.ods,.pptx,.ppt,.odp,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.zip,.tar,.gz,.rar"
                  aria-label="Upload file"
                />
                
                {file ? (
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-success-100 rounded-full mb-4">
                      <Check className="w-8 h-8 text-success-600" />
                    </div>
                    <p className="text-lg font-medium text-text-primary mb-2">{file.name}</p>
                    <p className="text-sm text-text-secondary mb-2">{formatFileSize(file.size)}</p>
                    {name && name === file.name.replace(/\.[^/.]+$/, "") && (
                      <p className="text-sm text-success-600">
                        ✓ Resource name auto-filled from filename
                      </p>
                    )}
                  </div>
                ) : (
                  <div 
                    className="text-center cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mx-auto h-12 w-12 text-text-tertiary mb-4" />
                    <p className="text-lg text-text-primary mb-2">
                      <span className="font-medium">{currentTypeInfo.contentPlaceholder}</span>
                    </p>
                    <p className="text-sm text-text-secondary mb-3">
                      {currentTypeInfo.contentNote}
                    </p>
                    <p className="text-sm text-primary-600">
                      Click to browse or drag and drop your file here
                    </p>
                  </div>
                )}
              </div>
              {errors.file && (
                <p className="flex items-center mt-2 text-sm text-error">
                  <AlertCircle size={14} className="mr-1" />
                  {errors.file}
                </p>
              )}
            </div>
          )}

          {/* Visual Separator */}
          {resourceType === 'file' && (
            <div className="border-t border-secondary-200 pt-6">
              <h3 className="text-lg font-medium text-text-primary mb-4">Resource Details</h3>
            </div>
          )}

          {/* Other Form Fields - Show After File Upload */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* URL Input - Show First for Web Resources */}
              {resourceType === 'web' && (
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    {currentTypeInfo.contentLabel}
                  </label>
                  <div className="relative">
                    <Input
                      placeholder={currentWebsiteTypeInfo.placeholder}
                      value={content}
                      onChange={(e) => {
                        setContent(e.target.value);
                        setErrors(prev => ({ ...prev, content: null, submit: null }));
                        setTitleFetched(false); // Reset title fetched status when URL changes
                      }}
                      onBlur={(e) => handleUrlBlur(e.target.value)}
                      disabled={uploading || fetchingTitle}
                      className={errors.content ? 'border-error focus:border-error focus:ring-error/20' : ''}
                    />
                    {fetchingTitle && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader className="animate-spin h-4 w-4 text-primary" />
                      </div>
                    )}
                  </div>
                  {fetchingTitle && (
                    <p className="flex items-center mt-1 text-sm text-primary">
                      <Loader size={14} className="mr-1 animate-spin" />
                      Fetching page title...
                    </p>
                  )}
                  {titleFetched && name && !nameManuallyEdited && (
                    <p className="text-xs text-success-600 mt-1">
                      ✓ Resource name auto-filled from webpage title
                    </p>
                  )}
                  {errors.content && (
                    <p className="flex items-center mt-1 text-sm text-error">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.content}
                    </p>
                  )}
                  <p className="text-xs text-text-tertiary mt-1">{currentWebsiteTypeInfo.note}</p>
                </div>
              )}

              {/* Resource Name */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Resource Name *
                </label>
                <div className="flex space-x-2">
                  <Input
                    placeholder={currentTypeInfo.placeholder}
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value);
                      setNameManuallyEdited(true);
                      setErrors(prev => ({ ...prev, name: null, submit: null }));
                    }}
                    disabled={uploading}
                    className={errors.name ? 'border-error focus:border-error focus:ring-error/20' : ''}
                  />
                  {resourceType === 'file' && file && name !== file.name.replace(/\.[^/.]+$/, "") && nameManuallyEdited && (
                    <Button
                      variant="outline"
                      size="1"
                      onClick={() => {
                        setName(file.name.replace(/\.[^/.]+$/, ""));
                        setNameManuallyEdited(false);
                      }}
                      disabled={uploading}
                    >
                      Update from filename
                    </Button>
                  )}
                </div>
                {resourceType === 'file' && !file && (
                  <p className="text-xs text-text-tertiary mt-1">
                    Upload a file first to auto-fill the resource name from the filename
                  </p>
                )}
                {resourceType === 'file' && file && name === file.name.replace(/\.[^/.]+$/, "") && (
                  <p className="text-xs text-success-600 mt-1">
                    ✓ Auto-filled from uploaded file. You can edit this name if needed.
                  </p>
                )}
                {resourceType === 'file' && file && name !== file.name.replace(/\.[^/.]+$/, "") && nameManuallyEdited && (
                  <p className="text-xs text-warning-600 mt-1">
                    ℹ️ Name was manually edited. Click "Update from filename" to use "{file.name.replace(/\.[^/.]+$/, "")}"
                  </p>
                )}
                {resourceType === 'web' && !content && (
                  <p className="text-xs text-text-tertiary mt-1">
                    Enter a website URL above to auto-fill the resource name from the page title
                  </p>
                )}
                {resourceType === 'web' && titleFetched && name && !nameManuallyEdited && (
                  <p className="text-xs text-success-600 mt-1">
                    ✓ Auto-filled from webpage title. You can edit this name if needed.
                  </p>
                )}
                {resourceType === 'web' && name && nameManuallyEdited && content && (
                  <p className="text-xs text-warning-600 mt-1">
                    ℹ️ Name was manually edited. It will not be auto-updated if you change the URL.
                  </p>
                )}
                {errors.name && (
                  <p className="flex items-center mt-1 text-sm text-error">
                    <AlertCircle size={14} className="mr-1" />
                    {errors.name}
                  </p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Description
                </label>
                <textarea
                  placeholder="Optional description..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  disabled={uploading}
                  rows={3}
                  className="w-full px-3 py-2 border border-secondary-200 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary/20 resize-none disabled:bg-secondary-50 disabled:text-secondary-500"
                />
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Tags
                </label>
                <Input
                  placeholder="tag1, tag2, tag3"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  disabled={uploading}
                />
                <p className="text-xs text-text-tertiary mt-1">Separate tags with commas</p>
              </div>
            </div>

            {/* Right Column - Content Input for Non-File Resources */}
            <div className="space-y-4">
              {resourceType === 'article' && (
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    {currentTypeInfo.contentLabel}
                  </label>
                  <div className={errors.content ? 'lexical-editor-error' : ''}>
                    <LexicalEditor
                      value={content}
                      onChange={(value) => {
                        setContent(value);
                        setErrors(prev => ({ ...prev, content: null, submit: null }));
                      }}
                      placeholder={currentTypeInfo.contentPlaceholder}
                      readOnly={uploading}
                      style={{ minHeight: '200px' }}
                      className={`${errors.content ? 'border-red-500' : ''} ${uploading ? 'opacity-50' : ''}`}
                    />
                  </div>
                  {errors.content && (
                    <p className="flex items-center mt-1 text-sm text-error">
                      <AlertCircle size={14} className="mr-1" />
                      {errors.content}
                    </p>
                  )}
                  <p className="text-xs text-text-tertiary mt-1">{currentTypeInfo.contentNote}</p>
                </div>
              )}

              {resourceType === 'web' && (
                <div className="space-y-4">
                  {/* Website Type Description */}
                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-2">
                      <div className="flex-shrink-0">
                        {websiteType === 'single' && <Globe className="h-4 w-4 text-blue-600 mt-0.5" />}
                        {websiteType === 'recursive' && <Network className="h-4 w-4 text-blue-600 mt-0.5" />}
                        {websiteType === 'sitemap' && <Map className="h-4 w-4 text-blue-600 mt-0.5" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-blue-800 font-medium">
                          {websiteTypes.find(t => t.value === websiteType)?.label} Mode
                        </p>
                        <p className="text-xs text-blue-700 mt-1">
                          {currentWebsiteTypeInfo.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Advanced Options for Recursive */}
                  {websiteType === 'recursive' && (
                    <div className="space-y-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                      <h4 className="text-sm font-medium text-gray-700">Advanced Options</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Max Depth
                          </label>
                          <select 
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:border-primary focus:ring-1 focus:ring-primary/20"
                            defaultValue="2"
                          >
                            <option value="1">1 level</option>
                            <option value="2">2 levels</option>
                            <option value="3">3 levels</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Processing
                          </label>
                          <span className="text-xs text-gray-500">Automatic</span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500">
                        Deeper levels take longer to process and may hit rate limits.
                      </p>
                    </div>
                  )}

                  {/* Examples for each type */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Examples:</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      {websiteType === 'single' && (
                        <>
                          <p>• https://docs.example.com/api-guide</p>
                          <p>• https://blog.example.com/article-title</p>
                          <p>• https://help.example.com/troubleshooting</p>
                        </>
                      )}
                      {websiteType === 'recursive' && (
                        <>
                          <p>• https://docs.example.com/ (will crawl all documentation)</p>
                          <p>• https://help.example.com/ (will crawl all help pages)</p>
                          <p>• https://blog.example.com/category/ (will crawl category)</p>
                        </>
                      )}
                      {websiteType === 'sitemap' && (
                        <>
                          <p>• https://example.com/sitemap.xml</p>
                          <p>• https://example.com/sitemap_index.xml</p>
                          <p>• https://docs.example.com/sitemap.xml</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="p-4 bg-success-50 border border-success-200 rounded-lg">
            <div className="flex items-center">
              <Check size={16} className="mr-2 text-success flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm text-success font-medium">Resource created successfully!</p>
                <p className="text-xs text-success-700 mt-1">
                  Your resource is being processed and will appear in the list shortly.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errors.submit && (
          <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle size={16} className="mr-2 mt-0.5 text-error flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm text-error font-medium">Failed to create resource</p>
                <p className="text-sm text-error-700 mt-1">{errors.submit}</p>
                <p className="text-xs text-error-600 mt-2">
                  Your form data has been preserved. Please fix any issues and try again.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-secondary-200">
          <Button
            variant="outline"
            onClick={() => {
              setName('');
              setContent('');
              setDescription('');
              setTags('');
              setFile(null);
              setWebsiteType('single');
              setErrors({});
              setShowSuccess(false);
              setNameManuallyEdited(false);
              setFetchingTitle(false);
              setTitleFetched(false);
              if (fileInputRef.current) {
                fileInputRef.current.value = '';
              }
              if (onClear) {
                onClear();
              }
            }}
            disabled={uploading}
          >
            Clear
          </Button>
          <Button
            color="blue"
            onClick={handleCreate}
            disabled={uploading}
          >
            {uploading && <Loader className="animate-spin" size={18} />}
            {uploading ? 'Creating...' : `Create ${currentTypeInfo.label}`}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ResourceCreator;
