import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  Globe, 
  Upload, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Download, 
  Eye, 
  Tag,
  Calendar,
  Clock,
  CheckSquare,
  Square,
  AlertCircle,
  CheckCircle,
  Loader,
  RefreshCw,
  Plus,
  Image
} from 'lucide-react';
import { Button, AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel, Popover, PopoverTrigger, PopoverContent, Badge } from 'components/ui';
import FileIcon from 'components/ui/FileIcon';
import { Skeleton } from 'components/ui/skeleton';

const ResourceList = ({ 
  resources, 
  loading, 
  viewMode, 
  selectedResources, 
  onResourceSelect, 
  onResourceAction,
  getStatusColor,
  refreshingResources = new Set(),
  emptyStateMessage = "Get started by adding your first resource. Upload files, create documents, or save web content.",
  emptyStateAction = null,
  emptyStateIcon = Upload,
  emptyStateButtonText = "Add Resource",
  currentTab = 'file' // Add currentTab prop
}) => {
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resourceToDelete, setResourceToDelete] = useState(null);

  const handleAction = (action, resource) => {
    if (action === 'delete') {
      setResourceToDelete(resource);
      setDeleteDialogOpen(true);
    } else if (action === 'view') {
      handleViewResource(resource);
    } else {
      onResourceAction(action, resource);
    }
  };

  // Map backend resource types to frontend URL types
  const getUrlResourceType = (resourceType) => {
    switch (resourceType) {
      case 'article':
        return 'text'; // Backend uses 'article', frontend uses 'text' in URLs
      case 'file':
      case 'web':
        return resourceType; // These are the same
      default:
        return resourceType;
    }
  };

  const handleViewResource = (resource) => {
    // Navigate to view page with resource type
    const urlType = getUrlResourceType(resource.type);
    navigate(`/resources/${urlType}/${resource.id}/view`);
  };

  const handleResourceNameClick = (resource) => {
    // Navigate to view page when clicking on resource name for all resource types
    const urlType = getUrlResourceType(resource.type);
    navigate(`/resources/${urlType}/${resource.id}/view`);
  };

  const handleDeleteConfirm = () => {
    if (resourceToDelete) {
      onResourceAction('delete', resourceToDelete);
    }
    setDeleteDialogOpen(false);
    setResourceToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setResourceToDelete(null);
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'file':
        return FileIcon;
      case 'text':
        return () => <span className="text-text-secondary">📄</span>;
      case 'web':
        return () => <span className="text-text-secondary">🌐</span>;
      default:
        return () => <span className="text-text-secondary">📄</span>;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatRelativeTime = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(dateString);
  };

  const getFileExtension = (filename) => {
    if (!filename) return '';
    return filename.split('.').pop().toLowerCase();
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '-';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Loading skeleton components
  const ResourceListSkeleton = () => (
    <div className="bg-surface rounded-lg border border-border">
      <div className="space-y-1">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 border-b border-border last:border-b-0">
            <Skeleton className="w-4 h-4" />
            <div className="flex items-center space-x-3 flex-1">
              <Skeleton className="w-10 h-10 rounded-lg" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-32" />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Skeleton className="w-16 h-5 rounded-full" />
              <Skeleton className="w-20 h-4" />
              <Skeleton className="w-20 h-4" />
              <Skeleton className="w-8 h-8 rounded-lg" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const ResourceGridSkeleton = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
      {[...Array(8)].map((_, index) => (
        <div key={index} className="bg-surface rounded-lg border border-border p-4">
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <Skeleton className="w-12 h-12 rounded-lg" />
              <Skeleton className="w-6 h-6 rounded" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="w-16 h-5 rounded-full" />
              <Skeleton className="w-16 h-4" />
            </div>
            <div className="flex flex-wrap gap-1">
              <Skeleton className="w-12 h-5 rounded-full" />
              <Skeleton className="w-16 h-5 rounded-full" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading) {
    return viewMode === 'grid' ? <ResourceGridSkeleton /> : <ResourceListSkeleton />;
  }

  // Grid View
  if (viewMode === 'grid') {
    return (
      <div>
        {resources.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {resources.map((resource) => {
              const isSelected = selectedResources.includes(resource.id);
              const TypeIcon = resource.type === 'file' ? getTypeIcon(resource.type) : getTypeIcon(resource.type);

              return (
                <div
                  key={resource.id}
                  data-resource-id={resource.id}
                  data-resource-type={resource.type}
                  className={`
                    bg-surface rounded-lg border transition-all duration-200 group relative
                    ${isSelected ? 'border-primary shadow-sm' : 'border-border hover:border-secondary-300'}
                  `}
                >
                  {/* More Actions Popover */}
                  <div className="absolute top-3 right-3 z-10">
                    <Popover>
                      <PopoverTrigger asChild>
                        <button className="p-2 rounded-lg hover:bg-surface-muted transition-colors opacity-0 group-hover:opacity-100">
                          <MoreVertical size={16} className="text-text-tertiary" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-40 p-1"
                        align="end"
                        sideOffset={5}
                      >
                          <div className="flex flex-col">
                            <button 
                              onClick={() => handleAction('view', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                            >
                              <Eye size={16} className="mr-2" />
                              View
                            </button>
                            <button 
                              onClick={() => handleAction('edit', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                            >
                              <Edit size={16} className="mr-2" />
                              Edit
                            </button>
                            <button 
                              onClick={() => handleAction('refresh', resource)}
                              disabled={refreshingResources.has(resource.id)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left disabled:opacity-50"
                            >
                              <RefreshCw size={16} className={`mr-2 ${refreshingResources.has(resource.id) ? 'animate-spin' : ''}`} />
                              {refreshingResources.has(resource.id) ? 'Refreshing...' : 'Refresh'}
                            </button>
                            {resource.type === 'file' && (
                              <button 
                                onClick={() => handleAction('download', resource)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                              >
                                <Download size={16} className="mr-2" />
                                Download
                              </button>
                            )}
                            <div className="border-t border-border my-1"></div>
                            <button 
                              onClick={() => handleAction('delete', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-red-50 rounded transition-colors text-left text-red-600 hover:text-red-700"
                            >
                              <Trash2 size={16} className="mr-2" />
                              Delete
                            </button>
                          </div>
                        </PopoverContent>
                    </Popover>
                  </div>

                  <div className="p-5">
                    {/* Status Badge at Top */}
                    <div className="flex items-center justify-end mb-3">
                      <Badge status={resource.status} size="sm" />
                    </div>

                    {/* Icon and Content */}
                    <div className="flex items-start gap-3 mb-4">
                      <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-lg flex-shrink-0">
                        {resource.type === 'file' ? (
                          <div className="w-8 h-8 flex items-center justify-center">
                            <FileIcon extension={getFileExtension(resource.filename)} size={24} />
                          </div>
                        ) : (
                          <TypeIcon size={20} className="text-secondary-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 
                          className="font-semibold text-text-primary mb-1 line-clamp-2 text-base cursor-pointer hover:text-primary transition-colors"
                          onClick={() => handleResourceNameClick(resource)}
                        >
                          {resource.name}
                        </h3>
                        {resource.type === 'web' && resource.url && (
                          <p className="text-xs text-primary mb-2 line-clamp-1 break-all">{resource.url}</p>
                        )}
                        {resource.description && (
                          <p className="text-sm text-text-secondary line-clamp-2">{resource.description}</p>
                        )}
                      </div>
                    </div>

                    {/* Tags */}
                    {resource.tags && resource.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {resource.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded"
                          >
                            <Tag size={10} className="mr-1" />
                            {tag}
                          </span>
                        ))}
                        {resource.tags.length > 3 && (
                          <span className="text-xs text-text-tertiary px-2 py-1">+{resource.tags.length - 3}</span>
                        )}
                      </div>
                    )}

                    {/* Meta Info */}
                    <div className="text-xs text-text-tertiary border-t border-secondary-200 pt-3 space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Calendar size={12} className="mr-1" />
                          {formatRelativeTime(resource.created_at)}
                        </div>
                        {resource.size && (
                          <div>{formatFileSize(resource.size)}</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          // Empty state for grid view
          <div className="text-center py-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-secondary-100 rounded-full mb-4">
              {React.createElement(emptyStateIcon, { className: "w-8 h-8 text-secondary-500" })}
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              No resources found
            </h3>
            <p className="text-text-secondary mb-4 max-w-sm mx-auto">
              {emptyStateMessage}
            </p>
            {emptyStateAction && (
              <Button 
                variant="primary"
                size="md"
                onClick={emptyStateAction}
                className="mt-4"
              >
                <Plus size={16} className="mr-2" />
                {emptyStateButtonText}
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  // List View
  return (
    <div className="bg-surface border border-border rounded-lg overflow-hidden">
      <div className="px-6 py-3 bg-surface-muted border-b border-border">
        <div className="grid grid-cols-12 gap-4 text-xs font-medium text-text-secondary uppercase tracking-wide items-center">
          <div className="col-span-1">
            <button
              onClick={() => {
                const allSelected = resources.every(r => selectedResources.includes(r.id));
                resources.forEach(r => onResourceSelect(r.id, !allSelected));
              }}
              className="p-1 rounded hover:bg-surface transition-colors"
            >
              {resources.length > 0 && resources.every(r => selectedResources.includes(r.id)) ? (
                <CheckSquare size={16} className="text-primary" />
              ) : (
                <Square size={16} className="text-text-tertiary" />
              )}
            </button>
          </div>
          <div className="col-span-4">Name</div>
          <div className="col-span-1">{/* Type moved to name column */}</div>
          <div className="col-span-2">Status</div>
          <div className="col-span-2">Modified</div>
          <div className="col-span-1">Size</div>
          <div className="col-span-1">Actions</div>
        </div>
      </div>

      <div className="divide-y divide-border">
        {resources.length > 0 ? (
          resources.map(resource => {
            const isSelected = selectedResources.includes(resource.id);
            const TypeIcon = resource.type === 'file' ? getTypeIcon(resource.type) : getTypeIcon(resource.type);

            return (
              <div
                key={resource.id}
                data-resource-id={resource.id}
                data-resource-type={resource.type}
                className={`
                  px-6 py-4 hover:bg-surface-muted transition-colors
                  ${isSelected ? 'bg-primary-50' : ''}
                `}
              >
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Selection */}
                  <div className="col-span-1">
                    <button
                      onClick={() => onResourceSelect(resource.id, !isSelected)}
                      className="p-1 rounded hover:bg-surface transition-colors"
                    >
                      {isSelected ? (
                        <CheckSquare size={16} className="text-primary" />
                      ) : (
                        <Square size={16} className="text-text-tertiary hover:text-text-secondary" />
                      )}
                    </button>
                  </div>

                  {/* Name */}
                  <div className="col-span-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-surface-muted rounded-lg flex items-center justify-center">
                          {resource.type === 'file' ? (
                            <div className="w-6 h-6 flex items-center justify-center">
                              <FileIcon extension={getFileExtension(resource.filename)} size={18} />
                            </div>
                          ) : (
                            <TypeIcon size={16} className="text-text-secondary" />
                          )}
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p 
                            className="text-sm font-medium text-text-primary truncate cursor-pointer hover:text-primary transition-colors"
                            onClick={() => handleResourceNameClick(resource)}
                          >
                            {resource.name}
                          </p>
                        </div>
                        {resource.type === 'web' && resource.url && (
                          <p className="text-xs text-primary truncate mb-1">{resource.url}</p>
                        )}
                        {resource.description && (
                          <p className="text-xs text-text-secondary truncate">{resource.description}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Type - Hidden since we moved it to name column */}
                  <div className="col-span-1">
                    {/* Empty - type moved to name column */}
                  </div>

                  {/* Status */}
                  <div className="col-span-2">
                    <Badge status={resource.status} size="default" />
                  </div>

                  {/* Modified */}
                  <div className="col-span-2">
                    <div className="text-sm text-text-secondary">
                      {formatRelativeTime(resource.updated_at)}
                    </div>
                  </div>

                  {/* Size */}
                  <div className="col-span-1">
                    <div className="text-sm text-text-secondary">
                      {resource.size ? formatFileSize(resource.size) : '-'}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-1">
                    <Popover>
                      <PopoverTrigger asChild>
                        <button className="p-1 rounded hover:bg-surface-muted transition-colors">
                          <MoreVertical size={16} className="text-text-tertiary" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-40 p-1"
                        align="end"
                        sideOffset={5}
                      >
                          <div className="flex flex-col">
                            <button 
                              onClick={() => handleAction('view', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                            >
                              <Eye size={16} className="mr-2" />
                              View
                            </button>
                            <button 
                              onClick={() => handleAction('edit', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                            >
                              <Edit size={16} className="mr-2" />
                              Edit
                            </button>
                            <button 
                              onClick={() => handleAction('refresh', resource)}
                              disabled={refreshingResources.has(resource.id)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left disabled:opacity-50"
                            >
                              <RefreshCw size={16} className={`mr-2 ${refreshingResources.has(resource.id) ? 'animate-spin' : ''}`} />
                              {refreshingResources.has(resource.id) ? 'Refreshing...' : 'Refresh'}
                            </button>
                            {resource.type === 'file' && (
                              <button 
                                onClick={() => handleAction('download', resource)}
                                className="flex items-center px-3 py-2 text-sm hover:bg-surface-muted rounded transition-colors text-left"
                              >
                                <Download size={16} className="mr-2" />
                                Download
                              </button>
                            )}
                            <div className="border-t border-border my-1"></div>
                            <button 
                              onClick={() => handleAction('delete', resource)}
                              className="flex items-center px-3 py-2 text-sm hover:bg-red-50 rounded transition-colors text-left text-red-600 hover:text-red-700"
                            >
                              <Trash2 size={16} className="mr-2" />
                              Delete
                            </button>
                          </div>
                        </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Tags Row */}
                {resource.tags && resource.tags.length > 0 && (
                  <div className="mt-2 ml-11">
                    <div className="flex flex-wrap gap-1">
                      {resource.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 bg-surface-muted text-text-secondary text-xs rounded"
                        >
                          <Tag size={10} className="mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })
        ) : (
          // Empty state inside table body
          <div className="px-6 py-16 text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-surface-muted rounded-full mb-4">
              {React.createElement(emptyStateIcon, { className: "w-8 h-8 text-text-secondary" })}
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              No resources found
            </h3>
            <p className="text-text-secondary mb-4 max-w-sm mx-auto">
              {emptyStateMessage}
            </p>
            {emptyStateAction && (
              <Button 
                variant="primary"
                size="md"
                onClick={emptyStateAction}
                className="mt-4"
              >
                <Plus size={16} className="mr-2" />
                {emptyStateButtonText}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Resource</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{resourceToDelete?.title || resourceToDelete?.name}"?
              This action cannot be undone and the resource will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete Resource
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ResourceList;
