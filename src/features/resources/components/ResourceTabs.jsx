import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from 'components/ui/Tabs';
import FileManager from './FileManager';
import TextManager from './TextManager';
// import WebsiteManager from './WebsiteManager'; // TODO: Create WebsiteManager component

const ResourceTabs = () => {
  return (
    <Tabs defaultValue="file" className="w-full">
      <TabsList>
        <TabsTrigger value="file">Files</TabsTrigger>
        <TabsTrigger value="text">Text</TabsTrigger>
        <TabsTrigger value="website">Websites</TabsTrigger>
      </TabsList>
      <TabsContent value="file">
        <FileManager />
      </TabsContent>
      <TabsContent value="text">
        <TextManager />
      </TabsContent>
      <TabsContent value="website">
        <div className="p-4 text-center text-gray-500">
          Website Manager - Coming Soon
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default ResourceTabs;
