import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, Eye, RefreshCw } from 'lucide-react';
import { Button, Badge } from 'components/ui';
import { resourceService } from 'services';

const DuplicateFileCleanup = () => {
  const [files, setFiles] = useState([]);
  const [duplicates, setDuplicates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const response = await resourceService.listResources('file', {
        skip: 0,
        limit: 1000, // Get more files to analyze
      });
      const filesData = response.resources || response.results || [];
      setFiles(filesData);
      identifyDuplicates(filesData);
    } catch (err) {
      console.error('Failed to fetch files:', err);
    } finally {
      setLoading(false);
    }
  };

  const identifyDuplicates = (filesData) => {
    const duplicateGroups = {};
    
    // Group files by similar names
    filesData.forEach(file => {
      const fileName = file.filename || file.name || '';
      const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '').toLowerCase().trim();
      
      if (!duplicateGroups[nameWithoutExt]) {
        duplicateGroups[nameWithoutExt] = [];
      }
      duplicateGroups[nameWithoutExt].push(file);
    });

    // Filter to only groups with more than one file
    const duplicatesList = Object.entries(duplicateGroups)
      .filter(([_, files]) => files.length > 1)
      .map(([baseName, files]) => ({
        baseName,
        files: files.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)) // Newest first
      }));

    setDuplicates(duplicatesList);
  };

  const handleDeleteFile = async (fileId) => {
    if (!window.confirm('Are you sure you want to delete this file?')) {
      return;
    }

    try {
      setProcessing(true);
      await resourceService.deleteResource('file', fileId);
      await fetchFiles(); // Refresh the list
    } catch (err) {
      console.error('Failed to delete file:', err);
      alert('Failed to delete file: ' + err.message);
    } finally {
      setProcessing(false);
    }
  };

  const handleKeepLatest = async (duplicateGroup) => {
    if (!window.confirm(`Keep only the latest file and delete ${duplicateGroup.files.length - 1} older versions?`)) {
      return;
    }

    try {
      setProcessing(true);
      // Delete all but the first (latest) file
      const filesToDelete = duplicateGroup.files.slice(1);
      await Promise.all(
        filesToDelete.map(file => resourceService.deleteResource('file', file.id))
      );
      await fetchFiles(); // Refresh the list
    } catch (err) {
      console.error('Failed to clean up duplicates:', err);
      alert('Failed to clean up duplicates: ' + err.message);
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text-primary">Duplicate File Cleanup</h2>
          <p className="text-text-secondary">
            Found {duplicates.length} groups with potential duplicates ({duplicates.reduce((sum, group) => sum + group.files.length, 0)} total files)
          </p>
        </div>
        <Button variant="outline" onClick={fetchFiles} disabled={processing}>
          <RefreshCw className={`w-4 h-4 mr-2 ${processing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {duplicates.length === 0 ? (
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
          <h3 className="text-lg font-semibold text-text-primary mb-2">No Duplicates Found</h3>
          <p className="text-text-secondary">All your files appear to be unique!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {duplicates.map((duplicateGroup, groupIndex) => (
            <div key={groupIndex} className="border border-yellow-200 bg-yellow-50 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-text-primary">
                    {duplicateGroup.baseName}
                  </h3>
                  <p className="text-text-secondary">
                    {duplicateGroup.files.length} files with similar names
                  </p>
                </div>
                <Button 
                  color="orange" 
                  onClick={() => handleKeepLatest(duplicateGroup)}
                  disabled={processing}
                >
                  Keep Latest Only
                </Button>
              </div>

              <div className="space-y-3">
                {duplicateGroup.files.map((file, fileIndex) => (
                  <div 
                    key={file.id} 
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      fileIndex === 0 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-white border-gray-200'
                    }`}
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="font-medium text-text-primary">
                            {file.filename || file.name}
                            {fileIndex === 0 && (
                              <Badge variant="soft" color="green" className="ml-2">
                                Latest
                              </Badge>
                            )}
                          </p>
                          <div className="flex items-center space-x-4 text-sm text-text-secondary">
                            <span>Created: {formatDate(file.created_at)}</span>
                            <span>Size: {formatFileSize(file.size)}</span>
                            {file.status && (
                              <Badge variant="soft" color={file.status === 'processed' ? 'green' : 'orange'}>
                                {file.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(`/resources/file/${file.id}`, '_blank')}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        color="red"
                        onClick={() => handleDeleteFile(file.id)}
                        disabled={processing}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DuplicateFileCleanup;
