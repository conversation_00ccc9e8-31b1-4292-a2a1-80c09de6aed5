import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, Copy, Download, Clock, User, Calendar, ExternalLink, FileText } from 'lucide-react';
import { <PERSON><PERSON>, Badge } from 'components/ui';
import { Separator } from '../../../components/ui/separator';
import resourceService from '../../../services/resourceService';

const ViewResource = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [resource, setResource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchResource = async () => {
      try {
        setLoading(true);
        // First, try to get the resource to determine its type
        // We'll need to make a call to get all resources and find this one, or modify the API
        // For now, let's try different types
        let data = null;
        const types = ['text', 'file', 'web'];
        
        for (const type of types) {
          try {
            data = await resourceService.getResource(type, id);
            break;
          } catch (err) {
            // Continue to next type
            continue;
          }
        }
        
        if (!data) {
          throw new Error('Resource not found');
        }
        
        setResource(data);
      } catch (err) {
        setError(err.message || 'Failed to load resource');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchResource();
    }
  }, [id]);

  const handleEdit = () => {
    // Navigate to edit page (to be implemented)
    console.log('Edit resource:', resource.id);
  };

  const handleDelete = () => {
    // Navigate back to resources and trigger delete
    navigate('/resources', { state: { deleteResource: resource.id } });
  };

  const handleCopyContent = () => {
    if (resource.type === 'text') {
      navigator.clipboard.writeText(resource.content);
    } else if (resource.type === 'web') {
      navigator.clipboard.writeText(resource.url);
    } else if (resource.type === 'file') {
      navigator.clipboard.writeText(resource.file_url || resource.name);
    }
    // Could add a toast notification here
  };

  const handleDownload = () => {
    if (resource.type === 'text') {
      const blob = new Blob([resource.content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resource.title || resource.name}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (resource.type === 'file' && resource.file_url) {
      // For file resources, open the file URL
      window.open(resource.file_url, '_blank');
    } else if (resource.type === 'web' && resource.url) {
      // For web resources, open the URL
      window.open(resource.url, '_blank');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper to get the correct file URL
  const getFileUrl = (resource) => {
    if (!resource) return null;
    if (resource.file_url && (resource.file_url.startsWith('http://') || resource.file_url.startsWith('https://') || resource.file_url.startsWith('/api/file/'))) {
      return resource.file_url;
    }
    if (resource.storage_key) {
      return `/api/file/${resource.storage_key}`;
    }
    return null;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 w-32 bg-surface-muted rounded mb-6"></div>
            <div className="h-12 w-2/3 bg-surface-muted rounded mb-4"></div>
            <div className="h-4 w-1/2 bg-surface-muted rounded mb-8"></div>
            <div className="h-96 bg-surface-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => navigate('/resources')}
            className="mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Resources
          </Button>
          <div className="text-center py-12">
            <div className="text-red-500 text-lg mb-2">Error Loading Resource</div>
            <div className="text-text-secondary">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  if (!resource) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={() => navigate('/resources')}
            className="mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Resources
          </Button>
          <div className="text-center py-12">
            <div className="text-text-secondary">Resource not found</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/resources')}
            className="flex items-center"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Resources
          </Button>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleCopyContent}
              className="flex items-center"
            >
              <Copy size={16} className="mr-2" />
              Copy
            </Button>
            <Button
              variant="outline"
              onClick={handleDownload}
              className="flex items-center"
            >
              <Download size={16} className="mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              onClick={handleEdit}
              className="flex items-center"
            >
              <Edit size={16} className="mr-2" />
              Edit
            </Button>
            <Button
              variant="outline"
              color="red"
              onClick={handleDelete}
              className="flex items-center"
            >
              <Trash2 size={16} className="mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Resource Info */}
        <div className="bg-surface rounded-lg border p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-2xl font-semibold text-text-primary mb-2">
                {resource.title || resource.name}
              </h1>
              {resource.description && (
                <p className="text-text-secondary mb-4">{resource.description}</p>
              )}
            </div>
            <Badge variant="soft" color={resource.status === 'processed' ? 'green' : 'orange'}>
              {resource.status}
            </Badge>
          </div>

          <Separator className="my-4" />

          {/* Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            {resource.created_at && (
              <div className="flex items-center text-text-secondary">
                <Calendar size={16} className="mr-2" />
                <span>Created {formatDate(resource.created_at)}</span>
              </div>
            )}
            {resource.updated_at && (
              <div className="flex items-center text-text-secondary">
                <Clock size={16} className="mr-2" />
                <span>Modified {formatDate(resource.updated_at)}</span>
              </div>
            )}
            {resource.author && (
              <div className="flex items-center text-text-secondary">
                <User size={16} className="mr-2" />
                <span>{resource.author}</span>
              </div>
            )}
          </div>

          {/* Tags */}
          {resource.tags && resource.tags.length > 0 && (
            <div className="mt-4">
              <div className="flex flex-wrap gap-2">
                {resource.tags.map((tag, index) => (
                  <Badge key={index} variant="soft" size="1">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="bg-surface rounded-lg border">
          <div className="border-b border-border px-6 py-4">
            <h2 className="text-lg font-medium text-text-primary">
              {resource.type === 'text' ? 'Content' : 
               resource.type === 'web' ? 'Web Resource' : 
               'File Information'}
            </h2>
            <div className="mt-2">
              <h3 className="text-base font-semibold text-text-primary">
                {resource.name || resource.title}
              </h3>
            </div>
          </div>
          <div className="p-6">
            {resource.type === 'text' && (
              <div className="h-[80vh] overflow-y-auto rounded-lg border p-4 bg-gray-50">
                <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed text-gray-900">
                  {resource.content}
                </div>
              </div>
            )}
            
            {resource.type === 'web' && (
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-surface-muted rounded-lg">
                  <ExternalLink size={20} className="text-primary" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-primary mb-1">URL</p>
                    <a 
                      href={resource.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary hover:underline break-all"
                    >
                      {resource.url}
                    </a>
                  </div>
                </div>
                {resource.content && (
                  <div>
                    <h3 className="text-sm font-medium text-text-primary mb-2">Extracted Content</h3>
                    <div className="h-[80vh] overflow-y-auto rounded-lg border p-4 bg-gray-50">
                      <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed text-gray-900">
                        {resource.content}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {resource.type === 'file' && (
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-4 bg-surface-muted rounded-lg">
                  <FileText size={20} className="text-primary" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text-primary mb-1">File Name</p>
                    <p className="text-text-secondary">{resource.name}</p>
                    {resource.size && (
                      <p className="text-xs text-text-tertiary mt-1">
                        Size: {(resource.size / 1024).toFixed(2)} KB
                      </p>
                    )}
                  </div>
                </div>
                {getFileUrl(resource) && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => window.open(getFileUrl(resource), '_blank')}
                      className="flex items-center"
                    >
                      <ExternalLink size={16} className="mr-2" />
                      Open File
                    </Button>
                  </div>
                )}
                {resource.content && (
                  <div>
                    <h3 className="text-sm font-medium text-text-primary mb-2">Extracted Content</h3>
                    <div className="h-[80vh] overflow-y-auto rounded-lg border p-4 bg-gray-50">
                      <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed text-gray-900">
                        {resource.content}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewResource;
