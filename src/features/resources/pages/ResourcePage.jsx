import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Search, Plus, Upload, Globe, FileText, Trash2, Edit, Eye, Download, AlertCircle, RefreshCw, List, LayoutGrid } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import ResourceCreator from '../components/ResourceCreator';
import ResourceList from '../components/ResourceList';
import { Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Card, CardContent, CardHeader, CardTitle, ToggleGroup, ToggleGroupItem } from 'components/ui';
import StandardPageHeader from 'components/ui/StandardPageHeader';
import { resourceService } from 'services';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Button, Tabs, TabsList, TabsTrigger, TabsContent } from 'components/ui';

const ResourcePage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [resources, setResources] = useState([]);
  const [filteredResources, setFilteredResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreator, setShowCreator] = useState(false);
  const [selectedResources, setSelectedResources] = useState([]);
  const [viewMode, setViewMode] = useState('list'); // grid or list
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [pagination, setPagination] = useState({ skip: 0, limit: 10, total: 0 });
  const [refreshing, setRefreshing] = useState(false);
  const [refreshingResources, setRefreshingResources] = useState(new Set());

  // Get selectedType from URL query parameters, default to 'file'
  const getSelectedTypeFromURL = () => {
    const urlParams = new URLSearchParams(location.search);
    return urlParams.get('tab') || 'file';
  };

  const [selectedType, setSelectedType] = useState(getSelectedTypeFromURL());

  // Update URL when selectedType changes
  const updateURLWithTab = useCallback((newTab) => {
    const urlParams = new URLSearchParams(location.search);
    urlParams.set('tab', newTab);
    navigate(`${location.pathname}?${urlParams.toString()}`, { replace: true });
  }, [location.pathname, location.search, navigate]);

  // Handle tab change
  const handleTabChange = useCallback((newTab) => {
    setSelectedType(newTab);
    updateURLWithTab(newTab);
  }, [updateURLWithTab]);

  // Sync selectedType with URL when location changes (e.g., back button)
  useEffect(() => {
    const urlTab = getSelectedTypeFromURL();
    if (urlTab !== selectedType) {
      setSelectedType(urlTab);
    }
  }, [location.search]);

  // Refs to access current state values in callbacks
  const resourcesRef = useRef(resources);
  const filteredResourcesRef = useRef(filteredResources);
  const selectedResourcesRef = useRef(selectedResources);

  // Update refs when state changes
  useEffect(() => {
    resourcesRef.current = resources;
  }, [resources]);

  useEffect(() => {
    filteredResourcesRef.current = filteredResources;
  }, [filteredResources]);

  useEffect(() => {
    selectedResourcesRef.current = selectedResources;
  }, [selectedResources]);

  // Fetch resources for the selected type only
  const fetchResources = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await resourceService.listResources(selectedType, {
        skip: pagination.skip,
        limit: pagination.limit,
        search: searchQuery,
        sortBy,
        sortOrder
      });
      const resourcesData = response.resources || response.results || [];
      // Ensure each resource has the correct type assigned
      const resourcesWithType = resourcesData.map(resource => ({
        ...resource,
        type: resource.type || selectedType // Use selectedType as fallback if type is missing
      }));
      setResources(resourcesWithType);
      setPagination(prev => ({ ...prev, total: response.total || response.total_returned || 0 }));
    } catch (err) {
      console.error('Failed to fetch resources:', err);
      setError(err.message || 'Failed to load resources');
    } finally {
      setLoading(false);
    }
  }, [selectedType, pagination.skip, pagination.limit, searchQuery, sortBy, sortOrder]);

  // Initial load and refetch on tab/type change
  useEffect(() => {
    fetchResources();
  }, [fetchResources]);

  // Filter and search resources (client-side for better UX)
  useEffect(() => {
    let filtered = resources;
    if (searchQuery && !loading) {
      filtered = filtered.filter(resource =>
        resource.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    setFilteredResources(filtered);
  }, [resources, searchQuery, loading]);

  const handleResourceCreated = useCallback(async (newResourceData) => {
    try {
      setLoading(true);
      const createdResource = await resourceService.createResource(newResourceData);
      
      // Add the new resource to the list
      const resourceWithMeta = {
        ...createdResource,
        id: createdResource.id || Date.now(),
        created_at: createdResource.created_at || new Date().toISOString(),
        updated_at: createdResource.updated_at || new Date().toISOString(),
        status: createdResource.status || 'processing',
        size: createdResource.size || resourceService.formatFileSize(0),
        type: createdResource.type || newResourceData.type, // Ensure type is set
        name: newResourceData.name,
        description: newResourceData.description || '',
        tags: newResourceData.tags || []
      };
      
      setResources(prev => [resourceWithMeta, ...prev]);
      setShowCreator(false);
      
      // Show success message
      console.log('Resource created successfully:', createdResource);
    } catch (err) {
      console.error('Failed to create resource:', err);
      // Re-throw the error so the ResourceCreator can handle it and keep form values
      throw new Error(err.message || 'Failed to create resource');
    } finally {
      setLoading(false);
    }
  }, []);

  const handleResourceSelect = useCallback((resourceId, selected) => {
    setSelectedResources(prev => 
      selected 
        ? [...prev, resourceId]
        : prev.filter(id => id !== resourceId)
    );
  }, []);

  const handleBulkDelete = useCallback(async () => {
    if (!window.confirm(`Delete ${selectedResources.length} selected resources?`)) {
      return;
    }

    try {
      setLoading(true);
      
      // Group resources by type for deletion
      const resourcesByType = {};
      selectedResources.forEach(id => {
        const resource = resources.find(r => r.id === id);
        if (resource) {
          if (!resourcesByType[resource.type]) {
            resourcesByType[resource.type] = [];
          }
          resourcesByType[resource.type].push(id);
        }
      });

      // Delete resources by type
      await Promise.all(
        Object.entries(resourcesByType).map(([type, ids]) =>
          resourceService.deleteResources(type, ids)
        )
      );

      // Remove deleted resources from state
      setResources(prev => prev.filter(resource => !selectedResources.includes(resource.id)));
      setSelectedResources([]);
      
      console.log('Resources deleted successfully');
    } catch (err) {
      console.error('Failed to delete resources:', err);
      setError(err.message || 'Failed to delete resources');
    } finally {
      setLoading(false);
    }
  }, [selectedResources, resources]);

  const handleSortChange = useCallback((newSortBy, newSortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setPagination(prev => ({ ...prev, skip: 0 }));
  }, []);

  const handleRefresh = useCallback(async (resourceId = null) => {
    try {
      setRefreshing(true);
      setError(null);
      let resourcesToRefresh = [];
      
      // Use refs to get current state values
      const currentResources = resourcesRef.current;
      const currentFilteredResources = filteredResourcesRef.current;
      const currentSelectedResources = selectedResourcesRef.current;
      
      // Use filteredResources as the source since that's what's actually displayed
      const sourceResources = currentFilteredResources.length > 0 ? currentFilteredResources : currentResources;
      
      if (resourceId) {
        // Always search in the full resources list for single-resource refresh
        const resource = currentResources.find(r => r.id.toString() === resourceId.toString());
        if (resource && resource.type) {
          resourcesToRefresh = [{ id: resourceId, type: resource.type }];
          setRefreshingResources(prev => new Set(prev).add(resourceId));
        }
      } else if (currentSelectedResources.length > 0) {
        resourcesToRefresh = currentSelectedResources.map(id => {
          const resource = sourceResources.find(r => r.id.toString() === id.toString());
          return { id, type: resource?.type };
        }).filter(r => r.type && r.type !== 'undefined'); // Filter out resources without valid type
        setRefreshingResources(prev => new Set([...prev, ...currentSelectedResources]));
      } else {
        // Use all displayed resources when refreshing without selection
        resourcesToRefresh = sourceResources
          .filter(r => r.type && r.type !== 'undefined') // Filter out resources without valid type
          .map(r => ({ id: r.id, type: r.type }));
        setRefreshingResources(prev => new Set([...prev, ...sourceResources.map(r => r.id)]));
      }
      
      if (resourcesToRefresh.length === 0) {
        toast.info('No resources to refresh');
        return;
      }

      console.log('Resources to refresh:', resourcesToRefresh);
      
      let results = [];
      let successful = [];
      let failed = [];
      if (resourcesToRefresh.length === 1) {
        const { id, type } = resourcesToRefresh[0];
        try {
          const result = await resourceService.reindexSingleResource(type, id);
          results = [{ id, success: true, result }];
          successful = results;
          failed = [];
        } catch (error) {
          results = [{ id, success: false, error: error.message }];
          successful = [];
          failed = results;
        }
      } else {
        try {
          const bulkResult = await resourceService.reindexSelectedResources(resourcesToRefresh);
          results = bulkResult.results.map(r => ({
            id: r.resource_id,
            success: r.status === 'success',
            result: r.result,
            error: r.error
          }));
          successful = results.filter(r => r.success);
          failed = results.filter(r => !r.success);
        } catch (error) {
          const refreshPromises = resourcesToRefresh.map(async ({ id, type }) => {
            try {
              const result = await resourceService.reindexSingleResource(type, id);
              return { id, success: true, result };
            } catch (error) {
              return { id, success: false, error: error.message };
            }
          });
          results = await Promise.all(refreshPromises);
          successful = results.filter(r => r.success);
          failed = results.filter(r => !r.success);
        }
      }
      setResources(prev => prev.map(resource => {
        const result = results.find(r => r.id === resource.id);
        if (result) {
          return {
            ...resource,
            status: result.success ? 'processed' : 'failed',
            updated_at: new Date().toISOString()
          };
        }
        return resource;
      }));
      setRefreshingResources(prev => {
        const newSet = new Set(prev);
        resourcesToRefresh.forEach(r => newSet.delete(r.id));
        return newSet;
      });
      if (failed.length > 0) {
        setError(`${failed.length} resources failed to refresh. Check console for details.`);
        toast.error(`${failed.length} resource${failed.length > 1 ? 's' : ''} failed to refresh.`);
      } else {
        toast.success('All resources refreshed successfully!');
      }
    } catch (err) {
      setError(err.message || 'Failed to refresh resources');
      toast.error(err.message || 'Failed to refresh resources');
      if (resourceId) {
        setRefreshingResources(prev => {
          const newSet = new Set(prev);
          newSet.delete(resourceId);
          return newSet;
        });
      }
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleResourceAction = useCallback(async (action, resource) => {
    try {
      switch (action) {
        case 'view':
          console.log('View resource:', resource);
          // Navigate to resource detail view
          break;
          
        case 'edit':
          console.log('Edit resource:', resource);
          // Open edit modal or navigate to edit page
          break;
          
        case 'refresh':
          console.log('Refresh resource:', resource);
          await handleRefresh(resource.id);
          break;
          
        case 'download':
          if (resource.type === 'file') {
            await resourceService.downloadResource(resource.type, resource.id);
          }
          break;
          
        case 'delete':
          if (window.confirm(`Delete "${resource.name}"?`)) {
            setLoading(true);
            await resourceService.deleteResource(resource.type, resource.id);
            setResources(prev => prev.filter(r => r.id !== resource.id));
            console.log('Resource deleted successfully');
          }
          break;
          
        default:
          console.warn('Unknown action:', action);
      }
    } catch (err) {
      console.error(`Failed to ${action} resource:`, err);
      setError(err.message || `Failed to ${action} resource`);
    } finally {
      setLoading(false);
    }
  }, []);

  const resourceTypes = [
    { value: 'file', label: 'Files', icon: Upload },
    { value: 'article', label: 'Text', icon: FileText },
    { value: 'web', label: 'Websites', icon: Globe }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'processed': return 'text-success bg-success-50';
      case 'processing': return 'text-warning bg-warning-50';
      case 'failed': return 'text-error bg-error-50';
      default: return 'text-secondary bg-secondary-50';
    }
  };

  const getEmptyStateMessage = (type) => {
    switch (type) {
      case 'file':
        return 'Upload your first file to get started. Support for PDFs, images, documents, and more.';
      case 'article':
        return 'Create your first text document or note. Perfect for documentation, notes, and articles.';
      case 'web':
        return 'Save your first website or web page. Capture and organize content from the web.';
      default:
        return 'Get started by adding your first resource. Upload files, create documents, or save web content.';
    }
  };

  const getEmptyStateIcon = (type) => {
    switch (type) {
      case 'file':
        return Upload;
      case 'article':
        return FileText;
      case 'web':
        return Globe;
      default:
        return Upload;
    }
  };

  const getEmptyStateButtonText = (type) => {
    switch (type) {
      case 'file':
        return 'Upload File';
      case 'article':
        return 'Create Document';
      case 'web':
        return 'Save Website';
      default:
        return 'Add Resource';
    }
  };

  const handleEmptyStateAction = () => {
    setShowCreator(true);
  };

  const handleSearch = useCallback((query) => {
    setSearchQuery(query);
    // Reset pagination when searching
    setPagination(prev => ({ ...prev, skip: 0 }));
  }, []);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <StandardPageHeader
        title="Resources"
        description={
          loading ? 'Loading...' : `${filteredResources.length} resources found`
        }
        actions={
          <div className="flex items-center gap-3">
            <ToggleGroup
              type="single"
              value={viewMode}
              onValueChange={setViewMode}
              size="default"
              className={cn("gap-0")}
            >
              <ToggleGroupItem
                value="list"
                variant="default"
                size="default"
                aria-label="List"
                className="gap-2"
              >
                <List className="h-4 w-4" />
                <span className="hidden sm:inline">List</span>
              </ToggleGroupItem>
              <ToggleGroupItem
                value="grid"
                variant="default"
                size="default"
                aria-label="Grid"
                className="gap-2"
              >
                <LayoutGrid className="h-4 w-4" />
                <span className="hidden sm:inline">Grid</span>
              </ToggleGroupItem>
            </ToggleGroup>
            <Button
              color="blue"
              size="2"
              onClick={() => setShowCreator(!showCreator)}
            >
              <Plus size={18} />
              Add Resource
            </Button>
          </div>
        }
      />

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-error-50 border border-error-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-error mr-2" />
            <p className="text-error font-medium">Error</p>
          </div>
          <p className="text-error-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="1"
            onClick={() => {
              setError(null);
              fetchResources();
            }}
          >
            Retry
          </Button>
        </div>
      )}

      {/* Search and Filters Section */}
      <div className="bg-surface border border-border rounded-lg p-4 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={20} />
          <Input
            placeholder="Search resources by name, description, or tags..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 pr-4 py-3 w-full bg-surface border-border focus:border-primary focus:ring-primary/20"
          />
        </div>
        {/* Filter and Sort Controls */}
        <div className="flex flex-col gap-4">
          {/* shadcn/ui Tabs for Resource Types */}
          <Tabs value={selectedType} onValueChange={handleTabChange}>
            <TabsList>
              {resourceTypes.map((type) => {
                const Icon = type.icon;
                const typeCount = resources.filter(r => r.type === type.value).length;
                
                return (
                  <TabsTrigger key={type.value} value={type.value}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Icon size={16} />
                      <span>{type.label}</span>
                      {typeCount > 0 && (
                        <span style={{ 
                          background: 'var(--gray-4)', 
                          borderRadius: '12px', 
                          padding: '2px 8px', 
                          fontSize: '12px',
                          fontWeight: '500'
                        }}>
                          {typeCount}
                        </span>
                      )}
                    </div>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </Tabs>

          {/* Controls Row */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Type Description */}
            <div className="flex items-center gap-3">
              <div className="text-sm text-text-secondary">
                {selectedType === 'file' && 'Upload and manage your documents, images, and other files'}
                {selectedType === 'article' && 'Create and organize text documents and notes'}
                {selectedType === 'web' && 'Save and organize content from websites and web pages'}
              </div>
              {selectedType === 'file' && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="1"
                    onClick={() => navigate('/resources/file')}
                  >
                    <Eye size={16} className="mr-2" />
                    File Manager
                  </Button>
                  <Button
                    variant="outline"
                    size="1"
                    onClick={() => navigate('/resources/cleanup')}
                  >
                    <AlertCircle size={16} className="mr-2" />
                    Clean Duplicates
                  </Button>
                </div>
              )}
            </div>

            {/* Sort Options */}
            <div className="flex items-center gap-2">
              <label className="text-sm text-text-secondary whitespace-nowrap">Sort by:</label>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  handleSortChange(field, order);
                }}
                className="px-3 py-2 bg-surface border border-border rounded-lg text-text-secondary focus:border-primary focus:ring-1 focus:ring-primary/20 text-sm"
              >
                <option value="created_at-desc">Newest First</option>
                <option value="created_at-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="updated_at-desc">Recently Updated</option>
              </select>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedResources.length > 0 && (
          <div className="flex items-center justify-between pt-3 border-t border-border">
            <span className="text-sm text-text-secondary">
              {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="1"
                onClick={() => handleRefresh()}
                disabled={refreshing}
                title="Refresh selected resources"
              >
                <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
                {refreshing ? 'Refreshing...' : 'Refresh Selected'}
              </Button>
              <Button
                color="red"
                size="1"
                onClick={handleBulkDelete}
                disabled={loading}
              >
                <Trash2 size={16} />
                Delete Selected
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Resource Creator */}
      {showCreator && (
        <div className="animate-in slide-in-from-top-2 duration-300">
          <ResourceCreator 
            onResourceCreated={handleResourceCreated}
            onClear={() => setShowCreator(false)}
            selectedType={selectedType}
          />
        </div>
      )}
      {/* Main Content Area */}
      <div className="min-h-[400px]">
        {/* Resource List */}
        {!showCreator && (
          <ResourceList 
            resources={filteredResources}
            loading={loading}
            viewMode={viewMode}
            selectedResources={selectedResources}
            onResourceSelect={handleResourceSelect}
            onResourceAction={handleResourceAction}
            getStatusColor={getStatusColor}
            refreshingResources={refreshingResources}
            emptyStateMessage={getEmptyStateMessage(selectedType)}
            emptyStateAction={handleEmptyStateAction}
            emptyStateIcon={getEmptyStateIcon(selectedType)}
            emptyStateButtonText={getEmptyStateButtonText(selectedType)}
            currentTab={selectedType}
          />
        )}

        {/* List View when creator is shown */}
        {showCreator && (
          <ResourceList 
            resources={filteredResources}
            loading={loading}
            viewMode={viewMode}
            selectedResources={selectedResources}
            onResourceSelect={handleResourceSelect}
            onResourceAction={handleResourceAction}
            getStatusColor={getStatusColor}
            refreshingResources={refreshingResources}
            emptyStateMessage={getEmptyStateMessage(selectedType)}
            emptyStateAction={handleEmptyStateAction}
            emptyStateIcon={getEmptyStateIcon(selectedType)}
            emptyStateButtonText={getEmptyStateButtonText(selectedType)}
            currentTab={selectedType}
          />
        )}
      </div>
    </div>
  );
};

export default ResourcePage;
