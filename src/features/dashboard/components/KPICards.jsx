import React from 'react';
import { Bot, Activity, AlertTriangle, Shield, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { Card, CardContent } from 'components/ui';

const KPICards = ({ metrics, refreshKey }) => {
  const kpiData = [
    {
      title: 'Total Agents',
      value: metrics.totalAgents,
      trend: metrics.trends.totalAgents,
      icon: Bot,
      color: 'primary',
      description: 'All registered agents'
    },
    {
      title: 'Active Agents',
      value: metrics.activeAgents,
      trend: metrics.trends.activeAgents,
      icon: Activity,
      color: 'success',
      description: 'Currently running'
    },
    {
      title: 'Failed Agents',
      value: metrics.failedAgents,
      trend: metrics.trends.failedAgents,
      icon: AlertTriangle,
      color: 'error',
      description: 'Require attention'
    },
    {
      title: 'System Health',
      value: `${metrics.systemHealth}%`,
      trend: metrics.trends.systemHealth,
      icon: Shield,
      color: 'accent',
      description: 'Overall performance'
    }
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      primary: {
        bg: 'bg-primary-50',
        icon: 'text-primary',
        trend: 'text-primary'
      },
      success: {
        bg: 'bg-success-50',
        icon: 'text-success',
        trend: 'text-success'
      },
      error: {
        bg: 'bg-error-50',
        icon: 'text-error',
        trend: 'text-error'
      },
      accent: {
        bg: 'bg-accent-50',
        icon: 'text-accent',
        trend: 'text-accent'
      }
    };
    return colorMap[color];
  };

  const getTrendIcon = (trend) => {
    if (trend > 0) return TrendingUp;
    if (trend < 0) return TrendingDown;
    return Minus;
  };

  const getTrendColor = (trend) => {
    if (trend > 0) return 'text-success';
    if (trend < 0) return 'text-error';
    return 'text-text-muted';
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {kpiData.map((kpi, index) => {
        const colors = getColorClasses(kpi.color);
        return (
          <Card
            key={`${kpi.title}-${refreshKey}`}
            className="hover:shadow-lg transition-shadow duration-200"
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg ${colors.bg}`}>
                  {React.createElement(kpi.icon, {
                    size: 24,
                    className: colors.icon
                  })}
                </div>
                <div className="flex items-center space-x-1">
                  {React.createElement(getTrendIcon(kpi.trend), {
                    size: 16,
                    className: getTrendColor(kpi.trend)
                  })}
                  <span className={`text-sm font-medium ${getTrendColor(kpi.trend)}`}>
                    {Math.abs(kpi.trend)}
                  </span>
                </div>
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-foreground mb-1">
                  {kpi.value}
                </h3>
                <p className="text-sm font-medium text-foreground mb-1">
                  {kpi.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  {kpi.description}
                </p>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default KPICards;