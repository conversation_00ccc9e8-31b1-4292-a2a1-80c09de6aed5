import React, { useState } from 'react';
import { Plus, Search } from 'lucide-react';

const QuickActions = ({ onCreateAgent }) => {
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  const statusOptions = [
    { value: 'all', label: 'All Status', count: 24 },
    { value: 'active', label: 'Active', count: 18 },
    { value: 'inactive', label: 'Inactive', count: 4 },
    { value: 'failed', label: 'Failed', count: 2 }
  ];

  const typeOptions = [
    { value: 'all', label: 'All Types', count: 24 },
    { value: 'chatbot', label: 'Chatbot', count: 12 },
    { value: 'analytics', label: 'Analytics', count: 8 },
    { value: 'automation', label: 'Automation', count: 4 }
  ];

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        {/* Create Agent CTA */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onCreateAgent}
            className="bg-primary text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200 flex items-center space-x-2 shadow-sm"
          >
            <Plus size={20} />
            <span>Create New Agent</span>
          </button>
          
          <div className="hidden sm:block text-sm text-text-secondary">
            Deploy and configure AI agents quickly
          </div>
        </div>

        {/* Quick Filters */}
        <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
          {/* Status Filter */}
          <div className="relative">
            <label className="block text-xs font-medium text-text-secondary mb-1">
              Filter by Status
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full sm:w-40 px-3 py-2 text-sm border border-border rounded-md bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label} ({option.count})
                </option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div className="relative">
            <label className="block text-xs font-medium text-text-secondary mb-1">
              Filter by Type
            </label>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="w-full sm:w-40 px-3 py-2 text-sm border border-border rounded-md bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {typeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label} ({option.count})
                </option>
              ))}
            </select>
          </div>

          {/* Search */}
          <div className="relative">
            <label className="block text-xs font-medium text-text-secondary mb-1">
              Quick Search
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Search agents..."
                className="w-full sm:w-48 pl-10 pr-3 py-2 text-sm border border-border rounded-md bg-surface text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <Search
                size={16}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;