import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { <PERSON>ren<PERSON>U<PERSON>, <PERSON>, Pie<PERSON>hart as Pie<PERSON><PERSON>Icon } from 'lucide-react';

const PerformanceCharts = ({ refreshKey }) => {
  const [activeChart, setActiveChart] = useState('performance');

  // Mock data for performance trends
  const performanceData = [
    { time: '00:00', agents: 18, requests: 1200, success: 95 },
    { time: '04:00', agents: 16, requests: 800, success: 97 },
    { time: '08:00', agents: 20, requests: 2400, success: 94 },
    { time: '12:00', agents: 22, requests: 3200, success: 96 },
    { time: '16:00', agents: 24, requests: 2800, success: 93 },
    { time: '20:00', agents: 21, requests: 1800, success: 98 }
  ];

  // Mock data for resource utilization
  const resourceData = [
    { name: 'CPU', usage: 45, color: '#3B82F6' },
    { name: 'Memory', usage: 62, color: '#10B981' },
    { name: 'Storage', usage: 38, color: '#F59E0B' },
    { name: 'Network', usage: 28, color: '#8B5CF6' }
  ];

  // Mock data for agent types distribution
  const agentTypeData = [
    { name: 'Chatbot', value: 12, color: '#3B82F6' },
    { name: 'Analytics', value: 8, color: '#10B981' },
    { name: 'Automation', value: 4, color: '#F59E0B' }
  ];

  const chartOptions = [
    { id: 'performance', label: 'Performance Trends', icon: TrendingUp },
    { id: 'resources', label: 'Resource Usage', icon: Server },
    { id: 'distribution', label: 'Agent Distribution', icon: PieChartIcon }
  ];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface border border-border rounded-lg p-3 shadow-elevation">
          <p className="text-sm font-medium text-text-primary mb-2">{`Time: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}${entry.dataKey === 'success' ? '%' : ''}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    switch (activeChart) {
      case 'performance':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
              <XAxis 
                dataKey="time" 
                stroke="#64748B"
                fontSize={12}
              />
              <YAxis 
                stroke="#64748B"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="agents" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                name="Active Agents"
              />
              <Line 
                type="monotone" 
                dataKey="success" 
                stroke="#10B981" 
                strokeWidth={2}
                dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                name="Success Rate (%)"
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'resources':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={resourceData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" />
              <XAxis 
                type="number" 
                domain={[0, 100]}
                stroke="#64748B"
                fontSize={12}
              />
              <YAxis 
                type="category" 
                dataKey="name"
                stroke="#64748B"
                fontSize={12}
              />
              <Tooltip 
                formatter={(value) => [`${value}%`, 'Usage']}
                labelStyle={{ color: '#0F172A' }}
                contentStyle={{ 
                  backgroundColor: '#FFFFFF',
                  border: '1px solid #E2E8F0',
                  borderRadius: '8px'
                }}
              />
              <Bar 
                dataKey="usage" 
                fill="#3B82F6"
                radius={[0, 4, 4, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'distribution':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={agentTypeData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                labelLine={false}
              >
                {agentTypeData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value) => [value, 'Agents']}
                labelStyle={{ color: '#0F172A' }}
                contentStyle={{ 
                  backgroundColor: '#FFFFFF',
                  border: '1px solid #E2E8F0',
                  borderRadius: '8px'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-6 border-b border-border">
        <div>
          <h2 className="text-lg font-semibold text-text-primary">Performance Analytics</h2>
          <p className="text-sm text-text-secondary mt-1">
            Real-time insights and system metrics
          </p>
        </div>
        
        {/* Chart Type Selector */}
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          {chartOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => setActiveChart(option.id)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                activeChart === option.id
                  ? 'bg-primary text-white' :'text-text-secondary hover:text-text-primary hover:bg-secondary-100'
              }`}
            >
              <option.icon size={16} />
              <span className="hidden sm:inline">{option.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Chart Content */}
      <div className="p-6">
        <div key={`${activeChart}-${refreshKey}`}>
          {renderChart()}
        </div>
      </div>

      {/* Chart Legend/Summary */}
      <div className="border-t border-border p-4">
        {activeChart === 'performance' && (
          <div className="flex flex-wrap items-center justify-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary rounded-full"></div>
              <span className="text-text-secondary">Active Agents</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-success rounded-full"></div>
              <span className="text-text-secondary">Success Rate</span>
            </div>
          </div>
        )}
        
        {activeChart === 'resources' && (
          <div className="text-center text-sm text-text-secondary">
            System resource utilization over the last 24 hours
          </div>
        )}
        
        {activeChart === 'distribution' && (
          <div className="flex flex-wrap items-center justify-center space-x-6 text-sm">
            {agentTypeData.map((item) => (
              <div key={item.name} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-text-secondary">{item.name} ({item.value})</span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceCharts;