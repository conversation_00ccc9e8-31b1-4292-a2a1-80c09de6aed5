import React from 'react';
import { <PERSON>S<PERSON>re, BarChart3, Zap, Bot, CheckCircle, Pause, XCircle, Circle, ArrowRight } from 'lucide-react';

const RecentActivity = ({ onViewAll, refreshKey }) => {
  // Mock data for recent agent activity
  const recentAgents = [
    {
      id: 1,
      name: "Customer Support Bot",
      status: "active",
      lastUpdated: "2 minutes ago",
      performance: 98,
      type: "chatbot",
      requests: 1247
    },
    {
      id: 2,
      name: "Data Analytics Agent",
      status: "active",
      lastUpdated: "5 minutes ago",
      performance: 94,
      type: "analytics",
      requests: 856
    },
    {
      id: 3,
      name: "Email Automation Bot",
      status: "inactive",
      lastUpdated: "1 hour ago",
      performance: 87,
      type: "automation",
      requests: 432
    },
    {
      id: 4,
      name: "Sales Lead Qualifier",
      status: "active",
      lastUpdated: "15 minutes ago",
      performance: 91,
      type: "chatbot",
      requests: 678
    },
    {
      id: 5,
      name: "Content Moderator",
      status: "failed",
      lastUpdated: "3 hours ago",
      performance: 45,
      type: "automation",
      requests: 123
    },
    {
      id: 6,
      name: "Report Generator",
      status: "active",
      lastUpdated: "30 minutes ago",
      performance: 96,
      type: "analytics",
      requests: 234
    },
    {
      id: 7,
      name: "Inventory Tracker",
      status: "active",
      lastUpdated: "1 hour ago",
      performance: 89,
      type: "automation",
      requests: 567
    },
    {
      id: 8,
      name: "FAQ Assistant",
      status: "active",
      lastUpdated: "45 minutes ago",
      performance: 93,
      type: "chatbot",
      requests: 789
    },
    {
      id: 9,
      name: "Performance Monitor",
      status: "inactive",
      lastUpdated: "2 hours ago",
      performance: 78,
      type: "analytics",
      requests: 345
    },
    {
      id: 10,
      name: "Workflow Optimizer",
      status: "active",
      lastUpdated: "20 minutes ago",
      performance: 92,
      type: "automation",
      requests: 456
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-success bg-success-50';
      case 'inactive':
        return 'text-text-muted bg-secondary-100';
      case 'failed':
        return 'text-error bg-error-50';
      default:
        return 'text-text-muted bg-secondary-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return CheckCircle;
      case 'inactive':
        return Pause;
      case 'failed':
        return XCircle;
      default:
        return Circle;
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'chatbot':
        return MessageSquare;
      case 'analytics':
        return BarChart3;
      case 'automation':
        return Zap;
      default:
        return Bot;
    }
  };

  const getPerformanceColor = (performance) => {
    if (performance >= 90) return 'text-success';
    if (performance >= 70) return 'text-warning';
    return 'text-error';
  };

  return (
    <div className="bg-surface rounded-lg border border-border">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div>
          <h2 className="text-lg font-semibold text-text-primary">Recent Agent Activity</h2>
          <p className="text-sm text-text-secondary mt-1">
            Latest updates from your AI agents
          </p>
        </div>
        <button
          onClick={onViewAll}
          className="text-primary hover:text-primary-700 text-sm font-medium transition-colors duration-200 flex items-center space-x-1"
        >
          <span>View All</span>
          <ArrowRight size={16} />
        </button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-secondary-50">
            <tr>
              <th className="text-left py-3 px-6 text-xs font-medium text-text-secondary uppercase tracking-wider">
                Agent
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-text-secondary uppercase tracking-wider">
                Status
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-text-secondary uppercase tracking-wider">
                Performance
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-text-secondary uppercase tracking-wider">
                Requests
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-text-secondary uppercase tracking-wider">
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {recentAgents.map((agent) => (
              <tr key={`${agent.id}-${refreshKey}`} className="hover:bg-secondary-50 transition-colors duration-200">
                <td className="py-4 px-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary-50 rounded-lg">
                      {React.createElement(getTypeIcon(agent.type), {
                        size: 16,
                        className: "text-primary"
                      })}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        {agent.name}
                      </div>
                      <div className="text-xs text-text-secondary capitalize">
                        {agent.type}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-6">
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                    {React.createElement(getStatusIcon(agent.status), { size: 12 })}
                    <span className="capitalize">{agent.status}</span>
                  </span>
                </td>
                <td className="py-4 px-6">
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${getPerformanceColor(agent.performance)}`}>
                      {agent.performance}%
                    </span>
                    <div className="w-16 bg-secondary-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          agent.performance >= 90 ? 'bg-success' :
                          agent.performance >= 70 ? 'bg-warning' : 'bg-error'
                        }`}
                        style={{ width: `${agent.performance}%` }}
                      />
                    </div>
                  </div>
                </td>
                <td className="py-4 px-6">
                  <span className="text-sm text-text-primary font-medium">
                    {agent.requests.toLocaleString()}
                  </span>
                </td>
                <td className="py-4 px-6">
                  <span className="text-sm text-text-secondary">
                    {agent.lastUpdated}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden divide-y divide-border">
        {recentAgents.map((agent) => (
          <div key={`mobile-${agent.id}-${refreshKey}`} className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary-50 rounded-lg">
                  {React.createElement(getTypeIcon(agent.type), {
                    size: 16,
                    className: "text-primary"
                  })}
                </div>
                <div>
                  <div className="text-sm font-medium text-text-primary">
                    {agent.name}
                  </div>
                  <div className="text-xs text-text-secondary capitalize">
                    {agent.type}
                  </div>
                </div>
              </div>
              <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                {React.createElement(getStatusIcon(agent.status), { size: 12 })}
                <span className="capitalize">{agent.status}</span>
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-text-secondary">Performance:</span>
                <div className="flex items-center space-x-2 mt-1">
                  <span className={`font-medium ${getPerformanceColor(agent.performance)}`}>
                    {agent.performance}%
                  </span>
                  <div className="flex-1 bg-secondary-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        agent.performance >= 90 ? 'bg-success' :
                        agent.performance >= 70 ? 'bg-warning' : 'bg-error'
                      }`}
                      style={{ width: `${agent.performance}%` }}
                    />
                  </div>
                </div>
              </div>
              <div>
                <span className="text-text-secondary">Requests:</span>
                <div className="font-medium text-text-primary mt-1">
                  {agent.requests.toLocaleString()}
                </div>
              </div>
            </div>
            
            <div className="mt-3 text-xs text-text-secondary">
              Last updated: {agent.lastUpdated}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentActivity;