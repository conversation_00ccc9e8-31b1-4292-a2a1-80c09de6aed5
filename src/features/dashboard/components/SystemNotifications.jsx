import React, { useState } from 'react';
import { Bell, Zap, ChevronRight, AlertTriangle, XCircle, CheckCircle, Info, RotateCcw, Download, FileText, Save } from 'lucide-react';

const SystemNotifications = ({ refreshKey }) => {
  const [activeTab, setActiveTab] = useState('notifications');

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'warning',
      title: 'High Memory Usage',
      message: 'Analytics Agent is using 85% memory. Consider optimization.',
      timestamp: '5 minutes ago',
      isRead: false
    },
    {
      id: 2,
      type: 'success',
      title: 'Agent Deployed',
      message: 'Customer Support Bot v2.1 successfully deployed.',
      timestamp: '1 hour ago',
      isRead: true
    },
    {
      id: 3,
      type: 'error',
      title: 'Connection Failed',
      message: 'Content Moderator lost connection to external API.',
      timestamp: '2 hours ago',
      isRead: false
    },
    {
      id: 4,
      type: 'info',
      title: 'Scheduled Maintenance',
      message: 'System maintenance scheduled for tonight at 2:00 AM.',
      timestamp: '3 hours ago',
      isRead: true
    },
    {
      id: 5,
      type: 'success',
      title: 'Performance Improved',
      message: 'Email Automation Bot performance increased by 15%.',
      timestamp: '4 hours ago',
      isRead: true
    }
  ];

  // Mock quick actions data
  const quickActions = [
    {
      id: 1,
      title: 'Restart Failed Agents',
      description: 'Restart 2 failed agents',
      icon: RotateCcw,
      color: 'error',
      action: () => console.log('Restart failed agents')
    },
    {
      id: 2,
      title: 'Update All Agents',
      description: 'Deploy latest updates',
      icon: Download,
      color: 'primary',
      action: () => console.log('Update all agents')
    },
    {
      id: 3,
      title: 'Generate Report',
      description: 'Weekly performance report',
      icon: FileText,
      color: 'accent',
      action: () => console.log('Generate report')
    },
    {
      id: 4,
      title: 'Backup Configuration',
      description: 'Save current settings',
      icon: Save,
      color: 'success',
      action: () => console.log('Backup configuration')
    }
  ];

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'warning':
        return AlertTriangle;
      case 'error':
        return XCircle;
      case 'success':
        return CheckCircle;
      case 'info':
        return Info;
      default:
        return Bell;
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'warning':
        return 'text-warning';
      case 'error':
        return 'text-error';
      case 'success':
        return 'text-success';
      case 'info':
        return 'text-accent';
      default:
        return 'text-text-muted';
    }
  };

  const getActionColor = (color) => {
    const colorMap = {
      primary: 'bg-primary-50 text-primary hover:bg-primary-100',
      success: 'bg-success-50 text-success hover:bg-success-100',
      error: 'bg-error-50 text-error hover:bg-error-100',
      accent: 'bg-accent-50 text-accent hover:bg-accent-100'
    };
    return colorMap[color] || colorMap.primary;
  };

  return (
    <div className="bg-surface rounded-lg border border-border">
      {/* Header with Tabs */}
      <div className="border-b border-border">
        <div className="flex">
          <button
            onClick={() => setActiveTab('notifications')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'notifications'
                ? 'text-primary border-b-2 border-primary bg-primary-50' :'text-text-secondary hover:text-text-primary'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <Bell size={16} />
              <span>Alerts</span>
              <span className="bg-error text-white text-xs px-1.5 py-0.5 rounded-full">
                {notifications.filter(n => !n.isRead).length}
              </span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('actions')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'actions' ?'text-primary border-b-2 border-primary bg-primary-50' :'text-text-secondary hover:text-text-primary'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <Zap size={16} />
              <span>Actions</span>
            </div>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="max-h-96 overflow-y-auto">
        {activeTab === 'notifications' ? (
          <div className="divide-y divide-border">
            {notifications.map((notification) => (
              <div
                key={`${notification.id}-${refreshKey}`}
                className={`p-4 hover:bg-secondary-50 transition-colors duration-200 ${
                  !notification.isRead ? 'bg-primary-50/30' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  {React.createElement(getNotificationIcon(notification.type), {
                    size: 16,
                    className: `mt-0.5 ${getNotificationColor(notification.type)}`
                  })}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-text-primary truncate">
                        {notification.title}
                      </h4>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-primary rounded-full ml-2" />
                      )}
                    </div>
                    <p className="text-xs text-text-secondary mt-1 line-clamp-2">
                      {notification.message}
                    </p>
                    <p className="text-xs text-text-muted mt-2">
                      {notification.timestamp}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 space-y-3">
            {quickActions.map((action) => (
              <button
                key={`${action.id}-${refreshKey}`}
                onClick={action.action}
                className={`w-full p-3 rounded-lg transition-colors duration-200 text-left ${getActionColor(action.color)}`}
              >
                <div className="flex items-center space-x-3">
                  {React.createElement(action.icon, { size: 18 })}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {action.title}
                    </div>
                    <div className="text-xs opacity-80 truncate">
                      {action.description}
                    </div>
                  </div>
                  <ChevronRight size={16} className="opacity-60" />
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="border-t border-border p-3">
        {activeTab === 'notifications' ? (
          <button className="w-full text-center text-sm text-primary hover:text-primary-700 transition-colors duration-200">
            Mark all as read
          </button>
        ) : (
          <button className="w-full text-center text-sm text-primary hover:text-primary-700 transition-colors duration-200">
            View all actions
          </button>
        )}
      </div>
    </div>
  );
};

export default SystemNotifications;