import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Server, HardDrive, Wifi } from 'lucide-react';
import KPICards from '../components/KPICards';
import QuickActions from '../components/QuickActions';
import RecentActivity from '../components/RecentActivity';
import SystemNotifications from '../components/SystemNotifications';
import PerformanceCharts from '../components/PerformanceCharts';
import StandardPageHeader from 'components/ui/StandardPageHeader';

const DashboardOverview = () => {
  const navigate = useNavigate();
  const [refreshKey, setRefreshKey] = useState(0);

  // Mock data for dashboard metrics
  const dashboardMetrics = {
    totalAgents: 24,
    activeAgents: 18,
    failedAgents: 2,
    systemHealth: 94,
    trends: {
      totalAgents: +12,
      activeAgents: +5,
      failedAgents: -1,
      systemHealth: +2
    }
  };

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshKey(prev => prev + 1);
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleCreateAgent = () => {
    navigate('/agents/new');
  };

  const handleViewAllAgents = () => {
    navigate('/agent-listing');
  };

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* KPI Cards */}
        <KPICards metrics={dashboardMetrics} refreshKey={refreshKey} />

        {/* Quick Actions and Filters */}
        <QuickActions onCreateAgent={handleCreateAgent} />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Left Content - Recent Activity */}
          <div className="xl:col-span-3 space-y-6">
            <RecentActivity 
              onViewAll={handleViewAllAgents}
              refreshKey={refreshKey}
            />
            <PerformanceCharts refreshKey={refreshKey} />
          </div>

          {/* Right Sidebar - Notifications and Quick Actions */}
          <div className="xl:col-span-1">
            <SystemNotifications refreshKey={refreshKey} />
          </div>
        </div>

        {/* System Status Footer */}
        <div className="bg-surface rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-success rounded-full"></div>
                <span className="text-sm font-medium text-text-primary">System Operational</span>
              </div>
              <div className="text-sm text-text-secondary">
                Last updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-text-secondary">
              <div className="flex items-center space-x-1">
                <Server size={16} />
                <span>CPU: 45%</span>
              </div>
              <div className="flex items-center space-x-1">
                <HardDrive size={16} />
                <span>Memory: 62%</span>
              </div>
              <div className="flex items-center space-x-1">
                <Wifi size={16} />
                <span>Network: Good</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;