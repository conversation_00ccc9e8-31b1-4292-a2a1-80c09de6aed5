import React, { useState } from 'react';
import { Mail, Smartphone, MessageSquare, Info } from 'lucide-react';

const NotificationPreferences = ({ onDataChange }) => {
  const [emailNotifications, setEmailNotifications] = useState({
    agentStatusUpdates: true,
    systemAlerts: true,
    weeklyReports: false,
    securityNotifications: true,
    productUpdates: false,
    maintenanceSchedule: true
  });

  const [inAppNotifications, setInAppNotifications] = useState({
    agentDeployments: true,
    errorAlerts: true,
    performanceWarnings: true,
    teamUpdates: false,
    systemMaintenance: true
  });

  const [notificationFrequency, setNotificationFrequency] = useState('immediate');
  const [quietHours, setQuietHours] = useState({
    enabled: true,
    startTime: '22:00',
    endTime: '08:00'
  });

  const handleEmailToggle = (key) => {
    setEmailNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
    onDataChange();
  };

  const handleInAppToggle = (key) => {
    setInAppNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
    onDataChange();
  };

  const handleFrequencyChange = (frequency) => {
    setNotificationFrequency(frequency);
    onDataChange();
  };

  const handleQuietHoursToggle = () => {
    setQuietHours(prev => ({
      ...prev,
      enabled: !prev.enabled
    }));
    onDataChange();
  };

  const handleQuietHoursChange = (field, value) => {
    setQuietHours(prev => ({
      ...prev,
      [field]: value
    }));
    onDataChange();
  };

  const ToggleSwitch = ({ enabled, onToggle, label, description }) => (
    <div className="flex items-center justify-between py-3">
      <div className="flex-1">
        <p className="font-medium text-text-primary">{label}</p>
        {description && (
          <p className="text-sm text-text-secondary">{description}</p>
        )}
      </div>
      <button
        onClick={onToggle}
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
          ${enabled ? 'bg-primary' : 'bg-secondary-300'}
        `}
      >
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
            ${enabled ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Email Notifications */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Email Notifications</h3>
        <div className="bg-surface border border-border rounded-lg p-4 space-y-1">
          <ToggleSwitch
            enabled={emailNotifications.agentStatusUpdates}
            onToggle={() => handleEmailToggle('agentStatusUpdates')}
            label="Agent Status Updates"
            description="Get notified when your AI agents change status or encounter issues"
          />
          
          <ToggleSwitch
            enabled={emailNotifications.systemAlerts}
            onToggle={() => handleEmailToggle('systemAlerts')}
            label="System Alerts"
            description="Critical system notifications and emergency alerts"
          />
          
          <ToggleSwitch
            enabled={emailNotifications.weeklyReports}
            onToggle={() => handleEmailToggle('weeklyReports')}
            label="Weekly Reports"
            description="Summary of your agents' performance and usage statistics"
          />
          
          <ToggleSwitch
            enabled={emailNotifications.securityNotifications}
            onToggle={() => handleEmailToggle('securityNotifications')}
            label="Security Notifications"
            description="Login attempts, password changes, and security-related events"
          />
          
          <ToggleSwitch
            enabled={emailNotifications.productUpdates}
            onToggle={() => handleEmailToggle('productUpdates')}
            label="Product Updates"
            description="New features, improvements, and platform announcements"
          />
          
          <ToggleSwitch
            enabled={emailNotifications.maintenanceSchedule}
            onToggle={() => handleEmailToggle('maintenanceSchedule')}
            label="Maintenance Schedule"
            description="Planned maintenance windows and system downtime notifications"
          />
        </div>
      </div>

      {/* In-App Notifications */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">In-App Notifications</h3>
        <div className="bg-surface border border-border rounded-lg p-4 space-y-1">
          <ToggleSwitch
            enabled={inAppNotifications.agentDeployments}
            onToggle={() => handleInAppToggle('agentDeployments')}
            label="Agent Deployments"
            description="Notifications when agents are deployed or updated"
          />
          
          <ToggleSwitch
            enabled={inAppNotifications.errorAlerts}
            onToggle={() => handleInAppToggle('errorAlerts')}
            label="Error Alerts"
            description="Immediate alerts for agent errors and failures"
          />
          
          <ToggleSwitch
            enabled={inAppNotifications.performanceWarnings}
            onToggle={() => handleInAppToggle('performanceWarnings')}
            label="Performance Warnings"
            description="Alerts when agents exceed performance thresholds"
          />
          
          <ToggleSwitch
            enabled={inAppNotifications.teamUpdates}
            onToggle={() => handleInAppToggle('teamUpdates')}
            label="Team Updates"
            description="Notifications about team member activities and changes"
          />
          
          <ToggleSwitch
            enabled={inAppNotifications.systemMaintenance}
            onToggle={() => handleInAppToggle('systemMaintenance')}
            label="System Maintenance"
            description="Notifications about ongoing or scheduled maintenance"
          />
        </div>
      </div>

      {/* Notification Frequency */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Notification Frequency</h3>
        <div className="bg-surface border border-border rounded-lg p-4">
          <p className="text-sm text-text-secondary mb-4">
            Choose how often you want to receive agent status update notifications
          </p>
          
          <div className="space-y-3">
            {[
              { value: 'immediate', label: 'Immediate', description: 'Get notified as soon as events occur' },
              { value: 'hourly', label: 'Hourly Digest', description: 'Receive a summary every hour' },
              { value: 'daily', label: 'Daily Digest', description: 'Get a daily summary at 9:00 AM' },
              { value: 'weekly', label: 'Weekly Digest', description: 'Receive a weekly summary on Mondays' }
            ].map((option) => (
              <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="frequency"
                  value={option.value}
                  checked={notificationFrequency === option.value}
                  onChange={() => handleFrequencyChange(option.value)}
                  className="mt-1 w-4 h-4 text-primary border-border focus:ring-primary"
                />
                <div>
                  <p className="font-medium text-text-primary">{option.label}</p>
                  <p className="text-sm text-text-secondary">{option.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Quiet Hours */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Quiet Hours</h3>
        <div className="bg-surface border border-border rounded-lg p-4">
          <ToggleSwitch
            enabled={quietHours.enabled}
            onToggle={handleQuietHoursToggle}
            label="Enable Quiet Hours"
            description="Pause non-critical notifications during specified hours"
          />
          
          {quietHours.enabled && (
            <div className="mt-4 pt-4 border-t border-border">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Start Time
                  </label>
                  <input
                    type="time"
                    value={quietHours.startTime}
                    onChange={(e) => handleQuietHoursChange('startTime', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    End Time
                  </label>
                  <input
                    type="time"
                    value={quietHours.endTime}
                    onChange={(e) => handleQuietHoursChange('endTime', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>
              
              <p className="text-xs text-text-muted mt-2">
                Critical security alerts will still be delivered during quiet hours
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Notification Channels */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Notification Channels</h3>
        <div className="bg-surface border border-border rounded-lg p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Mail size={20} className="text-primary" />
                </div>
                <div>
                  <p className="font-medium text-text-primary">Email</p>
                  <p className="text-sm text-text-secondary"><EMAIL></p>
                </div>
              </div>
              <span className="bg-success-100 text-success-600 text-xs px-2 py-1 rounded-full">
                Active
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <Smartphone size={20} className="text-secondary-600" />
                </div>
                <div>
                  <p className="font-medium text-text-primary">SMS</p>
                  <p className="text-sm text-text-secondary">+****************</p>
                </div>
              </div>
              <button className="text-primary hover:text-primary-700 text-sm font-medium">
                Configure
              </button>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <MessageSquare size={20} className="text-secondary-600" />
                </div>
                <div>
                  <p className="font-medium text-text-primary">Slack</p>
                  <p className="text-sm text-text-secondary">Not connected</p>
                </div>
              </div>
              <button className="text-primary hover:text-primary-700 text-sm font-medium">
                Connect
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-accent-50 border border-accent-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info size={20} className="text-accent-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-accent-700 mb-1">Notification Best Practices</h4>
            <p className="text-sm text-accent-600">
              We recommend keeping system alerts and security notifications enabled to stay informed about critical events. 
              You can always adjust these settings based on your workflow and preferences.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationPreferences;