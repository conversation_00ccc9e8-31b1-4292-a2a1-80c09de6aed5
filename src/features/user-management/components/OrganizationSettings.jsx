import React, { useState, useEffect } from 'react';
import { Lock, Building, Info } from 'lucide-react';
import Image from 'components/AppImage';
import { organizationService } from '@/services';
import { useAuth } from "@/contexts/AuthContext";
import { toast } from 'sonner';

const OrganizationSettings = ({ onDataChange }) => {
  const { user } = useAuth();
  const [organizationData, setOrganizationData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);

  useEffect(() => {
    // Provide fallback empty organization data immediately
    setOrganizationData({
      name: 'Default Organization',
      description: '',
      logo_url: null,
      website: '',
      industry: '',
      size: '',
      location: ''
    });
    setIsLoading(false);
    
    // Try to load organization settings with timeout protection
    loadOrganizationSettings();
  }, []);

  const loadOrganizationSettings = async () => {
    setIsLoading(true);
    try {
      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), 5000)
      );
      
      const apiPromise = organizationService.getOrganizationSettings();
      
      const result = await Promise.race([apiPromise, timeoutPromise]);
      
      if (result.success) {
        setOrganizationData(result.data);
      } else {
        toast.error(result.error || 'Failed to load organization settings');
        // Provide fallback empty organization data
        setOrganizationData({
          name: '',
          description: '',
          logo_url: null,
          website: '',
          industry: '',
          size: '',
          location: ''
        });
      }
    } catch (error) {
      console.error('Error loading organization settings:', error);
      toast.error('Error loading organization settings - using default data');
      // Provide fallback empty organization data
      setOrganizationData({
        name: '',
        description: '',
        logo_url: null,
        website: '',
        industry: '',
        size: '',
        location: ''
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = async (field, value) => {
    const updatedData = {
      ...organizationData,
      [field]: value
    };
    setOrganizationData(updatedData);

    // Debounce the API call
    if (window.orgUpdateTimeout) {
      clearTimeout(window.orgUpdateTimeout);
    }

    window.orgUpdateTimeout = setTimeout(async () => {
      await saveOrganizationSettings({ [field]: value });
    }, 1000);
  };

  const saveOrganizationSettings = async (updateData) => {
    setIsSaving(true);
    try {
      const result = await organizationService.updateOrganizationSettings(updateData);
      if (result.success) {
        setOrganizationData(result.data);
        onDataChange && onDataChange();
        // Don't show toast for every field change as it's auto-saving
      } else {
        toast.error(result.error || 'Failed to update organization settings');
      }
    } catch (error) {
      toast.error('Error updating organization settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogoUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file size (10MB max for organization logos)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size too large. Maximum 10MB allowed.');
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, GIF, WebP, and SVG are allowed.');
      return;
    }

    setIsUploadingLogo(true);
    try {
      const result = await organizationService.uploadOrganizationLogo(file);
      if (result.success) {
        const updatedOrg = {
          ...organizationData,
          organization_logo_url: result.data.organization_logo_url
        };
        setOrganizationData(updatedOrg);
        toast.success('Organization logo updated successfully');
        onDataChange && onDataChange();
      } else {
        toast.error(result.error || 'Failed to upload organization logo');
      }
    } catch (error) {
      toast.error('Error uploading organization logo');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const handleLogoDelete = async () => {
    setIsUploadingLogo(true);
    try {
      const result = await organizationService.deleteOrganizationLogo();
      if (result.success) {
        const updatedOrg = {
          ...organizationData,
          organization_logo_url: null
        };
        setOrganizationData(updatedOrg);
        toast.success('Organization logo deleted successfully');
        onDataChange && onDataChange();
      } else {
        toast.error(result.error || 'Failed to delete organization logo');
      }
    } catch (error) {
      toast.error('Error deleting organization logo');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  // Check if user has permission to manage organization settings
  const canManageOrganization = user?.role_name === 'Admin';

  if (isLoading) return (
    <div className="flex items-center justify-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );

  if (!organizationData) return <div>Unable to load organization settings</div>;

  if (!canManageOrganization) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Lock size={20} className="text-yellow-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-yellow-700 mb-1">Access Restricted</h4>
            <p className="text-sm text-yellow-600">
              You don't have permission to manage organization settings. Contact your administrator for access.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Organization Logo Section */}
      <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-6">
        <div className="relative">
          <div className="w-24 h-24 rounded-lg overflow-hidden bg-secondary-100 border-4 border-white shadow-lg flex items-center justify-center">
            {organizationData.organization_logo_url ? (
              <Image
                src={organizationData.organization_logo_url}
                alt="Organization logo"
                className="w-full h-full object-contain"
              />
            ) : (
              <Building size={32} className="text-gray-400" />
            )}
          </div>
          {isUploadingLogo && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Organization Logo</h3>
          <p className="text-sm text-text-secondary mb-4">
            Upload your organization's logo. Recommended size: 400x400px, max 10MB.
            Supported formats: JPEG, PNG, GIF, WebP, SVG.
          </p>
          <div className="flex space-x-3">
            <label className="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors duration-200 cursor-pointer">
              Upload Logo
              <input
                type="file"
                accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"
                onChange={handleLogoUpload}
                className="hidden"
                disabled={isUploadingLogo}
              />
            </label>
            {organizationData.organization_logo_url && (
              <button 
                onClick={handleLogoDelete}
                disabled={isUploadingLogo}
                className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors duration-200 disabled:opacity-50"
              >
                Remove
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Basic Organization Information */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Organization Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Organization Name
            </label>
            <input
              type="text"
              value={organizationData.organization_name || ''}
              onChange={(e) => handleInputChange('organization_name', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Your organization name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Industry
            </label>
            <select
              value={organizationData.industry || ''}
              onChange={(e) => handleInputChange('industry', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">Select industry</option>
              <option value="Technology">Technology</option>
              <option value="Healthcare">Healthcare</option>
              <option value="Finance">Finance</option>
              <option value="Education">Education</option>
              <option value="Manufacturing">Manufacturing</option>
              <option value="Retail">Retail</option>
              <option value="Consulting">Consulting</option>
              <option value="Government">Government</option>
              <option value="Non-profit">Non-profit</option>
              <option value="Other">Other</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Company Size
            </label>
            <select
              value={organizationData.company_size || ''}
              onChange={(e) => handleInputChange('company_size', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">Select size</option>
              <option value="1-10">1-10 employees</option>
              <option value="11-50">11-50 employees</option>
              <option value="51-200">51-200 employees</option>
              <option value="201-1000">201-1000 employees</option>
              <option value="1000+">1000+ employees</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Website
            </label>
            <input
              type="url"
              value={organizationData.website || ''}
              onChange={(e) => handleInputChange('website', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="https://www.example.com"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Description
            </label>
            <textarea
              value={organizationData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Brief description of your organization..."
            />
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Primary Contact Email
            </label>
            <input
              type="email"
              value={organizationData.primary_contact_email || ''}
              onChange={(e) => handleInputChange('primary_contact_email', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Primary Contact Phone
            </label>
            <input
              type="tel"
              value={organizationData.primary_contact_phone || ''}
              onChange={(e) => handleInputChange('primary_contact_phone', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="+****************"
            />
          </div>
        </div>
      </div>

      {/* Address Information */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Address</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Street Address
            </label>
            <input
              type="text"
              value={organizationData.address?.street || ''}
              onChange={(e) => handleInputChange('address', { ...organizationData.address, street: e.target.value })}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="123 Main Street"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              City
            </label>
            <input
              type="text"
              value={organizationData.address?.city || ''}
              onChange={(e) => handleInputChange('address', { ...organizationData.address, city: e.target.value })}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="New York"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              State/Province
            </label>
            <input
              type="text"
              value={organizationData.address?.state || ''}
              onChange={(e) => handleInputChange('address', { ...organizationData.address, state: e.target.value })}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="NY"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Country
            </label>
            <input
              type="text"
              value={organizationData.address?.country || ''}
              onChange={(e) => handleInputChange('address', { ...organizationData.address, country: e.target.value })}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="United States"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Postal Code
            </label>
            <input
              type="text"
              value={organizationData.address?.postal_code || ''}
              onChange={(e) => handleInputChange('address', { ...organizationData.address, postal_code: e.target.value })}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="10001"
            />
          </div>
        </div>
      </div>
      
      {/* Auto-save indicator */}
      {isSaving && (
        <div className="flex items-center text-sm text-text-secondary">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
          Saving changes...
        </div>
      )}

      {/* Help Text */}
      <div className="bg-accent-50 border border-accent-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info size={20} className="text-accent-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-accent-700 mb-1">Organization Settings</h4>
            <p className="text-sm text-accent-600">
              These settings apply to your entire organization and are visible to all team members. 
              Changes are automatically saved as you type.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationSettings;
