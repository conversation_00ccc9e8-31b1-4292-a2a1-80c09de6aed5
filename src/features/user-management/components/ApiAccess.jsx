import React, { useState } from 'react';
import { BarChart3, Calendar, Zap, Clock, Plus, Copy, RotateCcw, Trash2, X, Book, Code, MessageCircle, Info } from 'lucide-react';

const ApiAccess = ({ onDataChange }) => {
  const [apiKeys, setApiKeys] = useState([
    {
      id: 1,
      name: "Production API Key",
      key: "ak_live_1234567890abcdef",
      created: "2024-01-15",
      lastUsed: "2024-01-20",
      permissions: ["read", "write", "deploy"],
      status: "active"
    },
    {
      id: 2,
      name: "Development API Key",
      key: "ak_test_abcdef1234567890",
      created: "2024-01-10",
      lastUsed: "2024-01-19",
      permissions: ["read", "write"],
      status: "active"
    },
    {
      id: 3,
      name: "Analytics API Key",
      key: "ak_analytics_fedcba0987654321",
      created: "2024-01-05",
      lastUsed: "Never",
      permissions: ["read"],
      status: "inactive"
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newKeyData, setNewKeyData] = useState({
    name: '',
    permissions: []
  });

  const [usageStats] = useState({
    totalRequests: 15847,
    requestsThisMonth: 3421,
    rateLimitRemaining: 4579,
    rateLimitReset: "2024-01-21T00:00:00Z"
  });

  const [showRegenerateConfirm, setShowRegenerateConfirm] = useState(null);

  const handleCreateKey = () => {
    if (!newKeyData.name || newKeyData.permissions.length === 0) {
      alert('Please provide a name and select at least one permission');
      return;
    }

    const newKey = {
      id: Date.now(),
      name: newKeyData.name,
      key: `ak_${newKeyData.permissions.includes('deploy') ? 'live' : 'test'}_${Math.random().toString(36).substr(2, 16)}`,
      created: new Date().toISOString().split('T')[0],
      lastUsed: "Never",
      permissions: newKeyData.permissions,
      status: "active"
    };

    setApiKeys(prev => [...prev, newKey]);
    setNewKeyData({ name: '', permissions: [] });
    setShowCreateModal(false);
    onDataChange();
  };

  const handleDeleteKey = (keyId) => {
    if (window.confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      setApiKeys(prev => prev.filter(key => key.id !== keyId));
      onDataChange();
    }
  };

  const handleRegenerateKey = (keyId) => {
    setApiKeys(prev => prev.map(key => 
      key.id === keyId 
        ? { ...key, key: `ak_${key.permissions.includes('deploy') ? 'live' : 'test'}_${Math.random().toString(36).substr(2, 16)}` }
        : key
    ));
    setShowRegenerateConfirm(null);
    onDataChange();
  };

  const handleToggleKeyStatus = (keyId) => {
    setApiKeys(prev => prev.map(key => 
      key.id === keyId 
        ? { ...key, status: key.status === 'active' ? 'inactive' : 'active' }
        : key
    ));
    onDataChange();
  };

  const handlePermissionToggle = (permission) => {
    setNewKeyData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // Show temporary notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-20 right-4 bg-success text-white px-4 py-2 rounded-md shadow-lg z-50';
    notification.textContent = 'API key copied to clipboard!';
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 2000);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPermissionBadgeColor = (permission) => {
    const colors = {
      read: 'bg-accent-100 text-accent-600',
      write: 'bg-warning-100 text-warning-600',
      deploy: 'bg-error-100 text-error-600'
    };
    return colors[permission] || 'bg-secondary-100 text-secondary-600';
  };

  return (
    <div className="space-y-8">
      {/* Usage Statistics */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">API Usage Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-surface border border-border rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 size={20} className="text-primary" />
              <span className="text-sm font-medium text-text-secondary">Total Requests</span>
            </div>
            <p className="text-2xl font-bold text-text-primary">{usageStats.totalRequests.toLocaleString()}</p>
          </div>
          
          <div className="bg-surface border border-border rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar size={20} className="text-accent" />
              <span className="text-sm font-medium text-text-secondary">This Month</span>
            </div>
            <p className="text-2xl font-bold text-text-primary">{usageStats.requestsThisMonth.toLocaleString()}</p>
          </div>
          
          <div className="bg-surface border border-border rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Zap size={20} className="text-warning" />
              <span className="text-sm font-medium text-text-secondary">Rate Limit</span>
            </div>
            <p className="text-2xl font-bold text-text-primary">{usageStats.rateLimitRemaining.toLocaleString()}</p>
            <p className="text-xs text-text-muted">remaining</p>
          </div>
          
          <div className="bg-surface border border-border rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock size={20} className="text-success" />
              <span className="text-sm font-medium text-text-secondary">Reset Time</span>
            </div>
            <p className="text-sm font-bold text-text-primary">
              {new Date(usageStats.rateLimitReset).toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </p>
            <p className="text-xs text-text-muted">UTC</p>
          </div>
        </div>
      </div>

      {/* API Keys Management */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">API Keys</h3>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-primary text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <Plus size={18} />
            <span>Create New Key</span>
          </button>
        </div>

        <div className="space-y-4">
          {apiKeys.map((apiKey) => (
            <div key={apiKey.id} className="bg-surface border border-border rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-text-primary">{apiKey.name}</h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      apiKey.status === 'active' ?'bg-success-100 text-success-600' :'bg-secondary-100 text-secondary-600'
                    }`}>
                      {apiKey.status}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2 mb-3">
                    <code className="bg-secondary-100 px-2 py-1 rounded text-sm font-mono text-text-primary">
                      {apiKey.key}
                    </code>
                    <button
                      onClick={() => copyToClipboard(apiKey.key)}
                      className="text-primary hover:text-primary-700 transition-colors duration-200"
                      title="Copy to clipboard"
                    >
                      <Copy size={16} />
                    </button>
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-2 mb-3">
                    {apiKey.permissions.map((permission) => (
                      <span
                        key={permission}
                        className={`px-2 py-1 text-xs rounded-full ${getPermissionBadgeColor(permission)}`}
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                  
                  <div className="text-sm text-text-secondary">
                    <span>Created: {formatDate(apiKey.created)}</span>
                    <span className="mx-2">•</span>
                    <span>Last used: {apiKey.lastUsed === 'Never' ? 'Never' : formatDate(apiKey.lastUsed)}</span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => handleToggleKeyStatus(apiKey.id)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
                      apiKey.status === 'active' ?'bg-warning-100 text-warning-600 hover:bg-warning-200' :'bg-success-100 text-success-600 hover:bg-success-200'
                    }`}
                  >
                    {apiKey.status === 'active' ? 'Disable' : 'Enable'}
                  </button>
                  
                  <button
                    onClick={() => setShowRegenerateConfirm(apiKey.id)}
                    className="text-text-secondary hover:text-text-primary transition-colors duration-200"
                    title="Regenerate key"
                  >
                    <RotateCcw size={18} />
                  </button>
                  
                  <button
                    onClick={() => handleDeleteKey(apiKey.id)}
                    className="text-error-600 hover:text-error-700 transition-colors duration-200"
                    title="Delete key"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
              
              {/* Regenerate Confirmation */}
              {showRegenerateConfirm === apiKey.id && (
                <div className="mt-4 pt-4 border-t border-border">
                  <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                    <p className="text-sm text-warning-700 mb-3">
                      Are you sure you want to regenerate this API key? The current key will stop working immediately.
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleRegenerateKey(apiKey.id)}
                        className="bg-warning text-white px-3 py-1 text-sm rounded-md hover:bg-warning-600 transition-colors duration-200"
                      >
                        Regenerate
                      </button>
                      <button
                        onClick={() => setShowRegenerateConfirm(null)}
                        className="bg-secondary-100 text-text-primary px-3 py-1 text-sm rounded-md hover:bg-secondary-200 transition-colors duration-200"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Create New API Key Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-surface rounded-lg shadow-elevation max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-text-primary">Create New API Key</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-text-secondary hover:text-text-primary transition-colors duration-200"
                >
                  <X size={20} />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Key Name
                  </label>
                  <input
                    type="text"
                    value={newKeyData.name}
                    onChange={(e) => setNewKeyData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="e.g., Production API Key"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Permissions
                  </label>
                  <div className="space-y-2">
                    {[
                      { value: 'read', label: 'Read', description: 'View agents and configurations' },
                      { value: 'write', label: 'Write', description: 'Create and modify agents' },
                      { value: 'deploy', label: 'Deploy', description: 'Deploy and manage agent instances' }
                    ].map((permission) => (
                      <label key={permission.value} className="flex items-start space-x-3 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={newKeyData.permissions.includes(permission.value)}
                          onChange={() => handlePermissionToggle(permission.value)}
                          className="mt-1 w-4 h-4 text-primary border-border focus:ring-primary"
                        />
                        <div>
                          <p className="font-medium text-text-primary">{permission.label}</p>
                          <p className="text-sm text-text-secondary">{permission.description}</p>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={handleCreateKey}
                  className="flex-1 bg-primary text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200"
                >
                  Create Key
                </button>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-secondary-100 text-text-primary px-4 py-2 rounded-md font-medium hover:bg-secondary-200 transition-colors duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* API Documentation */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">API Documentation</h3>
        <div className="bg-surface border border-border rounded-lg p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Book size={20} className="text-primary" />
              <div>
                <p className="font-medium text-text-primary">API Reference</p>
                <p className="text-sm text-text-secondary">Complete documentation for all API endpoints</p>
              </div>
              <button className="ml-auto text-primary hover:text-primary-700 text-sm font-medium">
                View Docs
              </button>
            </div>
            
            <div className="flex items-center space-x-3">
              <Code size={20} className="text-accent" />
              <div>
                <p className="font-medium text-text-primary">Code Examples</p>
                <p className="text-sm text-text-secondary">Sample code in multiple programming languages</p>
              </div>
              <button className="ml-auto text-primary hover:text-primary-700 text-sm font-medium">
                View Examples
              </button>
            </div>
            
            <div className="flex items-center space-x-3">
              <MessageCircle size={20} className="text-success" />
              <div>
                <p className="font-medium text-text-primary">API Support</p>
                <p className="text-sm text-text-secondary">Get help with API integration and troubleshooting</p>
              </div>
              <button className="ml-auto text-primary hover:text-primary-700 text-sm font-medium">
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-accent-50 border border-accent-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info size={20} className="text-accent-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-accent-700 mb-1">API Security Best Practices</h4>
            <p className="text-sm text-accent-600">
              Keep your API keys secure and never share them publicly. Use different keys for different environments 
              and rotate them regularly. Monitor your API usage to detect any unusual activity.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiAccess;