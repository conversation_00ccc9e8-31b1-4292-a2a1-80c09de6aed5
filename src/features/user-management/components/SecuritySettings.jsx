import React, { useState } from 'react';
import { Smartphone, Monitor, AlertTriangle, Loader2 } from 'lucide-react';

const SecuritySettings = ({ onDataChange }) => {
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteConfirmStep, setDeleteConfirmStep] = useState(0);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  const activeSessions = [
    {
      id: 1,
      device: "MacBook Pro",
      browser: "Chrome 119.0",
      location: "San Francisco, CA",
      lastActive: "2 minutes ago",
      current: true,
      ip: "*************"
    },
    {
      id: 2,
      device: "iPhone 15 Pro",
      browser: "Safari Mobile",
      location: "San Francisco, CA",
      lastActive: "1 hour ago",
      current: false,
      ip: "*************"
    },
    {
      id: 3,
      device: "Windows Desktop",
      browser: "Edge 119.0",
      location: "New York, NY",
      lastActive: "3 days ago",
      current: false,
      ip: "************"
    }
  ];

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
    onDataChange();
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }
    // Simulate password change
    alert('Password updated successfully');
    setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
  };

  const handleTwoFactorToggle = () => {
    setTwoFactorEnabled(!twoFactorEnabled);
    onDataChange();
  };

  const handleLogoutSession = (sessionId) => {
    if (window.confirm('Are you sure you want to log out this session?')) {
      // Simulate session logout
      alert('Session logged out successfully');
    }
  };

  const handleDeleteAccount = () => {
    if (deleteConfirmStep === 0) {
      setDeleteConfirmStep(1);
    } else if (deleteConfirmStep === 1) {
      if (deleteConfirmText === 'DELETE MY ACCOUNT') {
        setDeleteConfirmStep(2);
        // Simulate account deletion
        setTimeout(() => {
          alert('Account deletion initiated. You will receive a confirmation email.');
          setShowDeleteConfirm(false);
          setDeleteConfirmStep(0);
          setDeleteConfirmText('');
        }, 1000);
      } else {
        alert('Please type "DELETE MY ACCOUNT" exactly as shown');
      }
    }
  };

  return (
    <div className="space-y-8">
      {/* Password Change */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Change Password</h3>
        <form onSubmit={handlePasswordSubmit} className="space-y-4 max-w-md">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Current Password
            </label>
            <input
              type="password"
              value={passwordData.currentPassword}
              onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              New Password
            </label>
            <input
              type="password"
              value={passwordData.newPassword}
              onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Confirm New Password
            </label>
            <input
              type="password"
              value={passwordData.confirmPassword}
              onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              required
            />
          </div>
          
          <button
            type="submit"
            className="bg-primary text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors duration-200"
          >
            Update Password
          </button>
        </form>
      </div>

      {/* Two-Factor Authentication */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Two-Factor Authentication</h3>
        <div className="bg-surface border border-border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${twoFactorEnabled ? 'bg-success' : 'bg-secondary-300'}`}></div>
              <div>
                <p className="font-medium text-text-primary">
                  Two-Factor Authentication {twoFactorEnabled ? 'Enabled' : 'Disabled'}
                </p>
                <p className="text-sm text-text-secondary">
                  {twoFactorEnabled 
                    ? 'Your account is protected with 2FA using authenticator app' :'Add an extra layer of security to your account'
                  }
                </p>
              </div>
            </div>
            <button
              onClick={handleTwoFactorToggle}
              className={`px-4 py-2 rounded-md font-medium transition-colors duration-200 ${
                twoFactorEnabled
                  ? 'bg-error-100 text-error-600 hover:bg-error-200' :'bg-primary text-white hover:bg-primary-700'
              }`}
            >
              {twoFactorEnabled ? 'Disable' : 'Enable'}
            </button>
          </div>
          
          {twoFactorEnabled && (
            <div className="mt-4 pt-4 border-t border-border">
              <p className="text-sm text-text-secondary mb-2">Backup codes available: 8 remaining</p>
              <button className="text-primary hover:text-primary-700 text-sm font-medium">
                View backup codes
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Active Sessions */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Active Sessions</h3>
        <div className="space-y-3">
          {activeSessions.map((session) => (
            <div key={session.id} className="bg-surface border border-border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center">
                    {session.device.includes('iPhone') ? (
                      <Smartphone size={20} className="text-secondary-600" />
                    ) : (
                      <Monitor size={20} className="text-secondary-600" />
                    )}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-text-primary">{session.device}</p>
                      {session.current && (
                        <span className="bg-success-100 text-success-600 text-xs px-2 py-1 rounded-full">
                          Current
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-text-secondary">
                      {session.browser} • {session.location}
                    </p>
                    <p className="text-xs text-text-muted">
                      Last active: {session.lastActive} • IP: {session.ip}
                    </p>
                  </div>
                </div>
                
                {!session.current && (
                  <button
                    onClick={() => handleLogoutSession(session.id)}
                    className="text-error-600 hover:text-error-700 text-sm font-medium"
                  >
                    Log out
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Account Deletion */}
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">Danger Zone</h3>
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle size={20} className="text-error-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-error-700 mb-1">Delete Account</h4>
              <p className="text-sm text-error-600 mb-4">
                Once you delete your account, there is no going back. This action cannot be undone.
                All your data, including AI agents and configurations, will be permanently deleted.
              </p>
              
              {!showDeleteConfirm ? (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="bg-error text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-error-700 transition-colors duration-200"
                >
                  Delete Account
                </button>
              ) : (
                <div className="space-y-4">
                  {deleteConfirmStep === 0 && (
                    <div>
                      <p className="text-sm text-error-700 mb-3">
                        Are you absolutely sure? This action cannot be undone.
                      </p>
                      <div className="flex space-x-3">
                        <button
                          onClick={handleDeleteAccount}
                          className="bg-error text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-error-700 transition-colors duration-200"
                        >
                          Yes, Delete Account
                        </button>
                        <button
                          onClick={() => setShowDeleteConfirm(false)}
                          className="bg-secondary-100 text-text-primary px-4 py-2 rounded-md text-sm font-medium hover:bg-secondary-200 transition-colors duration-200"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {deleteConfirmStep === 1 && (
                    <div>
                      <p className="text-sm text-error-700 mb-3">
                        Type <strong>DELETE MY ACCOUNT</strong> to confirm:
                      </p>
                      <input
                        type="text"
                        value={deleteConfirmText}
                        onChange={(e) => setDeleteConfirmText(e.target.value)}
                        className="w-full px-3 py-2 border border-error-300 rounded-md focus:outline-none focus:ring-2 focus:ring-error focus:border-transparent mb-3"
                        placeholder="DELETE MY ACCOUNT"
                      />
                      <div className="flex space-x-3">
                        <button
                          onClick={handleDeleteAccount}
                          className="bg-error text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-error-700 transition-colors duration-200"
                        >
                          Confirm Deletion
                        </button>
                        <button
                          onClick={() => {
                            setShowDeleteConfirm(false);
                            setDeleteConfirmStep(0);
                            setDeleteConfirmText('');
                          }}
                          className="bg-secondary-100 text-text-primary px-4 py-2 rounded-md text-sm font-medium hover:bg-secondary-200 transition-colors duration-200"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {deleteConfirmStep === 2 && (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin">
                        <Loader2 size={16} className="text-error-600" />
                      </div>
                      <span className="text-sm text-error-700">Processing account deletion...</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;