import React, { useState, useEffect, useRef } from 'react';
import { Save } from 'lucide-react';
import Image from 'components/AppImage';
import { useAuth } from "@/contexts/AuthContext";
import { userService } from '@/services';
import { toast } from 'sonner';
import { Avatar, AvatarImage, AvatarFallback } from 'components/ui/avatar';

const ProfileInformation = () => {
  const { user, updateUser } = useAuth();
  const [originalData, setOriginalData] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [isEditingPicture, setIsEditingPicture] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [hasLoadedProfile, setHasLoadedProfile] = useState(false);
  const fileInputRef = useRef(null);

  // Initialize with user data from auth context
  useEffect(() => {
    if (user && !profileData) {
      console.log('ProfileInformation - Using auth context user data');
      setProfileData(JSON.parse(JSON.stringify(user)));
      setOriginalData(JSON.parse(JSON.stringify(user)));
      // Load fresh data from API in background only once
      if (!hasLoadedProfile) {
        loadUserProfileInBackground();
      }
    }
  }, [user?.id]); // Only depend on user ID to prevent infinite loops

  const loadUserProfileInBackground = async () => {
    if (hasLoadedProfile) return; // Prevent multiple concurrent loads
    
    setHasLoadedProfile(true);
    try {
      const result = await userService.getUserProfile();
      
      if (result.success) {
        setProfileData(result.data);
        setOriginalData(result.data);
        // Only update user context if the data is significantly different
        const isDifferent = !user || user.id !== result.data.id || 
                           user.email !== result.data.email ||
                           user.first_name !== result.data.first_name ||
                           user.last_name !== result.data.last_name;
        
        if (isDifferent) {
          updateUser(result.data); 
        }
        console.log('Profile data updated in background');
      }
    } catch (error) {
      console.error('Background profile load failed:', error);
      setHasLoadedProfile(false); // Allow retry on error
      // Don't show error toast for background loading
    }
  };

  const handleInputChange = (field, value) => {
    const updatedData = {
      ...profileData,
      [field]: value
    };
    setProfileData(updatedData);
    
    // Check if there are unsaved changes
    const hasChanges = checkForChanges(updatedData);
    setHasUnsavedChanges(hasChanges);
  };

  const checkForChanges = (currentData) => {
    if (!originalData) return false;
    
    const fieldsToCompare = ['first_name', 'last_name', 'phone', 'job_title', 'department', 'location', 'timezone', 'bio'];
    
    return fieldsToCompare.some(field => {
      const original = originalData[field] || '';
      const current = currentData[field] || '';
      return original !== current;
    });
  };

  const handleSave = async () => {
    if (!hasUnsavedChanges) return;
    
    setIsSaving(true);
    try {
      // Create update object with only changed fields
      const updateData = {};
      const fieldsToCompare = ['first_name', 'last_name', 'phone', 'job_title', 'department', 'location', 'timezone', 'bio'];
      
      fieldsToCompare.forEach(field => {
        const original = originalData[field] || '';
        const current = profileData[field] || '';
        if (original !== current) {
          updateData[field] = current;
        }
      });

      const result = await userService.updateUserProfile(updateData);
      if (result.success) {
        setProfileData(result.data);
        setOriginalData(result.data);
        setHasUnsavedChanges(false);
        updateUser(result.data);
        toast.success('Profile updated successfully');
      } else {
        toast.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      toast.error('Error updating profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setProfileData(originalData);
    setHasUnsavedChanges(false);
  };

  const handlePictureUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    console.log('Profile image upload started:', file.name);

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size too large. Maximum 5MB allowed.');
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
      return;
    }

    setIsEditingPicture(true);
    try {
      const result = await userService.uploadProfileImage(file);
      if (result.success) {
        const updatedProfile = {
          ...profileData,
          profile_image_url: result.data.profile_image_url
        };
        setProfileData(updatedProfile);
        setOriginalData(updatedProfile);
        updateUser(updatedProfile);
        toast.success('Profile picture updated successfully');
        console.log('Profile image upload completed successfully');
      } else {
        toast.error(result.error || 'Failed to upload profile picture');
        console.error('Profile image upload failed:', result.error);
      }
    } catch (error) {
      toast.error('Error uploading profile picture');
      console.error('Profile image upload error:', error);
    } finally {
      setIsEditingPicture(false);
      // Reset the file input to prevent duplicate uploads
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handlePictureDelete = async () => {
    setIsEditingPicture(true);
    try {
      const result = await userService.deleteProfileImage();
      if (result.success) {
        const updatedProfile = {
          ...profileData,
          profile_image_url: null
        };
        setProfileData(updatedProfile);
        setOriginalData(updatedProfile);
        updateUser(updatedProfile);
        toast.success('Profile picture deleted successfully');
      } else {
        toast.error(result.error || 'Failed to delete profile picture');
      }
    } catch (error) {
      toast.error('Error deleting profile picture');
    } finally {
      setIsEditingPicture(false);
    }
  };

  // Show loading only if we don't have any user data
  if (!user && !profileData) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Show loading while saving
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Picture Section */}
      <div className="bg-surface rounded-lg p-6 border border-border">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Profile Picture</h3>
        <div className="flex items-center space-x-6">
        <div className="relative">
            <Avatar className="w-24 h-24 border-2 border-border">
              <AvatarImage 
                src={profileData?.profile_image_url} 
                alt="Profile"
              />
              <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/30 text-primary text-2xl font-medium">
                {profileData?.first_name && profileData?.last_name
                  ? `${profileData.first_name[0]}${profileData.last_name[0]}`.toUpperCase()
                  : profileData?.email
                  ? profileData.email[0].toUpperCase()
                  : 'U'}
              </AvatarFallback>
            </Avatar>
            {isEditingPicture && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full">
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex space-x-3">
              <label className="bg-primary text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-primary-700 transition-colors duration-200">
              <input
                type="file"
                  accept="image/*"
                onChange={handlePictureUpload}
                className="hidden"
                disabled={isEditingPicture}
                ref={fileInputRef}
              />
                {isEditingPicture ? 'Uploading...' : 'Upload Picture'}
            </label>
              
              {profileData?.profile_image_url && (
              <button 
                onClick={handlePictureDelete}
                disabled={isEditingPicture}
                  className="bg-error text-white px-4 py-2 rounded-lg hover:bg-error-700 transition-colors duration-200 disabled:opacity-50"
              >
                  {isEditingPicture ? 'Deleting...' : 'Delete'}
              </button>
            )}
            </div>
            <p className="text-sm text-text-secondary mt-2">
              JPG, PNG, GIF or WebP. Max 5MB.
            </p>
          </div>
        </div>
      </div>

      {/* Profile Information Form */}
      <div className="bg-surface rounded-lg p-6 border border-border">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Personal Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* First Name */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              First Name *
            </label>
            <input
              type="text"
              value={profileData?.first_name || ''}
              onChange={(e) => handleInputChange('first_name', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="Enter your first name"
            />
          </div>
          
          {/* Last Name */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Last Name *
            </label>
            <input
              type="text"
              value={profileData?.last_name || ''}
              onChange={(e) => handleInputChange('last_name', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="Enter your last name"
            />
          </div>
          
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={profileData?.email || ''}
              disabled
              className="w-full px-3 py-2 border border-border rounded-lg bg-secondary-50 text-text-secondary cursor-not-allowed"
              placeholder="<EMAIL>"
            />
            <p className="text-xs text-text-secondary mt-1">
              Email address cannot be changed
            </p>
          </div>
          
          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={profileData?.phone || ''}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="+****************"
            />
      </div>

          {/* Job Title */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Job Title
            </label>
            <input
              type="text"
              value={profileData?.job_title || ''}
              onChange={(e) => handleInputChange('job_title', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="e.g., Software Engineer"
            />
          </div>
          
          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Department
            </label>
            <input
              type="text"
              value={profileData?.department || ''}
              onChange={(e) => handleInputChange('department', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="e.g., Engineering"
            />
          </div>
          
          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Location
            </label>
            <input
              type="text"
              value={profileData?.location || ''}
              onChange={(e) => handleInputChange('location', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
              placeholder="e.g., San Francisco, CA"
            />
          </div>
          
          {/* Timezone */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Timezone
            </label>
            <select
              value={profileData?.timezone || ''}
              onChange={(e) => handleInputChange('timezone', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary"
            >
              <option value="">Select timezone</option>
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
              <option value="Europe/London">London (GMT)</option>
              <option value="Europe/Paris">Paris (CET)</option>
              <option value="Asia/Tokyo">Tokyo (JST)</option>
              <option value="Asia/Shanghai">Shanghai (CST)</option>
            </select>
          </div>
          </div>
          
        {/* Bio */}
        <div className="mt-6">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Bio
            </label>
            <textarea
            value={profileData?.bio || ''}
              onChange={(e) => handleInputChange('bio', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text-primary resize-vertical"
              placeholder="Tell us about yourself..."
            />
      </div>
      
        {/* Action Buttons */}
      {hasUnsavedChanges && (
          <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-border">
            <button 
              onClick={handleCancel}
              className="bg-secondary-100 text-text-primary px-6 py-2 rounded-lg hover:bg-secondary-200 transition-colors duration-200"
            >
              Cancel
            </button>
            <button 
              onClick={handleSave}
              disabled={isSaving}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 flex items-center space-x-2"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save size={16} />
                  <span>Save Changes</span>
                </>
              )}
            </button>
          </div>
        )}
        </div>
    </div>
  );
};

export default ProfileInformation;