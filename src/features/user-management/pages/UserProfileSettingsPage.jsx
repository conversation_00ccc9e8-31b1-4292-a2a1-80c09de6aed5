import React, { useState } from 'react';
import { Save } from 'lucide-react';
import { useAuth } from "@/contexts/AuthContext";

import ProfileInformation from '../components/ProfileInformation';
import OrganizationSettings from '../components/OrganizationSettings';
import SecuritySettings from '../components/SecuritySettings';
import NotificationPreferences from '../components/NotificationPreferences';
import ApiAccess from '../components/ApiAccess';

const UserProfileSettings = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Check if user can manage organization settings
  const canManageOrganization = user?.role_name === 'Admin';

  const tabs = [
    { id: 'profile', label: 'Profile Information', icon: 'User' },
    ...(canManageOrganization ? [{ id: 'organization', label: 'Organization Settings', icon: 'Building' }] : []),
    { id: 'security', label: 'Security Settings', icon: 'Shield' },
    { id: 'notifications', label: 'Notification Preferences', icon: 'Bell' },
    { id: 'api', label: 'API Access', icon: 'Key' }
  ];

  const handleTabChange = (tabId) => {
    // Profile tab manages its own save/cancel state, so don't check for global unsaved changes
    if (hasUnsavedChanges && activeTab !== 'profile') {
      if (window.confirm('You have unsaved changes. Are you sure you want to switch tabs?')) {
        setActiveTab(tabId);
        setHasUnsavedChanges(false);
      }
    } else {
      setActiveTab(tabId);
      // Reset global unsaved changes when switching from profile tab
      if (activeTab === 'profile') {
        setHasUnsavedChanges(false);
      }
    }
  };

  const handleSaveChanges = () => {
    // Simulate save operation
    setTimeout(() => {
      setHasUnsavedChanges(false);
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-20 right-4 bg-success text-white px-4 py-2 rounded-md shadow-lg z-50';
      notification.textContent = 'Changes saved successfully!';
      document.body.appendChild(notification);
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 3000);
    }, 500);
  };

  const handleCancelChanges = () => {
    setHasUnsavedChanges(false);
    // Reset form data to original state
    window.location.reload();
  };

  const renderTabContent = () => {
    try {
      switch (activeTab) {
        case 'profile':
          return <ProfileInformation />;
        case 'organization':
          return <OrganizationSettings onDataChange={() => setHasUnsavedChanges(true)} />;
        case 'security':
          return <SecuritySettings onDataChange={() => setHasUnsavedChanges(true)} />;
        case 'notifications':
          return <NotificationPreferences onDataChange={() => setHasUnsavedChanges(true)} />;
        case 'api':
          return <ApiAccess onDataChange={() => setHasUnsavedChanges(true)} />;
        default:
          return <ProfileInformation />;
      }
    } catch (error) {
      console.error('Error rendering tab content:', error);
      return (
        <div className="text-center py-8">
          <p className="text-error">Error loading settings content. Please refresh the page.</p>
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text-primary mb-2">Profile & Settings</h1>
          <p className="text-text-secondary">Manage your account settings and preferences</p>
        </div>

        {/* Tab Navigation */}
        <div className="bg-surface rounded-lg shadow-subtle border border-border mb-6">
          <div className="border-b border-border">
            <nav className="flex space-x-8 px-6" aria-label="Settings tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200
                    ${activeTab === tab.id
                      ? 'border-primary text-primary' :'border-transparent text-text-secondary hover:text-text-primary hover:border-secondary-300'
                    }
                  `}
                  aria-current={activeTab === tab.id ? 'page' : undefined}
                >
                  <Icon name={tab.icon} size={18} />
                  <span className="hidden sm:block">{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>

        {/* Floating Action Buttons - Only show for non-profile tabs */}
        {hasUnsavedChanges && activeTab !== 'profile' && (
          <div className="fixed bottom-6 right-6 flex space-x-3 z-50">
            <button
              onClick={handleCancelChanges}
              className="bg-secondary-100 text-text-primary px-6 py-3 rounded-lg font-medium hover:bg-secondary-200 transition-colors duration-200 shadow-elevation"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveChanges}
              className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-200 shadow-elevation flex items-center space-x-2"
            >
              <Save size={18} />
              <span>Save Changes</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserProfileSettings;