import React, { useState, useEffect } from 'react';
import { Mail, AlertCircle, Lock, EyeOff, Eye, LogIn, Info } from 'lucide-react';
import { Input, Button } from 'components/ui';

const LoginForm = ({ onSubmit, isLoading, onForgotPassword }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });

  // Auto-fill password in development environment
  useEffect(() => {
    const isDevelopment = import.meta.env.VITE_APP_ENV === 'development';
    if (isDevelopment) {
      setFormData(prev => ({
        ...prev,
        password: 'TestPassword123!'
      }));
    }
  }, []);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [touched, setTouched] = useState({});

  const validateField = (name, value) => {
    switch (name) {
      case 'email':
        if (!value) return 'Email is required';
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Please enter a valid email address';
        return '';
      case 'password':
        if (!value) return 'Password is required';
        if (value.length < 6) return 'Password must be at least 6 characters';
        return '';
      default:
        return '';
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleBlur = (e) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });
    
    setErrors(newErrors);
    setTouched({ email: true, password: true });
    
    // Submit if no errors
    if (Object.keys(newErrors).length === 0) {
      onSubmit(formData);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email Address
          </label>
          <div className="relative">
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="username email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`pl-10 ${errors.email && touched.email ? 'border-destructive focus:ring-destructive' : ''}`}
              placeholder="Enter your email"
              disabled={isLoading}
            />
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Mail size={16} className="text-muted-foreground" />
            </div>
          </div>
          {errors.email && touched.email && (
            <p className="text-sm text-destructive flex items-center">
              <AlertCircle size={14} className="mr-1" />
              {errors.email}
            </p>
          )}
        </div>

      {/* Password Field */}
      <div className="space-y-2">
        <label htmlFor="current-password" className="text-sm font-medium">
          Password
        </label>
        <div className="relative">
          <Input
            id="current-password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            value={formData.password}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`pl-10 pr-10 ${errors.password && touched.password ? 'border-destructive focus:ring-destructive' : ''}`}
            placeholder="Enter your password"
            disabled={isLoading}
          />
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <Lock size={16} className="text-muted-foreground" />
          </div>
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
            disabled={isLoading}
          >
            {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
          </button>
        </div>
        {errors.password && touched.password && (
          <p className="text-sm text-destructive flex items-center">
            <AlertCircle size={14} className="mr-1" />
            {errors.password}
          </p>
        )}

        {/* Development Mode Notice */}
        {import.meta.env.VITE_APP_ENV === 'development' && formData.password === 'TestPassword123!' && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-2 mt-2">
            <p className="text-xs text-blue-700 flex items-center">
              <Info size={12} className="mr-1" />
              Development mode: Password auto-filled with test credentials
            </p>
          </div>
        )}
      </div>

      {/* Remember Me & Forgot Password */}
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            className="h-4 w-4 text-primary focus:ring-primary border-input rounded"
            disabled={isLoading}
          />
          <span className="text-sm text-muted-foreground">Remember me</span>
        </label>
        
        <Button
          type="button"
          variant="link"
          onClick={onForgotPassword}
          className="h-auto p-0 text-sm text-primary hover:text-primary/80"
          disabled={isLoading}
        >
          Forgot password?
        </Button>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isLoading || Object.keys(errors).some(key => errors[key])}
        className="w-full"
        size="lg"
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Signing in...
          </>
        ) : (
          <>
            <LogIn size={16} className="mr-2" />
            Sign In
          </>
        )}
      </Button>

      {/* Loading Indicator - Below Submit Button */}
      {isLoading && (
        <div className="mt-4 flex items-center justify-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            <p className="text-sm text-blue-700 font-medium">Signing you in, please wait...</p>
          </div>
        </div>
      )}
    </form>
    </div>
  );
};

export default LoginForm;