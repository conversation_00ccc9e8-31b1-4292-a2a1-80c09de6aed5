import React, { useState, useEffect } from 'react';
import { CheckCircle, Mail, AlertCircle, Lock, EyeOff, Eye, UserPlus } from 'lucide-react';
import PropTypes from 'prop-types';

const RegistrationForm = ({ onSubmit, isLoading = false }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [passwordChecks, setPasswordChecks] = useState({
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
    length: false
  });

  useEffect(() => {
    const password = formData.password;
    setPasswordChecks({
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      length: password.length >= 8
    });
  }, [formData.password]);

  const validateField = (name, value) => {
    switch (name) {
      case 'email':
        if (!value) return 'Email is required';
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return 'Please enter a valid email address';
        return '';
      case 'password':
        if (!value) return 'Password is required';
        if (!passwordChecks.uppercase) return 'Password must contain at least one uppercase letter';
        if (!passwordChecks.lowercase) return 'Password must contain at least one lowercase letter';
        if (!passwordChecks.number) return 'Password must contain at least one number';
        if (!passwordChecks.special) return 'Password must contain at least one special character';
        if (!passwordChecks.length) return 'Password must be at least 8 characters';
        return '';
      default:
        return '';
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleBlur = (e) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });
    
    setErrors(newErrors);
    setTouched({ email: true, password: true });
    
    if (Object.keys(newErrors).length === 0) {
      try {
        const response = await onSubmit(formData);
        if (response?.success) {
          setIsSuccess(true);
        }
      } catch (error) {
        // Error will be handled by parent component
        setIsSuccess(false);
      }
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const getFieldClasses = (fieldName) => {
    const baseClasses = "w-full pl-10 pr-4 py-3 border rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent";
    
    if (errors[fieldName] && touched[fieldName]) {
      return `${baseClasses} border-error text-error-700 bg-error-50 focus:ring-error`;
    }
    
    if (touched[fieldName] && !errors[fieldName] && formData[fieldName]) {
      return `${baseClasses} border-success text-text-primary bg-success-50 focus:ring-success`;
    }
    
    return `${baseClasses} border-border text-text-primary bg-surface focus:ring-primary`;
  };

  const getPasswordStrengthClasses = (check) => {
    return `flex items-center text-sm ${
      check ? 'text-success' : 'text-text-muted'
    }`;
  };

  if (isSuccess) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle size={32} className="text-success" />
        </div>
        <h3 className="text-xl font-semibold text-text-primary mb-2">Account Created Successfully!</h3>
        <p className="text-text-secondary mb-6">
          Welcome to AI Control. Redirecting you to login...
        </p>
        <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-2">
            Email Address
          </label>
          <div className="relative">
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="username"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={getFieldClasses('email')}
              placeholder="Enter your email"
              disabled={isLoading}
            />
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Mail size={16} className="text-text-muted" />
            </div>
            {touched.email && (
              <div className="absolute inset-y-0 right-3 flex items-center">
                {errors.email ? (
                  <AlertCircle size={16} className="text-error" />
                ) : formData.email ? (
                  <CheckCircle size={16} className="text-success" />
                ) : null}
              </div>
            )}
          </div>
          {errors.email && touched.email && (
            <p className="mt-1 text-sm text-error flex items-center">
              <AlertCircle size={14} className="mr-1" />
              {errors.email}
            </p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-text-primary mb-2">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              onBlur={handleBlur}
              className={getFieldClasses('password')}
              placeholder="Enter your password"
              disabled={isLoading}
            />
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Lock size={16} className="text-text-muted" />
            </div>
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute inset-y-0 right-3 flex items-center text-text-muted hover:text-text-primary transition-colors duration-200"
              disabled={isLoading}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && touched.password && (
            <p className="mt-1 text-sm text-error flex items-center">
              <AlertCircle size={14} className="mr-1" />
              {errors.password}
            </p>
          )}

          {/* Password Requirements */}
          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className={getPasswordStrengthClasses(passwordChecks.uppercase)}>
              <Icon
                name={passwordChecks.uppercase ? 'CheckCircle' : 'Circle'}
                size={16}
                className="mr-2"
              />
              Uppercase letter
            </div>
            <div className={getPasswordStrengthClasses(passwordChecks.lowercase)}>
              <Icon
                name={passwordChecks.lowercase ? 'CheckCircle' : 'Circle'}
                size={16}
                className="mr-2"
              />
              Lowercase letter
            </div>
            <div className={getPasswordStrengthClasses(passwordChecks.number)}>
              <Icon
                name={passwordChecks.number ? 'CheckCircle' : 'Circle'}
                size={16}
                className="mr-2"
              />
              Number
            </div>
            <div className={getPasswordStrengthClasses(passwordChecks.special)}>
              <Icon
                name={passwordChecks.special ? 'CheckCircle' : 'Circle'}
                size={16}
                className="mr-2"
              />
              Special character
            </div>
            <div className={getPasswordStrengthClasses(passwordChecks.length)}>
              <Icon
                name={passwordChecks.length ? 'CheckCircle' : 'Circle'}
                size={16}
                className="mr-2"
              />
              8+ characters
            </div>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isLoading || Object.keys(errors).some(key => errors[key] !== '')}
        className="w-full bg-primary text-white py-3 px-4 rounded-md font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>Creating account...</span>
          </>
        ) : (
          <>
            <UserPlus size={16} />
            <span>Create Account</span>
          </>
        )}
      </button>
    </form>
  );
};

RegistrationForm.propTypes = {
  onSubmit: PropTypes.func.isRequired,
  isLoading: PropTypes.bool
};

export default RegistrationForm;
