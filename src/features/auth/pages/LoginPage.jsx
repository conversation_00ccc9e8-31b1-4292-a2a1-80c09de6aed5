import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle, X, CheckCircle, Info } from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import LoginForm from '../components/LoginForm';
import SocialLogin from '../components/SocialLogin';
import { Spinner, Alert, AlertDescription, Card, CardContent } from '../../../components/ui';

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated, isLoading: authLoading, error: authError, clearError } = useAuth();
  
  const [isLoading, setIsLoading] = useState(false);
  const [localError, setLocalError] = useState('');

  // Get the intended destination (where user was trying to go)
  const from = location.state?.from?.pathname || '/dashboard';

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, authLoading, navigate, from]);

  // Handle auth context errors
  useEffect(() => {
    if (authError) {
      setLocalError(authError);
    }
  }, [authError]);

  const handleLogin = async (credentials) => {
    setIsLoading(true);
    setLocalError('');
    clearError();

    try {
      const result = await login(credentials.email, credentials.password);

      if (result.success) {
        // Navigation will be handled by useEffect above
        // Keep loading state until navigation completes
      } else {
        setLocalError(result.error || 'Login failed. Please try again.');
        setIsLoading(false); // Clear loading on failure
      }
    } catch (err) {
      console.error('Login error:', err);
      setLocalError(err.message || 'An unexpected error occurred during login.');
      setIsLoading(false); // Always clear loading on error
    }
  };

  const handleCloseError = () => {
    setLocalError('');
    clearError();
  };



  // Remove early return for authLoading. We'll show a loading indicator above the form instead.

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 relative">
        {/* Loading Overlay or Spinner above form */}
        {(authLoading || isLoading) && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-white bg-opacity-70 z-10 rounded-lg">
            <Spinner size="large" text={authLoading ? 'Checking authentication...' : 'Signing you in...'} />
          </div>
        )}

        {/* Header */}
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <button
              onClick={() => navigate('/auth/register')}
              className="font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline transition ease-in-out duration-150"
            >
              create a new account
            </button>
          </p>
        </div>

        {/* Error Alert */}
        {localError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{localError}</span>
              <button
                onClick={handleCloseError}
                className="ml-2 text-red-600 hover:text-red-800 focus:outline-none"
              >
                <X className="h-4 w-4" />
              </button>
            </AlertDescription>
          </Alert>
        )}

        {/* Login Form */}
        <Card>
          <CardContent className="p-6">
            <LoginForm 
              onSubmit={handleLogin} 
              isLoading={isLoading || authLoading}
              disabled={isLoading || authLoading}
            />
            {/* Divider */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>
            </div>

            {/* Social Login */}
            <div className="mt-6">
              <SocialLogin 
                onSuccess={(result) => {
                  // Handle social login success
                  navigate(from, { replace: true });
                }}
                onError={(error) => {
                  setLocalError(error);
                }}
                disabled={isLoading || authLoading}
              />
            </div>
          </CardContent>
        </Card>

        {/* Footer Links */}
        <div className="text-center">
          <button
            onClick={() => navigate('/auth/forgot-password')}
            className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
          >
            Forgot your password?
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
