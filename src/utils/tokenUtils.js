/**
 * Token utility functions for JWT handling
 * Provides secure token validation, parsing, and expiry checking
 */

/**
 * Check if a string is a valid JWT format
 * @param {string} token - Token to validate
 * @returns {boolean} True if token has valid JWT format
 */
export const isValidJWTFormat = (token) => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  return parts.length === 3;
};

/**
 * Decode JWT payload without verification
 * Note: This is for client-side inspection only. Server-side verification is required for security.
 * @param {string} token - JWT token
 * @returns {Object|null} Decoded payload or null if invalid
 */
export const decodeJWTPayload = (token) => {
  if (!isValidJWTFormat(token)) {
    return null;
  }
  
  try {
    const parts = token.split('.');
    const payload = parts[1];
    
    // Decode base64url
    const decodedPayload = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decodedPayload);
  } catch (error) {
    console.error('Error decoding JWT payload:', error);
    return null;
  }
};

/**
 * Check if JWT token is expired
 * @param {string} token - JWT token
 * @returns {boolean} True if token is expired
 */
export const isTokenExpired = (token) => {
  const payload = decodeJWTPayload(token);
  
  if (!payload || !payload.exp) {
    return true;
  }
  
  // Convert exp (seconds) to milliseconds and compare with current time
  const expirationTime = payload.exp * 1000;
  const currentTime = Date.now();
  
  // Add a 30-second buffer to account for clock skew
  const bufferTime = 30 * 1000;
  
  return currentTime >= (expirationTime - bufferTime);
};

/**
 * Get token expiration date
 * @param {string} token - JWT token
 * @returns {Date|null} Expiration date or null if invalid
 */
export const getTokenExpiration = (token) => {
  const payload = decodeJWTPayload(token);
  
  if (!payload || !payload.exp) {
    return null;
  }
  
  return new Date(payload.exp * 1000);
};

/**
 * Get time until token expires in milliseconds
 * @param {string} token - JWT token
 * @returns {number} Milliseconds until expiration, or 0 if expired/invalid
 */
export const getTimeUntilExpiration = (token) => {
  const payload = decodeJWTPayload(token);
  
  if (!payload || !payload.exp) {
    return 0;
  }
  
  const expirationTime = payload.exp * 1000;
  const currentTime = Date.now();
  
  return Math.max(0, expirationTime - currentTime);
};

/**
 * Extract user information from JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} User information or null if invalid
 */
export const getUserFromToken = (token) => {
  const payload = decodeJWTPayload(token);
  
  if (!payload) {
    return null;
  }
  
  // Extract common user fields from JWT payload
  return {
    id: payload.sub || payload.user_id,
    email: payload.email,
    tenant_id: payload.tenant_id,
    role_name: payload.role_name,
    permissions: payload.permissions || [],
    // Include any other user-specific fields
    first_name: payload.first_name,
    last_name: payload.last_name,
    is_active: payload.is_active,
    is_email_verified: payload.is_email_verified
  };
};

/**
 * Check if token needs refresh (expires within specified time)
 * @param {string} token - JWT token
 * @param {number} thresholdMinutes - Minutes before expiration to trigger refresh
 * @returns {boolean} True if token needs refresh
 */
export const shouldRefreshToken = (token, thresholdMinutes = 5) => {
  if (!token || isTokenExpired(token)) {
    return true;
  }
  
  const timeUntilExpiration = getTimeUntilExpiration(token);
  const thresholdMs = thresholdMinutes * 60 * 1000;
  
  return timeUntilExpiration <= thresholdMs;
};

/**
 * Validate token structure and basic claims
 * @param {string} token - JWT token
 * @returns {Object} Validation result with isValid flag and details
 */
export const validateTokenStructure = (token) => {
  const result = {
    isValid: false,
    errors: []
  };
  
  if (!token) {
    result.errors.push('Token is required');
    return result;
  }
  
  if (!isValidJWTFormat(token)) {
    result.errors.push('Invalid JWT format');
    return result;
  }
  
  const payload = decodeJWTPayload(token);
  if (!payload) {
    result.errors.push('Invalid JWT payload');
    return result;
  }
  
  // Check required claims
  if (!payload.sub && !payload.user_id) {
    result.errors.push('Missing user identifier');
  }
  
  if (!payload.exp) {
    result.errors.push('Missing expiration time');
  }
  
  if (!payload.email) {
    result.errors.push('Missing email claim');
  }
  
  if (isTokenExpired(token)) {
    result.errors.push('Token is expired');
  }
  
  result.isValid = result.errors.length === 0;
  return result;
};

/**
 * Create a refresh schedule for token renewal
 * @param {string} token - JWT token
 * @param {Function} refreshCallback - Function to call when refresh is needed
 * @param {number} checkIntervalMinutes - How often to check (in minutes)
 * @returns {Function} Cleanup function to clear the interval
 */
export const createTokenRefreshSchedule = (token, refreshCallback, checkIntervalMinutes = 1) => {
  const checkInterval = setInterval(() => {
    if (shouldRefreshToken(token)) {
      refreshCallback();
    }
  }, checkIntervalMinutes * 60 * 1000);
  
  return () => clearInterval(checkInterval);
};
