/**
 * Cookie utility functions for secure token storage
 * Following modern web security best practices
 */

const COOKIE_OPTIONS = {
  // Secure cookies (HTTPS only in production)
  secure: import.meta.env.PROD,
  // HttpOnly equivalent (can't be accessed via JS for additional security)
  // Note: Since we need J<PERSON> access, we'll use sameSite and secure instead
  sameSite: import.meta.env.PROD ? 'strict' : 'lax',
  // Path for cookie
  path: '/',
  // Max age in seconds (7 days)
  maxAge: 7 * 24 * 60 * 60,
};

/**
 * Set a cookie with secure options
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {Object} options - Additional cookie options
 */
export const setCookie = (name, value, options = {}) => {
  const cookieOptions = { ...COOKIE_OPTIONS, ...options };
  
  let cookieString = `${name}=${encodeURIComponent(value)}`;
  
  if (cookieOptions.maxAge) {
    cookieString += `; max-age=${cookieOptions.maxAge}`;
  }
  
  if (cookieOptions.expires) {
    cookieString += `; expires=${cookieOptions.expires.toUTCString()}`;
  }
  
  if (cookieOptions.path) {
    cookieString += `; path=${cookieOptions.path}`;
  }
  
  if (cookieOptions.domain) {
    cookieString += `; domain=${cookieOptions.domain}`;
  }
  
  if (cookieOptions.secure) {
    cookieString += '; secure';
  }
  
  if (cookieOptions.sameSite) {
    cookieString += `; samesite=${cookieOptions.sameSite}`;
  }
  
  document.cookie = cookieString;
};

/**
 * Get a cookie value by name
 * @param {string} name - Cookie name
 * @returns {string|null} Cookie value or null if not found
 */
export const getCookie = (name) => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  
  if (parts.length === 2) {
    const cookieValue = parts.pop().split(';').shift();
    return decodeURIComponent(cookieValue);
  }
  
  return null;
};

/**
 * Remove a cookie by setting it to expire in the past
 * @param {string} name - Cookie name
 * @param {Object} options - Cookie options (should match the original cookie options)
 */
export const removeCookie = (name, options = {}) => {
  const cookieOptions = { ...COOKIE_OPTIONS, ...options };
  
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  
  if (cookieOptions.path) {
    cookieString += `; path=${cookieOptions.path}`;
  }
  
  if (cookieOptions.domain) {
    cookieString += `; domain=${cookieOptions.domain}`;
  }
  
  document.cookie = cookieString;
};

/**
 * Check if cookies are enabled in the browser
 * @returns {boolean} True if cookies are enabled
 */
export const areCookiesEnabled = () => {
  try {
    const testCookie = 'test_cookie_enabled';
    setCookie(testCookie, 'test', { maxAge: 1 });
    const enabled = getCookie(testCookie) === 'test';
    removeCookie(testCookie);
    return enabled;
  } catch (error) {
    return false;
  }
};

/**
 * Get all cookies as an object
 * @returns {Object} Object with cookie names as keys and values as values
 */
export const getAllCookies = () => {
  const cookies = {};
  
  if (document.cookie) {
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  
  return cookies;
};

/**
 * Clear all cookies for the current domain
 * Note: This only clears cookies that are accessible via JavaScript
 */
export const clearAllCookies = () => {
  const cookies = getAllCookies();
  
  Object.keys(cookies).forEach(cookieName => {
    removeCookie(cookieName);
  });
};
