/**
 * Authentication debugging utilities
 * Helps diagnose authentication issues in development
 */

import authService from '../services/authService';
import { validateTokenStructure, isTokenExpired, getUserFromToken } from '../utils/tokenUtils';

/**
 * Get comprehensive authentication debug information
 * @returns {Object} Debug information about current auth state
 */
export const getAuthDebugInfo = () => {
  const token = authService.getToken();
  const user = authService.getCurrentUser();
  const refreshToken = authService.getRefreshToken();

  // Local Storage check
  const localStorageToken = localStorage.getItem('authToken');
  const localStorageUser = localStorage.getItem('assivy_user_data');
  const localStorageRefresh = localStorage.getItem('refreshToken');

  // Cookie check
  const getCookie = (name) => {
    try {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) {
        return decodeURIComponent(parts.pop().split(';').shift());
      }
    } catch (error) {
      return null;
    }
    return null;
  };

  const cookieToken = getCookie('assivy_auth_token');
  const cookieRefresh = getCookie('assivy_refresh_token');

  // Token validation
  let tokenValidation = null;
  let tokenExpired = null;
  let userFromToken = null;

  if (token) {
    tokenValidation = validateTokenStructure(token);
    tokenExpired = isTokenExpired(token);
    userFromToken = getUserFromToken(token);
  }

  return {
    // Current state
    currentToken: token ? 'Present' : 'Missing',
    currentUser: user ? 'Present' : 'Missing',
    currentRefreshToken: refreshToken ? 'Present' : 'Missing',
    
    // Storage comparison
    storage: {
      localStorage: {
        token: localStorageToken ? 'Present' : 'Missing',
        user: localStorageUser ? 'Present' : 'Missing',
        refresh: localStorageRefresh ? 'Present' : 'Missing'
      },
      cookies: {
        token: cookieToken ? 'Present' : 'Missing',
        refresh: cookieRefresh ? 'Present' : 'Missing'
      },
      mismatch: {
        token: token !== localStorageToken && token !== cookieToken,
        tokenSources: {
          service: token ? 'Present' : 'Missing',
          localStorage: localStorageToken ? 'Present' : 'Missing',
          cookie: cookieToken ? 'Present' : 'Missing'
        }
      }
    },
    
    // Token analysis
    tokenAnalysis: token ? {
      isValid: tokenValidation?.isValid || false,
      isExpired: tokenExpired,
      errors: tokenValidation?.errors || [],
      userFromToken: userFromToken ? 'Present' : 'Missing',
      userMatches: user && userFromToken ? user.id === userFromToken.id : 'N/A'
    } : null,
    
    // Recommendations
    recommendations: []
  };
};

/**
 * Log authentication debug information to console
 */
export const logAuthDebugInfo = () => {
  const debugInfo = getAuthDebugInfo();
  
  console.group('🔐 Authentication Debug Information');
  console.log('Current State:', {
    token: debugInfo.currentToken,
    user: debugInfo.currentUser,
    refreshToken: debugInfo.currentRefreshToken
  });
  
  console.log('Storage Analysis:', debugInfo.storage);
  
  if (debugInfo.tokenAnalysis) {
    console.log('Token Analysis:', debugInfo.tokenAnalysis);
  }
  
  // Add recommendations
  const recommendations = [];
  
  if (debugInfo.storage.mismatch.token) {
    recommendations.push('Token mismatch detected between storage sources');
  }
  
  if (debugInfo.tokenAnalysis?.isExpired) {
    recommendations.push('Token is expired and needs refresh');
  }
  
  if (debugInfo.tokenAnalysis && !debugInfo.tokenAnalysis.isValid) {
    recommendations.push('Token structure is invalid: ' + debugInfo.tokenAnalysis.errors.join(', '));
  }
  
  if (debugInfo.currentToken === 'Present' && debugInfo.currentUser === 'Missing') {
    recommendations.push('Token exists but user data is missing');
  }
  
  if (debugInfo.tokenAnalysis?.userFromToken === 'Present' && !debugInfo.tokenAnalysis.userMatches) {
    recommendations.push('User data mismatch between stored user and token');
  }
  
  if (recommendations.length > 0) {
    console.warn('Recommendations:', recommendations);
  } else {
    console.log('✅ No issues detected');
  }
  
  console.groupEnd();
  
  return debugInfo;
};

/**
 * Attempt to fix common authentication issues
 * @returns {boolean} True if fixes were applied
 */
export const attemptAuthFix = () => {
  console.log('🔧 Attempting to fix authentication issues...');
  
  try {
    const success = authService.syncAuthState();
    
    if (success) {
      console.log('✅ Authentication state synchronized successfully');
      return true;
    } else {
      console.log('❌ Could not synchronize authentication state');
      return false;
    }
  } catch (error) {
    console.error('❌ Error during auth fix attempt:', error);
    return false;
  }
};

/**
 * Clear all authentication data (nuclear option)
 */
export const clearAllAuthData = () => {
  console.warn('🧹 Clearing all authentication data...');
  
  authService.clearAuth();
  
  // Also clear any orphaned data
  try {
    localStorage.removeItem('authToken');
    localStorage.removeItem('assivy_user_data');
    localStorage.removeItem('refreshToken');
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
  
  console.log('✅ All authentication data cleared');
};

// Make debug functions available globally in development
if (import.meta.env.VITE_APP_ENV === 'development') {
  window.authDebug = {
    getInfo: getAuthDebugInfo,
    logInfo: logAuthDebugInfo,
    fix: attemptAuthFix,
    clear: clearAllAuthData
  };
  
  console.log('🔧 Auth debug tools available: window.authDebug');
}
