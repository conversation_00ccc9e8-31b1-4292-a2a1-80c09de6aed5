[project]
name = "assivy-backend"
version = "0.1.0"
description = "Backend service for Assivy AI platform"
authors = [
    {name = "Assivy Team"}
]
requires-python = ">=3.12"
dependencies = [
    "fastapi",
    "python-multipart",
    "uvicorn",
    "motor",
    "pyjwt",
    "python-jose[cryptography]",
    "passlib[bcrypt]>=1.7.4",
    "bcrypt>=4.0.0",
    "pydantic[email]",
    "langchain",
    "langgraph",
    "langchain-core",
    "langchain-community",
    "langchain-openai",
    "langchain-google-genai",
    "langchain-anthropic",
    "python-dotenv",
    "langchain-mongodb>=0.6.1",
    "apscheduler>=3.11.0",
    "pydantic-settings>=2.9.1",
    "langchain-milvus>=0.1.10",
    "aiofiles>=24.1.0",
    "astrapy>=2.0.1",
    "weaviate-client>=4.15.2",
    "langchain-weaviate>=0.0.1",
    "authlib>=1.2.1",
    # Core file processing dependencies
    # "unstructured[all-docs]>=0.10.0",  # Commented out due to PyTorch dependency conflicts
    "python-magic>=0.4.27",
    "chardet>=5.2.0",
    "beautifulsoup4>=4.12.0",
    "requests>=2.31.0",
    "lxml>=4.9.0",
    "pypdf>=3.17.0",
    "python-docx>=1.1.0",
    "docx2txt>=0.8",
    "openpyxl>=3.1.0",
    "pandas>=2.1.0",
    "markdown>=3.5.0",
    "pdfminer.six>=20231228",
    "pdfplumber>=0.9.0",
    "eml-parser>=1.17.0",
    "extract-msg>=0.41.0",
    "python-pptx>=0.6.21",
    "nbconvert>=7.0.0",
    "jupyter>=1.0.0",
    "xlrd>=2.0.1",
    "odfpy>=1.4.1",
    "ebooklib>=0.18",
    "python-magic-bin>=0.4.14; sys_platform == 'win32'",
    # Audio dependencies commented out for platform compatibility
    # "openai-whisper>=20231117; extra == 'audio'",
    # "torch>=2.0.0; extra == 'audio'",
    # "librosa>=0.10.0; extra == 'audio'",
    # File storage dependencies
    "minio>=7.2.0",
    "boto3>=1.34.0",
    "botocore>=1.34.0",
    "scikit-learn>=1.7.0",
    "assistant-stream>=0.0.25",
    "google-generativeai>=0.8.5",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "httpx>=0.24.0",
    "factory-boy>=3.2.0",
    "faker>=18.0.0",
    "freezegun>=1.2.0",
]

dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.coverage.run]
source = ["."]
omit = [
    "tests/*",
    "venv/*",
    ".venv/*",
    "*/venv/*",
    "*/.venv/*",
    "__pycache__/*",
    "*/migrations/*",
    "conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[dependency-groups]
test = [
    "factory-boy>=3.3.3",
    "faker>=37.4.0",
    "freezegun>=1.5.2",
    "httpx>=0.27.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.14.1",
]
