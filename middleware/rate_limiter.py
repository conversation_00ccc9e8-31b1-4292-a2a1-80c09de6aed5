from fastapi import Request, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Callable, Optional, Protocol, Dict, Set
import time
from abc import ABC, abstractmethod
from collections import defaultdict
import asyncio

from config import settings

class RateLimitStorage(ABC):
    """Abstract base class for rate limit storage implementations"""
    
    @abstractmethod
    async def add_request(self, key: str, timestamp: int, window: int) -> None:
        """Add a request to the storage"""
        pass
    
    @abstractmethod
    async def get_request_count(self, key: str, window: int) -> int:
        """Get the number of requests in the current window"""
        pass
    
    @abstractmethod
    async def cleanup_old_requests(self, key: str, window: int) -> None:
        """Remove requests outside the current window"""
        pass

class InMemoryStorage(RateLimitStorage):
    """In-memory implementation of rate limit storage"""
    
    def __init__(self):
        # Dictionary to store request timestamps for each key
        self._storage: Dict[str, Set[int]] = defaultdict(set)
        self._lock = asyncio.Lock()
    
    async def add_request(self, key: str, timestamp: int, window: int) -> None:
        async with self._lock:
            self._storage[key].add(timestamp)
    
    async def get_request_count(self, key: str, window: int) -> int:
        current_time = int(time.time())
        window_start = current_time - window
        
        async with self._lock:
            return len([ts for ts in self._storage[key] if ts > window_start])
    
    async def cleanup_old_requests(self, key: str, window: int) -> None:
        current_time = int(time.time())
        window_start = current_time - window
        
        async with self._lock:
            self._storage[key] = {ts for ts in self._storage[key] if ts > window_start}

class RateLimiter:
    def __init__(
        self,
        storage: Optional[RateLimitStorage] = None,
        default_rate_limit: int = 100,  # requests per window
        default_window: int = 60,  # window size in seconds
        key_prefix: str = "rate_limit:"
    ):
        self.storage = storage or InMemoryStorage()
        self.default_rate_limit = default_rate_limit
        self.default_window = default_window
        self.key_prefix = key_prefix

    async def _get_client_identifier(self, request: Request) -> str:
        """Get a unique identifier for the client"""
        # Try to get user ID from request state (set by auth middleware)
        if hasattr(request.state, "user_id"):
            return f"user:{request.state.user_id}"
        
        # Fallback to IP address
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return f"ip:{forwarded.split(',')[0].strip()}"
        return f"ip:{request.client.host}"

    async def _get_rate_limit_key(self, request: Request) -> str:
        """Generate a unique key for rate limiting"""
        client_id = await self._get_client_identifier(request)
        path = request.url.path
        return f"{self.key_prefix}{client_id}:{path}"

    async def _check_rate_limit(
        self,
        key: str,
        rate_limit: Optional[int] = None,
        window: Optional[int] = None
    ) -> tuple[bool, int, int]:
        """Check if the request is within rate limits"""
        rate_limit = rate_limit or self.default_rate_limit
        window = window or self.default_window
        
        # Get current timestamp
        now = int(time.time())
        
        # Cleanup old requests
        await self.storage.cleanup_old_requests(key, window)
        
        # Get current request count
        request_count = await self.storage.get_request_count(key, window)
        
        # Add current request
        await self.storage.add_request(key, now, window)
        
        # Check if within limits
        is_allowed = request_count < rate_limit
        remaining = max(0, rate_limit - request_count - 1)
        
        return is_allowed, remaining, rate_limit

    async def __call__(
        self,
        request: Request,
        call_next: Callable,
        rate_limit: Optional[int] = None,
        window: Optional[int] = None
    ):
        # Skip rate limiting for certain paths
        if request.url.path in ["/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)

        # Get rate limit key
        key = await self._get_rate_limit_key(request)
        
        # Check rate limit
        is_allowed, remaining, limit = await self._check_rate_limit(
            key, rate_limit, window
        )
        
        # Add rate limit headers
        response = await call_next(request) if is_allowed else JSONResponse(
            status_code=429,
            content={
                "detail": "Rate limit exceeded",
                "retry_after": window or self.default_window
            },
            headers={
                "Retry-After": str(window or self.default_window)
            }
        )
        
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        
        return response

def rate_limit(rate_limit: int = None, window: int = None):
    """Decorator for rate limiting FastAPI endpoints"""
    def decorator(func):
        async def wrapper(*args, request: Request = None, **kwargs):
            # Get the request object from args if not in kwargs
            if request is None and args:
                request = next((arg for arg in args if isinstance(arg, Request)), None)
            if request is None:
                raise ValueError("Could not find FastAPI Request object")

            # Check rate limit
            key = await rate_limiter._get_rate_limit_key(request)
            is_allowed, remaining, limit = await rate_limiter._check_rate_limit(
                key, rate_limit, window
            )
            
            if not is_allowed:
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded",
                    headers={
                        "Retry-After": str(window or rate_limiter.default_window),
                        "X-RateLimit-Limit": str(limit),
                        "X-RateLimit-Remaining": str(remaining)
                    }
                )
            
            # Call the original function
            response = await func(*args, **kwargs)
            
            # Add rate limit headers if response is JSONResponse
            if isinstance(response, JSONResponse):
                response.headers["X-RateLimit-Limit"] = str(limit)
                response.headers["X-RateLimit-Remaining"] = str(remaining)
            
            return response
        return wrapper
    return decorator

# Create a global rate limiter instance with in-memory storage
rate_limiter = RateLimiter(
    storage=InMemoryStorage(),
    default_rate_limit=settings.DEFAULT_RATE_LIMIT,
    default_window=settings.DEFAULT_WINDOW
)