from typing import List, Optional

from fastapi import Depends, HTTPException, status
from models.role import Permission
from models.user import User
from session.running_context import RunningContext, require_context


class PermissionChecker:
    def __init__(self, required_permissions: Optional[List[Permission]] = None):
        self.required_permissions = set(required_permissions) if required_permissions else set()

    async def __call__(self) -> RunningContext:
        # Get the current context which must exist
        context = require_context()

        # Get the current user from context
        if not context.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No authenticated user found",
            )

        # Check permissions if any are required
        if self.required_permissions:
            user_permissions = await self._get_user_permissions(context.user)
            
            # Check if user has any of the required permissions OR admin permissions
            has_required_permission = bool(self.required_permissions.intersection(user_permissions))
            has_admin_permission = bool({Permission.TENANT_ADMIN, Permission.SYSTEM_ADMIN}.intersection(user_permissions))
            
            if not (has_required_permission or has_admin_permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"User '{context.user_id}' lacks required permissions. Required: {', '.join(r.value for r in self.required_permissions)}.",
                )

        return context

    async def _get_user_permissions(self, user: User) -> set:
        """Get all permissions for a user based on their role"""
        try:
            from services.role_service import role_service
            role = await role_service.get_role_with_default(user.role_id)
            return role.permissions if role else set()
        except Exception:
            # If we can't get permissions, return empty set (deny access)
            return set()


# Convenience function to create a permission checker
def requires_permissions(permissions: Optional[List[Permission]] = None):
    """
    Dependency function to check if the current user has required permissions

    Args:
        permissions: List of required permissions. If None, only authentication is checked.
    """
    return PermissionChecker(permissions)

# Convenience functions for common permission patterns
def requires_system_admin():
    """Require system admin permission"""
    return PermissionChecker([Permission.SYSTEM_ADMIN])

def requires_tenant_admin():
    """Require tenant admin permissions (all non-system permissions)"""
    return PermissionChecker([Permission.TENANT_ADMIN])

def requires_any_system_permission():
    """Require any system-level permission"""
    system_permissions = [perm for perm in Permission if perm.value.startswith("system:")]
    return PermissionChecker(system_permissions)