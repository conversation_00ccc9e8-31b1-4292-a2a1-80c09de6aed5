from fastapi import Request, Response
from session.running_context import clear_context, get_context
import logging

logger = logging.getLogger(__name__)

async def context_middleware(request: Request, call_next):
    """Middleware to manage running context lifecycle"""
    try:
        response = await call_next(request)
        
        # Log request completion with context info
        context = get_context()
        if context:
            logger.info(
                f"Request completed - ID: {context.request_id}, "
                f"User: {context.user_id}, Duration: {context.duration():.3f}s"
            )
        
        return response
    except Exception as e:
        # Log error with context info
        context = get_context()
        if context:
            logger.error(
                f"Request failed - ID: {context.request_id}, "
                f"User: {context.user_id}, Error: {str(e)}"
            )
        raise
    finally:
        # Always clear context after request
        clear_context()