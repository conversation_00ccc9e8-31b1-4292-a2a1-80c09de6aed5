# Docker Compose for Assivy Backend Development
# Includes MinIO for object storage and Weaviate for vector database

version: '3.8'

services:
  # MinIO Object Storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: assivy-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"   # MinIO API
      - "9001:9001"   # MinIO Console
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - assivy-network

  # Weaviate Vector Database
  weaviate:
    image: semitechnologies/weaviate:latest
    container_name: assivy-weaviate
    ports:
      - "8080:8080"   # Weaviate API
      - "50051:50051" # gRPC endpoint
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/meta"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - assivy-network

  # MongoDB (if you want to run it locally)
  # mongodb:
  #   image: mongo:6.0
  #   container_name: assivy-mongodb
  #   ports:
  #     - "27017:27017"
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: admin
  #     MONGO_INITDB_ROOT_PASSWORD: password
  #   volumes:
  #     - mongodb_data:/data/db
  #   networks:
  #     - assivy-network

  # Redis (for caching and task queues)
  # redis:
  #   image: redis:7-alpine
  #   container_name: assivy-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - assivy-network

volumes:
  minio_data:
    driver: local
  weaviate_data:
    driver: local
  # mongodb_data:
  #   driver: local
  # redis_data:
  #   driver: local

networks:
  assivy-network:
    driver: bridge
