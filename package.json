{"name": "ai_agent_dashboard", "version": "0.1.0", "private": true, "dependencies": {"@assistant-ui/react": "^0.10.26", "@assistant-ui/react-langgraph": "^0.5.10", "@assistant-ui/react-markdown": "^0.10.6", "@assistant-ui/react-syntax-highlighter": "^0.10.8", "@cyntler/react-doc-viewer": "^1.17.0", "@dhiwise/component-tagger": "^1.0.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@lexical/code": "^0.33.0", "@lexical/link": "^0.33.0", "@lexical/list": "^0.33.0", "@lexical/react": "^0.33.0", "@lexical/rich-text": "^0.33.0", "@lexical/selection": "^0.33.0", "@lexical/table": "^0.33.0", "@lexical/utils": "^0.33.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "^5.15.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.18.2", "lexical": "^0.33.0", "lucide-react": "^0.484.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-router-dom": "^6.8.0", "recharts": "^2.15.4", "redux": "^5.0.1", "shiki": "^3.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-elevation": "^2.0.0", "tailwindcss-fluid-type": "^2.0.7", "vaul": "^1.1.2", "zod": "^4.0.5"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/line-clamp": "^0.1.0", "@tailwindcss/typography": "^0.5.16", "@types/react": "18.3.23", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.2", "eslint": "^8.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.8", "tailwindcss": "^3.4.6", "typescript": "5.8.3", "vite": "^4.4.0", "vite-tsconfig-paths": "^4.2.0"}}