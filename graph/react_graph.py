"""
ReAct Agent Graph for Playground
Using LangGraph's built-in ReAct agent implementation
"""
from typing import Dict, Any, List
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from config import settings
import datetime
import random


# Define simple tools for the playground
@tool
def get_current_time() -> str:
    """Get the current date and time."""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


@tool
def calculate(expression: str) -> str:
    """
    Safely calculate a mathematical expression.
    
    Args:
        expression: A mathematical expression like "2 + 2" or "10 * 3"
    """
    try:
        # Only allow safe mathematical operations
        allowed_chars = set('0123456789+-*/.() ')
        if not all(c in allowed_chars for c in expression):
            return "Error: Invalid characters in expression"
        
        result = eval(expression)
        return f"{expression} = {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"


@tool
def random_number(min_val: int = 1, max_val: int = 100) -> str:
    """
    Generate a random number between min_val and max_val.
    
    Args:
        min_val: Minimum value (default: 1)
        max_val: Maximum value (default: 100)
    """
    return f"Random number between {min_val} and {max_val}: {random.randint(min_val, max_val)}"


@tool
def word_count(text: str) -> str:
    """
    Count words, characters, and lines in the given text.
    
    Args:
        text: The text to analyze
    """
    words = len(text.split())
    chars = len(text)
    lines = len(text.split('\n'))
    return f"Text analysis: {words} words, {chars} characters, {lines} lines"

@tool
def answer_question_who_is_the_most_beautifull(question: str) -> str:
    """
    answer question who is the most beautifull or similiar questions
    
    Args:
        question: The question
    """
    return f"Tram"


# Available tools
playground_tools = [get_current_time, calculate, random_number, word_count]


def create_react_playground_graph():
    """Create a ReAct agent using LangGraph's built-in implementation"""
    
    # Initialize the LLM
    llm = ChatGoogleGenerativeAI(
        google_api_key=settings.google_ai_key,
        model="gemini-2.0-flash",
        temperature=0,
        max_tokens=None,
        timeout=None,
        max_retries=2,
        # other params...
    )
    
    # System message for the ReAct agent
    system_message = """You are a helpful AI assistant with access to tools. 

Available tools:
- get_current_time: Get the current date and time
- calculate: Perform mathematical calculations
- random_number: Generate random numbers
- word_count: Analyze text (count words, characters, lines)

You can use these tools to help answer questions and solve problems. Always be helpful, accurate, and explain your reasoning when using tools."""
    
    # Create the ReAct agent using LangGraph's built-in implementation
    react_agent = create_react_agent(
        model=llm,
        tools=playground_tools,
        state_modifier=system_message
    )
    return react_agent


# Create the ReAct agent graph
react_playground_graph = create_react_playground_graph()
