import os
import logging
import asyncio
from contextlib import asynccontextmanager

# Import warnings suppression first - this sets up environment and suppresses warnings
import utils.warnings_suppression

# Now import other modules
from fastapi import FastAP<PERSON>, Request, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from middleware.context_middleware import context_middleware
from routes import tenants, resources, tasks, agents, entities, actions, system, integrations, users, organization, files, pipelines, libraries, unified_auth
# Temporarily disable shelves import due to dependency issues
# from routes import shelves
from middleware.rate_limiter import rate_limiter
from services.task_scheduler import TaskScheduler
from data_access.weaviatedb import connect_to_weaviate, close_weaviate_connection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create uploads directory if it doesn't exist
os.makedirs("uploads", exist_ok=True)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logging.info("Starting up the application...")
    
    # Connect to Weaviate first (optional for testing)
    try:
        await connect_to_weaviate()
    except Exception as e:
        logging.warning(f"Failed to connect to Weaviate: {e}. Continuing without Weaviate for testing.")
    
    # Initialize the service manager (optional for testing)
    try:
        from services.service_manager import service_manager
        await service_manager.initialize()
    except Exception as e:
        logging.warning(f"Failed to initialize service manager: {e}. Continuing without full service initialization for testing.")
    
    # Initialize services
    #from services.task_service import task_service
    
    # Initialize repositories
    #await task_service.initialize()
    
    # Initialize and start task scheduler
    #task_scheduler = TaskScheduler()
    #asyncio.create_task(task_scheduler.start())
    
    yield
    
    # Shutdown
    logging.info("Shutting down the application...")
    
    # Shutdown service manager
    await service_manager.shutdown()
    
    # Close Weaviate connection
    await close_weaviate_connection()

app = FastAPI(
    title="Assivy Backend",
    description="API for managing resources, agents, and actions",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware - Allow specific origins with credentials
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000", "http://localhost:4028"],  # Specific origins
    allow_credentials=True,  # Allow credentials with specific origins
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Add middleware
app.middleware("http")(rate_limiter)
app.middleware("http")(context_middleware)

# Mount static files for uploads
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Create main API router with /api prefix
api_router = APIRouter(prefix="/api")

# Include routers
api_router.include_router(unified_auth.router)  # Authentication routes
api_router.include_router(users.router)
api_router.include_router(organization.router)
api_router.include_router(tenants.router)
api_router.include_router(resources.router)
api_router.include_router(libraries.router)
api_router.include_router(libraries.shelf_router)  # Include shelf router from libraries module
api_router.include_router(tasks.router)
api_router.include_router(agents.router)
api_router.include_router(entities.router)
api_router.include_router(actions.router)
api_router.include_router(integrations.router)
api_router.include_router(files.router)
api_router.include_router(pipelines.router)
api_router.include_router(system.router)  # Now includes dev endpoints

app.include_router(api_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)