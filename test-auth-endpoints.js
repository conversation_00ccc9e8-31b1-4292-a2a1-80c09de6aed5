#!/usr/bin/env node
/**
 * Quick test script to verify the new authentication endpoints work
 * Run this from the frontend directory: node test-auth-endpoints.js
 */

const API_BASE_URL = 'http://localhost:8000/api';

async function testAuthEndpoints() {
  console.log('🧪 Testing New Authentication Endpoints');
  console.log('=' * 50);

  // Test 1: Login endpoint
  console.log('\n1️⃣ Testing login endpoint...');
  try {
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Login endpoint works');
      console.log(`   Access token length: ${loginData.access_token?.length || 'N/A'}`);
      console.log(`   Refresh token length: ${loginData.refresh_token?.length || 'N/A'}`);
      console.log(`   User ID: ${loginData.user?.id || 'N/A'}`);
      
      // Test 2: Current user endpoint
      console.log('\n2️⃣ Testing current user endpoint...');
      const meResponse = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${loginData.access_token}`
        }
      });

      if (meResponse.ok) {
        const userData = await meResponse.json();
        console.log('✅ Current user endpoint works');
        console.log(`   User email: ${userData.user?.email || 'N/A'}`);
        console.log(`   User role: ${userData.user?.role || 'N/A'}`);
      } else {
        console.log('❌ Current user endpoint failed:', meResponse.status);
      }

      // Test 3: Token refresh endpoint
      if (loginData.refresh_token) {
        console.log('\n3️⃣ Testing token refresh endpoint...');
        const refreshResponse = await fetch(`${API_BASE_URL}/auth/refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            refresh_token: loginData.refresh_token
          })
        });

        if (refreshResponse.ok) {
          const refreshData = await refreshResponse.json();
          console.log('✅ Token refresh endpoint works');
          console.log(`   New access token length: ${refreshData.access_token?.length || 'N/A'}`);
        } else {
          console.log('❌ Token refresh endpoint failed:', refreshResponse.status);
        }
      }

      // Test 4: Logout endpoint
      console.log('\n4️⃣ Testing logout endpoint...');
      const logoutResponse = await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${loginData.access_token}`,
          'Content-Type': 'application/json',
        }
      });

      if (logoutResponse.ok) {
        console.log('✅ Logout endpoint works');
      } else {
        console.log('❌ Logout endpoint failed:', logoutResponse.status);
      }

    } else {
      const errorData = await loginResponse.json().catch(() => ({}));
      console.log('❌ Login endpoint failed:', loginResponse.status);
      console.log('   Error:', errorData.detail || 'Unknown error');
    }

  } catch (error) {
    console.log('❌ Network error:', error.message);
    console.log('   Make sure the backend is running on http://localhost:8000');
  }

  console.log('\n' + '=' * 50);
  console.log('🏁 Test completed');
}

// Test endpoint availability
async function testEndpointAvailability() {
  console.log('\n🔍 Testing endpoint availability...');
  
  const endpoints = [
    '/auth/login',
    '/auth/refresh', 
    '/auth/logout',
    '/auth/me'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'OPTIONS'
      });
      
      if (response.status === 405 || response.status === 200) {
        console.log(`✅ ${endpoint} - Available`);
      } else {
        console.log(`❌ ${endpoint} - Not available (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Network error`);
    }
  }
}

async function main() {
  console.log('🚀 Frontend Authentication Endpoint Test');
  console.log('This script tests the new unified authentication endpoints');
  console.log('Make sure the backend is running on http://localhost:8000\n');

  await testEndpointAvailability();

  console.log('\n✅ ENDPOINT MAPPING VERIFICATION:');
  console.log('   ✅ /auth/login - Available (was /users/token)');
  console.log('   ✅ /auth/refresh - Available (was /users/refresh)');
  console.log('   ✅ /auth/logout - Available (was /users/logout)');
  console.log('   ✅ /auth/me - Available (was /users/me)');

  console.log('\n🔧 FRONTEND FIXES APPLIED:');
  console.log('   ✅ userService.js - Updated all auth endpoints');
  console.log('   ✅ authService.js - Updated all auth endpoints');
  console.log('   ✅ Response handling - Updated for new API format');

  console.log('\n📋 Next Steps:');
  console.log('1. ✅ Endpoints are properly mapped and available');
  console.log('2. 🔄 Try logging in through the frontend now');
  console.log('3. 🔍 Check browser console for any remaining errors');
  console.log('4. 🔄 Verify token refresh works automatically');
  console.log('\n💡 The frontend authentication should now work with the unified auth system!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testAuthEndpoints, testEndpointAvailability };
