# Default Test Credentials

This file contains the default test credentials created by the system initialization endpoints.

## 🚀 System Initialization

The system uses separate endpoints for initialization:

1. **System Setup**: `POST /api/system/v1/init` - Creates roles and platform tenant
2. **Test Tenants**: `POST /api/system/v1/test/init-tenants` - Creates test tenants (development only)
3. **Test Users**: `POST /api/system/v1/test/init-users` - Creates test users (development only)

## 🔑 Standard Test Users

All test users use the password: **`TestPassword123!`**

### System Administrator
**Purpose**: Platform-wide administration and tenant management

- **ID**: `00000000-0000-0000-0000-000000000001`
- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **Role**: SystemAdmin
- **Tenant**: Assivy System Platform (`00000000-0000-0000-0000-000000000001`)

**Capabilities**:
- ✅ Create and delete tenants
- ✅ Manage users across all tenants
- ✅ Access system-wide analytics
- ✅ Platform configuration and settings
- ✅ Cross-tenant operations
- ✅ Full API access with system permissions

### Tenant Administrator
**Purpose**: Full administration within the test customer tenant

- **ID**: `00000000-0000-0000-0000-000000000002`
- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **Role**: TenantAdmin
- **Tenant**: Test Customer Organization (`00000000-0000-0000-0000-000000000002`)

**Capabilities**:
- ✅ Full access within customer test tenant
- ✅ Create and manage tenant users
- ✅ Manage agents, resources, and tasks
- ✅ View tenant analytics
- ✅ Team management within tenant
- ❌ Cannot create/delete other tenants
- ❌ Cannot access other tenants' data

### Manager
**Purpose**: Team management and resource oversight

- **ID**: `00000000-0000-0000-0000-000000000003`
- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **Role**: Manager
- **Tenant**: Test Customer Organization (`00000000-0000-0000-0000-000000000002`)

**Capabilities**:
- ✅ Manage team members
- ✅ Create and manage resources
- ✅ Assign and monitor tasks
- ✅ View team analytics
- ❌ Cannot manage tenant settings
- ❌ Cannot access system administration

### Member
**Purpose**: Basic user access for testing standard functionality

- **ID**: `00000000-0000-0000-0000-000000000004`
- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **Role**: Member
- **Tenant**: Test Customer Organization (`00000000-0000-0000-0000-000000000002`)

**Capabilities**:
- ✅ Read and execute agents
- ✅ Read and search resources
- ✅ Read tasks
- ❌ Cannot create or manage resources
- ❌ Cannot manage other users
- ❌ Limited administrative access

## 🏢 Test Tenants

### System Platform Tenant
- **ID**: `00000000-0000-0000-0000-000000000001`
- **Name**: Assivy System Platform
- **Description**: System administration tenant
- **Type**: System

### Test Customer Tenant
- **ID**: `00000000-0000-0000-0000-000000000002`
- **Name**: Test Customer Organization
- **Description**: Standard test customer tenant
- **Type**: Customer

### Demo Tenant
- **ID**: `00000000-0000-0000-0000-000000000003`
- **Name**: Demo Organization
- **Description**: Demonstration tenant for showcasing features
- **Type**: Demo

---

## � API Testing Commands

### Authentication
```bash
# Login with any test user
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'

# Get user info (after login)
curl -X GET http://localhost:8000/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### System Administration (System Admin only)
```bash
# Initialize system (creates roles and platform tenant)
curl -X POST http://localhost:8000/api/system/v1/init

# Create test tenants (development only)
curl -X POST http://localhost:8000/api/system/v1/test/init-tenants

# Create test users (development only)
curl -X POST http://localhost:8000/api/system/v1/test/init-users

# Get test fixtures reference
curl -X GET http://localhost:8000/api/system/v1/test/fixtures

# List all tenants
curl -X GET http://localhost:8000/tenants \
  -H "Authorization: Bearer YOUR_SYSTEM_ADMIN_TOKEN"
```

### Development Utilities
```bash
# Generate permanent test token
curl -X POST http://localhost:8000/api/system/v1/test/tokens/permanent \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "00000000-0000-0000-0000-000000000001",
    "expires_days": 30
  }'

# Check system status
curl -X GET http://localhost:8000/api/system/v1/status

# List database collections
curl -X GET http://localhost:8000/api/system/v1/collections \
  -H "Authorization: Bearer YOUR_SYSTEM_ADMIN_TOKEN"
```

---

## 🔐 Security Configuration

### Development Mode Features
- Email verification is **disabled** (auto-verified)
- Test endpoints are **enabled** (init-users, init-tenants)
- Detailed error messages shown
- Bypass contexts available for testing

### Production Mode
⚠️ **Important**: Test endpoints are automatically disabled in production mode.

Before deploying to production:

1. **Set environment to production**:
   ```python
   environment: str = "production"
   ```

2. **Configure strong JWT secret**:
   ```python
   secret_key: str = "YOUR_STRONG_SECRET_KEY"
   ```

3. **Configure email service** for verification

4. **Review and update default passwords** if needed

---

## 🆘 Troubleshooting

### System Initialization
```bash
# Step 1: Initialize system (roles and platform tenant)
curl -X POST http://localhost:8000/api/system/v1/init

# Step 2: Create test tenants (development only)
curl -X POST http://localhost:8000/api/system/v1/test/init-tenants

# Step 3: Create test users (development only)
curl -X POST http://localhost:8000/api/system/v1/test/init-users
```

### Common Issues
1. **"Role not found"** → Run system initialization: `POST /api/system/v1/init`
2. **"Invalid credentials"** → Check email/password spelling (case-sensitive)
3. **"Permission denied"** → Verify user role and permissions
4. **"Tenant not found"** → Ensure user has proper tenant association
5. **"Test endpoints disabled"** → Ensure you're in development mode
6. **"User already exists"** → Test users have fixed UUIDs and may already exist

### Reset Development Environment
```bash
# Get current test fixtures
curl -X GET http://localhost:8000/api/system/v1/test/fixtures

# Reinitialize if needed (will skip existing entities)
curl -X POST http://localhost:8000/api/system/v1/init
curl -X POST http://localhost:8000/api/system/v1/test/init-tenants
curl -X POST http://localhost:8000/api/system/v1/test/init-users
```

---

## 📋 Test Data Reference

All test entities use **predictable UUIDs** for consistent testing:

- **System Admin**: `00000000-0000-0000-0000-000000000001`
- **Tenant Admin**: `00000000-0000-0000-0000-000000000002`
- **Manager**: `00000000-0000-0000-0000-000000000003`
- **Member**: `00000000-0000-0000-0000-000000000004`

- **System Platform Tenant**: `00000000-0000-0000-0000-000000000001`
- **Test Customer Tenant**: `00000000-0000-0000-0000-000000000002`
- **Demo Tenant**: `00000000-0000-0000-0000-000000000003`

This design enables reliable automated testing and AI-driven test scenarios.

---

**Last Updated**: July 27, 2025
**System Version**: Multi-tenant SaaS with Role-based Permissions and Predictable Test Data
