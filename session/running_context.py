from typing import List, Optional, Dict, Any, Set
from contextvars import <PERSON>text<PERSON><PERSON>
from dataclasses import dataclass, field
from datetime import datetime, timezone
from uuid import UUID
from models.user import User
from contextlib import contextmanager
import uuid

@dataclass
class RunningContext:
    """Running context that holds all request-scoped data"""
    user: User
    request_id: str
    started_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)
    _user_permissions: Optional[Set[str]] = field(default=None, init=False)
    
    @property
    def user_id(self) -> UUID:
        """Get current user ID"""
        return self.user.id
    
    @property
    def tenant_id(self) -> Optional[UUID]:
        """Get current tenant ID"""
        return self.user.tenant_id
    
    async def get_user_permissions(self) -> Set[str]:
        """Get user permissions (cached)"""
        if self._user_permissions is None:
            try:
                from services.service_manager import get_role_service
                role_service = await get_role_service()
                role = await role_service.get_role_with_default(self.user.role_id)
                self._user_permissions = {perm.value for perm in role.permissions} if role else set()
            except Exception:
                self._user_permissions = set()
        return self._user_permissions
    
    async def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
        user_permissions = await self.get_user_permissions()
        return permission in user_permissions
    
    async def has_any_permission(self, permissions: List[str]) -> bool:
        """Check if user has any of the specified permissions"""
        user_permissions = await self.get_user_permissions()
        return bool(set(permissions).intersection(user_permissions))
    
    async def has_all_permissions(self, permissions: List[str]) -> bool:
        """Check if user has all of the specified permissions"""
        user_permissions = await self.get_user_permissions()
        return set(permissions).issubset(user_permissions)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to the running context"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata from the running context"""
        return self.metadata.get(key, default)
    
    def duration(self) -> float:
        """Get request duration in seconds"""
        return (datetime.now(timezone.utc) - self.started_at).total_seconds()

# Use ContextVar for async-safe context storage
_context_var: ContextVar[Optional[RunningContext]] = ContextVar('running_context', default=None)

def get_context() -> Optional[RunningContext]:
    """Get the current running context from context variable"""
    return _context_var.get()

def set_context(context: RunningContext):
    """Set the running context in context variable"""
    _context_var.set(context)

def clear_context():
    """Clear the running context"""
    _context_var.set(None)

def require_context() -> RunningContext:
    """Get running context or raise exception if not found"""
    from fastapi import HTTPException, status
    context = get_context()
    if not context:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No running context found"
        )
    return context

def require_user() -> User:
    """Get current user or raise exception if not found"""
    from fastapi import HTTPException, status
    context = get_context()
    if not context or not context.user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authenticated user found"
        )
    return context.user


@contextmanager
def with_context(user: User, **metadata):
    """Context manager for background tasks and system operations"""
    context = RunningContext(
        user=user,
        request_id=str(uuid.uuid4()),
        metadata=metadata
    )
    
    # Store the previous context to restore later
    previous_context = get_context()
    set_context(context)
    try:
        yield context
    finally:
        # Restore the previous context
        if previous_context:
            set_context(previous_context)
        else:
            clear_context()