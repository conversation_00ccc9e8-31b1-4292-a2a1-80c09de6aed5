#!/usr/bin/env python3
"""
Test script to verify authentication endpoints are working
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import requests
from config import settings

def test_auth_endpoints():
    """Test authentication endpoints"""
    
    print("🔐 Testing Authentication Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test cases
    test_cases = [
        {
            "name": "Health Check",
            "method": "GET",
            "url": f"{base_url}/",
            "expected_status": 200
        },
        {
            "name": "Auth Login Endpoint (Invalid Credentials)",
            "method": "POST", 
            "url": f"{base_url}/api/auth/login",
            "data": {
                "email": "<EMAIL>",
                "password": "wrongpassword"
            },
            "expected_status": 401
        },
        {
            "name": "Test User Login (if exists)",
            "method": "POST",
            "url": f"{base_url}/api/auth/login", 
            "data": {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            },
            "expected_status": [200, 401]  # Either works or user doesn't exist
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print(f"Method: {test_case['method']}")
        print(f"URL: {test_case['url']}")
        print("-" * 30)
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], timeout=10)
            elif test_case['method'] == 'POST':
                response = requests.post(
                    test_case['url'], 
                    json=test_case.get('data', {}),
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
            
            print(f"Status Code: {response.status_code}")
            
            # Check if status code matches expected
            expected = test_case['expected_status']
            if isinstance(expected, list):
                status_ok = response.status_code in expected
            else:
                status_ok = response.status_code == expected
            
            if status_ok:
                print("✅ Status: Expected")
            else:
                print(f"❌ Status: Expected {expected}, got {response.status_code}")
            
            # Try to parse response
            try:
                response_data = response.json()
                print(f"Response: {json.dumps(response_data, indent=2)}")
            except:
                print(f"Response Text: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Server not running or not accessible")
        except requests.exceptions.Timeout:
            print("❌ Timeout: Server took too long to respond")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print(f"\n🎯 Testing Complete!")
    print("=" * 50)

def test_cors_headers():
    """Test CORS configuration"""
    
    print("\n🌐 Testing CORS Configuration")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test preflight request
        response = requests.options(
            f"{base_url}/api/auth/login",
            headers={
                'Origin': 'http://localhost:4028',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=10
        )
        
        print(f"Preflight Status: {response.status_code}")
        print("CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"  {header}: {value}")
                
    except Exception as e:
        print(f"❌ CORS Test Error: {str(e)}")

def main():
    """Main test function"""
    
    print("🚀 Starting Authentication Endpoint Tests")
    print("=" * 60)
    
    # Test auth endpoints
    test_auth_endpoints()
    
    # Test CORS
    test_cors_headers()
    
    print(f"\n✨ All tests completed!")
    print("=" * 60)
    
    print("\n💡 Troubleshooting Tips:")
    print("- Make sure the backend server is running on localhost:8000")
    print("- Check if test users are initialized: POST /api/system/v1/test/init-users")
    print("- Verify CORS settings allow your frontend origin")
    print("- Check server logs for detailed error information")

if __name__ == "__main__":
    main()
