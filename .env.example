# Assivy Backend Environment Configuration
# Copy this file to .env and update with your values

# Environment
ENVIRONMENT=development

# Application
APP_URL=http://localhost:8000

# Database
MONGO_URL=mongodb+srv://your-username:<EMAIL>/
TESTING_MODE=false

# Application Secret (Change in production!)
SECRET_KEY=your-secret-key-here

# System Initialization Credentials (Change these for production!)
PLATFORM_TENANT_NAME=Assivy Platform
CUSTOMER_TENANT_NAME=Assivy Customer Test
SYSTEM_ADMIN_EMAIL=<EMAIL>
SYSTEM_ADMIN_PASSWORD=SystemAdmin123!
CUSTOMER_ADMIN_EMAIL=<EMAIL>
CUSTOMER_ADMIN_PASSWORD=Administrator123!

# OAuth Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# AI Model API Keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_AI_KEY=AIzaSyB_LHToe8sscEv_Df0Z7yfxHn9l3U1lmFQ
ANTHROPIC_API_KEY=your-anthropic-api-key

# Zilliz Cloud Settings
ZILLIZ_URI=your-zilliz-uri
ZILLIZ_TOKEN=your-zilliz-token

# AstraDB Settings
ASTRA_API_ENDPOINT=your-astra-endpoint
ASTRA_TOKEN=your-astra-token

# Weaviate Settings
WEAVIATE_CLUSTER_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-api-key
WEAVIATE_USE_CLOUD=true

# Rate Limiting
DEFAULT_RATE_LIMIT=100
DEFAULT_WINDOW=60

# Text Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Frontend Environment Variables
VITE_APP_ENV=development
VITE_API_BASE_URL=http://localhost:8000/api

# Production Security Notes:
# - Change all default passwords above
# - Use strong, unique SECRET_KEY (32+ characters)
# - Set ENVIRONMENT=production for production deployment
# - Configure proper email service for verification
# - Use secure database connections with authentication
# - Enable SSL/TLS certificates in production
# - Consider using environment-specific secrets management
