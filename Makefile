# Makefile for Assivy Backend Development and Testing
# This Makefile delegates to the unified CLI tool: assivy.py

.PHONY: help install install-dev install-test test test-unit test-integration test-fast test-watch lint format clean coverage run-dev run-prod services-start services-stop

# Default target
help: ## Show this help message
	@echo "Assivy Backend - Available commands:"
	@echo ""
	@echo "🚀 Server Commands:"
	@echo "  make dev              # Start development server with auto-reload"
	@echo "  make start            # Start production server"
	@echo "  make run-dev          # Alias for dev"
	@echo "  make run-prod         # Alias for start"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  make test             # Run all tests with coverage"
	@echo "  make test-unit        # Run unit tests only"
	@echo "  make test-integration # Run integration tests only"
	@echo "  make test-fast        # Run fast tests (exclude slow tests)"
	@echo "  make test-watch       # Run tests in watch mode"
	@echo "  make coverage         # Generate coverage report"
	@echo ""
	@echo "🔧 Development Commands:"
	@echo "  make install          # Install dependencies"
	@echo "  make install-dev      # Install development dependencies"
	@echo "  make install-test     # Install test dependencies"
	@echo "  make lint             # Run code linting"
	@echo "  make format           # Format code"
	@echo "  make type-check       # Run type checking"
	@echo "  make security         # Run security checks"
	@echo ""
	@echo "🐳 Services Commands:"
	@echo "  make services-start   # Start Docker services (MinIO, Weaviate)"
	@echo "  make services-stop    # Stop Docker services"
	@echo ""
	@echo "🧹 Utility Commands:"
	@echo "  make clean            # Clean generated files"
	@echo "  make env-check        # Check environment setup"
	@echo ""
	@echo "📖 For more detailed help and options:"
	@echo "  python assivy.py --help"
	@echo "  python assivy.py <command> --help"

# Server targets
dev: ## Start development server with auto-reload
	python assivy.py dev

start: ## Start production server
	python assivy.py start

run-dev: dev ## Alias for dev
run-prod: start ## Alias for start

# Testing targets
test: ## Run all tests with coverage
	python assivy.py test

test-unit: ## Run unit tests only
	python assivy.py test --unit

test-integration: ## Run integration tests only
	python assivy.py test --integration

test-fast: ## Run fast tests (exclude slow tests)
	python assivy.py test --fast

test-watch: ## Run tests in watch mode
	python assivy.py test --watch

coverage: ## Generate coverage report
	python assivy.py test --coverage

# Development targets
install: ## Install dependencies
	python assivy.py install

install-dev: ## Install development dependencies
	python assivy.py install --dev

install-test: ## Install test dependencies
	python assivy.py install --test

lint: ## Run code linting
	python assivy.py lint

format: ## Format code
	python assivy.py format

type-check: ## Run type checking
	python assivy.py type-check

security: ## Run security checks
	python assivy.py security

# Services targets
services-start: ## Start Docker services
	python assivy.py services start --wait

services-stop: ## Stop Docker services
	python assivy.py services stop

# Utility targets
clean: ## Clean generated files
	python assivy.py clean

env-check: ## Check environment setup
	python assivy.py env-check

# Quick development workflow
setup: install install-dev install-test ## Install all dependencies
	python assivy.py format

validate: lint type-check test ## Validate code quality and functionality

quick-test: test-fast ## Quick test run for development

# Legacy targets for compatibility (delegated to CLI)
ci-test: ## Run tests for CI environment
	python assivy.py test --ci

ci-lint: ## Run linting for CI environment
	python assivy.py lint

ci: ci-lint ci-test ## Run all CI checks

# Docker targets (delegated to CLI)
docker-build: ## Build Docker image
	docker build -t assivy-backend .

docker-run: ## Run Docker container
	docker run -p 8000:8000 assivy-backend
